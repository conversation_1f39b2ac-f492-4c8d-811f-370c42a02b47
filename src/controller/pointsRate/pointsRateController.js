const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class globalPointsRate extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_global_points_rate'
  }

  static get PRIMARY_KEY () {
    return 'flag'
  }

  /**
     * Returns TABLE_NAME details by its PRIMARY_KEY
     */
  static async getByID (_, { id }) {
    // eslint-disable-next-line no-return-await
    return await this.find(id)
  }

  /**
     * Returns a list of neft details matching the passed fields
     * @param {*} fields - Fields to be matched
     */
  static async findMatching (_, fields) {
    // Returns early with all points if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching points
    return this.findByFields({
      fields
    })
  }

  /**
     * Creates a new entry in ma_neft_details table
     */
  // eslint-disable-next-line camelcase
  static async createEntry (_, { cash_value, points_value, flag, connection = null }) {
    log.logger({ pagename: 'pointsRateController.js', action: 'createEntry', type: 'request', fields: { cash_value, points_value, flag } })
    if ((connection === null || connection === undefined)) {
      connection = await mySQLWrapper.getConnectionFromPool()
    }
    try {
      const _result = await this.insert(connection, {
        data: {
          cash_value,
          points_value,
          flag
        }
      })
      const insertedId = { id: _result.insertId }
      insertedId.status = 200
      insertedId.respcode = 1000
      insertedId.message = errorMsg.responseCode[1000]
      log.logger({ pagename: 'pointsRateController.js', action: 'createEntry', type: 'response', fields: insertedId })
      return insertedId
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection._pool._freeConnections.indexOf(connection) === -1) connection.release()
    }
  }

  /**
    * Update
    */
  // eslint-disable-next-line camelcase
  static async updateEntry (_, { ma_global_points_rate_id, cash_value, points_value, flag, connection = null }) {
    log.logger({ pagename: 'pointsRateController.js', action: 'updateEntry', type: 'request', fields: { ma_global_points_rate_id, cash_value, points_value, flag } })
    if ((connection === null || connection === undefined)) {
      connection = await mySQLWrapper.getConnectionFromPool()
    }
    try {
      await this.update(connection, {
        ma_global_points_rate_id,
        data: {
          cash_value,
          points_value,
          flag
        }
      })

      const updatedId = { id: ma_global_points_rate_id }
      updatedId.status = 200
      updatedId.respcode = 1000
      updatedId.message = errorMsg.responseCode[1000]
      log.logger({ pagename: 'pointsRateController.js', action: 'updateEntry', type: 'response', fields: updatedId })
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection._pool._freeConnections.indexOf(connection) === -1) connection.release()
    }
  }

  /**
    * Add entry in Points rate table
    */
  // eslint-disable-next-line camelcase
  static async addGlobalPointsRate (_, { cash_value, points_value, flag }) {
    return await this.createEntry(_, { cash_value, points_value, flag })
  }

  /**
    * Get Points rate table entry
    */
  static async getGlobalPointsRate (_, { connection = null }) {
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'select ma_global_points_rate_id,cash_value,points_value,flag from ma_global_points_rate where flag=1'
      var _pointsRate = await this.rawQuery(sql, connection)
      if (_pointsRate.length > 0) {
        _pointsRate = _pointsRate[0]
        _pointsRate.status = 200
        _pointsRate.message = 'Success'
      } else {
        _pointsRate = { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], points_value: 1 }
      }
      log.logger({ pagename: 'pointsRateController.js', action: 'getGlobalPointsRate', type: 'response', fields: _pointsRate })
      return _pointsRate
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getGlobalPointsRate', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
    * Update Points rate table entry
    */
  // eslint-disable-next-line camelcase
  static async updateGlobalPointsRate (_, { ma_global_points_rate_id, cash_value, points_value, flag }) {
    return this.updateEntry(_, ma_global_points_rate_id, cash_value, points_value, flag)
  }
}

module.exports = globalPointsRate
