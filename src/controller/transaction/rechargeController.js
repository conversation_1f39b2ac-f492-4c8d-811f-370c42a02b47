const path = require('path')
const DAO = require('../../lib/dao')
const errorMsg = require('../../util/error')
const logs = require('../../util/log')
const util = require('../../util/util')
const errorEmail = require('../../util/errorHandler')
const validator = require('../../util/validator')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const maRechargeIntegration = require('./integration/recharge/maRechargeIntegration')
const common = require('../../util/common')
const common_fns = require('../../util/common_fns')
const securePinCtrl = require('../securityPin/securityPinController')
const balanceController = require('../balance/balanceController')
const transactionController = require('./transactionController')
const billPayController = require('../billpayTransaction/billpayTransaction')
const pointbankController = require('./pointbankpayment')
const integrated = require('../integrated/integratedController')
const sms = require('../../util/sms')
const parser = require('fast-xml-parser')

class RechargeController extends DAO {
  static get TABLE_NAME () {
    return 'ma_makepayment_requests'
  }

  static get PRIMARY_KEY () {
    return 'makepayment_request_id'
  }

  /**
   * getCircle description - Returns the list of circles
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {{ status: number, message: string, respcode: number, circles?: { circle_id: number, circle_name: string }[] }}
   */
  static async getCircle (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const privateKeyResponse = await this.generatePrivateKey(fields, null)
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'privateKeyResponse', fields: privateKeyResponse })
      if (privateKeyResponse.status !== 200) return privateKeyResponse

      const apiResponse = await maRechargeIntegration.getCircle({ ...fields, privatekey: privateKeyResponse.privatekey })
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.status !== 200) return apiResponse
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], circles: apiResponse.data }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getCircle',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * @deprecated
   * @ignore
   * getOperator description - Returns the list of operators
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {{ status: number, message: string, respcode: number, operators?: { circle_id: number, circle_name: string }[] }}
   */
  static async getOperator (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const privateKeyResponse = await this.generatePrivateKey(fields, null)
      logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'privateKeyResponse', fields: privateKeyResponse })
      if (privateKeyResponse.status !== 200) return privateKeyResponse

      const apiResponse = await maRechargeIntegration.getOperator({ ...fields, privatekey: privateKeyResponse.privatekey })
      logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'apiResponse', fields: apiResponse })
      if (apiResponse.status !== 200) return apiResponse

      const billpayProvidersQuery = `SELECT
          logo_url
        FROM
          ma_billpay_provider_master bpm
        WHERE
          operator_code = '${apiResponse.opid}'`
      const billpayProvidersData = await this.rawQuery(billpayProvidersQuery, connection)
      apiResponse.logo_url = billpayProvidersData && billpayProvidersData.length > 0 ? billpayProvidersData[0].logo_url : 'https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/Recharge.png'

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], operators: apiResponse.data }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getOperator',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * getOperatorDetails description - Returns the operator details of a particular mobile number
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, mobile: string, type: 'DTH'|'Mobile' }} fields
   * @returns {{ status: number, message: string, respcode: number, operatorDetails?: { status_id: number, message: string, mobile: string, opname: string, opid: number, circle: string, circleid: number, logo_url: string }[] }}
   */
  static async getOperatorDetails (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getOperatorDetails', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'mobile', 'type'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const privateKeyResponse = await this.generatePrivateKey(fields)
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'privateKeyResponse', fields: privateKeyResponse })
      if (privateKeyResponse.status !== 200) return privateKeyResponse

      const apiResponse = await maRechargeIntegration.getOperatordetail({ ...fields, privatekey: privateKeyResponse.privatekey })
      logs.logger({ pagename: path.basename(__filename), action: 'getOperatorDetails', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.status !== 200) return apiResponse
      apiResponse.data.circleid = apiResponse.data.circleid == 'NA' || apiResponse.data.circleid == 'N/A' ? 0 : apiResponse.data.circleid

      const logoUrlQuery = `SELECT logo_url FROM ma_billpay_provider_master WHERE operator_code = ${apiResponse.data.opid}`
      const logoUrl = await this.rawQuery(logoUrlQuery, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getOperatorDetails', type: 'logoUrl', fields: logoUrl })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], operatorDetails: [{ ...apiResponse.data, logo_url: logoUrl.length > 0 ? logoUrl[0].logo_url : 'https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/Recharge.png' }] }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getOperatorDetails', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getOperatorDetails',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * getRechargePlans description - Returns the list of recharge plans available
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, mobile: string, type: 'DTH'|'Mobile', circleid: number, opid: number }} fields
   * @returns {{ status: number, message: string, respcode: number, rechargePlans?: { status_id: number, message: string, opid: number, plans: string }[] }}
   */
  static async getRechargePlans (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRechargePlans', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'mobile', 'type', 'opid'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const privateKeyResponse = await this.generatePrivateKey(fields)
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'privateKeyResponse', fields: privateKeyResponse })
      if (privateKeyResponse.status !== 200) return privateKeyResponse

      const apiResponse = await maRechargeIntegration.getRechargePlans({ ...fields, privatekey: privateKeyResponse.privatekey })
      logs.logger({ pagename: path.basename(__filename), action: 'getRechargePlans', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.status !== 200) return apiResponse
      const plans = (apiResponse.data || {}).plans || {} // ({ title: m, plans: plans[m] })
      const rechargePlans = []

      console.time('plans')
      for (const title in plans) {
        const newPlanFormat = []
        for (const sectionplan of plans[title]) {
          if (typeof sectionplan.rs == 'object') {
            for (const rs in sectionplan.rs) {
              if (validator.validateField(sectionplan.rs[rs]) && +sectionplan.rs[rs] >= 0) newPlanFormat.push({ ...sectionplan, desc: sectionplan.desc + ' ' + rs, rs: sectionplan.rs[rs] })
            }
          } else if (validator.validateField(sectionplan.rs) && +sectionplan.rs >= 0) {
            newPlanFormat.push({ ...sectionplan })
          }
        }
        if (newPlanFormat.length > 0) rechargePlans.push({ title, plans: [...newPlanFormat] })
      }
      console.timeEnd('plans')

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], rechargePlans }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRechargePlans', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getRechargePlans',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * getBillerId description - Returns the list of circles
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, type: 'Mobile' | 'DTH' }} fields
   * @returns {{
   *  status: number,
   *  message: string,
   *  respcode: number,
   *  billers?: {
   *    opid: number,
   *    BILLER_MASTER_ID: number,
   *    BILLER_CATEGORY: string,
   *    BILLER_SUB_CATEGORY: string,
   *    BILLER_NAME: string,
   *    BILLER_LOGO: string,
   *    BILLER_BILL_COPY: string,
   *    BBPS_GATEWAY_ID: number,
   *    STATUS: string,
   *    register: { flag: number, FIELDNAMES: string, VALIDATION: string },
   *    instant: { flag: number, FIELDNAMES: string, VALIDATION: string },
   *    recharge: {flag: number, FIELDNAMES: string, VALIDATION: string },
   *    PAY_AFTER_DUEDATE: string,
   *    ISBILLERBBPS: string,
   *    ONLINE_VALIDATION: string,
   *    PAYMENTAMOUNT_VALIDATION: string,
   *    PARTIAL_PAY: string,
   *    BILLER_TYPE: string,
   *    PAYMENT_METHODS: string,
   *    BILLER_RECHARGE_TYPE: string
   *  }[]
   * }}
   */
  static async getBillerId (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getBillerId', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'type'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const privateKeyResponse = await this.generatePrivateKey(fields, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'privateKeyResponse', fields: privateKeyResponse })
      if (privateKeyResponse.status !== 200) return privateKeyResponse

      const apiResponse = await maRechargeIntegration.getBillerId({ ...fields, privatekey: privateKeyResponse.privatekey })
      logs.logger({ pagename: path.basename(__filename), action: 'getBillerId', type: 'apiResponse', fields: JSON.stringify(apiResponse) })
      if (apiResponse.status !== 200) return apiResponse

      const billpayProvidersQuery = `SELECT
          JSON_OBJECTAGG(lower(operator_code), logo_url) as operator_json
        FROM
          ma_billpay_provider_master bpm`
      const billpayProvidersData = await this.rawQuery(billpayProvidersQuery, connection)
      const billpayProviders = billpayProvidersData.length != 0 && validator.validateField(billpayProvidersData[0].operator_json) ? JSON.parse(billpayProvidersData[0].operator_json) : {}

      const billers = apiResponse.data.map(biller => ({ ...biller, BILLER_LOGO: biller.BILLER_LOGO || billpayProviders[biller.opid] || 'https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/Recharge.png', register: [biller.register], instant: [biller.instant], recharge: [biller.recharge], BILLER_RECHARGE_TYPE: biller.BILLER_SUB_CATEGORY == 'Prepaid' ? 'TELECOM' : 'DTH' }))

      if (apiResponse.status !== 200) return apiResponse
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], circles: apiResponse.data, billers }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBillerId', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getBillerId',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * doRecharge description - Processes a recharge transaction
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, amount: string, pin: string, mobile: string, PAYMENTAMOUNT_VALIDATION: string, BILLER_MASTER_ID: number, BILLER_NAME: string, aggregator_order_id: string, type: 'Mobile' | 'DTH', plan_name: string, plan_description: string, circle_name: string }} fields
   * @param {any} con connection
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async doRecharge (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    // 1) Validate Parameters
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'amount', 'pin', 'PAYMENTAMOUNT_VALIDATION', 'BILLER_MASTER_ID', 'BILLER_NAME', 'aggregator_order_id', 'type', 'plan_name'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // 2) DB validations
      // 3) Pin Validations
      const pinResponse = await this.verifyPin(fields)
      if (pinResponse.status == 400) return pinResponse

      const getMerchantDetailsResponse = await this.getMerchantDetails(fields, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'getMerchantDetailsResponse', fields: getMerchantDetailsResponse })
      if (getMerchantDetailsResponse.status !== 200) return getMerchantDetailsResponse

      // 4) insert into makepayment
      const api_request = {
        __rechargeType: fields.type,
        __planName: fields.plan_name,
        __planDescription: fields.plan_description || null,
        __circleName: fields.circle_name,
        email: getMerchantDetailsResponse.merchantDetails.email_id,
        fname: getMerchantDetailsResponse.merchantDetails.firstname,
        lname: getMerchantDetailsResponse.merchantDetails.lastname,
        mrcid: fields.ma_user_id,
        Amount: fields.amount,
        action: 'recharge',
        amount: fields.amount,
        gsm_id: '',
        plan_id: '',
        checksum: '',
        device_id: 'xyz',
        account_id: fields.mobile,
        utility_id: '0',
        provider_id: fields.BILLER_MASTER_ID,
        MobileNumber: fields.mobile,
        merchant_key: '',
        merchant_pass: '',
        merchant_user: '',
        mobile_number: fields.mobile
      }
      const env = process.env.NODE_ENV || 'development'

      const mer_dtls = util[env].merchant_details
      const api_response = {
        msg: 'success',
        data: { surl: '', email: getMerchantDetailsResponse.merchantDetails.email_id, fname: getMerchantDetailsResponse.merchantDetails.firstname, lname: getMerchantDetailsResponse.merchantDetails.lastname, refno: '', amount: fields.amount, gcm_id: null, details: JSON.stringify({ 'Mobile Number': fields.mobile }), mer_dtls, device_id: 'xyz', requestid: 4373, account_id: fields.mobile, customerid: fields.ma_user_id, invoice_id: 0, utility_id: '0', provider_id: fields.BILLER_MASTER_ID, payment_type: 'instapay', mobile_number: fields.mobile, BILLER_MASTER_ID: fields.BILLER_MASTER_ID, check_amount_req: 'amount_field_required', PAYMENTAMOUNT_VALIDATION: fields.PAYMENTAMOUNT_VALIDATION },
        status: 200
      }

      const { insertId } = await this.insert(connection, {
        data: {
          api_request: JSON.stringify(api_request),
          api_response: JSON.stringify(api_response),
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          aggregator_order_id: fields.aggregator_order_id,
          invoice_id: 0,
          bill_amount: fields.amount,
          transaction_amount: fields.amount,
          transaction_type: '17',
          mer_dtls: JSON.stringify(mer_dtls),
          request_status: 'S'
        }
      })
      logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'makepayment insert id', fields: insertId })

      // 5) create transaction
      const transactionInitResponse = await this.rechargeInitTransaction(fields, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'transactionInitResponse', fields: transactionInitResponse })
      if (transactionInitResponse.status !== 200) return transactionInitResponse

      // 6) Call PayIndex
      let pointBankResponse = await this.callPayIndex({
        ...fields,
        ma_transaction_master_id: transactionInitResponse.ma_transaction_master_id,
        ma_billpay_transactionid: transactionInitResponse.ma_billpay_transactionid
      }, connection)

      logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'pointBankResponse', fields: pointBankResponse })

      // Get bbps receipt
      const receiptDetails = await transactionController.getBbpsTransactionDetails(_, {
        aggregator_order_id: fields.aggregator_order_id,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid
      })
      logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'receiptDetails', fields: receiptDetails })

      if (receiptDetails.status === 200) {
        delete receiptDetails.status
        delete receiptDetails.message
        delete receiptDetails.respcode
        pointBankResponse = { ...pointBankResponse, ...receiptDetails }
      }

      pointBankResponse.status = 200
      console.log('pointBankResponse', pointBankResponse)
      return pointBankResponse
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'doRecharge', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'doRecharge',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * @private
   * rechargeInitTransaction description - Initates recharge Transaction
   * @param {{ ma_user_id: number, userid: number, aggregator_order_id: string, amount: string, mobile: string, BILLER_MASTER_ID: number, BILLER_NAME: string }} fields
   * @param {any} con connection
   * @returns {{ status: number, message: string, respcode: number, ma_transaction_master_id: number, ma_billpay_transactionid: number }}
   */
  static async rechargeInitTransaction (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'rechargeInitTransaction', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'aggregator_order_id', 'amount', 'mobile', 'BILLER_MASTER_ID', 'BILLER_NAME'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const amountData = {}
      // To do Balance Check validation
      // Check balance of Retailer
      const availableBalance = await balanceController.getWalletBalancesDirect(null, {
        ma_user_id: fields.ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection
      })

      if (availableBalance.amount < fields.amount) {
        return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], amountData }
      }

      const getTxnDetailsQuery = `SELECT ma_transaction_master_id from ma_transaction_master where aggregator_order_id='${fields.aggregator_order_id}'`
      const getTxnDetails = await this.rawQuery(getTxnDetailsQuery, connection)
      if (getTxnDetails.length > 0) return { status: 400, respcode: 1133, message: errorMsg.responseCode[1133] }

      const merchantDetailsResponse = await this.getMerchantDetails(fields, connection)
      if (merchantDetailsResponse.status !== 200) return merchantDetailsResponse

      const merchantDetails = merchantDetailsResponse.merchantDetails

      // Create Transaction with pending status
      const transactionData = await transactionController.initiateTransaction(null, {
        connection: connection,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.aggregator_order_id,
        amount: fields.amount,
        transaction_type: 17,
        remarks: fields.remarks || 'RECHARGE TRANSACTION',
        mobile_number: fields.mobile,
        provider_id: fields.BILLER_MASTER_ID,
        provider_name: fields.BILLER_NAME,
        utility_id: 0,
        utility_name: 'Recharge',
        action_type: 'recharge',
        transaction_status: 'I',
        makePaymentResponse: {
          mer_dtls: merchantDetails,
          invoice_id: 0,
          makepayment_request_id: 0 // ?
        }
      })

      if (transactionData.status === 400) return transactionData

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_transaction_master_id: transactionData.transaction_id, ma_billpay_transactionid: transactionData.ma_billpay_transactionid }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'rechargeInitTransaction', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'rechargeInitTransaction',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @private
   * getMerchantDetails description - retrives merchant details from db
   * @param {{ ma_user_id: number, userid: number }} fields
   * @param {any} con connection
   * @returns {Promise<{ status: number, message: string, respcode: number, merchantDetails: {
   *  email_id: string,
   *  mobile_id: string,
   *  firstname: string,
   *  lastname: string,
   *  address: string,
   *  city: string,
   *  state: string,
   *  pincode: string,
   *  country: string,
   *  userid: number,
   *  secret: string,
   *  username: string,
   *  password: string
   * }}>}
   */
  static async getMerchantDetails (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetails', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const merchantDetailsQuery = `SELECT
          um.email_id,
          um.mobile_id,
          um.firstname,
          um.lastname,
          um.address,
          mcm.name as city,
          mcm.name as state,
          um.pincode,
          um.country,       
          um.userid,
          um.apikey as secret,
          um.username,
          um.password
        FROM
          ma_user_master um
        INNER JOIN
          ma_states_master msm
        ON
          msm.id = um.state
        INNER JOIN
          ma_cities_master mcm
        ON
          mcm.id = um.city
        WHERE
          um.user_status = 'Y' AND
          um.profileid = '${fields.ma_user_id}' AND
          um.userid = '${fields.userid}'
        LIMIT 1`
      logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetails', type: 'merchantDetailsQuery', fields: { merchantDetailsQuery } })

      const merchantDetails = await this.rawQuery(merchantDetailsQuery, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetails', type: 'merchantDetails', fields: merchantDetails })
      if (merchantDetails.length <= 0) return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], merchantDetails: merchantDetails[0] }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetails', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getMerchantDetails',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @private
   * callPayIndex description - Calls Payindex API
   * @param {{
   *  ma_user_id: number,
   *  userid: number,
   *  aggregator_order_id: string,
   *  amount: string,
   *  pin: string,
   *  ma_billpay_transactionid: number|string,
   *  mobile: string,
   *  ma_transaction_master_id: number,
   *  PAYMENTAMOUNT_VALIDATION: string,
   *  BILLER_MASTER_ID: number
   * }} fields
   * @param {any} con connection
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async callPayIndex (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'aggregator_order_id', 'amount', 'pin', 'ma_billpay_transactionid', 'mobile', 'ma_transaction_master_id', 'PAYMENTAMOUNT_VALIDATION', 'BILLER_MASTER_ID'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const merchantDetailsResponse = await this.getMerchantDetails(fields, connection)
      if (merchantDetailsResponse.status !== 200) return merchantDetailsResponse

      const merchantDetails = merchantDetailsResponse.merchantDetails

      const privatekeyResponse = await this.generatePrivateKey(fields, connection, true)
      if (privatekeyResponse.status !== 200) return privatekeyResponse

      const response = await maRechargeIntegration.callPayIndex({ ...fields, privatekey: privatekeyResponse.privatekey, ...merchantDetails })
      logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'callPayIndexResponse', fields: response })
      if (response.status !== 200) return response

      let transactionObj = {}
      try {
        transactionObj = parser.parse(response.data)
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'Response Parsing Error', fields: error })
        transactionObj = {}
      }

      logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'PointBankResponseObject', fields: transactionObj })

      let aggregator_txn_id = 0
      let bank_rrn = ''
      let message = ''
      let ponintbankResObj = {}
      let requireConfirm = true // If we not got proper response from point bank need to confirm order again
      let successResponse = false
      let payment_status = 'P'
      let transaction_status = 'P'

      // If we got proper response from point bank
      if (typeof transactionObj === 'object' && Object.keys(transactionObj).length > 0 && (typeof transactionObj.RESPONSE === 'object' || typeof transactionObj.TRANSACTION === 'object')) {
        ponintbankResObj = transactionObj
        const TRANS_ORDER = (transactionObj.TRANSACTION || transactionObj.RESPONSE || {}).TRANSACTION
        bank_rrn = (TRANS_ORDER.RRN) ? TRANS_ORDER.RRN : ''
        aggregator_txn_id = TRANS_ORDER.APTRANSACTIONID
        message = TRANS_ORDER.MESSAGE

        // Success Case from Point Bank Payment Gateway
        if (TRANS_ORDER.TRANSACTIONSTATUS == 200) {
          logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'response', fields: {} })
          successResponse = true
          payment_status = 'S'
          transaction_status = 'P'
          // Other than Success Case from Point Bank Payment Gateway need to do confirmation
          requireConfirm = false
        }
      }

      if (requireConfirm) {
        const pointbankController = require('./pointbankpayment')
        const orderConfirmResponse = await pointbankController.orderConfirmation(null, { ...fields, mer_dtlsObj: { mercid: fields.ma_user_id, ...merchantDetailsResponse.merchantDetails } })
        // Initted ..? Failure mark

        // To do orderConfirmation Requery Cron

        logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'OuterresponseorderConfirmResponse', fields: orderConfirmResponse })

        if (orderConfirmResponse.status == 400) {
          return orderConfirmResponse
        }
        if (orderConfirmResponse.status == 200) {
          aggregator_txn_id = orderConfirmResponse.aggregator_txn_id
          bank_rrn = orderConfirmResponse.rrn
          message = orderConfirmResponse.transaction_reason
          ponintbankResObj = orderConfirmResponse.transactionObj

          if (orderConfirmResponse.transaction_status == 'S') {
            payment_status = 'S'
            transaction_status = 'P'
            successResponse = true
          } else if (orderConfirmResponse.transaction_status == 'F') {
            payment_status = 'F'
            transaction_status = 'F'
            successResponse = false
          }
        } else {
          logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'orderConfirmResponse400 >>', fields: orderConfirmResponse })
          return orderConfirmResponse
        }
      }

      return await this.updateRechargeTransaction({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.aggregator_order_id,
        aggregator_txn_id: aggregator_txn_id,
        payment_status: payment_status,
        transaction_status: transaction_status,
        amount: fields.amount,
        bank_rrn: bank_rrn,
        message: message,
        ponintbankResObj: ponintbankResObj,
        ma_transaction_master_id: fields.ma_transaction_master_id,
        ma_billpay_transactionid: fields.ma_billpay_transactionid,
        successResponse: successResponse,
        mobile: fields.mobile,
        PAYMENTAMOUNT_VALIDATION: fields.PAYMENTAMOUNT_VALIDATION,
        BILLER_MASTER_ID: fields.BILLER_MASTER_ID
      }, connection)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'callPayIndex',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @private
   * updateRechargeTransaction description - updates Recharge transaction
   * @param {{
   *  ma_user_id: number,
   *  userid: number,
   *  aggregator_order_id: string,
   *  aggregator_txn_id: string,
   *  payment_status: string,
   *  transaction_status: string,
   *  amount: string,
   *  bank_rrn: string,
   *  message: string,
   *  ponintbankResObj: any,
   *  ma_transaction_master_id: number,
   *  ma_billpay_transactionid: number,
   *  successResponse: any,
   *  mobile: string,
   *  PAYMENTAMOUNT_VALIDATION: string,
   *  BILLER_MASTER_ID: number
   * }} fields
   * @param {any} con connection
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async updateRechargeTransaction (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'aggregator_order_id', 'aggregator_txn_id', 'payment_status', 'transaction_status', 'amount', 'message', 'ponintbankResObj', 'ma_transaction_master_id', 'ma_billpay_transactionid', 'successResponse', 'mobile', 'PAYMENTAMOUNT_VALIDATION', 'BILLER_MASTER_ID'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const getBillTxnDetailsQuery = `SELECT * from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}'`
      logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'getBillTxnDetailsQuery', fields: getBillTxnDetailsQuery })

      const getBillTxnDetails = await this.rawQuery(getBillTxnDetailsQuery, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'getBillTxnDetails', fields: getBillTxnDetails })
      if (getBillTxnDetails.length <= 0) return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }

      const currenrPaymentStatus = getBillTxnDetails[0].payment_status
      logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'currenrPaymentStatus === fields.payment_status ', fields: { currenrPaymentStatus, payment_status: fields.payment_status } })

      // Initially currenrPaymentStatus = I , Incoming expected P,S,F status
      if (currenrPaymentStatus == fields.payment_status) return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }

      // Update Transactions recevied Data
      const updateTranResponse = await transactionController.updateTransaction(null, {
        aggregator_order_id: fields.aggregator_order_id,
        aggregator_txn_id: fields.aggregator_txn_id,
        transaction_status: fields.transaction_status,
        amount: fields.amount,
        bank_rrn: fields.bank_rrn,
        connection: connection,
        transaction_reason: fields.message
      })

      logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'updateTranResponse', fields: updateTranResponse })

      const updateBillPayTranResponse = await billPayController.updateWhereData(connection, {
        data: {
          transaction_status: fields.transaction_status,
          payment_status: fields.payment_status,
          payment_response: JSON.stringify(fields.ponintbankResObj)
        },
        id: fields.ma_billpay_transactionid,
        where: 'ma_billpay_transactionid'
      })

      logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })

      if (fields.successResponse === true && updateTranResponse.status == 200 && updateBillPayTranResponse.affectedRows >= 0) {
        logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'Success Payment Calling', fields: {} })

        const paymentResponseData = await this.payInstantBill(fields, connection)
        logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'paymentResponseData', fields: paymentResponseData })
        return paymentResponseData
      } else {
        logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'TRANSACTIONSTATUS != 200 Pending or Failure Case Payment', fields: { successResponse: fields.successResponse, updateTranResponse, updateBillPayTranResponse } })
        if (fields.payment_status == 'F') {
          const reverseBBPSLedgersResponse = await pointbankController.reverseBBPSLedgers({
            orderid: fields.aggregator_order_id
          }, connection) // What if failed
          logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'reverseBBPSLedgersResponse', fields: reverseBBPSLedgersResponse })
          return { status: 400, respcode: 1028, message: fields.message }
        }
      }
      return { status: 200, respcode: 1130, message: errorMsg.responseCode[1130] }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'updateRechargeTransaction', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'updateRechargeTransaction',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @private
   * payInstantBill description - What's the method about?
   * @param {{ ma_user_id: number, userid: number, ma_transaction_master_id: number, ma_billpay_transactionid: number, aggregator_order_id: string, mobile: string, amount: string, PAYMENTAMOUNT_VALIDATION: string, aggregator_txn_id: number, BILLER_MASTER_ID: number }} fields
   * @param {any} con connection
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async payInstantBill (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'ma_transaction_master_id', 'ma_billpay_transactionid', 'aggregator_order_id', 'aggregator_order_id', 'amount', 'PAYMENTAMOUNT_VALIDATION', 'aggregator_txn_id', 'BILLER_MASTER_ID'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const privatekeyResponse = await this.generatePrivateKey(fields, null, true)
      if (privatekeyResponse.status !== 200) return privatekeyResponse

      const mercid = util[process.env.NODE_ENV || 'staging'].merchant_details.mercid
      const payInstantBillData = await maRechargeIntegration.makePayment({ ...fields, mercid, privatekey: privatekeyResponse.privatekey }, connection)

      logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'payInstantBillData', fields: payInstantBillData })

      // 100 % Success
      let billpaymentstatus = 'P'
      let bank_response = {}

      if (validator.validateField(payInstantBillData) && validator.validateField(payInstantBillData.data) && validator.validateField(payInstantBillData.data.recharge_status)) {
        billpaymentstatus = payInstantBillData.data.recharge_status == 'FAILED' ? 'F' : 'S'
      }
      bank_response = JSON.stringify(payInstantBillData.bank_response)

      // const billPayTransactionCtrl = require('../billpayTransaction/billpayTransaction')

      const updateTranResponse = await transactionController.updateWhereData(connection, {
        data: {
          transaction_status: billpaymentstatus
        },
        id: fields.ma_transaction_master_id,
        where: 'ma_transaction_master_id'
      })

      logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'updateTranResponse', fields: updateTranResponse })

      const updateBillPayTranResponse = await billPayController.updateWhereData(connection, {
        data: {
          transaction_status: billpaymentstatus,
          bank_response: JSON.stringify(bank_response)
        },
        id: fields.ma_billpay_transactionid,
        where: 'ma_billpay_transactionid'
      })

      logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })

      let billTxnDetail = {}
      // Send SMS in case of succss/failure
      if (billpaymentstatus == 'S' || billpaymentstatus == 'F') {
        var sql = `SELECT * from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}'`
        const getBillTxnDetails = await this.rawQuery(sql, connection)
        if (getBillTxnDetails.length > 0) {
          let message = ''
          let template = ''
          if (billpaymentstatus == 'S') {
            message = util.communication.BILLPAYSUCCESS
            template = util.templateid.BILLPAYSUCCESS
          } else {
            message = util.communication.BILLPAYFAILURE
            template = util.templateid.BILLPAYFAILURE
          }
          billTxnDetail = getBillTxnDetails[0]

          var dt = new Date(billTxnDetail.updatedon)
          const datetimeformat = dt.toLocaleString('en-IN')
          message = message.replace('<Provider name>', billTxnDetail.provider_name)
          message = message.replace('<amount>', billTxnDetail.amount)
          message = message.replace('<date time>', datetimeformat)
          message = message.replace('<orderid>', fields.aggregator_order_id)
          message = message.replace('<signature>', util.communication.Signature)

          await sms.sentSmsAsync(message, fields.mobile, template)
        } else {
          logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'SMSSkippedAsTransactionDetailsNotFound', fields: {} })
        }
      }

      if (billpaymentstatus == 'S') {
        // give incentive to this
        const addBBPSIncentiveResponse = await pointbankController.addBBPSIncentive({
          aggregator_order_id: fields.aggregator_order_id,
          bill_amount: fields.amount
        }, connection) // What if failed
        logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'addBBPSIncentiveResponse', fields: addBBPSIncentiveResponse })
        logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'billTxnDetail', fields: billTxnDetail })

        // Check if integrated merchant
        const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${billTxnDetail.userid}`
        const integratedMer = await this.rawQuery(userSQL, connection)
        logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'response-int', fields: integratedMer })
        if (integratedMer.length > 0) {
          // Post receipt
          var receiptData = {}
          receiptData.action = 'POSTRECEIPT'
          receiptData.aggregator_user_id = integratedMer[0].aggregator_user_id
          receiptData.aggregator_order_id = fields.aggregator_order_id
          receiptData.ma_user_id = billTxnDetail.ma_user_id
          const responsePostReceipt = await integrated.index(receiptData, connection)
          logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'responsePostReceipt', fields: responsePostReceipt })
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else { // failed
        // revert ledgers incentive to this
        if (billpaymentstatus == 'F') {
          const reverseBBPSLedgersResponse = await pointbankController.reverseBBPSLedgers({
            orderid: fields.aggregator_order_id
          }, connection) // What if failed
          logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'reverseBBPSLedgersResponse', fields: reverseBBPSLedgersResponse })
          return { status: 400, respcode: 1131, message: errorMsg.responseCode[1131] + ': Transaction failed' }
        } else {
          logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'PendingCase', fields: payInstantBillData })
          return { status: 200, respcode: 1130, message: errorMsg.responseCode[1130] }
        }
      }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'payInstantBill', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'payInstantBill',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @private
   * generatePrivateKey description - generates private key with use of ma_user_id and userid
   * @param {{ ma_user_id: number, userid: number }} fields
   * @param {any} conn connection
   * @returns {{ status: number, message: string, respcode: number, privatekey?: string }}
   */
  static async generatePrivateKey (fields, conn, staticDetails = false) {
    logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()

    try {
      const merchantDetailsQuery = `SELECT
          apikey as secret,
          username,
          password
        FROM
          ma_user_master
        WHERE
          user_status = 'Y' AND
          profileid = '${fields.ma_user_id}' AND
          userid = ${fields.userid}
        LIMIT 1`
      logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'merchantDetailsQuery', fields: { merchantDetailsQuery } })

      const env = process.env.NODE_ENV || 'development'
      const merchantDetails = staticDetails ? [util[env].merchant_details] : (await this.rawQuery(merchantDetailsQuery, connection))
      if (merchantDetails.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ', Private key could not be generated' }
      logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'merchantDetails', fields: merchantDetails.map(m => common_fns.maskValue(m, ['apikey', 'password'], false, true)) })

      logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'merchantDetails', fields: merchantDetails })
      // // key format and data
      const keyData = `${merchantDetails[0].secret}@${merchantDetails[0].username}:|:${merchantDetails[0].password}`
      logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'keyData', fields: keyData })

      const privatekey = common.createHash(keyData)
      logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'privatekey', fields: privatekey })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], privatekey }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'generatePrivateKey', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'generatePrivateKey',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @private
   * Verify Pin
   * @param {{ ma_user_id:number, userid: number, pin: string }} fields
   * @param {Object|null} con connection
   */
  static async verifyPin (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'request', fields })
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'userid', 'pin'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const securePinData = await securePinCtrl.verifySecurePin(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        security_pin: fields.pin,
        connection
      })
      logs.logger({ pagename: path.basename(__filename), action: 'securePinData', type: 'catcherror', fields: securePinData })
      return securePinData
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'catcherror', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'verifyPin',
        data: fields,
        error: err
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }
}

module.exports = RechargeController
