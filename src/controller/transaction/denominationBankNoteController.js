const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const path = require('path')
const validator = require('../../util/validator')

class denominationBankNoteController extends DAO {
  static get TABLE_NAME () {
    return 'ma_denomination_bank_note_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_denomination_bank_note_details_id'
  }

  /**
   * denominationBankNote: Insert data into the ma_denomination_bank_note_details table
   * @param {*} _
   * @param {*} fields
   */
  static async denominationBankNote ({ ma_user_id, userid, aggregator_order_id, bank_name, customer_name, customer_mobile, denomination_form_data }) {
    log.logger({ pagename: path.basename(__filename), action: 'denominationBankNote', type: 'request', fields: { ma_user_id, userid, aggregator_order_id, bank_name, customer_name, customer_mobile, denomination_form_data } })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Validate the required request fields
    const validateFieldsResponse = await this.validateFields({ ma_user_id, userid, aggregator_order_id, bank_name, customer_name, customer_mobile, denomination_form_data }, ['ma_user_id', 'userid', 'aggregator_order_id', 'denomination_form_data'])
    log.logger({ pagename: path.basename(__filename), action: 'denominationBankNote', type: 'validateFieldsResponse', fields: validateFieldsResponse })
    if (validateFieldsResponse.status == 400) return validateFieldsResponse

    try {
      // Decrypt denomination_form_data. e.g = { denomination_note_value: '2000', denomination_note_count: '1' }
      denomination_form_data = JSON.parse(Buffer.from(denomination_form_data, 'base64').toString('ascii'))
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNote', type: 'denomination_form_data', fields: denomination_form_data })

      if (!denomination_form_data.denomination_amount || denomination_form_data.denomination_amount <= 0) return { status: 400, respcode: 1002, message: 'Fail: Denomination amount is not valid' }
      // Fetch Transaction details and validate
      const checkTransactionSql = `SELECT mtm.addedon, mtm.amount, mtm.transaction_status, mtmd.customer_name, mom.mobile
      FROM ma_transaction_master mtm 
      LEFT JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      LEFT JOIN ma_otp_master mom ON mtm.aggregator_order_id = mom.aggregator_order_id
      WHERE mtm.aggregator_order_id = ?
      LIMIT 1`
      const checkTransactionSqlResp = await this.secureRawQuery(checkTransactionSql, { connection, params: [aggregator_order_id] })
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNote', type: 'checkTransactionSqlResp', fields: checkTransactionSqlResp })
      if (checkTransactionSqlResp.length <= 0) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      if (checkTransactionSqlResp[0].transaction_status == 'F') return { status: 400, respcode: 1002, message: 'Fail: Transaction Failed' }

      // Create insert data object
      const insertData = {
        ma_user_id,
        userid,
        aggregator_order_id,
        transaction_date: checkTransactionSqlResp[0].addedon,
        bank_name,
        customer_name: customer_name || checkTransactionSqlResp[0].customer_name,
        customer_mobile: checkTransactionSqlResp[0].mobile || customer_mobile,
        total_transaction_amount: checkTransactionSqlResp[0].amount,
        // denomination_note_value: denomination_form_data.denomination_note_value || 0,
        // denomination_note_count: denomination_form_data.denomination_note_count || 0,
        denomination_amount: denomination_form_data.denomination_amount
      }

      // Insert Data in ma_denomination_bank_note_details table
      const insertDenominationData = await this.insert(connection, { data: insertData })
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNote', type: 'insertDenominationData', fields: insertDenominationData })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNote', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * denominationBankNoteUpdate :: Update ma_denomination_bank_note_details table
   * @param {*} _
   * @param {*} fields
   */
  static async denominationBankNoteUpdate ({ ma_user_id, aggregator_order_id, bank_name, customer_name }) {
    log.logger({ pagename: path.basename(__filename), action: 'denominationBankNoteUpdate', type: 'request', fields: { ma_user_id, aggregator_order_id, bank_name, customer_name } })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Validate required request fields
    const validateFieldsResponse = await this.validateFields({ ma_user_id, aggregator_order_id, bank_name, customer_name }, ['ma_user_id', 'aggregator_order_id'])
    log.logger({ pagename: path.basename(__filename), action: 'denominationBankNoteUpdate', type: 'validateFieldsResponse', fields: validateFieldsResponse })
    if (validateFieldsResponse.status == 400) return validateFieldsResponse

    try {
      // Fetch transaction data and validate
      const checkTransactionSql = `SELECT mtmd.customer_name, mtm.transaction_status
      FROM ma_transaction_master mtm 
      LEFT JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      WHERE mtm.aggregator_order_id = ?
      LIMIT 1`
      const checkTransactionSqlResp = await this.secureRawQuery(checkTransactionSql, { connection, params: [aggregator_order_id] })
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNoteUpdate', type: 'checkTransactionSqlResp', fields: checkTransactionSqlResp })
      if (checkTransactionSqlResp.length <= 0) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      if (checkTransactionSqlResp[0].transaction_status == 'F') return { status: 400, respcode: 1002, message: 'Fail: Transaction Failed' }

      // Create update data object
      const updateData = {
        bank_name,
        customer_name: customer_name || checkTransactionSqlResp[0].customer_name
      }

      // Update ma_denomination_bank_note_details table
      const updateDenominationResp = await this.updateWhere(connection, {
        id: aggregator_order_id,
        where: 'aggregator_order_id',
        data: updateData
      })
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNoteUpdate', type: 'updateDenominationResp', fields: updateDenominationResp })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'denominationBankNoteUpdate', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async validateFields (fields, requiredParams = []) {
    for (const requiredParam of requiredParams) {
      if (!validator.definedVal(fields[requiredParam])) {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateFields', type: 'invalid request missing params', fields: { missingParams: [requiredParam] } })
        return { status: 400, respcode: 1019, message: errorMsg.responseCode[1019] }
      }
    }
    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
  }
}
module.exports = denominationBankNoteController
