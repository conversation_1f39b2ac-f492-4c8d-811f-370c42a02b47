const qs = require('qs')
const crypto = require('crypto')
const path = require('path')
const DAO = require('../../../../lib/dao')
const logs = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const errorEmail = require('../../../../util/errorHandler')
const validator = require('../../../../util/validator')
const { default: Axios } = require('axios')
const util = require('../../../../util/util')
const { md5checksum } = require('../../../../util/checksum')

class MaRechargeIntegration extends DAO {
  static get TABLE_NAME () {
    return 'ma_makepayment'
  }

  static get PRIMARY_KEY () {
    return 'ma_makepayment_id'
  }

  static get URL () {
    return util.paymentsUrl + 'bbps/bbps.php'
  }

  static get CONFIG () {
    return {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.110 Safari/537.36'
      },
      timeout: 20000
    }
  }

  /**
     * getCircle description - Calls the Ma getCircle API
     * @param {{ ma_user_id: number, privatekey: string }} fields
     * @returns {{ status: number, message: string, respcode: number, data: any }}
     */
  static async getCircle (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'privatekey'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      // set Data for API Call
      const data = {
        privatekey: fields.privatekey,
        mercid: fields.ma_user_id,
        checksum: this.generateChecksum(fields),
        api: 'getCircle'
      }

      // make getCircle API call to MA
      const response = await this.doApiCall({ api_name: 'getCircle', data })
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'response', fields: response })

      return this.getResponse(response)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getCircle', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getCircle',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
     * getOperator description - Calls the Ma getOperator API
     * @param {{ ma_user_id: number, privatekey: string }} fields
     * @returns {{ status: number, message: string, respcode: number, data: any }}
     */
  static async getOperator (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'privatekey'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      // set Data for API Call
      const data = {
        privatekey: fields.privatekey,
        mercid: fields.ma_user_id,
        checksum: this.generateChecksum(fields),
        api: 'getOperator'
      }

      // make getOperator API call to MA
      const response = await this.doApiCall({ api_name: 'getOperator', data })
      logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'response', fields: response })

      return this.getResponse(response)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getOperator', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getOperator',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
     * getOperatordetail description - Calls the Ma getOperatordetail API
     * @param {{ ma_user_id: number, privatekey: string, mobile: string, type: 'DTH'|'Mobile' }} fields
     * @returns {{ status: number, message: string, respcode: number, data: any }}
     */
  static async getOperatordetail (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getOperatordetail', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'privatekey', 'mobile', 'type'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      // set Data for API Call
      const data = {
        privatekey: fields.privatekey,
        mercid: fields.ma_user_id,
        checksum: this.generateChecksum(fields),
        api: 'getOperatordetail',
        mobile_number: fields.mobile,
        type: fields.type
      }

      // make getOperatordetail API call to MA
      const response = await this.doApiCall({ api_name: 'getOperatordetail', data })

      return this.getResponse(response)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getOperatordetail', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getOperatordetail',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
     * getRechargePlans description - Calls the Ma getRechargePlans API
     * @param {{ ma_user_id: number, privatekey: string, mobile: string, type: 'DTH'|'Mobile', circleid: number, opid: number }} fields
     * @returns {{ status: number, message: string, respcode: number, data: any }}
     */
  static async getRechargePlans (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRechargePlans', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'privatekey', 'mobile', 'type', 'opid'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      // set Data for API Call
      const data = {
        privatekey: fields.privatekey,
        mercid: fields.ma_user_id,
        checksum: this.generateChecksum(fields),
        api: 'getRechargePlans',
        mobile_number: fields.mobile,
        opid: fields.opid,
        circleid: fields.circleid || '0',
        type: fields.type
      }

      if (fields.type == 'DTH') {
        delete data.circleid
      }

      // make getRechargePlans API call to MA
      const response = await this.doApiCall({ api_name: 'getRechargePlans', data })
      const getResponse = await this.getResponse(response)
      if (getResponse.status == 200) {
        const checkPlans = (getResponse.data || {}).plans || {}
        if (Array.isArray(checkPlans) || typeof checkPlans != 'object') {
          return { status: 400, respcode: 1028, message: 'Error: Plan Not Available' }
        }
      }
      return getResponse
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRechargePlans', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getRechargePlans',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
     * getBillerId description - Returns the Biller details
     * @param {{ ma_user_id: number, type: 'Mobile' | 'DTH', privatekey: string }} fields
     * @returns {{ status: number, message: string, respcode: number, data: any }}
     */
  static async getBillerId (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getBillerId', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'type', 'privatekey'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      // set Data for API Call
      const data = {
        privatekey: fields.privatekey,
        mercid: fields.ma_user_id,
        checksum: this.generateChecksum(fields),
        api: 'getBillerId',
        biller_category: 'Recharge',
        biller_subcategory: fields.type == 'Mobile' ? 'Prepaid' : 'DTH'
      }

      // make getBillerId API call to MA
      const response = await this.doApiCall({ api_name: 'getBillerId', data })

      return this.getResponse(response)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBillerId', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getBillerId',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * makePayment description - calls recharge makepayment api
   * @param {{ ma_user_id: number, userid: number, mercid: number, privatekey: string, PAYMENTAMOUNT_VALIDATION: string, aggregator_txn_id: number, BILLER_MASTER_ID: number, mobile: string, amount: string  }} fields
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async makePayment (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'makePayment', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      // set Data for API Call
      const data = {
        privatekey: fields.privatekey,
        mercid: fields.mercid,
        checksum: this.generateChecksum({ ...fields, ma_user_id: fields.mercid }),
        api: 'makePayment_v2',
        customerid: fields.ma_user_id,
        paymentamount_validation: fields.PAYMENTAMOUNT_VALIDATION,
        action: 'recharge',
        airpay_id: fields.aggregator_txn_id,
        payment_type: 'instapay',
        provider_id: fields.BILLER_MASTER_ID,
        amount: fields.amount,
        provider_bill_details_id: fields.aggregator_txn_id, // ?
        account_id: fields.mobile,
        mobile_number: fields.mobile,
        details: JSON.stringify({ 'Mobile Number': fields.mobile }),
        bou_fee: fields.bou_fee || 0, // ?
        cou_fee: fields.cou_fee || 0, // ?
        callbackurl: util.bbpscallbackurl,
        viewbillresponseid: 0 // invoice_id
      }

      // make makePayment_v2 API call to MA
      const response = await this.doApiCall({ api_name: 'makePayment', data })

      return await this.getResponse(response)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'makePayment', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'makePayment',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * callPayIndex description - Call's payindex API
   * @param {{
   *  ma_user_id: number,
   *  userid: number,
   *  email_id: string,
   *  firstname: string,
   *  lastname: string,
   *  address: string,
   *  city: string,
   *  state: string,
   *  country: string,
   *  amount: string,
   *  aggregator_order_id: string,
   *  pincode: string,
   *  pin: string
   * }} fields
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async callPayIndex (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const env = process.env.NODE_ENV || 'development'
      const { payIndexchecksum } = require('./../../../../util/checksum.js')
      const { mercid } = util[env].merchant_details
      const alldata = {
        email_id: fields.email_id.trim(),
        firstname: fields.firstname.trim(),
        lastname: fields.lastname,
        address: fields.address,
        city: fields.city,
        state: fields.state,
        country: fields.country,
        amount: fields.amount,
        aggregator_order_id: fields.aggregator_order_id,
        merchantDet: {
          username: util[env].merchant_details.username,
          password: util[env].merchant_details.password
        }
      }

      const checksum = payIndexchecksum(alldata)
      const data = {
        privatekey: fields.privatekey,
        checksum,
        mercid: mercid,
        orderid: fields.aggregator_order_id,
        currency: '356',
        isocurrency: 'INR',
        chmod: 'apcrdt',
        channel: 'apcrdt',
        buyerEmail: fields.email_id,
        buyerPhone: fields.mobile_id,
        buyerFirstName: fields.firstname,
        buyerLastName: fields.lastname,
        buyerAddress: fields.address,
        buyerCity: fields.city,
        buyerState: fields.state,
        buyerCountry: fields.country,
        buyerPinCode: fields.pincode,
        amount: fields.amount,
        apcrdtpin: fields.pin,
        customvar: '',
        arpyVer: util.arpyVer,
        mer_dom: Buffer.from('https://apmerchantapp.nowpay.co.in').toString('base64')
      }

      const response = await this.doApiCall({
        api_name: 'callPayIndex',
        url: util.paymentsUrl + 'pay/payindexapi.php',
        data: qs.stringify(data),
        config: {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/69.0.3497.105 Mobile/15E148 Safari/605.1'
          },
          timeout: 20000
        }
      })

      if (response.status !== 200) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in point bank transaction' }
      return response
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'callPayIndex', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'callPayIndex',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * @private
   * @description This function is used to make axios api call and log the response in db
   * @param {{ api_name: string, data: any, url?: string, config?: { headers?: JSON, timeout?: number } }} requestInfo
   * @param {Object} requestInfo.data
  */
  static async doApiCall (requestInfo) {
    logs.logger({ pagename: path.basename(__filename), action: 'doApiCall', type: 'request Information', fields: requestInfo })

    try {
      logs.logger({ pagename: path.basename(__filename), action: 'doApiCall', type: 'request Information', fields: { url: this.URL, config: this.CONFIG } })

      console.time('TIMER_RECHARGE_' + requestInfo.api_name.toUpperCase())
      const response = await Axios.post(requestInfo.url || this.URL, requestInfo.data, requestInfo.config || this.CONFIG)
      console.timeEnd('TIMER_RECHARGE_' + requestInfo.api_name.toUpperCase())
      logs.logger({ pagename: path.basename(__filename), action: 'doApiCall', type: 'response', fields: { data: response.data } })

      const requestParams = { api_name: requestInfo.api_name, url: requestInfo.url, data: requestInfo.data, config: requestInfo.config, method: requestInfo.method }
      logs.logger({ pagename: path.basename(__filename), action: 'doApiCall', type: 'response', fields: { data: { request: JSON.stringify(requestParams), response: JSON.stringify(response.data) } } })

      if (response.status == 200) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: response.data }
      }
      return { status: 400, respcode: 1144, message: errorMsg.responseCode[1144] }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'doApiCall', type: 'error', fields: error })

      if (error.response && error.response.data) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: error.response.data }
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('CONNECTION_TIMEOUT: ' + requestInfo.api_name)
        return { status: 301, respcode: 1145, message: errorMsg.responseCode[1145] }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * @private
   * getResponse description - Returns Appropriate response
   * @param {{ response: { status: number, message: string, data: { STATUS: number, MESSAGE?: string, DATA?: any} } }} fields
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static async getResponse (response) {
    logs.logger({ pagename: path.basename(__filename), action: 'getResponse', type: 'request', fields: { response } })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(response, ['status', 'message', 'respcode'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      if (response.status !== 200) return response
      else if (response.data.STATUS != 200 && (validator.validateField(response.data.MESSAGE) || validator.validateField(response.data.MSG) || validator.validateField(response.data[400]))) return { status: 400, respcode: 1028, message: `Error::: ${response.data.MESSAGE || response.data.MSG || response.data[400] || ''}` }
      else if (response.data.STATUS != 200) return { status: 400, respcode: 1144, message: errorMsg.responseCode[1144] }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: response.data.DATA || response.data.BILLERDATA }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getResponse', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getResponse',
        data: { response },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * @private
   * generateChecksum description - generates a checksum synchronously
   * @param {{ ma_user_id: number, privatekey: string }} fields
   * @returns {{ status: number, message: string, respcode: number }}
   */
  static generateChecksum (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'generateChecksum', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['privatekey', 'ma_user_id'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const today = new Date()
      const date = today.getFullYear() + '' + ('0' + (today.getMonth() + 1)).slice(-2) + '' + ('0' + today.getDate()).slice(-2)
      const checksum = crypto.createHash('sha256').update(date + fields.ma_user_id + fields.privatekey).digest('hex')
      logs.logger({ pagename: path.basename(__filename), action: 'generateChecksum', type: 'checksum', fields: checksum })
      return checksum
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'generateChecksum', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'generateChecksum',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }
}

module.exports = MaRechargeIntegration
