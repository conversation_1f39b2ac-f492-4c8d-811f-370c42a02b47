const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common = require('../../util/common')

const qs = require('qs')
const parser = require('fast-xml-parser')
const axios = require('axios')
const mailer = require('../../util/sendEmails')
const nodemailer = require('nodemailer')
const mailconfig = require('../../lib/mailConfig')
const transactionCtrl = require('./transactionController')
const crypto = require('crypto')
const { generateOrderchecksum } = require('../../util/checksum')
const refundTransactionController = require('../refund/refundtransactionController')
const ministatementController = require('./ministatementController')
const collectMoneyController = require('./collectMoneyController')
const integrated = require('../integrated/integratedController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const balanceController = require('../balance/balanceController')

const common_fns = require('../../util/common_fns')
const redis = require('redis')
const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
const redisHost = util[env].redisHostIp
const client = redis.createClient({ port: 6379, host: redisHost })
const moment = require('moment')
const momentTimezone = require('moment-timezone')
const validator = require('../../util/validator')
const { FINO_BANK_ID, CREDOPAY_BANK_ID } = require('../aepsMerchantAuth/config').AEPS_AUTH_CONSTANTS
/* SOUNDBOX CHANGES */
const SoundBoxManagementController = require('../soundBoxManagement/soundBoxManagementController')

class aepsTransactionController extends DAO {
  /**
   * Do AEPS Cash Withrawal Transactions
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} fields
   */
  static async doAepsCashWithdrawal (_, fields) {
    let connection = await mySQLWrapper.getConnectionFromPool()
    log.logger({ pagename: require('path').basename(__filename), action: 'doAepsCashWithdrawal', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    try {
      fields.connection = connection
      let transaction_status = 'P' // Set Initial transaction status as Pending
      fields.mercid = fields.ma_user_id
      fields.orderid = fields.aggregator_order_id
      const transaction_mode = fields.createTransactionParams.transaction_mode
      const transaction_type = fields.createTransactionParams.transaction_type

      var merchant_type = fields.merchant_type
      var ssotoken = fields.ssotoken
      var txn_ref_number = fields.txn_ref_number
      var login_token = fields.login_token
      var ssoid = fields.ssoid
      var checksumstr = fields.checksumstr

      delete fields.merchant_type
      delete fields.ssotoken
      delete fields.txn_ref_number
      delete fields.login_token
      delete fields.ssoid
      delete fields.checksumstr

      // Validate the Required Amount field
      if (!fields.amount) {
        return { status: 400, respcode: 1136, message: errorMsg.responseCode[1136], action_code: 1001 }
      }
      fields.transactionAmount = fields.amount
      // add merchant type in createTransactionParams
      fields.createTransactionParams.merchant_type = merchant_type

      // Step 1 :

      const captureResponseDecodeResult = Buffer.from(fields.captureResponse, 'base64').toString()
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'captureResponseDecodeResult', fields: captureResponseDecodeResult })

      const jsonObjectResult = JSON.parse(captureResponseDecodeResult)

      let authentication_type = ''

      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'jsonObjectResult', fields: jsonObjectResult })
      console.log('fauth value', jsonObjectResult.fauth)

      if (typeof jsonObjectResult === 'object' && jsonObjectResult != '') {
        authentication_type = jsonObjectResult.fauth ? jsonObjectResult.fauth : ''
      } else {
        authentication_type = ''
      }

      console.log('---STEP 1 : initiateTransaction (doAepsCashWithdrawal) : Start---')
      // Create AEPS transaction in Pending State
      console.time('TIME_FOR_AEPS_INITIATE_TRANSACTION')
      const createTransaResponse = await transactionCtrl.initiateTransaction(_, fields.createTransactionParams)
      console.timeEnd('TIME_FOR_AEPS_INITIATE_TRANSACTION')
      console.log('---Create AEPS trans response---', createTransaResponse)

      if (createTransaResponse.status === 400 || createTransaResponse.status === 401) {
        console.log('---STEP 1 : initiateTransaction (doAepsCashWithdrawal) : Failed---')
        if (merchant_type == 'emitra') {
          checksumstr = 400 + '' + 1001 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
          const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          return { status: 400, respcode: 1001, message: createTransaResponse.message, amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1001 }
        } else {
          createTransaResponse.action_code = 1001
          return createTransaResponse // Return if error
        }
      }
      const authTypeUpdateQuery = `UPDATE ma_aeps_transaction_logs SET authentication_type = '${authentication_type}' where order_id = '${fields.aggregator_order_id}';`
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'authTypeUpdateQuery', fields: authTypeUpdateQuery })
      const authTypeUpdateRes = await this.rawQuery(authTypeUpdateQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'authTypeUpdateRes', fields: authTypeUpdateRes })

      // add entry in cron table
      console.log('--- insert transaction details in ma_aeps_aadhaar_ledger_log able (doAepsCashWithdrawal) : Start---')
      const saveAdpayOrderVerifyData = `Insert into ma_aeps_aadhaar_ledger_log
      (ma_user_id, userid, order_id, amount, transaction_type)
      VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','${fields.amount}','5')`
      const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsCashWithdrawal', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })
      const ledgerCronId = saveAdpayOrderVerifyResult.insertId
      console.log('--- End---')

      console.log('---STEP 1 : initiateTransaction (doAepsCashWithdrawal) : Success---')
      // Step 2 :
      console.log('---STEP 2 : callGenerateOrder (doAepsCashWithdrawal) : Start---')
      // Call Generate order Payments API
      connection.release()
      const response = await this.callGenerateOrder(fields)

      connection = await mySQLWrapper.getConnectionFromPool()
      fields.connection = connection
      console.log('---Generate order response---', response)
      console.log('---Generate order response---', (response.data) ? response.data : response)
      const response_data = JSON.stringify(response.data)
      console.log('response_data', response_data)

      if (fields.two_factor_order_id) {
        const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
          bankCode: fields.bank_code,
          connection
        })
        if (iscw2FAAuthRequiredResult) {
          const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
          log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
          console.log('cwAuthRequired', cwAuthRequired)
          if (cwAuthRequired == 'Y' || iscw2FAAuthRequiredResult.length < 0 || cwAuthRequired == 'N/A' || cwAuthRequired == '' || iscw2FAAuthRequiredResult == undefined || iscw2FAAuthRequiredResult == []) {
            let flag = false
            let chargesAmount = ''
            let gstType = ''
            let gst_per = ''
            let charge_amount_type = ''
            let GSTAmount = ''
            // Check condition for integrated users
            const userintegrated = `Select ma_user_id from ma_integration_user_master Where ma_user_id = '${fields.ma_user_id}' AND integration_code = 'EMITRA' AND record_status = 'Y'`
            log.logger({ pagename: 'doAepsCashWithdrawal.js', action: 'get mid', type: 'request - Integrated user check', fields: userintegrated })
            const resultint = await this.rawQuery(userintegrated, connection)
            log.logger({ pagename: 'doAepsCashWithdrawal.js', action: 'get mid', type: 'response - Integrated user check', fields: resultint })
            if (resultint.length > 0) {
              flag = true
            } else {
              const sqlCharge = `select charge_amount,gst_type,gst_amount,charge_amount_type,bank_id from ma_slabwise_distribution_2fa where charge_type ='Cash Withdrawal' and record_status = 'Y' and bank_id = '${fields.bank_code}' limit 1`
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'doAepsCashWithdrawal',
                type: 'sqlCharge',
                fields: sqlCharge
              })
              const sqlChargeDetails = await this.rawQuery(sqlCharge, connection)
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'doAepsCashWithdrawal',
                type: 'sqlChargeResult',
                fields: sqlChargeDetails
              })
              if (sqlChargeDetails.length == 0) {
                // all
                const sqlCharge = 'select charge_amount,gst_type,gst_amount,charge_amount_type,bank_id from ma_slabwise_distribution_2fa where charge_type =\'Cash Withdrawal\' and record_status = \'Y\' and bank_id = \'-1\' limit 1'
                log.logger({
                  pagename: require('path').basename(__filename),
                  action: 'doAepsCashWithdrawal',
                  type: 'sqlCharge',
                  fields: sqlCharge
                })
                const sqlChargeDetails = await this.rawQuery(sqlCharge, connection)
                log.logger({
                  pagename: require('path').basename(__filename),
                  action: 'doAepsCashWithdrawal',
                  type: 'sqlChargeResult',
                  fields: sqlChargeDetails
                })
                if (sqlChargeDetails.length == 0) {
                  log.logger({
                    pagename: require('path').basename(__filename),
                    action: 'doAepsCashWithdrawal',
                    type: 'sqlChargeResult',
                    fields: 'No bank slabs found'
                  })
                  flag = true
                } else {
                  chargesAmount = sqlChargeDetails[0].charge_amount
                  if (chargesAmount == 0 || chargesAmount == null) {
                    flag = true
                  }
                  console.log('chargesAmount', chargesAmount)
                  gstType = sqlChargeDetails[0].gst_type
                  gst_per = sqlChargeDetails[0].gst_amount
                  charge_amount_type = sqlChargeDetails[0].charge_amount_type
                  console.log('charge_amount_type', charge_amount_type)
                  console.log('gst_per', gst_per)

                  GSTAmount = chargesAmount * (gst_per / 100)
                  console.log('GSTAmount', GSTAmount)
                  if (charge_amount_type == 2) { // percentage type
                    chargesAmount = (fields.amount * chargesAmount) / 100
                    console.log('chargesAmount2', chargesAmount)
                    GSTAmount = chargesAmount * (gst_per / 100)
                    console.log('GSTAmount2', GSTAmount)
                  }
                }
              } else {
                // bank wise
                chargesAmount = sqlChargeDetails[0].charge_amount
                if (chargesAmount == 0 || chargesAmount == null) {
                  flag = true
                }
                console.log('chargesAmount', chargesAmount)
                gstType = sqlChargeDetails[0].gst_type
                gst_per = sqlChargeDetails[0].gst_amount
                charge_amount_type = sqlChargeDetails[0].charge_amount_type
                console.log('charge_amount_type', charge_amount_type)
                console.log('gst_per', gst_per)

                GSTAmount = chargesAmount * (gst_per / 100)
                console.log('GSTAmount', GSTAmount)
                if (charge_amount_type == 2) { // percentage type
                  chargesAmount = (fields.amount * chargesAmount) / 100
                  console.log('chargesAmount2', chargesAmount)
                  GSTAmount = chargesAmount * (gst_per / 100)
                  console.log('GSTAmount2', GSTAmount)
                }
              }
            }
            if (flag == false) {
              let totalAmount = ''
              if (gstType == 'I') {
                totalAmount = (chargesAmount - GSTAmount).toFixed(2)
              } else if (gstType == 'E') {
                totalAmount = (chargesAmount + GSTAmount).toFixed(2)
              } else {
                totalAmount = chargesAmount.toFixed(2)
              }
              const availableBalance =
              await balanceController.getWalletBalancesDirect(_, {
                ma_user_id: fields.ma_user_id,
                ma_status: 'ACTUAL',
                balance_flag: 'SUMMARY',
                connection: connection
              })
              if (availableBalance.status != 200) return availableBalance
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'doAepsCashWithdrawal',
                type: 'balanceCheck',
                fields: availableBalance
              })
              if (availableBalance.amount < totalAmount) {
                return {
                  status: 400,
                  respcode: 1001,
                  message: 'Insufficient balance, kindly fund the account to enable AePS Service.'
                }
              }

              const newFields = {
                ma_user_id: fields.ma_user_id,
                userid: fields.userid,
                orderid: fields.two_factor_order_id,
                amount: totalAmount,
                chargesAmount: chargesAmount,
                mobile_number: fields.mobile_number,
                customer_mobile: fields.mobile_number ? fields.mobile_number : '0',
                transaction_type: '66',
                remarks: `AEPS 2FA cash withdrawal Transaction Ref ID : ${fields.aggregator_order_id}`,
                gstType: gstType,
                GSTAmount: GSTAmount,
                newAmount: totalAmount,
                gst_amount: gst_per
              }
              await mySQLWrapper.beginTransaction(connection)

              const transactionResponse = await this.create2FATransaction({
                fields: newFields,
                connection
              })
              console.log('transactionResponse', transactionResponse)
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'doAepsCashWithdrawal',
                type: 'transaction Response',
                fields: transactionResponse
              })
              if (transactionResponse.status != 200) {
                await mySQLWrapper.rollback(connection)
                return transactionResponse
              }
              // update ledger entries
              const ledgerEntries = await this.ledgerEntries({
                fields: newFields,
                connection
              })
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'doAepsCashWithdrawal',
                type: 'ledgerEntries',
                fields: ledgerEntries
              })
              if (ledgerEntries.status != 200) {
                await mySQLWrapper.rollback(connection)
                return ledgerEntries
              }
              console.log('Bank Code', fields.bank_code)
              const aepsTransactionLogsQuery = `insert into ma_aeps_transaction_logs (ma_user_id, order_id, amount, transaction_status, aeps_mode, aggregator_bank,bank_id,bank_response) values ( '${fields.ma_user_id}','${fields.two_factor_order_id}','${totalAmount}', 'S', 'AEPS CW Charges','${fields.bank_code || 0}','${fields.bank_id || 0}','${response_data}')`
              log.logger({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogsQuery', type: 'LogQuery', fields: aepsTransactionLogsQuery })
              const aepsTransactionLogs = await this.rawQuery(aepsTransactionLogsQuery, connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogs', type: 'LogResult', fields: aepsTransactionLogs })
              const updateOrderQuery = `UPDATE ma_aeps_cw_transaction_logs SET two_factor_order_id = '${fields.two_factor_order_id}' WHERE order_id = '${fields.aggregator_order_id}';`
              log.logger({ pagename: 'aepsTransactionController.js', action: 'updateOrderQuery', type: 'updateorderidQuery', fields: updateOrderQuery })
              const updateOrderQueryRes = await this.rawQuery(updateOrderQuery, connection)
              log.logger({ pagename: 'aepsTransactionController.js', action: 'updateOrderQuery', type: 'updateorderidQueryRes', fields: updateOrderQueryRes })
            } else if (flag == true) {
              const newFields = {
                ma_user_id: fields.ma_user_id,
                userid: fields.userid,
                orderid: fields.two_factor_order_id,
                amount: 0,
                chargesAmount: 0,
                transaction_type: '66',
                mobile_number: fields.mobile_number ? fields.mobile_number : '0',
                customer_mobile: fields.mobile_number ? fields.mobile_number : '0',
                remarks: `AEPS 2FA cash withdrawal Transaction Ref ID : ${fields.aggregator_order_id}`,
                gstType: gstType,
                GSTAmount: GSTAmount,
                merchant_type: 'emitra'
              }
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'registerMerchantForAepsAuth',
                type: 'transaction Response for No slab',
                fields: newFields
              })
              await mySQLWrapper.beginTransaction(connection)

              const transactionResponse = await this.create2FATransaction({
                fields: newFields,
                connection
              })
              console.log('transactionResponse for No slab', transactionResponse)
              log.logger({
                pagename: require('path').basename(__filename),
                action: 'registerMerchantForAepsAuth',
                type: 'transaction Response for No slab',
                fields: transactionResponse
              })
              if (transactionResponse.status != 200) {
                await mySQLWrapper.rollback(connection)
                return transactionResponse
              }
              const updateOrderQuery = `UPDATE ma_aeps_cw_transaction_logs SET two_factor_order_id = '${fields.two_factor_order_id}' WHERE order_id = '${fields.aggregator_order_id}';`
              log.logger({ pagename: 'aepsTransactionController.js', action: 'updateOrderQuery', type: 'updateorderidQuery', fields: updateOrderQuery })
              const updateOrderQueryRes = await this.rawQuery(updateOrderQuery, connection)
              log.logger({ pagename: 'aepsTransactionController.js', action: 'updateOrderQuery', type: 'updateorderidQueryRes', fields: updateOrderQueryRes })
            }
          }
        }
      }
      if (response.status != 200) {
        console.log('---STEP 2 : callGenerateOrder (doAepsCashWithdrawal) : Failed---')

        // Step 3
        console.log('---STEP 3 : orderConfirmation (doAepsCashWithdrawal) : Start---')
        // Call order confirmation
        console.time('TIME_FOR_AEPS_ORDER_CONFIRMATION')
        const orderConfirmation = await this.orderConfirmation(fields)
        console.timeEnd('TIME_FOR_AEPS_ORDER_CONFIRMATION')
        console.log('---Order Confirmation response---', orderConfirmation)
        const updateOrderParams = {
          transactionid: 0,
          rrn: null,
          message: '',
          bankbalance: 0,
          ledger_cron_id: ledgerCronId
        }
        console.log(JSON.stringify(orderConfirmation.transactionObj))
        if (orderConfirmation.status === 200) {
          console.log('---STEP 3 : orderConfirmation (doAepsCashWithdrawal) : Success---')
          transaction_status = orderConfirmation.transaction_status
          updateOrderParams.transactionid = orderConfirmation.aggregator_txn_id
          updateOrderParams.rrn = orderConfirmation.rrn
          updateOrderParams.message = orderConfirmation.transaction_reason
          updateOrderParams.bankbalance = orderConfirmation.bankbalance
          updateOrderParams.transaction_mode = transaction_mode
          // Converting Pending transaction to Failed
          console.log('Transaction status --- ', transaction_status)
          if (transaction_status == 'P') {
            console.log('---STEP 3 : orderConfirmation (doAepsCashWithdrawal) : Success--- returning P')
            var refund = await refundTransactionController.refundAeps('_', {
              ma_user_id: fields.ma_user_id,
              userid: fields.userid,
              amount: fields.amount,
              aggregator_order_id: orderConfirmation.aggregator_txn_id
            })
            console.log('---Refund Response---', refund)
            if (refund.status == '200') {
              transaction_status = 'F'
            }
          }
        } else {
          console.log(`---Refund : orderConfirmation (doAepsCashWithdrawal) : Failure (${orderConfirmation})---`)
          transaction_status = 'F'
          console.log(`---Failed : ma_aeps_failed_temp (doAepsCashWithdrawal) : On Failure (${JSON.stringify(orderConfirmation)})---`)
          var temptable = {}
          temptable.ma_transaction_master_id = ''
          temptable.ma_user_id = fields.ma_user_id
          temptable.aggregator_txn_id = fields.aggregator_order_id
          temptable.order_id = fields.aggregator_order_id
          temptable.ma_transaction_master_id = createTransaResponse.transaction_id
          temptable.amount = fields.amount
          temptable.status = 'I'
          temptable.attempts = '1'
          temptable.remark = orderConfirmation.message

          this.TABLE_NAME = 'ma_aeps_failed_temp'
          const _result = await this.insert(connection, {
            data: temptable
          })
          /* comment below code as per discussion with compliance team - 08-01-2025
          // save order detaisl in adpay cron table - transaction_type = 10
          const status_code = orderConfirmation.status_code ? orderConfirmation.status_code : 211
          const saveAdpayOrderVerifyData = `Insert into ma_aadhaar_order_verify_log
                                        (ma_user_id, userid, airpay_id, ma_transaction_master_id, order_id, amount, status_code, transaction_type)
                                        VALUES (${fields.ma_user_id},${fields.userid},'',${createTransaResponse.transaction_id},'${fields.aggregator_order_id}','${fields.amount}','${status_code}',10)`
          const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'saveAdpayOrderVerifyResult', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })
          */
        }
        console.log(`---STEP 3 : orderConfirmation (doAepsCashWithdrawal) : Success (${orderConfirmation.status})---`)

        // Step 4
        console.log('---STEP 4 : callUpdateAepsTrans (doAepsCashWithdrawal) : Start---')
        // Update the AEPS transaction
        console.time('TIME_FOR_UPDATE_AEPS_TRANSACTION')
        const updateAepsResponse = await this.callUpdateAepsTrans(fields, transaction_status, updateOrderParams)
        console.timeEnd('TIME_FOR_UPDATE_AEPS_TRANSACTION')
        console.log('---Update Aeps Transaction response---', updateAepsResponse)

        if (updateAepsResponse.status === 400) {
          console.log('---STEP 4 : callUpdateAepsTrans (doAepsCashWithdrawal) : Failed---')
        } else {
          console.log('---STEP 4 : callUpdateAepsTrans (doAepsCashWithdrawal) : Success---')
        }

        if (merchant_type == 'emitra') {
          let checksum = ''
          if (transaction_status == 'F') {
            /* AEPS EMITRA CHANGES 'REFUND' */
            fields.action = 'REFUND'
            fields.aggregator_user_id = ssoid
            const responseRefund = await integrated.index(fields, connection)
            console.log('integrated returned this', responseRefund)
            /* END AEPS EMITRA CHANGES 'REFUND' */

            checksumstr = 400 + '' + 1028 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
            checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          } else if (transaction_status == 'S') {
            const receiptData = {}
            receiptData.action = 'POSTRECEIPT'
            receiptData.aggregator_user_id = ssoid
            receiptData.aggregator_order_id = fields.aggregator_order_id
            receiptData.ma_user_id = fields.ma_user_id

            const responsePostReceipt = await integrated.index(receiptData, connection)
            console.log('integrated returned this', responsePostReceipt)
            /* END AEPS EMITRA CHANGES 'POSTRECEIPT' */

            checksumstr = 200 + '' + 1000 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
            checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          } else {
            checksumstr = 200 + '' + 1170 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
            checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          }
          updateAepsResponse.checksum = checksum
          updateAepsResponse.sso_token = ssotoken
        }
        return updateAepsResponse
      }
      // Generate order response
      const generateOrderResp = response.data
      generateOrderResp.transaction_mode = transaction_mode
      if (generateOrderResp.status === 400) {
        if (response.data.message == null || response.data.message == 'undefined') {
          response.data.message = errorMsg.responseCode[1001]
        }
        if (generateOrderResp.responseCode == '0059' || generateOrderResp.responseCode == '59') {
          generateOrderResp.message = errorMsg.responseCode[12364]
        } else if (generateOrderResp.message.toLowerCase() == 'Please try next transaction after 30 minutes'.toLowerCase()) {
          generateOrderResp.message = errorMsg.responseCode[12365]
        }
        console.log('---STEP 2 : callGenerateOrder (doAepsCashWithdrawal) : Success (Transaction Failed)---')
        transaction_status = 'F'
      } else if (generateOrderResp.status === 200) {
        console.log('---STEP 2 : callGenerateOrder (doAepsCashWithdrawal) : Success (Transaction Success)---')
        transaction_status = 'S'
      } else {
        console.log('Bank response status', generateOrderResp.status)
        if (generateOrderResp.status === undefined) {
          console.log('Invalid response', generateOrderResp)
          transaction_status = 'F'
          var tempTable = {}
          tempTable.ma_transaction_master_id = ''
          tempTable.ma_user_id = fields.ma_user_id
          tempTable.aggregator_txn_id = fields.aggregator_order_id
          tempTable.order_id = fields.aggregator_order_id
          tempTable.ma_transaction_master_id = createTransaResponse.transaction_id
          tempTable.amount = fields.amount
          tempTable.status = 'E'
          tempTable.attempts = '1'
          tempTable.remark = 'Transaction Details not Available'

          this.TABLE_NAME = 'ma_aeps_failed_temp'
          const _result = await this.insert(connection, {
            data: tempTable
          })
        }
      }
      console.log('Final transaction status : ' + transaction_status)

      // Step 3
      console.log('---STEP 3 : callUpdateAepsTrans (doAepsCashWithdrawal) : Start---')
      // Update the AEPS transaction
      console.time('TIME_FOR_UPDATE_AEPS_TRANSACTION')
      generateOrderResp.ledger_cron_id = ledgerCronId
      const updateAepsResponse = await this.callUpdateAepsTrans(fields, transaction_status, generateOrderResp)
      console.timeEnd('TIME_FOR_UPDATE_AEPS_TRANSACTION')
      console.log('---Update Aeps Transaction response---', updateAepsResponse)

      if (updateAepsResponse.status === 400) {
        console.log('---STEP 3 : callUpdateAepsTrans (doAepsCashWithdrawal) : Failed---')
      }
      console.log('---STEP 3 : callUpdateAepsTrans (doAepsCashWithdrawal) : Success---')

      /* SOUNDBOX CHANGES */
      if (transaction_status == 'S') {
        const getTransactionTypeDataSql = `Select transaction_types from ma_soundbox_details_master where ma_user_id =${fields.mercid} and device_status ='Y' limit 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'getTransactionTypeDataSql', fields: getTransactionTypeDataSql })
        const getTransactionTypeData = await this.rawQuery(getTransactionTypeDataSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'getTransactionTypeData', fields: getTransactionTypeData })
        if (getTransactionTypeData.length > 0) {
          const transactionTypeValue = getTransactionTypeData[0].transaction_types
          console.log('transaction_type>>>>', transaction_type)
          console.log('typeof', typeof (transaction_type))
          if (transactionTypeValue) {
            const valuesArray = transactionTypeValue.split(',')
            console.log('valuesArray>>', valuesArray)
            // const intvaluesArray = valuesArray.map(Number)
            // console.log('intvaluesArray>>>', intvaluesArray)
            const isPresent = valuesArray.includes(transaction_type)
            console.log('isPresent>>', isPresent)
            if (isPresent) {
              const sendSoundBoxNotificationResp = await SoundBoxManagementController.sendSoundBoxNotification({
                ma_user_id: fields.mercid,
                amount: fields.amount,
                txnType: 'AEPS',
                orderid: fields.orderid,
                connectionRead: connection
              })
              log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'response', fields: sendSoundBoxNotificationResp })
            }
          }
        }
      }
      if (merchant_type == 'emitra') {
        /* AEPS EMITRA CHANGES 'POSTRECEIPT' */
        if (transaction_status == 'S') {
          const receiptData = {}
          receiptData.action = 'POSTRECEIPT'
          receiptData.aggregator_user_id = ssoid
          receiptData.aggregator_order_id = fields.aggregator_order_id
          receiptData.ma_user_id = fields.ma_user_id

          const responsePostReceipt = await integrated.index(receiptData, connection)
          console.log('integrated returned this', responsePostReceipt)
        }
        /* AEPS EMITRA CHANGES 'REFUND' */
        if (transaction_status == 'F') {
          fields.action = 'REFUND'
          fields.aggregator_user_id = ssoid
          const responseRefund = await integrated.index(fields, connection)
          console.log('integrated returned this', responseRefund)
        }

        // update request status
        let checksum = ''
        if (transaction_status == 'F') {
          checksumstr = 400 + '' + 1028 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
          checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
        } else {
          checksumstr = 200 + '' + 1000 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
          checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
        }
        updateAepsResponse.checksum = checksum
        updateAepsResponse.sso_token = ssotoken
      }
      return updateAepsResponse
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsCashWithdrawal', type: 'catcherror', fields: err })
      if (merchant_type == 'emitra') {
        // update request status
        checksumstr = 200 + '' + 1170 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
        const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
        return { status: 200, respcode: 1170, message: errorMsg.responseCode[1170], amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1000 }
      }
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!', action_code: 1001 }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * Get last 10 transaction details
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} fields
   */
  static async doAepsMiniStatement (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    log.logger({ pagename: require('path').basename(__filename), action: 'doAepsMiniStatement', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    try {
      fields.connection = connection
      fields.mercid = fields.ma_user_id
      fields.orderid = fields.aggregator_order_id
      let returnData = {}
      let transaction_status = 'P'
      const transaction_type = fields.createTransactionParams.transactionType
      const transaction_mode = fields.createTransactionParams.transaction_mode
      fields.amount = fields.createTransactionParams.amount = 0

      var merchant_type = fields.merchant_type
      var ssotoken = fields.ssotoken
      var txn_ref_number = fields.txn_ref_number
      var login_token = fields.login_token
      var ssoid = fields.ssoid
      var checksumstr = fields.checksumstr

      delete fields.merchant_type
      delete fields.ssotoken
      delete fields.txn_ref_number
      delete fields.login_token
      delete fields.ssoid
      delete fields.checksumstr

      console.log('---STEP 1 : Initiate Transaction (doAepsMiniStatement) : Start---')
      const createTransaResponse = await transactionCtrl.initiateTransaction(_, { ...fields.createTransactionParams, merchant_type })
      console.log('---STEP 1 : Initiate Transaction (doAepsMiniStatement) : End---', createTransaResponse, merchant_type)
      if (createTransaResponse.status === 400 || createTransaResponse.status === 401) {
        if (merchant_type == 'emitra' && createTransaResponse.respcode != 14014) {
          checksumstr = 400 + '' + 1001 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
          const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          return { status: 400, respcode: 1001, message: createTransaResponse.message, amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1001 }
        } else {
          if (createTransaResponse.respcode != 14014) {
            console.log('---STEP 1 : initiateTransaction (doAepsMiniStatement) : Failed---', createTransaResponse)
            createTransaResponse.action_code = 1001
            return createTransaResponse // Return if error
          }
        }
      }

      // add entry in cron table
      console.log('--- insert transaction details in ma_aeps_aadhaar_ledger_log able (doAepsMiniStatement) : Start---')
      const saveAdpayOrderVerifyData = `Insert into ma_aeps_aadhaar_ledger_log
      (ma_user_id, userid, order_id, amount, transaction_type)
      VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','${fields.amount}','40')`
      const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsMiniStatement', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })
      const ledgerCronId = saveAdpayOrderVerifyResult.insertId
      console.log('--- End---')

      console.log('---STEP 2 : callGenerateOrder (doAepsMiniStatement) : Start---')
      var response = await this.callGenerateOrder(fields) // { data: {} } // await this.callGenerateOrder(fields)
      console.log('---Generate order response---', response)
      // response.status = 200
      // response.data.status = 200
      // response.data.message = 'Success'
      if (response.status === 200) {
        if (response.data.status === 200) {
          console.log('---STEP 2 : callGenerateOrder (doAepsMiniStatement) : Success---')
          // Return data structure
          var cardData = JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8'))
          var lastRecordArray = response.data.lastrecord
          // var lastRecordArray = ['OSIT2021-10-04DR 5010.00 WITHDR', 'AWAL2021-10-03CR 10000.00 DEP', 'OSIT2021-10-03DR 2510.00 WITHDR', 'AWAL2021-10-02CR 9680.00 DEP', 'OSIT2021-10-02DR 1000.00 WITHDR', 'AWAL2021-09-30CR 35.00 DEP', 'OSIT2021-09-30DR 7500.00 WITHDR', 'AWAL2021-09-29DR 537.96 WITHDR', 'AWAL Balance 15202.0', '4 CR']
          if (lastRecordArray != 'undefined' && lastRecordArray != null) {
            var lastRecords = await ministatementController.extractLastRecordData(lastRecordArray, cardData.bankName, response.data.aggregatorBank)
            // var lastRecords = await ministatementController.extractLastRecordData(lastRecordArray, 'Indian Bank Erstwhile Allahabad Bank', 'csb')
            console.log('Returned array =>=>=>', lastRecords)
            // if (!lastRecords.structure_type) {
            if (!lastRecords.structure_type) {
              returnData.lastrecord = []
              returnData.lastrecord_unstructured = JSON.stringify(lastRecords.returnArray)
              const sqlEnableLogging = 'SELECT * from ma_ms_switch where enable_logging="1"'
              const dataEnableLogging = await this.rawQuery(sqlEnableLogging, connection)
              if (dataEnableLogging.length > 0) {
                console.log('ministatement logging is ON')
                this.TABLE_NAME = 'ma_ms_bank_responses'
                const _result = await this.insert(connection, {
                  data: {
                    bank_response: JSON.stringify(lastRecords.returnArray),
                    bank_name: cardData.bankName,
                    aggregator_bank_name: response.data.aggregatorBank
                  }
                })
              }
            } else {
              returnData.lastrecord = lastRecords.returnArray
              returnData.lastrecord_unstructured = JSON.stringify([])
              returnData.structure_type = true
            }
          }
          returnData.status = response.data.status
          returnData.respcode = 1000
          returnData.responseCode = response.data.responseCode
          returnData.message = response.data.message
          returnData.stan = response.data.stan
          returnData.uidaiAuthCode = response.data.uidaiAuthCode
          returnData.orderid = response.data.orderid
          returnData.rrn = response.data.rrn
          returnData.aggregator_bank_name = response.data.aggregatorBank
          returnData.bank_balance = typeof lastRecords.balance == 'string' ? lastRecords.balance.replace('RS.', '').trim() : lastRecords.balance
          returnData.structure_type = lastRecords.structure_type
          returnData.terminalId = response.data.terminalId
          returnData.customer_mobile = fields.mobile_number
          returnData.customer_aadhaar = common_fns.maskValue(cardData.adhaarNumber)
          returnData.customer_name = fields.customer_name
          returnData.transaction_time = moment.unix(fields.timestamp).utcOffset(330).format('DD-MM-YYYY h:mm:ss')
          returnData.bank_name = cardData.bankName
          returnData.action_code = 1000
          returnData.transaction_mode = transaction_mode
          transaction_status = 'S'
          returnData.ledger_cron_id = ledgerCronId
          // return returnData
        } else {
          transaction_status = 'F'
          returnData = {
            status: response.data.status,
            respcode: 1000,
            responseCode: response.data.responseCode,
            message: response.data.message,
            stan: response.data.stan,
            uidaiAuthCode: response.data.uidaiAuthCode,
            orderid: response.data.orderid,
            rrn: response.data.rrn,
            bank_balance: response.data.bankbalance,
            terminalId: response.data.terminalId,
            transaction_time: moment.unix(fields.timestamp).utcOffset(330).format('DD-MM-YYYY h:mm:ss'),
            action_code: 1000,
            ledger_cron_id: ledgerCronId
          }
        }
        if (createTransaResponse.respcode != 14014 || (merchant_type === 'emitra' && transaction_type === 'CW')) {
          const updateAepsResponse = await this.callUpdateAepsTrans(fields, transaction_status, returnData)
          if (updateAepsResponse.status === 400) {
            console.log('---STEP 3 : callUpdateAepsTrans (doAepsBalanceEnquiry) : Failed---')
          }
        } else {
          const updateAepsLog = await transactionCtrl.updateAEPSTransactionLog({}, {
            order_id: returnData.orderid ? returnData.orderid : fields.orderid,
            ma_status: transaction_status,
            transaction_status: transaction_status,
            no_of_attempts: 1,
            bank_response: JSON.stringify(response.data)
          })
          if (updateAepsLog.status === 400) {
            console.log('---STEP 3 : updateAEPSTransactionLog (doAepsMiniStatement) : Failed---')
          }
        }
        return returnData
      } else {
        console.log('---STEP 2 : callGenerateOrder (doAepsMiniStatement) : Failed---')
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsCashWithdrawal', type: 'catcherror', fields: err })
      if (merchant_type == 'emitra') {
        // update request status
        checksumstr = 200 + '' + 1170 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
        const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
        return { status: 200, respcode: 1170, message: errorMsg.responseCode[1170], amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1000 }
      }
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!', action_code: 1001 }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async callUpdateAepsTrans (fields, transaction_status, orderResponse) {
    log.logger({ pagename: require('path').basename(__filename), action: 'callUpdateAepsTrans', type: 'Request', fields: { fields, transaction_status, orderResponse } })
    try {
      // Update AEPS transaction
      transaction_status = transaction_status == 'P' ? 'F' : transaction_status
      const updateTransParams = {
        ma_user_id: fields.ma_user_id,
        orderid: fields.aggregator_order_id,
        aggregator_txn_id: orderResponse.transactionid ? orderResponse.transactionid : 0,
        transaction_status: transaction_status,
        amount: fields.amount,
        rrn: orderResponse.rrn ? orderResponse.rrn : null,
        userid: fields.userid,
        transaction_reason: orderResponse.message,
        bank_balance: orderResponse.bankbalance ? orderResponse.bankbalance : 0.00,
        transaction_type: fields.createTransactionParams.transaction_type,
        transaction_mode: orderResponse.transaction_mode,
        ledger_cron_id: orderResponse.ledger_cron_id
      }
      if (updateTransParams.aggregator_txn_id == '[]' || updateTransParams.aggregator_txn_id == '') {
        updateTransParams.aggregator_txn_id = 0
      }
      console.log('aggregator_txn_id value : ', updateTransParams.aggregator_txn_id)
      const AepstransactionResponse = await transactionCtrl.updateAEPSTransactionLog({}, {
        order_id: updateTransParams.orderid,
        airpay_id: updateTransParams.aggregator_txn_id,
        ma_status: updateTransParams.transaction_status,
        no_of_attempts: 1,
        bank_response: JSON.stringify(orderResponse),
        // Changes for bank down notification
        bank_response_code: orderResponse.responseCode || 123
      })
      console.log('---AepstransactionResponse---', AepstransactionResponse)
      // if (AepstransactionResponse.status == 400) {
      //   return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      // }

      const updateTransResponse = await transactionCtrl.updateAepsTransaction({}, updateTransParams)
      console.log('---updateAepsTransResponse---', updateTransResponse)
      updateTransResponse.respcode = 1000
      updateTransResponse.action_code = 1001
      return updateTransResponse
    } catch (err) {
      return { status: 400, respcode: 1028, message: err.message, action_code: 1001 }
    }
  }

  static async callGenerateOrder (fields) {
    try {
      console.time('TIME_FOR_AEPS_GENERATE_ORDER')
      console.log('Capture Response Decoded : ', JSON.parse(Buffer.from(fields.captureResponse, 'base64').toString('utf-8')))
      console.log('Card number response decoded : ', common_fns.maskValue(JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8')), 'adhaarNumber'))

      // SBM - Additional Parameters
      const sqlUserReq = `SELECT 
          um.state, 
          um.pincode as pincode, 
          um.city, sm.two_digit as state_code, 
          cm.name as district, CONCAT(IFNULL(um.firstname,''),IFNULL(um.lastname,'')) AS merchant_name
        FROM ma_user_master um 
        LEFT JOIN ma_states_master sm 
          ON um.state = sm.id 
        LEFT JOIN ma_cities_master cm 
          ON um.city = cm.id 
        WHERE profileid = ${fields.ma_user_id} 
        AND userid = ${fields.userid} limit 1`

      const dataUserRes = await this.rawQuery(sqlUserReq, fields.connection)

      // Call generate order
      const postParams = {
        merchantTransactionId: fields.aggregator_order_id,
        merchantTranId: fields.aggregator_order_id,
        captureResponse: JSON.parse(Buffer.from(fields.captureResponse, 'base64').toString('utf-8')),
        cardnumberORUID: JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8')),
        languageCode: fields.languageCode,
        latitude: fields.latitude,
        longitude: fields.longitude,
        mobileNumber: fields.mobileNumber,
        paymentType: fields.paymentType,
        requestRemarks: fields.requestRemarks,
        timestamp: fields.timestamp,
        transactionAmount: fields.transactionAmount,
        transactionType: fields.transactionType,
        merchantAggName: dataUserRes[0].merchant_name,
        merchantPin: fields.merchantPin,
        subMerchantId: fields.subMerchantId,
        call_type: fields.call_type,
        email: fields.email,
        mercid: fields.mercid,
        userid: fields.userid,
        privatekey: fields.privatekey,
        bankCode: fields.bank_code ? fields.bank_code : null,
        mer_dom: Buffer.from((Buffer.from(fields.mer_dom, 'base64').toString('utf-8')).replace(/(^"|"$)/g, '')).toString('base64'),
        arpyVer: 3,
        BcDistrict: dataUserRes[0].district ? dataUserRes[0].district : null,
        BcPincode: dataUserRes[0].pincode ? dataUserRes[0].pincode : null,
        BcState: dataUserRes[0].state_code ? dataUserRes[0].state_code : null,
        BcCountry: 'IN',
        MerAuthTxnId: fields.MerAuthTxnId
      }
      console.log('---Generate Order Params---', { ...postParams, cardnumberORUID: common_fns.maskValue(postParams.cardnumberORUID, 'adhaarNumber') })

      const postData = await generateOrderchecksum(this, postParams)
      if (postData.status != 200) {
        return { status: postData.status, respcode: postData.respcode, message: postData.message }
      }

      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'api/generateOrder.php',
        data: postData.data,
        headers: { 'Content-Type': 'application/json' },
        timeout: 2400000 // 240 secs
      })
      console.timeEnd('TIME_FOR_AEPS_GENERATE_ORDER')

      return response
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'callGenerateOrder', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    }
  }

  static async orderConfirmation (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'request', fields: fields })
    try {
      const privatekey = await this.getPrivateKey(fields)
      if (privatekey.status && privatekey.status === 400) {
        return privatekey
      }

      const postParams = {
        privatekey: privatekey,
        mercid: fields.ma_user_id,
        merchant_txnId: fields.aggregator_order_id ? fields.aggregator_order_id : '',
        airpayId: ''
      }

      console.log('---postParams orderconfirmation---', postParams)
      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)

      console.log('---postDataStringify orderconfirmation---', postData)
      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'order/verify.php',
        data: postData,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 60000 // 60 secs
      })

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Order Confirmation Status ' + response.status }
      }
      console.log('---orderConfirmationResponse---', response.data)
      const transactionObj = parser.parse(response.data)

      console.log('---orderConfirmationResponseObj---', transactionObj)
      // console.log(transactionObj)
      let transaction_status = ''
      let transaction_status_text = ''
      if (typeof transactionObj === 'object' && typeof transactionObj.RESPONSE === 'object') {
        let transaction_reason = ''
        const rrn = (transactionObj.RESPONSE.TRANSACTION.RRN) ? transactionObj.RESPONSE.TRANSACTION.RRN : ''
        const aggregator_txn_id = (transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID) ? transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID : 0
        const bankbalance = (transactionObj.RESPONSE.TRANSACTION.CUSTOMERBANKBAL) ? transactionObj.RESPONSE.TRANSACTION.CUSTOMERBANKBAL : 0.0
        // Update aeps transaction according to transaction status return from order confirmation
        if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 200) {
          // Transaction is success
          transaction_status = 'S'
          transaction_reason = 'Success'
          transaction_status_text = 'Success'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 201) {
          // Transaction in pending state
          transaction_status = 'P'
          transaction_reason = 'Initiated'
          transaction_status_text = 'Initiated'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 400) {
          // Transaction failed
          transaction_status = 'F'
          transaction_reason = (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON) ? transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON : 'Failed'
          transaction_status_text = 'Failed'
        }

        if (transaction_status) { // If transaction status in success,fail,pending
          const updateParams = {
            ma_user_id: fields.ma_user_id,
            orderid: fields.aggregator_order_id,
            aggregator_txn_id: aggregator_txn_id,
            transaction_status: transaction_status,
            amount: fields.transaction_amount,
            rrn: rrn,
            userid: fields.userid,
            transaction_reason,
            bankbalance
          }

          log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: updateParams, transaction_status })
          let message = 'Your Previous Txn #txnid# is now #status#'
          message = message.replace('#txnid#', fields.aggregator_order_id)
          message = message.replace('#status#', transaction_status_text)
          return { status: 200, respcode: 1000, message: message, updateParams, transaction_status, aggregator_txn_id, rrn, transaction_reason, transactionObj, bankbalance }
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + transactionObj.RESPONSE.TRANSACTION.MESSAGE }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: { notanobject: transactionObj } })
      return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    }
  }

  static async getPrivateKey (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'request', fields })
    try {
      // Get merchant details
      const userSql = `SELECT 
                        a.apikey,
                        a.username,
                        a.password,
                        b.amount
                      FROM ma_user_master AS a 
                      INNER JOIN ma_transaction_master AS b ON a.userid = b.userid
                      WHERE a.profileid = '${fields.ma_user_id}' 
                      AND a.userid = ${fields.userid}
                      AND b.aggregator_order_id = '${fields.orderid}' LIMIT 1`
      // console.log(userSql)
      const userResponse = await this.rawQuery(userSql, fields.connection)
      if (userResponse.length <= 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'response', fields: userResponse })
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
      }
      const user = {
        secretKey: userResponse[0].apikey,
        userName: userResponse[0].username,
        password: userResponse[0].password
      }

      console.log('user>>', user)
      // Create hash private key for order confirmation api
      const keyData = `${user.secretKey}@${user.userName}:|:${user.password}`
      console.log(keyData)

      const privatekey = common.createHash(keyData)
      log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'response', fields: privatekey })
      return privatekey
    } catch (error) {
      console.log('getPrivateKeyError')
      return false
    }
  }

  static async aepsRequeryCron () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const cronMailData = {
        recordsFetched: 0,
        recordsFetchedArr: [],
        recordsProcessed: 0,
        recordsProcessedArr: [],
        recordsFailed: 0,
        recordsFailedArr: []
      }
      // Get pending transactions with payment_status success
      const sql = `SELECT ma_user_id,userid,aggregator_order_id,amount
                  FROM ma_transaction_master 
                  WHERE transaction_status = 'P'
                  AND transaction_type = '5'
                  AND addedon > NOW() - INTERVAL 24 HOUR
                  AND addedon < NOW() - INTERVAL 15 MINUTE`
      const queryResult = await this.rawQuery(sql, connection)
      console.log('---AEPS REQUERY CRON QUERY--- ', sql)
      console.log('---AEPS REQUERY CRON QUERY RESULT LENGTH--- ', queryResult.length)
      if (queryResult.length > 0) {
        cronMailData.recordsFetched = queryResult.length // Total records fetched
        for (const result of queryResult) {
          const resultDataObj = {}
          resultDataObj.orderid = result.aggregator_order_id
          cronMailData.recordsFetchedArr.push(resultDataObj)
          console.log('--AEPS REQUERY CRON LOOP RESULT--- ', result)
          let transaction_status = 'P'
          console.time('TIME_FOR_AEPS_REQUERY_ORDER_CONFIRMATION')
          const fields = {
            ma_user_id: result.ma_user_id,
            userid: result.userid,
            orderid: result.aggregator_order_id,
            amount: result.amount,
            aggregator_order_id: result.aggregator_order_id,
            transaction_amount: result.amount,
            connection
          }
          const orderConfirmation = await this.orderConfirmation(fields)
          console.timeEnd('TIME_FOR_AEPS_REQUERY_ORDER_CONFIRMATION')
          console.log('---Order Confirmation response---', orderConfirmation)
          const updateOrderParams = {
            transactionid: 0,
            rrn: null,
            message: '',
            bankbalance: 0
          }
          resultDataObj.status = orderConfirmation.status
          if (orderConfirmation.status === 200) {
            cronMailData.recordsProcessed++

            transaction_status = orderConfirmation.transaction_status
            updateOrderParams.transactionid = orderConfirmation.aggregator_txn_id
            updateOrderParams.rrn = orderConfirmation.rrn
            updateOrderParams.message = orderConfirmation.transaction_reason
            updateOrderParams.bankbalance = orderConfirmation.bankbalance

            // Update the AEPS transaction
            console.time('TIME_FOR_UPDATE_AEPS_TRANSACTION')
            const updateAepsResponse = await this.callUpdateAepsTrans(fields, transaction_status, updateOrderParams)
            console.timeEnd('TIME_FOR_UPDATE_AEPS_TRANSACTION')
            console.log('---Update Aeps Transaction response---', updateAepsResponse)
            // Updating Mail data
            if (updateAepsResponse.status === 200) {
              resultDataObj.transaction_status = orderConfirmation.transaction_status
              resultDataObj.message = orderConfirmation.transaction_reason
            } else {
              resultDataObj.transaction_status = 'P'
              resultDataObj.message = updateAepsResponse.message
            }
            cronMailData.recordsProcessedArr.push(resultDataObj)
          } else if (orderConfirmation.status === 400) {
            cronMailData.recordsFailed++

            resultDataObj.transaction_status = transaction_status
            resultDataObj.message = orderConfirmation.message
            cronMailData.recordsFailedArr.push(resultDataObj)
          }
        }
        const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
        const mailerData = {
          template: 'aepsRequery.ejs',
          content: cronMailData,
          from: '"Cron Mail" <<EMAIL>>',
          to: util.retailDevEmails,
          subject: 'AEPS Requery Cron Summary - [' + env + ']'
        }
        const mailResponse = await mailer(mailerData)
        log.logger({ pagename: require('path').basename(__filename), action: 'aepsRequeryCron', type: 'Mail response', fields: mailResponse })
        return { status: 200, respcode: 100, message: 'Cron executed successfully.' }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'aepsRequeryCron', type: 'query response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : No records found' }
      }
    } catch (err) {
      const transporter = nodemailer.createTransport(mailconfig)
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      const info = await transporter.sendMail({
        from: '"Cron Mail" <<EMAIL>>', // sender address
        to: util.retailDevEmails, // list of receivers
        subject: 'AEPS Requery Cron Summary - [' + env + '] ', // Subject line
        html: `<p> ${err} </p>` // html body
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'aepsRequeryCron', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async inactivateEmitraRequest (token, ssoid, connection) {
    try {
      // update redis
      ssoid = ssoid.toLowerCase()
      console.log('client******', client)
      console.log('ssoid*****', ssoid)
      // if (client.connected) {
      console.log('connected true')
      client.del(ssoid)
      //   client.exists(ssoid, function(err, data) {
      //     if (data === 1) {
      //       client.del(ssoid)
      //       console.log("Deleted Successfully!")
      //     } else {
      //       console.log("Cannot delete")
      //     }
      //  })
      // }
      const updsql = `UPDATE  ma_integration_request SET request_status = 'inactive' WHERE login_token = '${token}'`
      const respupd = await this.rawQuery(updsql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'inactivateEmitraRequest', type: 'update-query', fields: updsql })
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'inactivateEmitraRequest', type: 'catcherror', fields: error })
      return false
    }
  }

  /**
   * Get all partner banks from database
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} fields
   */
  static async getAepsPartnerBankList (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAepsPartnerBankList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let bank_code = ''
      let table_name = ''
      if (((!fields.transaction_type) || fields.transaction_type == '' || fields.transaction_type == ' ' || fields.transaction_type == null) || fields.transaction_type == 'aeps') {
        fields.transaction_type = 'aeps'
        bank_code = ''
        table_name = 'ma_aeps_bank_master'
      } else {
        bank_code = ''
        table_name = 'ma_adpay_bank_master'
        fields.transaction_type = 'adpay'
      }

      const sqlAEPSBankList = `SELECT bank_name, bank_code, priority FROM ${table_name} WHERE active_status = "A" ${bank_code} ORDER BY priority ASC`
      log.logger({ pagename: require('path').basename(__filename), action: 'getAepsPartnerBankList', type: 'sqlAEPSBankList', fields: sqlAEPSBankList })
      const _bankList = await this.rawQuery(sqlAEPSBankList, connection)
      if (_bankList.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bankList: _bankList }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAepsPartnerBankList', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1028, message: err.message }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * Do AEPS Transaction
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} fields
   */
  static async doAepsTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'request', fields: _ })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Changes for bank down notification
      if (validator.definedVal(fields.cardnumberORUID)) {
        var cardData = JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8'))
        fields.bank_id = cardData.nationalBankIdentificationNumber
      }
      /*
      const getConsentDetailsSql = `Select * from ma_merchant_consent where ma_user_id = ${fields.ma_user_id} and userid = ${fields.userid} and transaction_type = 'aeps' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getConsentDetailsSql', fields: getConsentDetailsSql })
      const getConsentDetailsSqlResp = await this.rawQuery(getConsentDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getConsentDetailsSqlResp', fields: getConsentDetailsSqlResp })
      if (getConsentDetailsSqlResp.length > 0) {
        if (getConsentDetailsSqlResp[0].consent_flag != 'Y') {
          return { status: 400, respcode: 1001, message: 'Please Accept the Consent to proceed AEPS Transaction', action_code: 1001 }
        }
      }
      else {
        return { status: 400, respcode: 1001, message: 'Please Accept the Consent to proceed AEPS Transaction', action_code: 1001 }
      }
      */
      // const transaction_status = 'P' // Set Initial transaction status as Pending
      const aepsTransactionType = fields.transactionType
      fields.connection = connection
      fields.mercid = fields.ma_user_id
      fields.orderid = fields.aggregator_order_id
      var response = ''

      var ssotoken = ''
      var txn_ref_number = ''
      var checksumstr = ''

      // Validate bankCode - Comment Old Code - as we will retrieve bank code from aeps auth table
      /* if ((!fields.bank_code) || fields.bank_code == '' || fields.bank_code == ' ') {
        const sqlIntReq = 'SELECT bank_code FROM ma_aeps_bank_master WHERE active_status = "A" ORDER BY priority ASC LIMIT 1'
       const dataIntReq = await this.rawQuery(sqlIntReq, connection)
       fields.bank_code = dataIntReq[0].bank_code
     log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request', fields: fields })
      } */
      connection.release()

      // check hard code lat and long
      if (fields.latitude == '19.0760' && fields.longitude == '72.8777') {
        const getDynamicLatLong = await this.getLatLongFromUserDetails({ ma_user_id: fields.ma_user_id })
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'response_getDynamicLatLong', fields: getDynamicLatLong })
        if (getDynamicLatLong.status == 200) {
          fields.latitude = getDynamicLatLong.lat_val
          fields.longitude = getDynamicLatLong.long_val
        }
      }

      // Create Transaction params

      console.log('source>', fields.user_agent)
      const str = fields.user_agent.substring(0, 6)
      if (str != 'okhttp') {
        const data = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          login_ip: fields.login_ip
        }
        const getSuccessLatLongData = await this.getSuccessGeoLocationData(data)
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getSuccessLatLongData', fields: getSuccessLatLongData })
        if (getSuccessLatLongData.status == 400) {
          return getSuccessLatLongData
        }
        if (getSuccessLatLongData.status == 200 && getSuccessLatLongData.data) {
          fields.latitude = getSuccessLatLongData.data.latitude
          fields.longitude = getSuccessLatLongData.data.longitude
        }
      }
      console.log('fields.latitude', fields.latitude)
      console.log('fields.longitude', fields.longitude)

      fields.createTransactionParams = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.aggregator_order_id,
        amount: ['CW', 'AP'].includes(aepsTransactionType) ? fields.amount : 0,
        transaction_status: 'P',
        remarks: fields.remarks ? fields.remarks : '',
        mobile_number: fields.mobile_number,
        customer_name: fields.customer_name,
        customer_aadhaar: fields.customer_aadhaar,
        bank_name: (fields.bank_name) ? fields.bank_name : '',
        connection: fields.connection,
        bank_code: fields.bank_code,
        transactionType: fields.transactionType,
        // Changes for bank down notification
        bank_id: fields.bank_id,
        additionalData: { ..._, latitude: fields.latitude, longitude: fields.longitude }
      }

      // Emitra changes for BE and MS
      const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid}`
      const integratedMer = await this.rawQuery(userSQL, fields.connection)
      if (integratedMer.length > 0) {
        /* check if the aeps emitra is serviceable for current timestamp */
        const isAepsEmitraServiceAbleResult = await this.isAepsEmitraServiceAble({
          ma_user_id: fields.ma_user_id,
          connection
        })
        if (isAepsEmitraServiceAbleResult.status != 200) {
          return isAepsEmitraServiceAbleResult
        }

        const sqlIntReq = `SELECT * FROM ma_integration_request WHERE retailer_user_id='${integratedMer[0].aggregator_user_id}' and request_status = 'active' ORDER BY ma_integration_request_id DESC LIMIT 1`
        const dataIntReq = await this.rawQuery(sqlIntReq, fields.connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'ma_integration_request', type: 'response', fields: dataIntReq })
        if (dataIntReq.length === 0) {
          const sqlLoginReq = `SELECT * FROM ma_integration_request WHERE login_token = '${fields.logintoken}' LIMIT 1`
          const dataLoginReq = await this.rawQuery(sqlLoginReq, fields.connection)
          txn_ref_number = dataLoginReq[0].transaction_ref_number
          ssotoken = dataLoginReq[0].retailer_token
          checksumstr = 400 + '' + 1170 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
          const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          return { status: 400, respcode: 1170, message: errorMsg.responseCode[1170], amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1001 }
        }

        fields.merchant_type = 'emitra'
        fields.ssotoken = dataIntReq[0].retailer_token
        fields.txn_ref_number = fields.aggregator_order_id
        fields.login_token = dataIntReq[0].login_token
        fields.ssoid = integratedMer[0].aggregator_user_id
      }
      const aepsTwoFactorAuthCheckVal = await common.getSystemCodes(this, util.aepsTwoFactorAuthCheck, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'aepsTwoFactorAuthCheckVal', fields: aepsTwoFactorAuthCheckVal })
      if (aepsTwoFactorAuthCheckVal == 'Y') {
        // New code -- Start --
        // get bank id from aeps auth table - as he is registered with Bank for transaction
        let bankCodeVal = ''
        const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' order by ma_aeps_merchant_auth_token_id DESC limit 1 `
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getMerchantQuery', fields: getMerchantQuery })
        const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getMerchantResult', fields: getMerchantResult })
        if (getMerchantResult.length > 0) {
          const expiry_time = getMerchantResult[0].expiry_time
          let received_bank_id = getMerchantResult[0].received_bank_id ? getMerchantResult[0].received_bank_id : ''
          // check whether current timestamp is less then expiry timestamp saved in the table
          const sqlCurrentTime = `SELECT IF(CURRENT_TIMESTAMP() < '${expiry_time}','Y','N') as expiry_flag`
          const resultTime = await this.rawQuery(sqlCurrentTime, connection)
          console.log('resultTime>>', resultTime)
          if (resultTime[0].expiry_flag == 'Y') {
            if (received_bank_id == '' || received_bank_id == null) {
              const sqlIntReq = 'SELECT bank_code FROM ma_aeps_bank_master WHERE active_status = "A" ORDER BY priority ASC LIMIT 1'
              const dataIntReq = await this.rawQuery(sqlIntReq, connection)
              received_bank_id = dataIntReq[0].bank_code
              log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request', fields: fields })
            }
            if ((received_bank_id == '' || received_bank_id == null) && fields.call_type == 'adpay') {
              const sqlIntReq = 'SELECT bank_code FROM ma_adpay_bank_master WHERE active_status = "A" ORDER BY priority ASC LIMIT 1'
              const dataIntReq = await this.rawQuery(sqlIntReq, connection)
              received_bank_id = dataIntReq[0].bank_code
              log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request', fields: fields })
            }

            if (fields.call_type == 'aeps') {
              if ((!fields.bank_code) || fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == '0') {
                bankCodeVal = received_bank_id
                console.log('Merchant authorized')
              }
              // if bank code is not empty, it means merchant is trying to retry the transaction.
              // Check whether the bank code is registerd else call 2FA API
              if (fields.bank_code != '' && fields.bank_code != '0' && fields.bank_code != ' ' && fields.bank_code != undefined) {
                if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' || fields.bank_code != ' ' || fields.bank_code != '0')) { // if selected bank code & received bank code is same then we send the received bank code for transaction
                  const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and received_bank_id = '${fields.bank_code}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
                  log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                  const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
                  log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank-', fields: getMerchantResult })
                  if (getMerchantResult.length > 0) {
                    bankCodeVal = fields.bank_code
                    // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code }
                  } else {
                    const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
                      bankCode: fields.bank_code,
                      connection
                    })
                    let cwAuthRequired = ''
                    if (iscw2FAAuthRequiredResult.status == 200) {
                      cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                      log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                      console.log('cwAuthRequired', cwAuthRequired)
                      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: cwAuthRequired }
                    } else {
                      console.log('iscw2FAAuthRequiredResult ', iscw2FAAuthRequiredResult.status)
                      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: 'N/A' }
                    }
                  }
                }
              }
            } else if (fields.call_type == 'adpay') {
              // get Fino bank value from constant file
              const fino_bank_value = FINO_BANK_ID
              const credopay_bank_value = CREDOPAY_BANK_ID
              // console.log('fino bank value : ', fino_bank_value)
              console.log('Received bank id value : ', received_bank_id)
              console.log('get merchant result bank id value : ', getMerchantResult[0].received_bank_id)
              console.log('bank code val ', fields.bank_code)

              if ((fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == undefined || fields.bank_code == '0') && (fino_bank_value == getMerchantResult[0].received_bank_id || credopay_bank_value == getMerchantResult[0].received_bank_id)) {
                // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code } // && getMerchantResult[0].sent_bank_id != getMerchantResult[0].received_bank_id, check below condition, if sent bank id is not null and bank code is not equal to received bank id
                bankCodeVal = received_bank_id
              } else if ((fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == undefined || fields.bank_code == '0') && (fino_bank_value != getMerchantResult[0].received_bank_id || credopay_bank_value != getMerchantResult[0].received_bank_id)) {
                // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code } // && getMerchantResult[0].sent_bank_id != getMerchantResult[0].received_bank_id, check below condition, if sent bank id is not null and bank code is not equal to received bank id
                // const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and received_bank_id = '${fino_bank_value}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
                const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and received_bank_id = '${fields.bank_code}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
                log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
                log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank--', fields: getMerchantResult })
                if (getMerchantResult.length > 0) {
                  bankCodeVal = fields.bank_code
                  // bankCodeVal = FINO_BANK_ID
                  // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code }
                } else {
                  // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fino_bank_value }
                  return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code }
                }
              } else if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' && fields.bank_code != ' ' && fields.bank_code != '0')) {
                // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code }
                const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and received_bank_id = '${fields.bank_code}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
                log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
                log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank--', fields: getMerchantResult })
                if (getMerchantResult.length > 0) {
                  bankCodeVal = fields.bank_code
                  // bankCodeVal = FINO_BANK_ID
                  // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code }
                } else {
                  return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code }
                }
              }
            }
          } else {
            return { status: 200, message: 'Your Authorization has been expired.', respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', cw_auth_required: 'N/A' }
          }
        } else {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Merchant Not Registered, Please Register for 2FA', authentication_required: 'Y', userRegistered: 'N', cw_auth_required: 'N/A' }
        }
        fields.bank_code = bankCodeVal
      } else if ((!fields.bank_code || fields.bank_code == '' || fields.bank_code == ' ') && fields.call_type == 'adpay') {
        const sqlIntReq = 'SELECT bank_code FROM ma_adpay_bank_master WHERE active_status = "A" ORDER BY priority ASC LIMIT 1'
        console.log(' <<<< adpay >>>>')
        const dataIntReq = await this.rawQuery(sqlIntReq, connection)
        fields.bank_code = dataIntReq[0].bank_code
        console.log('new bank code', fields.bank_code)
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request-Adpay', fields: fields })
      } else {
        if ((!fields.bank_code) || fields.bank_code == '' || fields.bank_code == ' ') {
          const sqlIntReq = 'SELECT bank_code FROM ma_aeps_bank_master WHERE active_status = "A" ORDER BY priority ASC LIMIT 1'
          const dataIntReq = await this.rawQuery(sqlIntReq, connection)
          fields.bank_code = dataIntReq[0].bank_code
          log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request', fields: fields })
        }
      }

      console.log('call type : ', fields.call_type)
      console.log('bank code : ', fields.bank_code)
      fields.MerAuthTxnId = ''
      let cwAuthRequiredFlag = ''
      const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
        bankCode: fields.bank_code,
        connection
      })
      if (iscw2FAAuthRequiredResult.status == 200) {
        cwAuthRequiredFlag = iscw2FAAuthRequiredResult.cw_auth_required
        log.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
        console.log('cwAuthRequired', cwAuthRequiredFlag)
      }

      // added for Cash withdrawal AEPS transaction - Fino bank
      // for cash withdrawal, take bank code in new aeps cw transaction logs table
      if (aepsTransactionType == 'CW') {
        if (cwAuthRequiredFlag == 'Y' || cwAuthRequiredFlag == 'N/A' || cwAuthRequiredFlag == '') {
          console.log('Get bank code from aeps cw transaction table')
          const getMerchantQuery = `select ma_aeps_cw_transaction_logs_id, merchant_txn_id, sent_bank_id, received_bank_id from ma_aeps_cw_transaction_logs mamat where two_factor_auth_status ='S' and ma_user_id = '${fields.ma_user_id}' and order_id = '${fields.aggregator_order_id}'  ORDER BY ma_aeps_cw_transaction_logs_id DESC limit 1`
          log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantResult-CW_new_log_qry', type: 'getMerchantQuery', fields: getMerchantQuery })
          const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getMerchantResult-CW_new_log_table', fields: getMerchantResult })

          const received_bank_id = 0
          const orderTxnId = fields.aggregator_order_id
          if (getMerchantResult.length > 0) {
            fields.bank_code = getMerchantResult[0].received_bank_id
            fields.MerAuthTxnId = getMerchantResult[0].merchant_txn_id
            console.log('bank code for CW transaction : ', fields.bank_code)
          } else if (orderTxnId.indexOf('MAWEB') > -1) {
            return { status: 400, respcode: 1001, message: "Important! Please note merchant's bio-metric authentication is mandatory for Cash Withdrawal services. You may try deleting browsing history and cookies to ensure uninterrupted service.", action_code: 1001 }
          } else if (orderTxnId.indexOf('MAAPP') > -1) {
            return { status: 400, respcode: 1001, message: 'Alert! immediately update your app version to continue providing the services.', action_code: 1001 }
          } else {
            return { status: 400, respcode: 1001, message: "Important! Please note merchant's bio-metric authentication is mandatory for Cash Withdrawal services.", action_code: 1001 }
          }
        }
      }

      // check merchant location within assigned kilometers
      const aepsMerchantLocationValidationsVal = await common.getSystemCodes(this, util.aepsMerchantLocationValidation, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'aepsMerchantLocationValidationsVal', fields: aepsMerchantLocationValidationsVal })
      let radius_distance = 0
      if (aepsMerchantLocationValidationsVal == 'Y') { // check status flag 'Y' then continue, else skip validations
        // check profile id exists or not
        if (fields.amount == undefined || fields.amount == '') {
          fields.amount = 0
        }
        const skipSavingLocation = 'N'
        const checkMerchantRadiusResult = await this.checkMerchantRadius(fields, skipSavingLocation)
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'checkMerchantRadiusResult', fields: checkMerchantRadiusResult })

        if (checkMerchantRadiusResult.status == 400) {
          return checkMerchantRadiusResult
        }
        radius_distance = checkMerchantRadiusResult.radiusDistance
      } else {
        const insertUserQuery = `INSERT INTO ma_geo_location (ma_user_id,user_id,order_id,request_type,amount,latitude,longitude,login_ip) VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','aeps','${fields.amount}','${fields.latitude}','${fields.longitude}','${fields.login_ip}')`
        log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: insertUserQuery })
        const params = await this.rawQuery(insertUserQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: params })
      }
      console.log('Bank code : ', fields.bank_code)
      if (fields.bank_code == CREDOPAY_BANK_ID && fields.call_type == 'adpay') {
        // check record exists
        const kycStatusQuery = `SELECT approval_status,rekyc_id FROM ma_credopay_merchant_onboarding WHERE ma_user_id = '${fields.ma_user_id}' AND user_id = '${fields.userid}' ORDER BY addedon DESC LIMIT 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'kycStatusApproval', type: 'check record exists or not', fields: kycStatusQuery })
        const kycStatusResult = await this.rawQuery(kycStatusQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'kycStatusApproval', type: 'check record exists or not', fields: kycStatusResult })
        if (kycStatusResult.length > 0 && kycStatusResult[0].approval_status == 'S') {
          fields.bank_code = CREDOPAY_BANK_ID
        } else {
          // route to next priority bank for adpay
          fields.bank_code = FINO_BANK_ID
        }
      } else if (fields.bank_code == CREDOPAY_BANK_ID && fields.call_type == 'aeps') {
        // route to next priority bank for aeps
        const kycStatusQuery = `SELECT approval_status,rekyc_id FROM ma_credopay_merchant_onboarding WHERE ma_user_id = '${fields.ma_user_id}' AND user_id = '${fields.userid}' ORDER BY addedon DESC LIMIT 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'kycStatusApproval', type: 'check record exists or not', fields: kycStatusQuery })
        const kycStatusResult = await this.rawQuery(kycStatusQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'kycStatusApproval', type: 'check record exists or not', fields: kycStatusResult })
        if (kycStatusResult.length > 0 && kycStatusResult[0].approval_status == 'S') {
          fields.bank_code = CREDOPAY_BANK_ID
        } else {
          // route to next priority bank for aeps
          const sqlIntReq = 'SELECT bank_code FROM ma_aeps_bank_master WHERE active_status = \'A\' ORDER BY priority ASC LIMIT 1 OFFSET 1'
          log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'sqlIntReq', fields: sqlIntReq })
          const dataIntReq = await this.rawQuery(sqlIntReq, connection)
          fields.bank_code = dataIntReq[0].bank_code
          log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request', fields: fields })
        }
      }
      console.log('Final Bank code : ', fields.bank_code)
      fields.createTransactionParams.bank_code = fields.bank_code
      switch (aepsTransactionType) {
        case 'CW':
          fields.createTransactionParams.transaction_type = '5'
          fields.createTransactionParams.transaction_mode = '1'
          console.log('here')
          response = this.doAepsCashWithdrawal(_, fields)
          console.log('here too')
          break
        case 'BE':
          fields.createTransactionParams.transaction_type = '42'
          fields.createTransactionParams.transaction_mode = '3'
          response = this.doAepsBalanceEnquiry(_, fields)
          break
        case 'MS':
          fields.createTransactionParams.transaction_type = '40'
          fields.createTransactionParams.transaction_mode = '2'
          response = this.doAepsMiniStatement(_, fields)
          break
        // AADHAAR PAY for Collect Money
        case 'AP':
          fields.createTransactionParams.transaction_type = '10'
          fields.createTransactionParams.surcharge_amount = fields.surcharge_amount
          fields.createTransactionParams.customer_email = fields.email
          console.log('fields bank code adpay', fields.bank_code)
          response = collectMoneyController.createAdhaarPayOrder(_, fields)
          break
        default:
          response = { status: 400, respcode: 14013, message: errorMsg.responseCode[14013], action_code: 1001 }
          break
      }

      // save device information

      const captureResponseDecodeResult = Buffer.from(fields.captureResponse, 'base64').toString()
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'captureResponseDecodeResult', fields: captureResponseDecodeResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'captureResponseDecodeResult', fields: captureResponseDecodeResult })
      const jsonObjectResult = JSON.parse(captureResponseDecodeResult)

      let device_serial_no = ''
      let device_name = ''
      let device_model_no = ''

      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'jsonObjectResult', fields: jsonObjectResult })
      console.log('Serial no ', jsonObjectResult.srno)
      console.log('Device name ', jsonObjectResult.dpID)
      console.log('Device model no ', jsonObjectResult.mi)
      console.log('fauth value', jsonObjectResult.fauth)

      if (jsonObjectResult.fauth == '4') {
        const deviceSlNoQuery = `select imei from ma_login_details where userid = '${fields.userid}' and deviceos = 'android' order by apploginid desc limit 1;`
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepstransaction', type: 'deviceSlNoQuery', fields: deviceSlNoQuery })
        const deviceSlNoRes = await this.rawQuery(deviceSlNoQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepstransaction', type: 'deviceSlNoRes', fields: deviceSlNoRes })
        if (deviceSlNoRes.length > 0) {
          device_serial_no = deviceSlNoRes[0].imei
        } else {
          return { status: 400, respcode: 1028, message: 'Unable to find serial number of the face authentication device. Kindly upgrade your Vyaapaar app to the latest version.', action_code: 1001 }
        }
      } else {
        device_serial_no = jsonObjectResult.srno ? jsonObjectResult.srno : ''
      }

      // get bank name
      const bank_name = await this.getBankName(fields.bank_code, connection)

      if (typeof jsonObjectResult === 'object' && jsonObjectResult != '') {
        // device_serial_no = jsonObjectResult.srno ? jsonObjectResult.srno : ''
        device_name = jsonObjectResult.dpID ? jsonObjectResult.dpID : ''
        device_model_no = jsonObjectResult.mi ? jsonObjectResult.mi : ''
      }

      // save Merchant Geo Location
      console.log('Radius Distance : ', radius_distance)
      // If order is not present in ma_transaction_master table, then add entry in geo_location table
      const sqlIntReq = `SELECT ma_geo_location_id FROM ma_geo_location WHERE order_id = '${fields.aggregator_order_id}' LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'insertUserQuery2', fields: sqlIntReq })
      const dataIntReq = await this.rawQuery(sqlIntReq, connection)
      if (dataIntReq.length <= 0) {
        const insertUserQuery2 = `INSERT INTO ma_geo_location (ma_user_id,user_id,order_id,request_type,amount,latitude,longitude,radius,login_ip,device_name,device_model_no,device_serial_no,bank_name) VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','aeps','${fields.amount}','${fields.latitude}','${fields.longitude}','${radius_distance}','${fields.login_ip}','${device_name}','${device_model_no}','${device_serial_no}','${bank_name}')`
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'insertUserQuery2', fields: insertUserQuery2 })
        const params2 = await this.rawQuery(insertUserQuery2, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'insertUserQuery', fields: params2 })
      }

      return response
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      // connection.release()
    }
  }

  static async availableAuthenticationDevices (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let bank_name
      if (fields.source == 'AEPS') {
        bank_name = 'ma_aeps_bank_master'
      }
      if (fields.source == 'ADPAY' || fields.source == 'AADHAAR_PAY') {
        bank_name = 'ma_adpay_bank_master'
      }
      const deviceList = []
      if (fields.bankcode == '' || !fields.bankcode || fields.bankcode == null) {
        // take 2fa bank id
        let bankCodeVal
        const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'getMerchantQuery', fields: getMerchantQuery })
        const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'getMerchantResult', fields: getMerchantResult })
        if (getMerchantResult.length > 0) {
          bankCodeVal = getMerchantResult[0].received_bank_id
          const getAuthenticationDataQuery = `select biometric_authentication,iris_authentication,face_authentication,biometric_authentication_details,iris_authentication_details,face_authentication_details from ${bank_name} where bank_code = '${bankCodeVal}' limit 1`
          log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'getAuthenticationDataQuery', fields: getAuthenticationDataQuery })
          const getAuthenticationDataResult = await this.rawQuery(getAuthenticationDataQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'getAuthenticationDataResult', fields: getAuthenticationDataResult })
          if (getAuthenticationDataResult.length > 0) {
            let biometricDetails
            if (getAuthenticationDataResult[0].biometric_authentication_details) {
              biometricDetails = JSON.parse(getAuthenticationDataResult[0].biometric_authentication_details)
            }
            deviceList.push({
              bt_description: biometricDetails && biometricDetails.bt_description ? biometricDetails.bt_description : 'Fingerprint minutiae',
              bt_type: biometricDetails && biometricDetails.bt_type ? biometricDetails.bt_type : 'FMR',
              bt_value: biometricDetails && biometricDetails.bt_value ? biometricDetails.bt_value : '2',
              bt_icon: biometricDetails && biometricDetails.bt_icon ? biometricDetails.bt_icon : util.aepsAuthenticationBiometricUrl
            })
            if (getAuthenticationDataResult[0].iris_authentication === 'Y') {
              let irisDetails
              if (getAuthenticationDataResult[0].iris_authentication_details) {
                irisDetails = JSON.parse(getAuthenticationDataResult[0].iris_authentication_details)
              }
              deviceList.push({
                bt_description: irisDetails && irisDetails.bt_description ? irisDetails.bt_description : 'IRIS biometric',
                bt_type: irisDetails && irisDetails.bt_type ? irisDetails.bt_type : 'IIR',
                bt_value: irisDetails && irisDetails.bt_value ? irisDetails.bt_value : '1',
                bt_icon: irisDetails && irisDetails.bt_icon ? irisDetails.bt_icon : util.aepsAuthenticationIrisUrl
              })
            }
            if (getAuthenticationDataResult[0].face_authentication === 'Y') {
              let faceDetails
              if (getAuthenticationDataResult[0].face_authentication_details) {
                faceDetails = JSON.parse(getAuthenticationDataResult[0].face_authentication_details)
              }
              deviceList.push({
                bt_description: faceDetails && faceDetails.bt_description ? faceDetails.bt_description : 'Face Biometric',
                bt_type: faceDetails && faceDetails.bt_type ? faceDetails.bt_type : 'FIR,FMR',
                bt_value: faceDetails && faceDetails.bt_value ? faceDetails.bt_value : '4',
                bt_icon: faceDetails && faceDetails.bt_icon ? faceDetails.bt_icon : util.aepsAuthenticationFaceUrl
              })
            }
          } else {
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001, devices: [] }
          }
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, devices: deviceList }
      } else {
        // if bank code is present
        const getAuthenticationDataQuery = `select biometric_authentication,iris_authentication,face_authentication,biometric_authentication_details,iris_authentication_details,face_authentication_details from ${bank_name} where bank_code = '${fields.bankcode}' limit 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'getAuthenticationDataQuery', fields: getAuthenticationDataQuery })
        const getAuthenticationDataResult = await this.rawQuery(getAuthenticationDataQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'getAuthenticationDataResult', fields: getAuthenticationDataResult })
        if (getAuthenticationDataResult.length > 0) {
          let biometricDetails
          if (getAuthenticationDataResult[0].biometric_authentication_details) {
            biometricDetails = JSON.parse(getAuthenticationDataResult[0].biometric_authentication_details)
          }
          deviceList.push({
            bt_description: biometricDetails && biometricDetails.bt_description ? biometricDetails.bt_description : 'Fingerprint minutiae',
            bt_type: biometricDetails && biometricDetails.bt_type ? biometricDetails.bt_type : 'FMR',
            bt_value: biometricDetails && biometricDetails.bt_value ? biometricDetails.bt_value : '2',
            bt_icon: biometricDetails && biometricDetails.bt_icon ? biometricDetails.bt_icon : util.aepsAuthenticationBiometricUrl
          })
          if (getAuthenticationDataResult[0].iris_authentication === 'Y') {
            let irisDetails
            if (getAuthenticationDataResult[0].iris_authentication_details) {
              irisDetails = JSON.parse(getAuthenticationDataResult[0].iris_authentication_details)
            }
            deviceList.push({
              bt_description: irisDetails && irisDetails.bt_description ? irisDetails.bt_description : 'IRIS biometric',
              bt_type: irisDetails && irisDetails.bt_type ? irisDetails.bt_type : 'IIR',
              bt_value: irisDetails && irisDetails.bt_value ? irisDetails.bt_value : '1',
              bt_icon: irisDetails && irisDetails.bt_icon ? irisDetails.bt_icon : util.aepsAuthenticationIrisUrl
            })
          }
          if (getAuthenticationDataResult[0].face_authentication === 'Y') {
            let faceDetails
            if (getAuthenticationDataResult[0].face_authentication_details) {
              faceDetails = JSON.parse(getAuthenticationDataResult[0].face_authentication_details)
            }
            deviceList.push({
              bt_description: faceDetails && faceDetails.bt_description ? faceDetails.bt_description : 'Face Biometric',
              bt_type: faceDetails && faceDetails.bt_type ? faceDetails.bt_type : 'FIR,FMR',
              bt_value: faceDetails && faceDetails.bt_value ? faceDetails.bt_value : '4',
              bt_icon: faceDetails && faceDetails.bt_icon ? faceDetails.bt_icon : util.aepsAuthenticationFaceUrl
            })
          }
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, devices: deviceList }
        } else {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001, devices: [] }
        }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'availableAuthenticationDevices', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * Do AEPS Balance Enquiry Transaction
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} fields
   */
  static async doAepsBalanceEnquiry (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doAepsBalanceEnquiry', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.connection = connection
      fields.amount = fields.createTransactionParams.amount = 0
      const transaction_mode = fields.createTransactionParams.transaction_mode
      let beResponse = null
      let transaction_status = 'P'
      const transaction_type = fields.createTransactionParams.transactionType

      var merchant_type = fields.merchant_type
      var ssotoken = fields.ssotoken
      var txn_ref_number = fields.txn_ref_number
      var login_token = fields.login_token
      var ssoid = fields.ssoid
      var checksumstr = fields.checksumstr

      delete fields.merchant_type
      delete fields.ssotoken
      delete fields.txn_ref_number
      delete fields.login_token
      delete fields.ssoid
      delete fields.checksumstr

      console.log('---STEP 1 : Initiate Transaction (doAepsBalanceEnquiry) : Start---')
      const createTransaResponse = await transactionCtrl.initiateTransaction(_, { ...fields.createTransactionParams, merchant_type })
      console.log('---STEP 1 : Initiate Transaction (doAepsBalanceEnquiry) : End---', createTransaResponse)
      if (createTransaResponse.status === 400 || createTransaResponse.status === 401) {
        if (merchant_type == 'emitra' && createTransaResponse.respcode != 14014) {
          checksumstr = 400 + '' + 1001 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
          const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          return { status: 400, respcode: 1001, message: createTransaResponse.message, amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1001 }
        } else {
          if (createTransaResponse.respcode != 14014) {
            createTransaResponse.action_code = 1001
            return createTransaResponse // Return if error
          }
        }
      }

      // add entry in cron table
      console.log('--- insert transaction details in ma_aeps_aadhaar_ledger_log able (doAepsBalanceEnquiry) : Start---')
      const saveAdpayOrderVerifyData = `Insert into ma_aeps_aadhaar_ledger_log
       (ma_user_id, userid, order_id, amount, transaction_type)
       VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','${fields.amount}','42')`
      const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsBalanceEnquiry', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })
      const ledgerCronId = saveAdpayOrderVerifyResult.insertId
      console.log('--- End---')

      console.log('---STEP 1 : callGenerateOrder (doAepsBalanceEnquiry) : Start---')
      const response = await this.callGenerateOrder(fields)
      console.log('---Generate order response---', response)
      if (response.status === 200) {
        console.log('---STEP 1 : callGenerateOrder (doAepsBalanceEnquiry) : Success---')
        if (response.data != 'undefined' && response.data != null) {
          if (response.data.status != 'undefined' && response.data.status != null) {
            if (response.data.status === 200) {
              transaction_status = 'S'
              beResponse = {
                status: 200,
                respcode: 1000,
                message: errorMsg.responseCode[1000],
                data: Buffer.from(JSON.stringify(response.data.data)).toString('base64'),
                action_code: 1000,
                ledger_cron_id: ledgerCronId
              }
            } else {
              transaction_status = 'F'
              let error_message = errorMsg.responseCode[1001]

              if (response.data.message) {
                error_message = response.data.message
              } else if (response.data.Message) {
                error_message = response.data.Message
              } else if (response.data.data) {
                if (response.data.data.message) {
                  error_message = response.data.data.message
                }
              }

              beResponse = {
                status: response.data.status,
                respcode: 1001,
                message: error_message,
                ledger_cron_id: ledgerCronId
              }
            }

            if (createTransaResponse.respcode != 14014 || (merchant_type === 'emitra' && transaction_type === 'CW')) {
              const updateAepsResponse = await this.callUpdateAepsTrans(fields, transaction_status, { transaction_mode, ...response.data, ...beResponse })
              if (updateAepsResponse.status === 400) {
                console.log('---STEP 3 : callUpdateAepsTrans (doAepsBalanceEnquiry) : Failed---')
              }
            } else {
              const updateAepsLog = await transactionCtrl.updateAEPSTransactionLog({}, {
                order_id: fields.aggregator_order_id,
                ma_status: transaction_status,
                transaction_status: transaction_status,
                no_of_attempts: 1,
                bank_response: JSON.stringify(response.data)
              })
              if (updateAepsLog.status === 400) {
                console.log('---STEP 3 : updateAEPSTransactionLog (doAepsMiniStatement) : Failed---')
              }
            }
          }
        }
      } else {
        console.log('---STEP 1 : callGenerateOrder (doAepsBalanceEnquiry) : Failed---')
        beResponse = { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
      }
      return beResponse
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsCashWithdrawal', type: 'catcherror', fields: err })
      if (merchant_type == 'emitra') {
        // update request status
        checksumstr = 200 + '' + 1170 + '' + fields.amount.toFixed(2) + '' + txn_ref_number + '' + fields.aggregator_order_id + util.emitraChecksumKey
        const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
        return { status: 200, respcode: 1170, message: errorMsg.responseCode[1170], amount: fields.amount, aggregator_order_id: fields.aggregator_order_id, transaction_id: txn_ref_number, sso_token: ssotoken, checksum: checksum, action_code: 1000 }
      }
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!', action_code: 1001 }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:string,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async is2FAAuthRequired ({ bankCode, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const getAuthFlag = `select cw_auth_flag from ma_aeps_bank_master where bank_code = '${bankCode}' and active_status = 'A' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'is2FAAuthRequired received bank', fields: getAuthFlag })
      const getAuthFlagResult = await this.rawQuery(getAuthFlag, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'is2FAAuthRequired-check received bank', fields: getAuthFlagResult })
      if (getAuthFlagResult.length > 0) {
        const cw_auth_flag = getAuthFlagResult[0].cw_auth_flag
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, cw_auth_required: cw_auth_flag }
      } else {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, cw_auth_required: 'N/A' }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async isAepsEmitraServiceAble ({ ma_user_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      /* integrated code must be emitra */
      const integratedCodeSql = `SELECT integration_code from ma_integration_user_master where ma_user_id=${ma_user_id}`
      log.logger({ pagename: require('path').basename(__filename), action: 'isAepsEmitraServiceAble', type: 'integratedCodeSql', fields: integratedCodeSql })
      const integratedCodeResult = await this.rawQuery(integratedCodeSql, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'isAepsEmitraServiceAble', type: 'integratedCodeResult', fields: integratedCodeResult })
      if (integratedCodeResult.length === 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
      }

      if (integratedCodeResult[0].integration_code !== 'EMITRA') {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
      }
      const currentTime = moment().tz('Asia/Kolkata')
      const startTimeValue = await common.getSystemCodes(this, util.aeps_emitra_start_timing, conn)
      const endTimeValue = await common.getSystemCodes(this, util.aeps_emitra_close_timing, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'isAepsEmitraServiceAble', type: 'system code values', fields: { startTimeValue, endTimeValue } })
      const [startHrs, startMins] = startTimeValue.split(':')
      const [endtHrs, endtMins] = endTimeValue.split(':')
      const startTime = moment().tz('Asia/Kolkata').hours(startHrs).minutes(startMins)
      const endTime = moment().tz('Asia/Kolkata').hours(endtHrs).minutes(endtMins)

      log.logger({ pagename: require('path').basename(__filename), action: 'isAepsEmitraServiceAble', type: 'timestamp', fields: { startTime, endTime, currentTime } })
      /* start time  greater than end time than invalid time format  */
      if (startTime > endTime) {
        return { status: 400, message: `${errorMsg.responseCode[1028]} : invalid time format`, respcode: 1028, action_code: 1000 }
      }

      /* current must be between start and end time if not throw error */
      if (currentTime < startTime || currentTime > endTime) {
        const message = errorMsg.responseCode[50001].replace('$endTime', moment(endTime).format('h:mm A')).replace('$startTime', moment(startTime).format('h:mm A'))
        return { status: 400, message: message, respcode: 50001, action_code: 1000 }
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isAepsEmitraServiceAble', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async checkMerchantRadius (fields, skipSavingLocation = 'N') {
    const conn = await mySQLWrapper.getConnectionFromPool()
    try {
      const sqlPindcodeQry = `SELECT IF(latitude  is not null,
      IF(latitude != '' && latitude != 'undefined',latitude ,0),0) as latitude, IF(longitude  is not null,
        IF(longitude != '' && longitude != 'undefined',longitude ,0),0) as longitude, IF(pincode  is not null,
          IF(pincode != '',pincode ,0),0) as pincode FROM ma_user_master WHERE profileid = ${fields.ma_user_id} limit 1`
      const userDetailsResult = await this.rawQuery(sqlPindcodeQry, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'userDetailsResult', fields: userDetailsResult })
      let merchantPincode = 0
      let latitude = 0
      let longitude = 0
      if (userDetailsResult.length > 0) {
        merchantPincode = userDetailsResult[0].pincode
        latitude = userDetailsResult[0].latitude
        longitude = userDetailsResult[0].longitude
      }

      if (latitude && longitude == '0' && merchantPincode == 0) {
        return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Found, Please try again later', respcode: 1028 }
      }
      let lat_long_query_from_table = 'N'
      let totalRadiusDistance = 0
      // const radiusDistance = util.aepsMerchantLocationValidation.radius_distance
      // const deviationPercent = util.aepsMerchantLocationValidation.deviationPercent
      const radiusDistance = await common.getSystemCodes(this, util.aepsMidLocValidateRadius, conn)
      const deviationPercent = await common.getSystemCodes(this, util.aepsMidLocDeviationPer, conn)
      const totalRadiusDistanceVal = radiusDistance * (deviationPercent / 100)
      totalRadiusDistance = radiusDistance + totalRadiusDistanceVal
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'radiusDistance', fields: radiusDistance })
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'deviationPercent', fields: deviationPercent })

      console.log('total radius distance ', totalRadiusDistance)
      let radiusDistanceVal = 0
      // return false
      if (latitude != '0' && longitude != '0') {
        // check the distance between two latitude & longitue (mid's lat long and received lat long)
        // the constant 6371 is used to get distance in KM, while 3959 is used to get distance in miles
        const check_merchant_jurisdication_sql = `SELECT mum.firstname, mum.lastname, mum.company, mum.address, mum.pincode, mum.mobile_id,
    mum.email_id ,(6371 * acos(
       cos( radians(${fields.latitude}) ) * cos( radians( mum.latitude ) ) * cos( radians( mum.longitude ) - radians(${fields.longitude}) )
       + sin( radians (${fields.latitude}) ) * sin( radians( mum.latitude ) ) ) ) as distance
     FROM ma_user_master mum
     WHERE (mum.latitude IS NOT NULL or mum.latitude != '')
     AND (mum.longitude IS NOT NULL or mum.longitude != '')
     AND mum.profileid = ${fields.ma_user_id}
     Having distance <= ${totalRadiusDistance} LIMIT  1`
        log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'check_merchant_jurisdication_sql', fields: check_merchant_jurisdication_sql })
        const check_merchant_jurisdication_response = await this.rawQuery(check_merchant_jurisdication_sql, conn)
        // this.rawQuery(sqlIntReq, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'check_merchant_jurisdication_response', fields: check_merchant_jurisdication_response })

        if (check_merchant_jurisdication_response.length > 0) {
          radiusDistanceVal = check_merchant_jurisdication_response[0].distance
          if (skipSavingLocation == 'N') {
          // insert records in logs table
            // const insertUserQuery = `INSERT INTO ma_geo_location (ma_user_id,user_id,order_id,request_type,amount,latitude,longitude,radius,login_ip) VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','aeps','${fields.amount}','${fields.latitude}','${fields.longitude}','${check_merchant_jurisdication_response[0].distance}','${fields.login_ip}')`
            // log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: insertUserQuery })
            // const params = await this.rawQuery(insertUserQuery, conn)
            // log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: params })
          }
          lat_long_query_from_table = 'Y'
          return { status: 200, message: 'Success', responseCode: 1000, radiusDistance: radiusDistanceVal }
        } else {
          return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028, radiusDistance: radiusDistanceVal }
        }
      }
      console.log('lat lon flag ', lat_long_query_from_table)
      if (lat_long_query_from_table == 'N' && merchantPincode > 0) {
        let latitude_val = 0
        let longitude_val = 0
        const fetch_lat_long_sql = `SELECT
      latitude, longitude
      FROM ma_pincode_mapping
      WHERE pincode = ${merchantPincode}
      ORDER BY ma_pincode_mapping_id desc limit 1 `
        // const fetch_lat_long_sql_resp = await this.secureRawQuery(fetch_lat_long_sql, { conn, params: [merchantPincode] })
        const fetch_lat_long_sql_resp = await this.rawQuery(fetch_lat_long_sql, conn)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'fetch_lat_long_sql_resp', fields: fetch_lat_long_sql_resp })

        if (fetch_lat_long_sql_resp.length > 0) {
          latitude_val = fetch_lat_long_sql_resp[0].latitude
          longitude_val = fetch_lat_long_sql_resp[0].longitude
        } else {
        // Call google api to check
          const responseLatLong = await axios({
            method: 'get',
            url: `https://maps.googleapis.com/maps/api/geocode/json?address=${merchantPincode}&&key=AIzaSyA2hm7fs2plQ0bhLGN6EQW5nMCMfAw_6qA`,
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 20000
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'responseLatLong', fields: responseLatLong })

          if (responseLatLong != '' && responseLatLong.status === 200) {
            const responseData = typeof (responseLatLong.data) == 'string' ? JSON.parse(responseLatLong.data) : responseLatLong.data
            log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'responseData', fields: responseData })
            if (responseData.status == 'OK') {
              if (responseData.results != '' && responseData.results[0].geometry != '' && responseData.results[0].geometry.location != '' && responseData.results[0].geometry.location.lat != '') {
                latitude_val = responseData.results[0].geometry.location.lat
              }
              if (responseData.results[0] != '' && responseData.results[0].geometry != '' && responseData.results[0].geometry.location != '' && responseData.results[0].geometry.location.lng != '') {
                longitude_val = responseData.results[0].geometry.location.lng
              }
              console.log('lat ', latitude_val)
              console.log('long ', longitude_val)
              if (latitude_val != 0 && longitude_val != 0) {
                const areaAddress = responseData.results[0].formatted_address ? responseData.results[0].formatted_address : ''

                // Insert lat long data in ma_pincode_mapping table
                const insertPincodeMappingSql = `INSERT INTO ma_pincode_mapping (area, pincode, latitude, longitude) VALUES ('${areaAddress}','${merchantPincode}','${latitude_val}','${longitude_val}')`
                // const insertPincodeMappingSqlRes = await this.secureRawQuery(insertPincodeMappingSql, { conn, params: [[[areaAddress, merchantPincode, latitude_val, longitude_val]]] })
                const insertPincodeMappingSqlRes = await this.rawQuery(insertPincodeMappingSql, conn)
                log.logger({ pagename: require('path').basename(__filename), action: 'fetchLatitudeLogitude', type: 'insertPincodeMappingSqlRes', fields: insertPincodeMappingSqlRes })
              }
            } else {
            // return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028 }
            }
          } else {
          // return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028 }
          }
        }

        const sqlDistanceQry = `Select (6371 * acos(cos( radians(${fields.latitude}) ) * cos( radians( ${latitude_val} ) ) * cos( radians( ${longitude_val} ) - radians(${fields.longitude}) ) + sin( radians (${fields.latitude}) ) * sin( radians( ${latitude_val} ) ) ) ) as distance`
        const userDistanceResult = await this.rawQuery(sqlDistanceQry, conn)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'userDistanceResult', fields: userDistanceResult })
        // if result is less than radius_distance then add log entry
        if (userDistanceResult.length > 0 && userDistanceResult[0].distance <= totalRadiusDistance) {
          radiusDistanceVal = userDistanceResult[0].distance
          if (skipSavingLocation == 'N') {
          // insert records in logs table
            // const insertUserQuery2 = `INSERT INTO ma_geo_location (ma_user_id,user_id,order_id,request_type,amount,latitude,longitude,radius,login_ip) VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','aeps','${fields.amount}','${fields.latitude}','${fields.longitude}','${userDistanceResult[0].distance}','${fields.login_ip}')`
            // log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery2', fields: insertUserQuery2 })
            // const params2 = await this.rawQuery(insertUserQuery2, conn)
            // log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: params2 })
          }
          return { status: 200, message: 'Success', respcode: 1000, radiusDistance: radiusDistanceVal }
        } else {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Merchant cannot do transaction outside jurisdiction, Please try again later!', radiusDistance: radiusDistanceVal }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      conn.release()
    }
  }

  /**
   * @private
   * methodName description - What's the method about?
   * @param {{ ma_user_id: number, userid: number }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async getBankName (bank_code, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBankName', type: 'request', bank_code })

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const sqlIntReq = `SELECT bank_name FROM ma_aeps_bank_master WHERE active_status = "A" and bank_code = '${bank_code}' LIMIT 1`
      const dataIntReq = await this.rawQuery(sqlIntReq, connection)
      let bank_name = ''
      if (dataIntReq.length > 0) {
        bank_name = dataIntReq[0].bank_name
      }
      return bank_name
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBankName', type: 'err', fields: err })
      // errorEmail.notifyCatchErrorEmail({ function: 'methodName', data: { ...fields }, error: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  static async create2FATransaction ({ fields, connection }) {
    log.logger({
      pagename: require('path').basename(__filename),
      action: 'create2FATransaction',
      type: 'request',
      fields: fields
    })
    const isSet = connection === null || connection === undefined
    const conn = isSet
      ? await mySQLWrapper.getConnectionFromPool()
      : connection
    try {
      if (fields.gstType == 'I') {
        fields.amount = fields.chargesAmount
      }
      console.log('amountt', fields.amount)
      const transactionData = await transactionCtrl.initiateTransaction('', {
        connection: conn,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.orderid,
        transaction_id: fields.orderid,
        amount: fields.amount,
        commission_amount: fields.GSTAmount ? fields.GSTAmount : '0',
        transaction_type: fields.transaction_type, // aeps 2FA  charge
        remarks: fields.remarks,
        mobile_number: fields.mobile_number,
        provider_id: '0', // to do
        utility_id: '0', // to do
        utility_name: 'AEPS', // to do
        action_type: 'instapay',
        transaction_status: 'S',
        bank_name: '',
        customer_mobile: fields.customer_mobile,
        merchant_type: fields.merchant_type
      })

      log.logger({
        pagename: require('path').basename(__filename),
        action: 'create2FATransaction',
        type: 'transactionData',
        fields: transactionData
      })

      if (transactionData.status != 200) return transactionData
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        action_code: 1000,
        orderid: fields.orderid
      }
    } catch (error) {
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'create2FATransaction',
        type: 'catcherror',
        fields: error
      })
      return {
        status: 400,
        message: errorMsg.responseCode[1001],
        respcode: 1001
      }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async ledgerEntries ({ fields, connection }) {
    log.logger({
      pagename: require('path').basename(__filename),
      action: 'ledgerEntries',
      type: 'request',
      fields: fields
    })
    const isSet = connection === null || connection === undefined
    const conn = isSet
      ? await mySQLWrapper.getConnectionFromPool()
      : connection
    try {
      let final_amount = ''
      if (fields.gstType == 'E') {
        final_amount = fields.chargesAmount
      } else if (fields.gstType == 'I') {
        final_amount = fields.newAmount
      }
      console.log('finalAmount', final_amount)
      const updateTransactionResult = await transactionCtrl.updateWhereData(
        conn,
        {
          data: {
            transaction_reason: `Balance Deducted ${fields.amount}`,
            transaction_status: 'S'
          },
          id: fields.orderid,
          where: 'aggregator_order_id'
        }
      )
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'updateTransactionResult',
        fields: updateTransactionResult
      })
      if (updateTransactionResult.status != 200) return updateTransactionResult

      var pointsDetailsEntries = {}
      pointsDetailsEntries = await balanceController.getWalletBalanceDirect(
        '_',
        {
          ma_user_id: fields.ma_user_id,
          amount: final_amount,
          transactionType: '1',
          connection: conn
        }
      )
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'getWalletBalanceDirect',
        fields: pointsDetailsEntries
      })
      if (pointsDetailsEntries.status === 400) return pointsDetailsEntries

      // point leger entry
      const pointLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: final_amount,
        mode: 'dr',
        transaction_type: 66,
        description: 'AEPS 2FA cash withdrawal Transaction',
        ma_status: 'S',
        orderid: fields.orderid,
        userid: fields.userid,
        conn
      })
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'pointsLedger',
        fields: pointLedgerId
      })
      if (pointLedgerId.status === 400) return pointLedgerId

      // pointsDetails entry
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: pointLedgerId.id,
          orderid: fields.orderid,
          ma_status: 'S',
          connection: conn
        })
        log.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'pointsDetailsController',
          fields: entry
        })
        if (entry.status === 400) return entry
      }

      // airpay id credit entry

      const retailerLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: final_amount,
        mode: 'cr',
        transaction_type: 66,
        description: 'AEPS 2FA cash withdrawal Transaction',
        ma_status: 'S',
        userid: fields.userid,
        orderid: fields.orderid,
        corresponding_id: fields.ma_user_id,
        conn
      })
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'createEntry',
        fields: retailerLedgerId
      })
      if (retailerLedgerId.status === 400) return retailerLedgerId

      if (fields.gstType == 'E' || fields.gstType == 'I') {
        const gst_amount = fields.GSTAmount
        console.log('GSTAmount', gst_amount)

        pointsDetailsEntries = await balanceController.getWalletBalanceDirect(
          '_',
          {
            ma_user_id: fields.ma_user_id,
            amount: gst_amount.toFixed(2),
            transactionType: '1',
            connection: conn
          }
        )
        log.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'getWalletBalanceDirect',
          fields: pointsDetailsEntries
        })
        if (pointsDetailsEntries.status === 400) return pointsDetailsEntries

        // point leger entry
        const pointLedgerId = await pointsLedger.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: gst_amount.toFixed(2),
          mode: 'dr',
          transaction_type: 65,
          description: `AEPS 2FA CW GST charges at ${fields.gst_amount}%`,
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          conn
        })
        log.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'pointsLedger',
          fields: pointLedgerId
        })
        if (pointLedgerId.status === 400) return pointLedgerId

        // pointsDetails entry
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: pointLedgerId.id,
            orderid: fields.orderid,
            ma_status: 'S',
            connection: conn
          })
          log.logger({
            pagename: require('path').basename(__filename),
            action: 'ledgerEntries',
            type: 'pointsDetailsController',
            fields: entry
          })
          if (entry.status === 400) return entry
        }

        // airpay id credit entry

        const retailerLedgerId = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: gst_amount.toFixed(2),
          mode: 'cr',
          transaction_type: 65,
          description: `AEPS 2FA CW GST charges at ${fields.gst_amount}%`,
          ma_status: 'S',
          userid: fields.userid,
          orderid: fields.orderid,
          corresponding_id: fields.ma_user_id,
          conn
        })
        log.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'createEntry',
          fields: retailerLedgerId
        })
        if (retailerLedgerId.status === 400) return retailerLedgerId
      }

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        action_code: 1000
      }
    } catch (error) {
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'catcherror',
        fields: error
      })
      return {
        status: 400,
        message: errorMsg.responseCode[1001],
        respcode: 1001,
        action_code: 1001
      }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async generateOrderNo () {
    const random = await this.generateOTP(4)
    const timestamp = await this.getTimestamp('')
    const orderId = `MAWEB${random}${timestamp}`
    return orderId
  }

  static async generateOTP (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  static async getTimestamp () {
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    const timeStamp = Math.floor(Date.now() / 1000)
    return `${timeStamp}`
  }

  static async getLatLongFromUserDetails (fields) {
    const conn = await mySQLWrapper.getConnectionFromPool()
    try {
      const sqlPindcodeQry1 = `SELECT IF(latitude  is not null,
      IF(latitude != '' && latitude != 'undefined',latitude ,0),0) as latitude, IF(longitude  is not null,
        IF(longitude != '' && longitude != 'undefined',longitude ,0),0) as longitude, IF(pincode  is not null,
          IF(pincode != '',pincode ,0),0) as pincode FROM ma_user_master WHERE profileid = ${fields.ma_user_id} limit 1`
      const userDetailsResult1 = await this.rawQuery(sqlPindcodeQry1, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'userDetailsResult1', fields: userDetailsResult1 })
      let merchantPincode = 0
      let latitude = 0
      let longitude = 0
      if (userDetailsResult1.length > 0) {
        merchantPincode = userDetailsResult1[0].pincode
        latitude = userDetailsResult1[0].latitude
        longitude = userDetailsResult1[0].longitude
      }

      if (latitude && longitude == '0' && merchantPincode == 0) {
        return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Found, Please try again later', respcode: 1028 }
      }

      // if lat & long not equal to 0 then send
      if (latitude != 0 && longitude != 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, lat_val: latitude, long_val: longitude }
      }
      if (merchantPincode > 0) {
        let latitude_val = 0
        let longitude_val = 0
        const fetch_lat_long_sql1 = `SELECT latitude, longitude FROM ma_pincode_mapping WHERE pincode = ${merchantPincode} ORDER BY ma_pincode_mapping_id desc limit 1 `
        const fetch_lat_long_sql_resp1 = await this.rawQuery(fetch_lat_long_sql1, conn)
        log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'fetch_lat_long_sql_resp1', fields: fetch_lat_long_sql_resp1 })

        if (fetch_lat_long_sql_resp1.length > 0) {
          latitude_val = fetch_lat_long_sql_resp1[0].latitude
          longitude_val = fetch_lat_long_sql_resp1[0].longitude
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, lat_val: latitude_val, long_val: longitude_val }
        } else {
        // Call google api to check
          const responseLatLong = await axios({
            method: 'get',
            url: `https://maps.googleapis.com/maps/api/geocode/json?address=${merchantPincode}&&key=AIzaSyA2hm7fs2plQ0bhLGN6EQW5nMCMfAw_6qA`,
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 20000
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'responseLatLong_API', fields: responseLatLong })

          if (responseLatLong != '' && responseLatLong.status === 200) {
            const responseData = typeof (responseLatLong.data) == 'string' ? JSON.parse(responseLatLong.data) : responseLatLong.data
            log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'responseData', fields: responseData })
            if (responseData.status == 'OK') {
              if (responseData.results != '' && responseData.results[0].geometry != '' && responseData.results[0].geometry.location != '' && responseData.results[0].geometry.location.lat != '') {
                latitude_val = responseData.results[0].geometry.location.lat
              }
              if (responseData.results[0] != '' && responseData.results[0].geometry != '' && responseData.results[0].geometry.location != '' && responseData.results[0].geometry.location.lng != '') {
                longitude_val = responseData.results[0].geometry.location.lng
              }
              console.log('lat ', latitude_val)
              console.log('long ', longitude_val)
              if (latitude_val != 0 && longitude_val != 0) {
                const areaAddress = responseData.results[0].formatted_address ? responseData.results[0].formatted_address : ''

                // Insert lat long data in ma_pincode_mapping table
                const insertPincodeMappingSql = `INSERT INTO ma_pincode_mapping (area, pincode, latitude, longitude) VALUES ('${areaAddress}','${merchantPincode}','${latitude_val}','${longitude_val}')`
                const insertPincodeMappingSqlRes = await this.rawQuery(insertPincodeMappingSql, conn)
                log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'insertPincodeMappingSqlRes', fields: insertPincodeMappingSqlRes })

                // latitude & longitude in user master table - comment below code for now
                /* const updatePincodeMappingSql = `UPDATE ma_user_master SET latitude = '${latitude_val}' and longitude = '${longitude_val}' WHERE profileid = '${fields.ma_user_id}'`
                const updatePincodeMappingSqlRes = await this.rawQuery(updatePincodeMappingSql, conn)
                log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'updatePincodeMappingSqlRes', fields: updatePincodeMappingSqlRes }) */

                return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, lat_val: latitude_val, long_val: longitude_val }
              }
            } else {
              return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028 }
            }
          } else {
            return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028 }
          }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      conn.release()
    }
  }

  static async getSuccessGeoLocationData (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'req', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const getGeoLocationDataTime = await common.getSystemCodes(this, util.getGeoLocationTime, connection)
      console.log('getGeoLocationDataTime>>', getGeoLocationDataTime)
      const getTransactionDataSql = `Select id,ma_status,transaction_status,bank_response,addedon from ma_aeps_transaction_logs where ma_user_id = ${fields.ma_user_id} and ma_status ='F' and bank_response is NOT NULL and  addedon>= NOW() - INTERVAL ${getGeoLocationDataTime} HOUR order by addedon desc limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'getTransactionDataSql', fields: getTransactionDataSql })
      const getTransactionData = await this.rawQuery(getTransactionDataSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'getTransactionData', fields: getTransactionData })

      if (getTransactionData.length == 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      // const transactionStatus = getTransactionData[0].ma_status
      const apiResponse = JSON.parse(getTransactionData[0].bank_response)
      console.log('apiresponse', apiResponse)
      //  if(apiResponse.status){
      console.log('iside the condition')
      if ((apiResponse.status == 400) && (apiResponse.responseCode == '00W8')) {
        const getSuccessDataSql = `Select mtl.id,mgl.latitude,mgl.longitude,mgl.addedon from ma_aeps_transaction_logs mtl left join ma_geo_location mgl on mtl.order_id = mgl.order_id  where mtl.ma_user_id = ${fields.ma_user_id} and mtl.ma_status ='S' and mgl.login_ip = '${fields.login_ip}' and mtl.addedon>= NOW() - INTERVAL ${getGeoLocationDataTime} HOUR order by mtl.addedon desc limit 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'getSuccessDataSql', fields: getSuccessDataSql })

        const getSuccessData = await this.rawQuery(getSuccessDataSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'getSuccessData', fields: getSuccessData })

        if (getSuccessData.length > 0) {
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: getSuccessData[0] }
        } else {
          const getLatLongDataFromLoginSql = `Select latitude, longitude from ma_user_login_details where ma_user_id = ${fields.ma_user_id} and userid =  ${fields.userid} and source_type ='app' and login_ip = '${fields.login_ip}' and latitude is not null and longitude is not null and latitude != '' and longitude != '' and addedon>= NOW() - INTERVAL ${getGeoLocationDataTime} HOUR order by addedon desc limit 1`
          log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'getLatLongDataFromLoginSql', fields: getLatLongDataFromLoginSql })
          const getLatLongDataFromLogin = await this.rawQuery(getLatLongDataFromLoginSql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'getLatLongDataFromLogin', fields: getLatLongDataFromLogin })
          if (getLatLongDataFromLogin.length == 0) return { status: 400, respcode: 1001, message: 'Please login in Mobile, continue with AePS/ Aadhaar Pay transaction and update your APP.' }
          else {
            return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: getLatLongDataFromLogin[0] }
          }
          //  }
        }
      }
      console.log('end>>>')
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSuccessGeoLocationData', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = aepsTransactionController
