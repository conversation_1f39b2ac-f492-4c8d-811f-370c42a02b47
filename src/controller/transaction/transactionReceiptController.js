const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const cmsController = require('../cms/cmsController')
const fastagController = require('../fastTag/fastTagController')
const shoppingController = require('../shopping/shoppingController')
const transfersController = require('../transfers/transfersController')
const ondcController = require('../ondc/ondcController')
const ppiController = require('../ppiwallet/ppiwalletController')
const transactionController = require('./transactionController')
const goldController = require('../gold/goldController')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const util = require('../../util/util')
const common = require('../../util/common')
const umangController = require('../umang/umangController')
const bbpsConfig = require('../bbps/bbpsconfig')
const SoundBoxManagementController = require('../soundBoxManagement/soundBoxManagementController')

class TransactionReceipt extends DAO {
  /**
   * Get receipt for the transaction
   * @param {*} _
   * @param {*} fields
   * <AUTHOR> Hassan
   * @returns transaction_type_data
   */
  static async getTransactionReceipt (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'getTransactionReceipt', type: 'request', fields: fields })
    console.log('reached')
    try {
      let receipt_data = []
      const final_receipt = {}
      // Params Validation
      if ((!fields.aggregator_order_id) && (['34'].includes(fields.transaction_type))) {
        return { status: 400, message: errorMsg.responseCode[12359], respcode: 1001 }
      }

      if ((!fields.ma_transaction_master_id) && (['2', '31', '32'].includes(fields.transaction_type))) {
        return { status: 400, message: errorMsg.responseCode[12360], respcode: 1001 }
      }

      switch (fields.transaction_type) {
        case '5':
          console.log('case AEPS')
          receipt_data = await transactionController.getAepsTransactionDetails(_, { aggregator_order_id: fields.aggregator_order_id, ma_transaction_master_id: fields.ma_transaction_master_id })
          break

        case '10':
          console.log('case ADPAY')
          receipt_data = await transactionController.getADPayTransactionDetails(_, { aggregator_order_id: fields.aggregator_order_id, ma_transaction_master_id: fields.ma_transaction_master_id })
          break

        case '6':
        case '17':
          console.log('case BBPS/Recharge')
          receipt_data = await transactionController.getBbpsTransactionDetails(_, { aggregator_order_id: fields.aggregator_order_id, ma_transaction_master_id: fields.ma_transaction_master_id })
          break

        case '24':
          console.log('case CMS')
          receipt_data = await cmsController.getCmsTransactionDetails(_, { ma_transaction_master_id: fields.ma_transaction_master_id, aggregator_order_id: fields.aggregator_order_id })
          break

        case '31':
        case '32':
          console.log('case Fastag/Fastag recharge')
          receipt_data = await fastagController.getFastagRechargeReceiptDetails(_, { ma_transaction_master_id: fields.ma_transaction_master_id })
          receipt_data.amount = receipt_data.initial_bal
          break

        case '28':
          console.log('case Gold')
          receipt_data = await goldController.getGoldTransactionDetails(_, { aggregator_order_id: fields.aggregator_order_id, ma_transaction_master_id: fields.ma_transaction_master_id })
          break

        case '34':
          console.log('case Baazaar')
          receipt_data = await shoppingController.getShoppingTransactionOrderReceipt(_, { userid: fields.userid, ma_user_id: fields.ma_user_id, aggregator_order_id: fields.aggregator_order_id })
          break

        case '2':
          console.log('case Transfer')
          receipt_data = await transfersController.transferDetails(_, fields)
          break
        case '75':
          console.log('case Digikhata')
          receipt_data = await ppiController.getPpiTransactionDetails(_, fields)
          break

          // case '15':
          //   console.log('case Insurance')
          //   receipt_data = await transactionController.getInsuranceTransactionDetails(_, { userid: fields.userid, ma_user_id: fields.ma_user_id, aggregator_order_id: fields.aggregator_order_id })
          //   break
        case '44':
          console.log('case ONDC')
          receipt_data = await ondcController.getOndcTransactionDetails(_, { ma_transaction_master_id: fields.ma_transaction_master_id, aggregator_order_id: fields.aggregator_order_id })
          break
        case '46':
          console.log('case Government Services')
          receipt_data = await umangController.getUmangReceiptDetails(_, { ma_transaction_master_id: fields.ma_transaction_master_id })
          break

        case '23':
          console.log('case HUMAN_ATM')
          receipt_data = await transactionController.getHatmTransactionDetails(_, { aggregator_order_id: fields.aggregator_order_id, ma_transaction_master_id: fields.ma_transaction_master_id })
          break
        case '81':
          console.log('sim Renewal recept ')
          receipt_data = await SoundBoxManagementController.simRenewalReceiptDetails(_, { ...fields })
          break
        default:
          break
      }
      console.log('receipt_data', receipt_data)
      /** BBPS New Changes- Added Logo_url in receipt */
      let logo_url_bbps = bbpsConfig.LOGO_URL_BBPS_ASSURED
      if (receipt_data.status == 200) {
        if (fields.transaction_type == '24') {
          final_receipt.api_params_json = receipt_data.api_params_json
          final_receipt.api_params = receipt_data.api_params
          delete receipt_data.api_params_json
          delete receipt_data.api_params
        } else if (fields.transaction_type == '75') {
          // added changes for PPI Wallet
          // final_receipt.transactionDetails = receipt_data.transactionDetails
          // final_receipt.logo_url = receipt_data.logo_url
          // delete receipt_data.logo_url
          // delete receipt_data.transactionDetails
          final_receipt.transactionDetails = receipt_data.transactionDetails
          final_receipt.logo_url = receipt_data.logo_url
          receipt_data.remitter_name = receipt_data.transactionDetails.remitter_name
          receipt_data.bank_name = receipt_data.transactionDetails.bank_name
          receipt_data.transfer_id = receipt_data.transactionDetails.transfer_id
          delete receipt_data.logo_url
          delete receipt_data.transactionDetails
        } else if (fields.transaction_type == '2') {
          final_receipt.uic = receipt_data.uic
          final_receipt.transfersData = receipt_data.transfersData
          final_receipt.tryWithOtherBank = receipt_data.tryWithOtherBank
        } else if (fields.transaction_type == '28') {
          receipt_data.transaction_status = receipt_data.transactionStatus ? receipt_data.transactionStatus : receipt_data.transaction_status
          receipt_data.amount = receipt_data.total_order_amount
          receipt_data.aggregator_order_id = receipt_data.transaction_id
          receipt_data.customer_name = receipt_data.customerName
          receipt_data.customer_mobile = receipt_data.customerMobile
          delete receipt_data.total_order_amount
          delete receipt_data.customerName
          delete receipt_data.customerMobile
          delete receipt_data.transaction_id
        } else if (fields.transaction_type == '6' || fields.transaction_type == '17') {
          if (fields.transaction_type == '6') {
            receipt_data.bill_amount = common.addRupeeSymbol(receipt_data.transaction_amount)
            receipt_data.consumer_convenience_fee = common.addRupeeSymbol(receipt_data.transaction_charges)
            delete receipt_data.transaction_amount
            delete receipt_data.transaction_charges
            delete receipt_data.transaction_id
          }
          final_receipt.receipt_bill_response = receipt_data.receipt_bill_response
          delete receipt_data.bill_response
          delete receipt_data.__rechargeType
          delete receipt_data.__planName
          delete receipt_data.__planDescription
          delete receipt_data.__circleName
          delete receipt_data.logo_url
          delete receipt_data.ma_transaction_master_id
          delete receipt_data.merchant_mobile
          delete receipt_data.merchant_name
        } else if (fields.transaction_type == '46') {
          final_receipt.customer_name = receipt_data.customer_name
          final_receipt.agent_id = receipt_data.agent_id
          final_receipt.tracker_id = receipt_data.tracker_id
          final_receipt.department_name = receipt_data.department_name
          final_receipt.service_name = receipt_data.service_name
        } else if (fields.transaction_type == '10') {
          delete receipt_data.customer_virtualid
          delete receipt_data.customer_aadhaar_virtual_id
          delete receipt_data.bank_balance
        } else if (fields.transaction_type == '23') {
          receipt_data.transaction_status = receipt_data.transactionStatus ? receipt_data.transactionStatus : receipt_data.transaction_status
          final_receipt.aggregator_order_id = receipt_data.aggregator_order_id
          final_receipt.transaction_status = receipt_data.transaction_status
          final_receipt.bank_name = receipt_data.bank_name
          final_receipt.amount = receipt_data.amount
          final_receipt.bank_mid = receipt_data.bank_mid
          final_receipt.bank_tid = receipt_data.bank_tid
          final_receipt.merchant_id = receipt_data.merchant_id
          final_receipt.card_type = receipt_data.card_type
          delete receipt_data.merchant_mobile
        }
        final_receipt.status = receipt_data.status
        final_receipt.respcode = receipt_data.respcode
        final_receipt.message = receipt_data.message
        final_receipt.amount = receipt_data.amount
        final_receipt.address = receipt_data.address
        final_receipt.transaction_time = receipt_data.transaction_time
        final_receipt.transaction_status = receipt_data.transaction_status
        if (fields.transaction_type != '46') {
          final_receipt.customer_name = receipt_data.customer_name
        }
        final_receipt.customer_mobile = receipt_data.customer_mobile
        final_receipt.aggregator_order_id = receipt_data.aggregator_order_id
        final_receipt.transaction_reason = receipt_data.transaction_reason
        final_receipt.aggregator_txn_id = receipt_data.aggregator_txn_id
        final_receipt.userid = receipt_data.userid
        delete receipt_data.amount
        delete receipt_data.transaction_time
        delete receipt_data.transaction_status
        if (fields.transaction_type != '46') {
          delete receipt_data.customer_name
        }
        if (fields.transaction_type == '81') {
          if (receipt_data.transaction_receipt_data && receipt_data.transaction_receipt_data.length > 0) {
            const firstTransaction = receipt_data.transaction_receipt_data[0]
            const keyMapping = {
              order_id: 'aggregator_order_id',
              transaction_time: 'transaction_date_time',
              amount: 'amount',
              transaction_status: 'transaction_status',
              transaction_reason: 'transaction_reason',
              aggregator_txn_id: 'aggregator_txn_id',
              userid: 'userid'
            }

            const keysToBind = [
              'order_id',
              'amount',
              'transaction_status',
              'transaction_reason',
              'aggregator_txn_id',
              'userid'
            ]
            final_receipt.transaction_time = firstTransaction.transaction_date_time
            final_receipt.address = firstTransaction.address

            keysToBind.forEach(key => {
              const internalKey = keyMapping[key] || key
              final_receipt[internalKey] = firstTransaction[key] || ''
            })
          }
          receipt_data.receipt_bill_response = receipt_data.transaction_receipt_data.flatMap(item => [
            { parameter_name: 'selected_biller', value: 'SIM recharge' },
            { parameter_name: 'operator', value: item.operator || '' },
            { parameter_name: 'sim_number', value: item.sim_number || '' },
            { parameter_name: 'remarks', value: 'Soundbox SIM - Plan renewal' }
          ])

          delete receipt_data.transaction_receipt_data
          delete receipt_data.action_code
        }

        delete receipt_data.address
        delete receipt_data.customer_mobile
        delete receipt_data.aggregator_order_id
        delete receipt_data.transaction_reason
        delete receipt_data.aggregator_txn_id
        delete receipt_data.userid
        delete receipt_data.message
        delete receipt_data.respcode
        delete receipt_data.status
        delete receipt_data.tryWithOtherBank
        delete receipt_data.transfersData
        delete receipt_data.uic 
        delete receipt_data.receipt_bill_additional_details
        log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionReceipt final_receipt', type: 'response', fields: final_receipt })

        console.log('receipt data ', receipt_data)
        // return false
        let transaction_receipt_data = []

        Object.keys(receipt_data).forEach(function (key) {
          if (key === 'receipt_bill_response') {
            receipt_data.receipt_bill_response.map(function (receipt_key) {
              transaction_receipt_data.push({ key: receipt_key.parameter_name, value: receipt_key.value })
            })
          } else {
            transaction_receipt_data.push({ key, value: key === 'transaction_amount' ? common.addRupeeSymbol(receipt_data[key]) : receipt_data[key] })
          }
        })
        Array.prototype.push.apply(transaction_receipt_data, final_receipt.api_params_json)
        // Remove duplicate values

        transaction_receipt_data = transaction_receipt_data.filter((value, index, self) =>
          index === self.findIndex((t) => (
            t.key === value.key
          )) && value && value.value
        )

        return {
          status: final_receipt.status,
          respcode: final_receipt.respcode,
          message: final_receipt.message,
          transaction_receipt_data: transaction_receipt_data,
          amount: final_receipt.amount,
          address: final_receipt.address,
          transaction_time: final_receipt.transaction_time,
          transaction_status: final_receipt.transaction_status,
          customer_name: final_receipt.customer_name,
          customer_mobile: final_receipt.customer_mobile,
          aggregator_order_id: final_receipt.aggregator_order_id,
          transaction_reason: final_receipt.transaction_reason,
          aggregator_txn_id: final_receipt.aggregator_txn_id,
          userid: final_receipt.userid,
          uic: final_receipt.uic,
          logo_url: fields.transaction_type == '6' ? logo_url_bbps : final_receipt.logo_url,
          transfersData: final_receipt.transfersData,
          tryWithOtherBank: final_receipt.tryWithOtherBank
        }
      } else {
        return { status: 400, message: receipt_data.message ? receipt_data.message : errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getTransactionReceipt', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // connection.release()
    }
  }
}

module.exports = TransactionReceipt
