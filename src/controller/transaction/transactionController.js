const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const pointsrate = require('../pointsRate/pointsRateController')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const util = require('../../util/util')
const common = require('../../util/common')
const aepsDistribution = require('../incentive/aepsDistributionController')
const bbpsinecntive = require('../incentive/bbpsIncentiveController')
const ledgerEntriesCont = require('../ledgerEntries/ledgerEntriesController')
const balanceController = require('../balance/balanceController')

const axios = require('axios')
const qs = require('qs')
const parser = require('fast-xml-parser')
const sms = require('../../util/sms')
const integrated = require('../integrated/integratedController')
const checksum = require('../../util/checksum')
const transactionDetailsController = require('./transactionDetailsController')
const common_fns = require('../../util/common_fns')
const validator = require('../../util/validator')
const { generateOrderchecksum } = require('../../util/checksum')
const mccLevelSettingController = require('../users/mccLevelSettingController')
const collectMoneyDistribution = require('../incentive/collectMoneyDistributionController')
/* RISK MANAGEMENT Changes */
const riskManagementController = require('../riskManagement/riskManagementController')
/* NEW CHANGES : LEIN BALANCE CHECK */
const lienBalanceController = require('../lienBalance/lienBalanceController')

class Transaction extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_transaction_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_transaction_master_id'
  }

  /**
     * Returns a transaction by its ID
     */
  static getByID (_, { id }) {
    return this.find(id)
  }

  static async findMatching (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'findMatching', type: 'request', fields: fields })
    // Returns early with all transactions if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching transactions
    const res = await this.findByFields({
      fields,
      limit: 1
    })
    log.logger({ pagename: 'transactionController.js', action: 'findMatching', type: 'response', fields: res })
    return res
  }

  static async transactionDetails (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const datefrom = fields.datefrom
      const dateto = fields.dateto
      const limit = fields.limit
      const offset = fields.offset
      const bene_account_name = fields.bene_account_name
      const bene_account_number = fields.bene_account_number
      const aggregator_txn_id = fields.aggregator_txn_id
      const mobile_number = fields.mobile_number
      const transaction_status = fields.transaction_status
      delete fields.datefrom
      delete fields.dateto
      delete fields.limit
      delete fields.offset
      delete fields.userid
      delete fields.bene_account_name
      delete fields.bene_account_number
      delete fields.aggregator_txn_id
      delete fields.mobile_number
      delete fields.transaction_status

      // Check if integrated merchant
      // Check if Emitra merchant is on-boarded in Vyaapaar
      const userEmitraSQL = `SELECT id,name FROM users WHERE user_type = 'integrated' and status = 2 and profileid =${fields.ma_user_id} limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionDetails', type: 'integrated merchantuserEmitraSQL', fields: userEmitraSQL })
      const integratedEmitraMerData = await this.rawQuery(userEmitraSQL, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionDetails', type: 'integratedEmitraMerData', fields: integratedEmitraMerData })
      let txn_source = ''
      if (integratedEmitraMerData.length > 0) {
        txn_source = 'EMITRA'
      }
      /**
       * In case of DMT Awaiting Settlement handled as Pending so following changes added
       * IF(transaction_type = 2 AND transaction_status = 'P','PND',transaction_status) as transaction_status,
       */
      var sql = `SELECT SQL_CALC_FOUND_ROWS 
                        t.ma_transaction_master_id,
                        t.ma_user_id,
                        t.userid,
                        t.aggregator_txn_id,
                        t.parent_id AS aggregator_order_id,
                        t.transaction_id,
                       (CASE
                          WHEN t.transaction_type = 10 and td.utility_name !='AP' THEN t.amount
                          WHEN t.transaction_type = 34 AND shopping.delivery_charges IS NOT NULL THEN t.amount + shopping.delivery_charges        
                          ELSE t.amount + IF(t.commission_amount IS NULL, 0.0, t.commission_amount)
                         END) AS amount,
                        t.commission_amount,
                        CASE
                           WHEN (t.transaction_type = 2 OR t.transaction_type = '75' OR t.transaction_type = 15 OR t.transaction_type = 6 OR t.transaction_type = 17 OR t.transaction_type = 5 OR t.transaction_type = 24 OR t.transaction_type = 31 OR t.transaction_type = 32 OR t.transaction_type = 28 OR t.transaction_type = 21 OR t.transaction_type = 34 OR t.transaction_type = 23 OR t.transaction_type = 50 OR t.transaction_type = '77') AND t.transaction_status = 'P' THEN 'PND'
                           WHEN t.transaction_type = 2 AND t.transaction_status = 'I' THEN 'PROC'
                           WHEN t.transaction_type = '75' AND t.transaction_status = 'I' THEN 'PROC'
                           WHEN t.transaction_type = 10 AND td.utility_name = 'AP' AND t.transaction_status = 'P' THEN 'PND'
                           ELSE t.transaction_status
                        END as transaction_status,
                        t.transaction_type,
                        CASE
                           WHEN (t.transaction_type = 2 OR t.transaction_type = 5 OR t.transaction_type = 6 OR t.transaction_type = 17 OR t.transaction_type = 24 OR t.transaction_type = 31 OR t.transaction_type = 32 OR t.transaction_type = 28 OR t.transaction_type = 34 OR t.transaction_type = 46 OR t.transaction_type = 23 OR t.transaction_type = 50 OR t.transaction_type = '81') AND ad.transaction_type IS NULL THEN 'Y'
                           WHEN t.transaction_type = 15 AND td.utility_name = 'insurance' AND td.provider_name = 'KOTAK' AND t.transaction_status = 'S' THEN 'Y'
                           WHEN t.transaction_type = 10 OR t.transaction_type = '75'  THEN 'Y'
                           ELSE 'N'
                        END as receipt_flag,   
                        t.recon_status,
                        t.recon_amount,
                        t.remarks,
                        CASE
                            WHEN t.transaction_type = 6 OR t.transaction_type = 17 THEN IF(td.provider_name IS NULL,'N/A',td.provider_name)
                            WHEN t.transaction_type = 24 THEN IF(cms.merchant_legal_name IS NULL,'N/A',cms.merchant_legal_name)
                            WHEN t.transaction_type = 10 THEN IF(td.customer_virtualid IS NULL,IF(td.bank_name IS NULL,'N/A',td.bank_name),td.customer_virtualid)
                            ELSE IF(td.bank_name IS NULL,'N/A',td.bank_name)
                        END AS bank_name,
                        t.points_factor,
                        t.bank_rrn,
                        CASE
                            WHEN t.transaction_type = 15 THEN IF(td.customer_mobile IS NULL,'N/A',td.customer_mobile)
                            ELSE IF(t.mobile_number IS NULL,'N/A',t.mobile_number)
                        END AS mobile_number,
                        t.gcmkey,
                        t.addedon,
                        t.updatedon,
                        t.bank_charges,
                        0 AS lts_flag,
                        CASE
                            WHEN t.transaction_type = 15 THEN IF(td.customer_name IS NULL,'N/A',td.customer_name)
                            WHEN t.transaction_type = 23 THEN IF(mial.customer_name IS NULL,'N/A',mial.customer_name)
                            ELSE IF(td.receiver_name IS NULL,'N/A',td.receiver_name)
                        END AS bene_account_name,
                        CASE
                          WHEN t.transaction_type = 23 THEN IF(mial.card_num IS NULL,'N/A',mial.card_num)
                          ELSE td.receiver_account_number
                        END AS bene_account_number,
                        CASE
                            WHEN t.transaction_type = 15 and ri.transaction_status = 'P' THEN 'Y'
                            ELSE 'N'
                        END AS riskcovry_redirection_flag,
                        CASE
                            WHEN t.transaction_type = 15 and ri.transaction_status = 'S' THEN 'Y'
                            ELSE 'N'
                        END AS riskcovry_receipt_flag,
                        CASE
                            WHEN t.transaction_type = 15 and (li.transaction_status = 'P' OR li.transaction_status = 'PS') THEN 'Y'
                            ELSE 'N'
                        END AS lombard_redirection_flag,
                        CASE
                            WHEN t.transaction_type = 15 and (li.transaction_status = 'S' OR li.transaction_status = 'F') THEN 'Y'
                            ELSE 'N'
                        END AS lombard_receipt_flag
                  FROM ma_transaction_master as t
                  LEFT JOIN adhoc_entries as ad on ad.orderid = t.parent_id AND ad.transaction_type = t.transaction_type
                  LEFT JOIN ma_transaction_master_details as td on td.ma_transaction_master_id = t.ma_transaction_master_id
                  LEFT JOIN ma_cms_merchant_on_boarding as cms on td.cms_ma_user_id = cms.ma_user_id
                  LEFT JOIN ma_shopping_order_master as shopping on t.ma_transaction_master_id = shopping.ma_transaction_master_id
                  LEFT JOIN ma_riskcovry_customer_details as ri on ri.orderid = t.aggregator_order_id 
                  LEFT JOIN ma_ipn_additional_logs as mial on mial.order_id = t.aggregator_order_id 
                  LEFT JOIN ma_lombard_policy_details as li on li.order_id = t.aggregator_order_id
                  `

      // Additional select columns
      // td.receiver_name AS bene_account_name,
      // td.receiver_account_number AS bene_account_number
      let tempSqlCondition = ''
      tempSqlCondition += (fields.ma_user_id) ? ` AND temp.ma_user_id = '${fields.ma_user_id}' ` : ''
      tempSqlCondition += (fields.userid) ? ` AND temp.userid = '${fields.userid}' ` : ''
      sql = sql.replace('#tempSqlCondition#', tempSqlCondition)

      if (Object.keys(fields).length !== 0 && fields.constructor === Object) {
        sql += ' WHERE '
      }
      Object.keys(fields).forEach((key, index) => {
        if (key === 'aggregator_order_id') {
          sql += `t.parent_id = "${fields[key]}"`
          // sql += `${key} = "${fields[key]}"`
        } else {
          sql += `t.${key} = "${fields[key]}"`
        }
        if (index + 1 !== Object.keys(fields).length) sql += ' AND '
      })
      if (Object.keys(fields).length !== 0 && fields.constructor === Object) {
        sql += ` AND t.addedon BETWEEN "${datefrom} 00:00:00" AND "${dateto} 23:59:59"`
      } else {
        sql += ` WHERE t.addedon BETWEEN "${datefrom} 00:00:00" AND "${dateto} 23:59:59"`
      }
      sql += validator.definedVal(bene_account_name) ? ` AND td.receiver_name LIKE '%${bene_account_name}%'` : ''
      sql += validator.definedVal(bene_account_number) ? ` AND td.receiver_account_number LIKE '%${bene_account_number}%'` : ''
      sql += validator.definedVal(aggregator_txn_id) ? ` AND t.aggregator_txn_id = ${aggregator_txn_id}` : ''
      sql += validator.definedVal(mobile_number) ? ` AND t.mobile_number = '${mobile_number}'` : ''
      sql += validator.definedVal(transaction_status) ? ` AND t.transaction_status = '${transaction_status}'` : ''
      sql += ' AND t.transaction_type NOT IN (36, 40, 42)'
      sql += ' AND NOT (t.transaction_type IN (\'64\', \'66\') AND t.amount = 0)'
      if (txn_source != '') {
        sql += ' AND t.txn_source != "EMITRA" '
      }
      sql += ` ORDER BY  t.addedon DESC LIMIT ${offset},${limit}`
      console.log(sql)
      const result = await this.rawQuery(sql, connection)

      log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'result', fields: result })
      let nextFlag = false
      if (result.length > 0) {
        const countSql = 'SELECT FOUND_ROWS() AS total'
        const countResult = await this.rawQuery(countSql, connection)
        if (countResult.length > 0) {
          const total = countResult[0].total
          // console.log('Total : ' + total)
          // console.log('Current : ' + (limit + offset))
          if ((limit + offset) < total) {
            nextFlag = true
          }
        }
      }
      // console.log('Next Flag : ' + nextFlag)
      log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'response', fields: result })
      const newResult = result.map(transaction => ({ ...transaction, transaction_type: transaction.transaction_type.toString() }))
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, transactionList: newResult }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async retailerTransactionDetails (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      var fieldsCopy = { ...fields }
      console.log('fieldsCopy', fieldsCopy)
      // Get user data
      var user = `SELECT user_type, email_id from ma_user_master WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid}`
      const user_result = await this.rawQuery(user, connection)
      console.log('user_result', user_result)
      log.logger({ pagename: 'transactionController.js', action: 'retailerTransactionDetails', type: 'user_result', fields: user_result })
      // End..........
      if (user_result.length > 0) {
        const distributor_list = fields.distributor_list
        const retailer_list = fields.retailer_list
        const datefrom = fields.datefrom
        const dateto = fields.dateto
        const limit = fields.limit
        const offset = fields.offset
        // const bene_account_name = fields.bene_account_name
        // const bene_account_number = fields.bene_account_number
        const aggregator_txn_id = fields.aggregator_txn_id
        const aggregator_order_id = fields.aggregator_order_id
        const mobile_number = fields.mobile_number
        delete fields.datefrom
        delete fields.dateto
        delete fields.limit
        delete fields.offset
        delete fields.ma_user_id
        delete fields.userid
        // delete fields.bene_account_name
        // delete fields.bene_account_number
        delete fields.aggregator_txn_id
        delete fields.aggregator_order_id
        delete fields.mobile_number
        delete fields.distributor_list
        delete fields.retailer_list
        console.log('fields', fields)
        log.logger({ pagename: 'transactionController.js', action: 'retailerTransactionDetails', type: 'fields after deleting', fields: fields })
        /**
         * In case of DMT Awaiting Settlement handled as Pending so following changes added
         * IF(transaction_type = 2 AND transaction_status = 'P','PND',transaction_status) as transaction_status,
         */
        var sql = `SELECT
                      SQL_CALC_FOUND_ROWS
                      t.ma_transaction_master_id,
                      allRT.profileid AS retailer_id,
                      CONCAT(allRT.firstname,' ',allRT.lastname) AS retailer_name,
                      t.parent_id AS aggregator_order_id,
                      t.transaction_id,
                      (t.amount + IF (t.commission_amount IS NULL, 0.0,t.commission_amount)) AS amount,
                      t.commission_amount,
                      IF((t.transaction_type = 2 OR t.transaction_type = 15 OR t.transaction_type = 6 OR t.transaction_type = 17 OR t.transaction_type = 5 OR t.transaction_type = 24 OR t.transaction_type = 31 OR t.transaction_type = 32) AND t.transaction_status = 'P','PND',t.transaction_status) as transaction_status,
                      t.transaction_type,
                      t.bank_rrn,
                      IF(t.mobile_number IS NULL,'N/A',t.mobile_number) AS mobile_number,
                      t.remarks,
                      t.addedon,
                      nd.utr_no,
                      (
                      CASE 
                      WHEN ium.integration_code = 'emitra' THEN (SELECT aw.updatedon FROM ma_allow_withdrawal aw WHERE aw.ma_user_id = t.ma_user_id LIMIT 1)
                      ELSE (SELECT mplm.addedon FROM ma_points_ledger_master mplm WHERE mplm.parent_transaction_master_id = t.ma_transaction_master_id LIMIT 1)
                      END
                      ) AS settlement_date,
                      COALESCE(dum.profileid,null) as distributor_id,
                      COALESCE(dum.company,'N/A') as distributor_company,
                      COALESCE(sdum.profileid ,null) as super_distributor_id,
                      COALESCE(sdum.company ,'N/A') as super_distributor_company
                  FROM ma_transaction_master as t
                  `
        //
        // td.receiver_name AS bene_account_name,
        // td.receiver_account_number AS bene_account_number
        // LEFT JOIN ma_transaction_master_details as td. on td.ma_transaction_master_id = t.ma_transaction_master_id
        if (user_result[0].user_type == 'SDT') {
          sql += ` join (SELECT mum.profileid, mum.userid, mum.firstname, mum.lastname, mum.distributer_user_master_id
                    FROM ma_user_master mum
                    JOIN (SELECT * FROM ma_user_master WHERE user_type = 'DT' AND distributer_user_master_id = ${fieldsCopy.ma_user_id} AND mer_user = 'mer' `
          sql += validator.definedVal(distributor_list) ? ` AND profileid IN (${distributor_list}) ` : ''
          sql += ` ) mm ON mum.distributer_user_master_id = mm.profileid
                    WHERE mum.mer_user = 'mer' 
                    AND mum.user_type = 'RT' `
          sql += (validator.definedVal(retailer_list) && validator.definedVal(distributor_list)) ? ` AND mum.profileid IN (${retailer_list}) ` : ''
          sql += ' ) as allRT on t.ma_user_id = allRT.profileid AND t.userid = allRT.userid '
        } else {
          sql += ` join (SELECT profileid, userid, firstname, lastname, distributer_user_master_id FROM ma_user_master mum WHERE mum.distributer_user_master_id = ${fieldsCopy.ma_user_id}  AND mum.mer_user = 'mer' AND user_type = 'RT' `
          sql += validator.definedVal(retailer_list) ? ` AND mum.profileid IN (${retailer_list}) ` : ''
          sql += ' ) as allRT on t.ma_user_id = allRT.profileid AND t.userid = allRT.userid '
        }

        sql += `
                LEFT JOIN ma_user_master AS dum ON allRT.distributer_user_master_id = dum.profileid AND dum.mer_user = 'mer'
                LEFT JOIN ma_user_master AS sdum ON dum.distributer_user_master_id = sdum.profileid AND sdum.mer_user = 'mer'
                LEFT JOIN ma_integration_user_master AS ium ON allRT.profileid = ium.ma_user_id
                LEFT JOIN ma_neft_details AS nd ON t.aggregator_order_id = nd.orderid `
        sql += ` WHERE t.addedon BETWEEN '${datefrom} 00:00:00' AND '${dateto} 23:59:59' `
        // sql += validator.definedVal(bene_account_name) ? ` AND td.receiver_name LIKE '%${bene_account_name}%'` : ``
        // sql += validator.definedVal(bene_account_number) ? ` AND td.receiver_account_number LIKE '%${bene_account_number}%'` : ``
        sql += validator.definedVal(aggregator_txn_id) ? ` AND t.aggregator_txn_id = ${aggregator_txn_id}` : ''
        sql += validator.definedVal(aggregator_order_id) ? ` AND t.aggregator_order_id = '${aggregator_order_id}'` : ''
        sql += validator.definedVal(mobile_number) ? ` AND t.mobile_number = '${mobile_number}'` : ''
        sql += ' ORDER BY  t.addedon DESC'

        // Check if date range more than 30 Days
        const diffTime = Math.abs((new Date(dateto)) - (new Date(datefrom)))
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        if (diffDays > 31) {
          var reports_request = `insert into ma_reports_request(request_params,request_status,report_type,userid,download_type, user_email,query) values ('${JSON.stringify(fieldsCopy)}', "I", 10, ${fieldsCopy.ma_user_id}, 1, "${user_result[0].email_id}", "${sql}")`
          log.logger({ pagename: 'transactionController.js', action: 'retailerTransactionDetails', type: 'reports_request', fields: reports_request })

          const reports_request_result = await this.rawQuery(reports_request, connection)
          log.logger({ pagename: 'transactionController.js', action: 'retailerTransactionDetails', type: 'reports_request_result', fields: reports_request_result })
          if (reports_request_result.affectedRows > 0) {
            return { status: 400, respcode: 1001, message: 'Report will send you on email shortly!.' }
          }
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        }
        // End..................................

        sql += ` LIMIT ${offset},${limit}`
        console.log('sql', sql)
        log.logger({ pagename: 'transactionController.js', action: 'retailerTransactionDetails', type: 'sql', fields: sql })
        const result = await this.rawQuery(sql, connection)
        console.log('result', result)
        // log.logger({ pagename: 'transactionController.js', action: 'retailerTransactionDetails', type: 'sql result', fields: result.length })

        let nextFlag = false
        if (result.length > 0) {
          const countSql = 'SELECT FOUND_ROWS() AS total'
          const countResult = await this.rawQuery(countSql, connection)
          if (countResult.length > 0) {
            const total = countResult[0].total
            if ((limit + offset) < total) {
              nextFlag = true
            }
          }
        }
        log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'response', fields: result })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, retailerTransactionList: result }
      }
      return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'transactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async mismatchTransactions (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'mismatchTransactions', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const datefrom = fields.datefrom
      const dateto = fields.dateto
      delete fields.datefrom
      delete fields.dateto
      var sql = 'SELECT *  FROM ma_transaction_master'
      if (Object.keys(fields).length !== 0 && fields.constructor === Object) {
        sql += ' WHERE '
      }
      Object.keys(fields).forEach((key, index) => {
        sql += `${key} = "${fields[key]}"`
        if (index + 1 !== Object.keys(fields).length) sql += ' AND '
      })
      if (Object.keys(fields).length !== 0 && fields.constructor === Object) {
        sql += ` AND updatedon BETWEEN "${datefrom} " AND "${dateto} "`
      } else {
        sql += ` WHERE updatedon BETWEEN "${datefrom} " AND "${dateto} "`
      }
      sql += ' ORDER BY  updatedon DESC '
      const result = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'transactionController.js', action: 'mismatchTransactions', type: 'response', fields: result })
      return result
    } finally {
      connection.release()
    }
  }

  /**
     * Creates a new transaction
     */
  static async createTransaction (_, fields, connection = null) {
    log.logger({ pagename: 'transactionController.js', action: 'createTransaction', type: 'request', fields: fields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // Check if User exists or not
      /* NEW CHANGES : MERCHANT ONBOARDING CHANGES */
      var sql = `select ma_user_master_id,sales_id,state,user_type,activation_level from ma_user_master where profileid=${fields.ma_user_id} AND userid=${fields.userid} limit 1`
      log.logger({ pagename: 'transactionController.js', action: 'createTransaction', type: 'sql', fields: sql })
      const userDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'transactionController.js', action: 'createTransaction', type: 'userDetails', fields: userDetails })
      if (userDetails.length <= 0) {
        return { status: 400, message: 'Invalid user' }
      }
      fields.sales_id = userDetails[0].sales_id

      const { activation_level } = userDetails[0]

      /* NEW CHANGES : MERCHANT ONBOARDING CHANGES */
      if (activation_level == mccLevelSettingController.MCC_ACTIVATION_LEVEL_1) {
        const activationLevelCheckResult = await this.activationLevelCheck({
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          transaction_type: fields.transaction_type,
          state: userDetails[0].state,
          user_type: userDetails[0].user_type,
          transactionAmount: fields.amount,
          connection
        })

        if (activationLevelCheckResult.status != 200) return activationLevelCheckResult
      }

      /* NEW CHANGES : MERCHANT ONBOARDING CHANGES */
      // Check merchant has the channels assigned
      if (fields.transaction_type != 20 &&
        fields.transaction_type != 23 &&
        fields.transaction_type != 27 &&
        fields.transaction_type != 10 &&
        fields.transaction_type != 67 &&
        fields.transaction_type != 68 &&
        fields.transaction_type != 69 &&
        activation_level == mccLevelSettingController.MCC_ACTIVATION_LEVEL_2
      ) {
        const transactionChannelExists = await this.checkChannelExists(fields.transaction_type, fields.ma_user_id, connection, userDetails[0].state, userDetails[0].user_type)
        console.log('transactionChannelExists : ' + transactionChannelExists)
        if (transactionChannelExists === false) {
          log.logger({ pagename: 'transactionController.js', action: 'createTransaction - transaction channel exists', type: 'response', fields: {} })
          return { status: 400, respcode: 1028, message: 'Transaction not allowed as channel is not assigned' }
        }
      }

      if (fields.sales_id != undefined && fields.sales_id != null && fields.sales_id > 0) {
        var hierarchySql = `select sales_hierarchy_details_id from sales_hierarchy_details where TSM_id=${fields.sales_id} and record_status='active' limit 1`
        const hierarchyDetails = await this.rawQuery(hierarchySql, connection)
        log.logger({ pagename: 'transactionController.js', action: 'createTransaction', type: 'response-hierarchyDetails', fields: hierarchyDetails })
        if (hierarchyDetails.length > 0) {
          fields.hierarchy_id = hierarchyDetails[0].sales_hierarchy_details_id
        }
      }
      // Condition for parentid
      fields.parent_transaction_master_id = 0
      if ((fields.aggregator_order_id).includes('-')) {
        fields.parent_id = (fields.aggregator_order_id).substr(fields.aggregator_order_id.lastIndexOf('-') + 1)
      } else {
        fields.parent_id = fields.aggregator_order_id
      }
      if (fields.transaction_status && (fields.transaction_status === 'R' || fields.transaction_status === 'V')) {
        // Update parent is_refund flag to 1
        // await this.rawQuery(`UPDATE ma_transaction_master SET is_refund = '1' WHERE parent_id = '${fields.parent_id}'`, connection)
        this.updateWhereData(connection, {
          data: { is_refund: '1' },
          id: fields.parent_id,
          where: 'parent_id'
        })
        fields.parent_transaction_master_id = await this.getTransactionDetailsByParentId(fields.parent_id, connection)
      }
      if (fields.transaction_status && fields.transaction_status === 'REV') {
        fields.parent_transaction_master_id = await this.getTransactionDetailsByParentId(fields.parent_id, connection)
      }
      fields.is_refund = '0'

      /* NEW CHANGES : LEIN BALANCE CHECK */
      const isTransactionAmountAboveLienBalanceResp = await lienBalanceController.isTransactionAmountAboveLienBalance({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        transactionType: fields.transaction_type,
        transactionAmount: parseFloat(fields.amount) + parseFloat(fields.commission_amount || 0),
        connectionRead: connection
      })

      log.logger({ pagename: 'transactionController.js', action: 'isTransactionAmountAboveLienBalance', type: 'response', fields: isTransactionAmountAboveLienBalanceResp })
      if (isTransactionAmountAboveLienBalanceResp.status != 200) return isTransactionAmountAboveLienBalanceResp

      /* LEIN BALANCE CHECK END */

      let ma_bank_on_boarding_id = 0
      let kyc_status = 'NONKYC'
      if ('kyc_status' in fields) {
        kyc_status = fields.kyc_status
        delete fields.kyc_status
      }
      if ('ma_bank_on_boarding_id' in fields) {
        ma_bank_on_boarding_id = fields.ma_bank_on_boarding_id
        delete fields.ma_bank_on_boarding_id
      }
      if ('collectmoney_type' in fields) {
        delete fields.collectmoney_type
        delete fields.transactionType
        delete fields.bank_code
      }
      if ('provider_name' in fields) {
        delete fields.provider_name
      }

      // check merchant_type is emitra
      if ('merchant_type' in fields && fields.merchant_type == 'emitra') {
        fields.txn_source = 'EMITRA'
      }
      delete fields.merchant_type

      const _result = await this.insert(connection, {
        data: fields
      })
      log.logger({ pagename: 'transactionController.js', action: 'createTransaction', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        // Check if integrated merchant
        const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid} limit 1`
        const integratedMer = await this.rawQuery(userSQL, connection)
        console.log("SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =" + fields.userid)
        /* AEPS EMITRA CHANGES ADDED */
        if (integratedMer.length > 0 && (fields.transaction_type == '2' || fields.transaction_type == '21' || fields.transaction_type == '5')) {
          fields.action = 'CREATETXN'
          fields.aggregator_user_id = integratedMer[0].aggregator_user_id
          fields.ma_bank_on_boarding_id = ma_bank_on_boarding_id
          fields.kyc_status = kyc_status
          const res = await integrated.index(fields, connection)
          console.log('integrated returned this', res)
          if (res.status != 200) {
            return res
          }
        }

        const surchargeflag = (fields.commission_amount > 0)
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transaction_id: _result.insertId, surcharge: fields.commission_amount, amount: fields.amount + fields.commission_amount, surchargeflag: surchargeflag }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028], respcode: 1028 }
      }
    } catch (err) {
      let mesg = ''
      if (err.code && err.code === 'ER_DUP_ENTRY') {
        mesg = 'Kindly refresh the page ,and try again after 5 mins'
      } else {
        mesg = 'Fail: Something went wrong.'
      }
      log.logger({ pagename: 'transactionController.js', action: 'createTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: mesg, respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
     * Creates database logs for AEPS Transaction
     */
  static async createAEPSTransactionLog (_, fields, connection = null) {
    log.logger({ pagename: 'transactionController.js', action: 'createAEPSTransactionLog', type: 'request', fields: fields })
    let isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // Changes for bank down notification
      const aepsTransactionLogsQuery = `insert into ma_aeps_transaction_logs (ma_user_id, order_id, amount, ma_status, transaction_status, aeps_mode, verify_status, aggregator_bank, bank_id) values ( '${fields.ma_user_id}', '${fields.aggregator_order_id}', '${fields.amount}', 'I', 'I', '${fields.transactionType}', 'P', '${fields.bank_code}','${fields.bank_id || 0}')`
      const aepsTransactionLogs = await this.rawQuery(aepsTransactionLogsQuery, connection)
      return { status: 200, responseCode: 1000, aepsTransactionLogs: aepsTransactionLogs }
    } catch (err) {
      console.log('error>>', err)
      let mesg = ''
      if (err.code && err.code === 'ER_DUP_ENTRY') {
        mesg = 'Kindly refresh the page ,and try again after 5 mins'
      } else {
        mesg = 'Fail: Something went wrong.'
      }
      log.logger({ pagename: 'transactionController.js', action: 'createAEPSTransactionLog', type: 'catcherror', fields: err })
      return { status: 400, message: mesg, respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
     * Updates database logs for AEPS Transaction
     */
  static async updateAEPSTransactionLog (_, fields, connection = null) {
    log.logger({ pagename: 'transactionController.js', action: 'updateAEPSTransactionLog', type: 'request', fields: fields })
    let isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      const _result = await mySQLWrapper.createTransactionalQuery({
        query: `UPDATE ??
                        SET ?
                        WHERE ?? = ?;`,
        params: ['ma_aeps_transaction_logs', fields, 'order_id', fields.order_id],
        connection
      })

      console.log('_result_result_result_result', _result)
      if (_result.affectedRows > 0) {
        return { status: 200, responseCode: 1000 }
      }
      return { status: 400, responseCode: 1001 }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'updateAEPSTransactionLog', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
  * Creates a transaction details
  */
  static async createTransactionDetails (_, fields, connection = null) {
    log.logger({ pagename: 'transactionController.js', action: 'createTransactionDetails', type: 'request', fields: fields })
    var isSet = false
    try {
      console.log('---Transaction Details---', fields)
      return await transactionDetailsController.createTransactionDetails(_, fields, connection)
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'createTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
   * Updates a transaction Entry
   * @param {*} _
   * @param {aggregator_order_id:string, aggregator_txn_id:string, transaction_status:string, amount:Number, bank_rrn?:string, connection:Promise<mySQLWrapper.getConnectionFromPool()>, transaction_reason?:string } param1
   * @returns
   */
  // eslint-disable-next-line camelcase
  static async updateTransaction (_, { aggregator_order_id, aggregator_txn_id, transaction_status, amount, bank_rrn = null, connection = null, transaction_reason = null }) {
    log.logger({ pagename: 'transactionController.js', action: 'updateTransaction', type: 'request', fields: { aggregator_order_id, aggregator_txn_id, transaction_status, amount, bank_rrn, transaction_reason } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      if (isSet) await mySQLWrapper.beginTransaction(connection)
      console.log('Details', transaction_status,
        aggregator_txn_id,
        amount,
        bank_rrn)
      let transUpdateObj = {}
      if (transaction_reason !== null) {
        transUpdateObj = {
          transaction_status,
          aggregator_txn_id,
          amount,
          bank_rrn,
          transaction_reason
        }
      } else {
        transUpdateObj = {
          transaction_status,
          aggregator_txn_id,
          amount,
          bank_rrn
        }
      }
      const _result = await this.updateWhere(connection, {
        id: aggregator_order_id,
        where: 'aggregator_order_id',
        data: transUpdateObj
      })
      log.logger({ pagename: 'transactionController.js', action: 'updateTransaction', type: 'response', fields: _result })
      if (isSet) await mySQLWrapper.commit(connection)
      console.log('Details', _result)
      if (_result.affectedRows > 0) {
        /* RISK MANAGEMENT Changes */
        if (transaction_status) {
          const resp = await this.saveRiskAnalysisRecord({ where: 'aggregator_order_id', id: aggregator_order_id, transaction_status, connection })
          log.logger({ pagename: 'transactionController.js', action: 'updateWhereData', type: 'saveRiskAnalysisRecord', fields: resp })
        }

        // sent notification
        /* comment below code - as we are using SendNotification function to notify user - 20-02-2025
        const transactionsql = `SELECT * FROM ma_transaction_master WHERE aggregator_order_id = '${aggregator_order_id}'`
        const transactionData = await this.rawQuery(transactionsql, connection)
        console.log('txndata', transactionData.map(e => common_fns.maskValue(e, 'customer_aadhaar')))
        // var data = { recipient: transactionData[0].gcmkey, message: `Transaction for amount ${transactionData[0].amount} against orderid ${transactionData[0].aggregator_order_id} was completed successfully` }
        */
        // notify.sendNotifications(data)
        return { status: 200, message: 'success' }
      } else {
        return { status: 401, message: 'fail' }
      }
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(connection)
      log.logger({ pagename: 'transactionController.js', action: 'updateTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (isSet) connection.release()
    }
  }

  /**
   * This function is used to update the transaction_master_details table by aggregator_order_id
   */
  static async updateTransactionDetails (updateFields, connection) {
    log.logger({ pagename: 'transactionController.js', action: 'updateTransactionDetails', type: 'request', fields: updateFields })
    try {
      return await transactionDetailsController.updateTransactionDetails(updateFields, connection)
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'updateTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * This api is used to create transaction based on transaction type in Initiated state
   * @param {*} _
   * @param {*} transactionFields
   */
  static async initiateTransaction (_, transactionFields) {
    log.logger({ pagename: 'transactionController.js', action: 'initiateTransaction', type: 'request', fields: common_fns.maskValue(transactionFields, 'customer_aadhaar') })
    log.logger({ pagename: 'transactionController.js', action: 'initiateTransaction', type: 'request', fields: { _ } })
    let isSet = false
    let connection = null
    if (typeof transactionFields.connection === 'undefined') {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    } else {
      connection = transactionFields.connection
    }
    try {
      /* RISK MANAGEMENT Changes : NEW CHANGES */
      let additionalData = {}
      if ('additionalData' in transactionFields) {
        additionalData = transactionFields.additionalData
        delete transactionFields.additionalData
      }

      if (_ && _.ip_address) {
        additionalData.ip_address = _.ip_address
      }

      const transactionDetailsFields = Object.assign({}, transactionFields)

      // Transaction Begin
      if (transactionFields.transaction_type != '17' && transactionFields.transaction_type != '64' && transactionFields.mobile_number.length != 10) {
        return { status: 400, message: 'Fail : Invalid Mobile NUmber' }
      }
      // await mySQLWrapper.beginTransaction(connection)

      // Validation for user exists
      const sql = `SELECT ma_user_master_id,state,user_type FROM ma_user_master WHERE profileid = '${transactionFields.ma_user_id}' AND userid = '${transactionFields.userid}'`
      const userData = await this.rawQuery(sql, connection)
      if (userData.length <= 0) {
        // Transaction Rollback
      //  await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'fail : User does not exists.' }
      }

      // restrict topup for retailer
      /* if (transactionFields.transaction_type === '1') {
        if (userData[0].user_type == 'RT') {
          await mySQLWrapper.rollback(connection)
          return { status: 400, message: 'Fail : Online Transaction is disabled' }
        }
      } */

      let commission = 0
      // Amount validation for adhaar pay
      if (transactionFields.transaction_type == 10 && transactionFields.transactionType == 'AP') {
        commission = transactionFields.surcharge_amount
        transactionDetailsFields.utility_name = 'AP'
        transactionDetailsFields.customer_email = transactionFields.customer_email
        const adpay_min_limit = await common.getSystemCodes(this, util.adpay_min_limit, connection)
        if (transactionFields.amount < adpay_min_limit) return { status: 400, respcode: 1028, message: `Minimum amount ${adpay_min_limit} required` }
        const adpay_max_limit = await common.getSystemCodes(this, util.adpay_max_limit, connection)
        console.log('Adhar max', adpay_max_limit)
        console.log('trans field', transactionFields.amount)
        if (transactionFields.amount > adpay_max_limit) return { status: 400, respcode: 1028, message: `Maximum amount should not be greater than  ${adpay_max_limit}` }
        // Min max amount
        //  transactionFields.transactionType = transactionFields.collectmoney_type
        const collectMoneyTransactionLog = await this.createAEPSTransactionLog(_, transactionFields, connection)
        console.timeEnd('createCollectMoneyTransactionLog time >>>>')
        console.log('CollectMoneytransactionLog', collectMoneyTransactionLog)
        if (collectMoneyTransactionLog.status == 400) {
          return { status: 400, respcode: 1001, message: collectMoneyTransactionLog.message ? collectMoneyTransactionLog.message : errorMsg.responseCode[1001] }
        }
        delete transactionFields.bank_code
        delete transactionFields.transactionType
        delete transactionFields.surcharge_amount
        delete transactionFields.customer_email
      }

      // AEPS transaction amount validation
      var transactionid = transactionFields.aggregator_order_id
      if (['5', '40', '42'].includes(transactionFields.transaction_type)) {
        const AepstransactionResponse = await this.createAEPSTransactionLog(_, transactionFields, connection)
        console.log('AepstransactionResponse', AepstransactionResponse)
        if (AepstransactionResponse.status == 400) {
          return { status: 400, respcode: 1001, message: AepstransactionResponse.message ? AepstransactionResponse.message : errorMsg.responseCode[1001] }
        }

        /* AEPS EMITRA CHANGE : AMOUNT and 'transaction_ref_number' CHECK REMOVED */
        /* const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${transactionFields.userid}`
        const integratedMer = await this.rawQuery(userSQL, connection)
        if (integratedMer.length > 0) {
          const sqlIntReq = `SELECT * from ma_integration_request where retailer_user_id='${integratedMer[0].aggregator_user_id}' and request_status = 'active' order by ma_integration_request_id desc limit 1`
          const dataIntReq = await this.rawQuery(sqlIntReq, connection)
          transactionid = dataIntReq[0].transaction_ref_number
          if (transactionFields.amount != dataIntReq[0].amount) {
            return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015] }
          }
        } */

        if (transactionFields.transaction_type == 5 && transactionFields.amount < util.AEPSMinAmount) {
          // Transaction Rollback
          // await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1028, message: 'Minimum amount ' + util.AEPSMinAmount + ' required' }
        }

        // No tranaction for Emitra BE and MS
        if (transactionFields.merchant_type === 'emitra' && ['MS', 'BE'].includes(transactionFields.transactionType)) {
          return { status: 400, respcode: 14014, message: errorMsg.responseCode[14014] }
        }

        // Remove unnecessory variables
        delete transactionFields.transactionType
        delete transactionFields.bank_code
        // delete transactionFields.merchant_type

        transactionFields.connection = connection
        const availableSlabs = await aepsDistribution.getDistribution(transactionFields)
        if (availableSlabs.status === 400) {
          // Transaction Rollback
          // await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 14014, message: 'Crossed Maximum amount' } // need clarity here about slab
        }
        delete transactionFields.transaction_mode
        console.log('availableSlabs>>>>>>>>>', availableSlabs)
        if (availableSlabs.status === 200) {
          // if Customer charge is 0 no transction entries
          // if (availableSlabs[0].customer_charges === 0) {
          //   return { status: 400, respcode: 14014, message: errorMsg.responseCode[14014] }
          // }
          // Merchant wallet balance check for customer charges
          if (availableSlabs[0].customer_charges > 0 && availableSlabs[0].charge_source === 'M') {
            const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
              ma_user_id: transactionFields.ma_user_id,
              amount: availableSlabs[0].customer_charges,
              transactionType: transactionFields.transaction_type,
              connection
            })
            if (pointsDetailsEntries.status === 400) {
              return pointsDetailsEntries
            }
          }
        }
      }
      // Changes for bank down notification
      delete transactionFields.bank_id

      if (transactionFields.transaction_type === '31' || transactionFields.transaction_type === '32') {
        transactionid = transactionFields.transaction_id
      }
      let pointsFactor = 1
      /* if (transactionFields.transaction_type == '6') {
        const bbpssurcharge = await bbpsinecntive.getSurcharge(_, {
          ma_user_id: transactionFields.ma_user_id,
          amount: transactionFields.amount,
          stateid: userData[0].state,
          connection: connection
        })
        if (bbpssurcharge.status !== undefined && bbpssurcharge.status === 200) {
          commission = bbpssurcharge.commissionVal
        }
      } */
      /* else {
        const commissionData = await comission.getCommission(_, {
          ma_user_id: transactionFields.ma_user_id,
          ma_commission_type: transactionFields.transaction_type,
          ma_deduction_type: 2,
          amount: transactionFields.amount,
          connection: connection
        })
        if (commissionData.status !== undefined && commissionData.status === 200) {
          commission = commissionData.commissionVal
        }
      } */
      const pointsFactorData = await pointsrate.getGlobalPointsRate(_, { connection })
      if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
        pointsFactor = pointsFactorData.points_value
      }
      var gcmval = 0
      if (transactionFields.gcmid !== 'undefined' && transactionFields.gcmid != null) {
        const sqlgcm = `SELECT gcmid FROM ma_login_details WHERE apploginid = '${transactionFields.gcmid}'`
        console.log(sqlgcm)
        const gcmData = await this.rawQuery(sqlgcm, connection)
        console.log('gcmdata', gcmData)
        if (gcmData[0].gcmid === 'undefined' || gcmData[0].gcmid == null) {
          gcmval = 0
        } else {
          gcmval = gcmData[0].gcmid
        }
      }
      // transactionFields.commission_amount = commission
      transactionFields.points_factor = pointsFactor
      transactionFields.transaction_id = transactionid
      transactionFields.gcmkey = gcmval
      delete transactionFields.connection
      delete transactionFields.gcmid

      let billPayTransactionField = {}
      let storeToBillPay = false
      // BBPS/RECHARGE CASE DELETE
      if (transactionFields.transaction_type == '6' || transactionFields.transaction_type == '17') {
        billPayTransactionField = { ...transactionFields, connection }

        if ('makePaymentResponse' in transactionFields) {
          billPayTransactionField.mer_dtls = transactionFields.makePaymentResponse.mer_dtls
          billPayTransactionField.invoice_id = transactionFields.makePaymentResponse.invoice_id
          billPayTransactionField.makepayment_request_id = transactionFields.makePaymentResponse.makepayment_request_id
          billPayTransactionField.consent_flag = transactionFields.consent_flag
          delete transactionFields.makePaymentResponse
        }

        storeToBillPay = true
        delete transactionFields.provider_name
        delete transactionFields.provider_id
        delete transactionFields.utility_name
        delete transactionFields.utility_id
        delete transactionFields.action_type
        delete transactionFields.consent_flag
      }

      if (transactionFields.transaction_type == '6') {
        const bbpssurcharge = await bbpsinecntive.getSurcharge(_, {
          ma_user_id: transactionFields.ma_user_id,
          amount: transactionFields.amount,
          stateid: userData[0].state,
          connection: connection
        })
        if (bbpssurcharge.status !== undefined && bbpssurcharge.status === 200) {
          commission = bbpssurcharge.commissionVal
        }
      }

      // Adding customer charge in commision amount for CMS
      /* if (transactionFields.transaction_type == '24') {
        commission = transactionFields.customer_charge
        delete transactionFields.customer_charge
      } */

      transactionFields.commission_amount = commission

      // -------------- Remove transaction details fields from transactionFields----------------
      if (typeof transactionFields.customer_name !== 'undefined') { delete transactionFields.customer_name }
      if (typeof transactionFields.customer_aadhaar !== 'undefined') { delete transactionFields.customer_aadhaar }
      if (typeof transactionFields.bank_name !== 'undefined') { delete transactionFields.bank_name }
      if (typeof transactionFields.terminalid !== 'undefined') { delete transactionFields.terminalid }
      if (typeof transactionFields.cms_ma_user_id !== 'undefined') { delete transactionFields.cms_ma_user_id }
      if (typeof transactionFields.cms_unique_id !== 'undefined') { delete transactionFields.cms_unique_id }
      if (typeof transactionFields.affiliated_id !== 'undefined') { delete transactionFields.affiliated_id }
      if (typeof transactionFields.category_master_id !== 'undefined') { delete transactionFields.category_master_id }
      if (typeof transactionFields.subcategory_code !== 'undefined') { delete transactionFields.subcategory_code }
      if (typeof transactionFields.provider_id !== 'undefined') { delete transactionFields.provider_id }
      if (typeof transactionFields.provider_name !== 'undefined') { delete transactionFields.provider_name }
      if (typeof transactionFields.utility_id !== 'undefined') { delete transactionFields.utility_id }
      if (typeof transactionFields.utility_name !== 'undefined') { delete transactionFields.utility_name }
      if (typeof transactionFields.action_type !== 'undefined') { delete transactionFields.action_type }
      if (typeof transactionFields.customer_mobile !== 'undefined') { delete transactionFields.customer_mobile }
      if (typeof transactionFields.customer_email !== 'undefined') { delete transactionFields.customer_email }
      if (typeof transactionFields.company_id !== 'undefined') { delete transactionFields.company_id }
      if (typeof transactionFields.uuid !== 'undefined') { delete transactionFields.uuid }
      if (transactionFields.form_data) { delete transactionFields.form_data }
      if (transactionFields.ma_cms_merchant_on_boarding_id) { delete transactionFields.ma_cms_merchant_on_boarding_id }
      if (transactionFields.cms_client_id) { delete transactionFields.cms_client_id }
      if (transactionFields.cms_merchant_type) { delete transactionFields.cms_merchant_type }
      if (transactionFields.cms_userid) { delete transactionFields.cms_userid }
      if (transactionFields.cms_partner_id) { delete transactionFields.cms_partner_id }
      if (transactionFields.cms_parent_partner_id) { delete transactionFields.cms_parent_partner_id }

      await mySQLWrapper.beginTransaction(connection)
      // Create entry in transaction master
      const transactionResponse = await this.createTransaction(_, transactionFields, connection)
      console.log('transactionResponse', transactionResponse)

      if (transactionResponse.status === 200) {
        // Insert transaction master details
        transactionDetailsFields.ma_transaction_master_id = transactionResponse.transaction_id
        const formData = { amount: transactionFields.amount || '', mobile: transactionFields.mobile_number || '', ...transactionDetailsFields.form_data }
        transactionDetailsFields.form_data = JSON.stringify(formData)
        const transactionDetailsResponse = await transactionDetailsController.createTransactionDetails(_, transactionDetailsFields, connection)
        console.log('transactionDetailsResponse', transactionDetailsResponse)
        if (transactionDetailsResponse.status !== 200) {
          // Transaction Rollback
          await mySQLWrapper.rollback(connection)
          return transactionDetailsResponse
        }

        if (storeToBillPay) {
          billPayTransactionField.ma_transaction_master_id = transactionResponse.transaction_id
          const billPayTransactionCtrl = require('../billpayTransaction/billpayTransaction')
          const billpayTransResponse = await billPayTransactionCtrl.createEntry(_, billPayTransactionField)
          // console.log('billpayTransResponse', billpayTransResponse)
          if (billpayTransResponse.status === 400) {
            // Transaction Rollback
            await mySQLWrapper.rollback(connection)
            return billpayTransResponse
          }
          transactionResponse.ma_billpay_transactionid = billpayTransResponse.ma_billpay_transactionid
        }

        // Transaction commit
        await mySQLWrapper.commit(connection)

        /*
        RISK MANAGEMENT Changes : NEW CHANGES
        AEPS CashWithdrawal  : 5
        COLLECTMONEY/ADPAY   : 10
        AEPS Balance Enquiry : 42
        AEPS Mini  Statement : 40
        */
        const transaction_type = transactionFields.transaction_type.toString()
        if (['5', '10', '42', '40'].includes(transaction_type)) {
          const requestParams = {
            ma_user_id: transactionFields.ma_user_id,
            userid: transactionFields.userid,
            aggregator_order_id: transactionFields.aggregator_order_id,
            amount: transactionFields.amount,
            transaction_status: transactionFields.transaction_status,
            transaction_type: transaction_type,
            customer_name: transactionFields.customer_name || '',
            customer_phone: transactionFields.mobile_number || '',
            customer_email: transactionFields.customer_email || '',
            aadhaar_last_4: transactionDetailsFields.customer_aadhaar ? transactionDetailsFields.customer_aadhaar.substr(-4, 4) : '',
            account_number: '',
            ben_mobile_number: '',
            beneficiary_name: '',
            bank_name: '',
            ip_address: additionalData.ip_address,
            latitude: additionalData.latitude || '',
            longitude: additionalData.longitude || ''
          }
          const checkRiskAnalysisResp = await riskManagementController.checkTransactionRiskAnalysis({ requestParams, connection })
          log.logger({ pagename: 'transactionController.js', action: 'checkTransactionRiskAnalysis', type: 'response', fields: checkRiskAnalysisResp })
          // if (checkRiskAnalysisResp.status != 200) return checkRiskAnalysisResp
        }

        /* End RISK MANAGEMENT Changes : NEW CHANGES */

        return transactionResponse
      } else {
        // Transaction Rollback
        await mySQLWrapper.rollback(connection)
        return transactionResponse
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'initiateTransaction', type: 'response', fields: err })
      return { status: 400, message: 'fail : ' + err.message }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
   * This api is used to update Aeps Transaction for Payment response from App
   * @param {*} _
   * @param {*} updateFields
   */
  static async updateAepsTransaction (_, updateFields) {
    log.logger({ pagename: 'transactionController.js', action: 'updateAepsTransaction', type: 'request', fields: updateFields })
    // console.log(updateFields)
    let isSet = true
    let connection = {}
    if (updateFields.connection) {
      connection = updateFields.connection
      isSet = false
    } else {
      connection = await mySQLWrapper.getConnectionFromPool()
    }
    try {
      updateFields.connection = connection
      /* if (updateFields.transaction_status === 'P') {
        updateFields.transaction_status = 'S'
      } */

      // await mySQLWrapper.beginTransaction(connection)
      if ((typeof updateFields.transaction_status !== 'undefined') && (updateFields.transaction_status === 'S' || updateFields.transaction_status === 'F' || updateFields.transaction_status === 'P')) {
        const sql = `SELECT transaction_status, amount, commission_amount, ma_transaction_master_id, aggregator_order_id, addedon, mobile_number FROM ma_transaction_master WHERE ma_user_id = ${updateFields.ma_user_id} AND aggregator_order_id = '${updateFields.orderid}' AND transaction_type = '${updateFields.transaction_type}' LIMIT 1 `
        let transactionData = await this.rawQuery(sql, connection)
        console.log('sql', sql)
        console.log('queryresult', transactionData)
        if (transactionData.length > 0) {
          transactionData = transactionData[0]
          // if (transactionData.transaction_status === 'I' || updateFields.ltsFlag) {
          if (transactionData.transaction_status === 'I' || transactionData.transaction_status === 'P' || updateFields.ltsFlag) {
            if (transactionData.amount == updateFields.amount) {
              updateFields.bank_rrn = updateFields.rrn
              updateFields.aggregator_order_id = updateFields.orderid
              const userid = updateFields.userid
              delete updateFields.userid
              const ledgerCronId = updateFields.ledger_cron_id
              delete updateFields.ledger_cron_id
              let updateTxn = await this.updateTransaction(_, updateFields)
              if (updateTxn.status === 200) {
                // Save Bank RRN & Balance - Start
                let bank_rrn_val = ''
                let bank_balance_val = ''
                if (updateFields.rrn !== null) {
                  bank_rrn_val = updateFields.rrn
                }

                if (updateFields.bank_balance !== null) {
                  bank_balance_val = updateFields.bank_balance
                }

                const ma_transaction_master_id = transactionData.ma_transaction_master_id
                const updateTranDetailsSql = `UPDATE ma_transaction_master_details set bank_rrn='${bank_rrn_val}', bank_balance='${bank_balance_val}' where ma_transaction_master_id = '${ma_transaction_master_id}'`
                console.log('updatetransactiondetails', updateTranDetailsSql)
                await this.rawQuery(updateTranDetailsSql, connection)
                // End
                if (updateFields.transaction_status === 'S') {
                  let ledgerEntries
                  // save order detaisl in adpay cron table - transaction_type = 10
                  // add entry in cron table
                  /* const saveAdpayOrderVerifyData = `Insert into ma_aeps_aadhaar_ledger_log
                                        (ma_user_id, userid, order_id, amount, transaction_type)
                                        VALUES (${updateFields.ma_user_id},${userid},'${updateFields.orderid}','${updateFields.amount}','${updateFields.transaction_type}')`
                  const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, connection)
                  log.logger({ pagename: require('path').basename(__filename), action: 'updateAepsTransaction', type: 'Aeps log result', fields: { saveAdpayOrderVerifyResult } })

                  const ledgerCronId = saveAdpayOrderVerifyResult.insertId */
                  console.log('ledgerCronId-AEPS>>>>>>>', ledgerCronId)
                  // new table record insert
                  // INSERT QUERY
                  const aeps_credit_log = `INSERT INTO ma_aeps_credits_log (aggregator_order_id, transaction_status, transaction_amt_credits, incentive_amt_credits) values ('${transactionData.aggregator_order_id}', '${transactionData.transaction_status}', 'N', 'N' )`

                  console.log('INSERT_QUERY: ', aeps_credit_log)
                  const aeps_credit_log_res = await this.rawQuery(aeps_credit_log, connection)
                  console.log('INSERT_QUERY_RES: ', aeps_credit_log_res)
                  // bgin
                  await mySQLWrapper.beginTransaction(connection)

                  if (updateFields.transaction_type == 5) {
                    ledgerEntries = await ledgerEntriesCont.aepsLedgerEntries(updateFields, userid)
                  } else {
                    ledgerEntries = await ledgerEntriesCont.aepsNoAmountLedgerEntries(updateFields, userid)
                  }
                  // if ledger entries success -> Mark transaction_amt_credits as yes
                  if (ledgerEntries.status != 200) {
                    await mySQLWrapper.rollback(connection)
                  } else {
                    const sql = `UPDATE ma_aeps_credits_log set transaction_amt_credits = 'Y' where ma_aeps_credits_log_id = ${aeps_credit_log_res.insertId}`
                    const update_transaction_amt_credits_log = await this.rawQuery(sql, connection)
                    await mySQLWrapper.commit(connection)
                  }

                  console.log('ledgerEntries>>>>>>>', ledgerEntries)
                  if (ledgerEntries.status === 200) {
                    // update ledger entries status to 'S' in adpay cron table -- Start
                    const sql_ledger = `UPDATE ma_aeps_aadhaar_ledger_log set ledger_status = 'S' where id = ${ledgerCronId}`
                    const update_transaction_amt_ledger_log = await this.rawQuery(sql_ledger, connection)

                    // NEW mySQL begin transaction for incentives entry
                    await mySQLWrapper.beginTransaction(connection)

                    const incentiveEntries = await aepsDistribution.incentiveEntries(updateFields, userid)
                    console.log('INCENTIVE_ENTRIES: ', incentiveEntries)
                    if (incentiveEntries.status === 200) {
                      // update incentive entries status to 'S' in adpay cron table -- Start
                      const sql_incentive = `UPDATE ma_aeps_aadhaar_ledger_log set incentive_status = 'S', cron_status = 'V', remarks = 'REALTIME' where id = ${ledgerCronId}`
                      const update_transaction_amt_ledger_log = await this.rawQuery(sql_incentive, connection)

                      const sql = `UPDATE ma_aeps_credits_log set incentive_amt_credits = 'Y' where ma_aeps_credits_log_id = ${aeps_credit_log_res.insertId}`
                      console.log('Incentive amount credit log SQL: ', sql)

                      const incentive_amt_credits_log = await this.rawQuery(sql, connection)
                      console.log('Incentive amount credit log: ', incentive_amt_credits_log)

                      // Call receipt api
                      const receiptDetails = await this.getAepsTransactionDetails(_, { ma_transaction_master_id: transactionData.ma_transaction_master_id, ma_status: updateFields.transaction_status, bank_rrn: updateFields.rrn, bank_balance: updateFields.bank_balance })
                      if (receiptDetails.status === 200) {
                        delete receiptDetails.status
                        delete receiptDetails.message
                        delete receiptDetails.respcode
                        receiptDetails.transaction_reason = (updateFields.transaction_reason) ? updateFields.transaction_reason : null
                        ledgerEntries = Object.assign(ledgerEntries, receiptDetails)
                      }
                      await mySQLWrapper.commit(connection)
                      log.logger({ pagename: 'transactionController.js', action: 'updateAepsTransaction', type: 'response', fields: incentiveEntries })

                      const integratedaeps = await this.aepsWithdrawalIntegrated(updateFields, userid)
                      console.log('INTEGRATED_AEPS: ', integratedaeps)
                      // [START] Send AEPS Success transaction notification
                      await common.sendNotification({
                        txnId: updateFields.aggregator_txn_id,
                        orderId: ledgerEntries.aggregator_order_id,
                        amount: ledgerEntries.amount,
                        txnStatus: ledgerEntries.transaction_status,
                        txnTime: ledgerEntries.transaction_time,
                        userId: ledgerEntries.userid,
                        instance: this,
                        connection
                      })
                      // [END] Send AEPS Success transaction notification

                      const sendAepsSms = await this.sendSmsToAepsCustomer(transactionData.amount, transactionData.aggregator_order_id, transactionData.addedon, transactionData.mobile_number, updateFields.transaction_status, updateFields.transaction_type)
                      return ledgerEntries
                    } else {
                      await mySQLWrapper.rollback(connection)
                    }
                  }
                  // await mySQLWrapper.rollback(connection)
                  const receiptDetails = await this.getAepsTransactionDetails(_, { ma_transaction_master_id: transactionData.ma_transaction_master_id, ma_status: updateFields.transaction_status, bank_rrn: updateFields.rrn, bank_balance: updateFields.bank_balance })
                  if (receiptDetails.status === 200) {
                    delete receiptDetails.status
                    delete receiptDetails.message
                    delete receiptDetails.respcode
                    receiptDetails.transaction_reason = (updateFields.transaction_reason) ? updateFields.transaction_reason : null
                    ledgerEntries = Object.assign(ledgerEntries, receiptDetails)
                  }
                  // [START] Send AEPS Success transaction notification
                  await common.sendNotification({
                    txnId: updateFields.aggregator_txn_id,
                    orderId: ledgerEntries.aggregator_order_id,
                    amount: ledgerEntries.amount,
                    txnStatus: ledgerEntries.transaction_status,
                    txnTime: ledgerEntries.transaction_time,
                    userId: ledgerEntries.userid,
                    instance: this,
                    connection
                  })
                  // [END] Send AEPS Success transaction notification

                  const sendAepsSms = await this.sendSmsToAepsCustomer(transactionData.amount, transactionData.aggregator_order_id, transactionData.addedon, transactionData.mobile_number, updateFields.transaction_status, updateFields.transaction_type)
                  return ledgerEntries
                }
                // Call receipt api
                const receiptDetails = await this.getAepsTransactionDetails(_, { ma_transaction_master_id: transactionData.ma_transaction_master_id, ma_status: updateFields.transaction_status, bank_rrn: updateFields.rrn, bank_balance: updateFields.bank_balance })
                if (receiptDetails.status === 200) {
                  delete receiptDetails.status
                  delete receiptDetails.message
                  delete receiptDetails.respcode
                  receiptDetails.transaction_reason = (updateFields.transaction_reason) ? updateFields.transaction_reason : null
                  updateTxn = Object.assign(updateTxn, receiptDetails)
                }
                // await mySQLWrapper.commit(connection)
                log.logger({ pagename: 'transactionController.js', action: 'updateAepsTransaction', type: 'response', fields: updateTxn })

                // [START] Send AEPS Success transaction notification
                await common.sendNotification({
                  txnId: updateFields.aggregator_txn_id,
                  orderId: updateTxn.aggregator_order_id,
                  amount: updateTxn.amount,
                  txnStatus: updateTxn.transaction_status,
                  txnTime: updateTxn.transaction_time,
                  userId: updateTxn.userid,
                  instance: this,
                  connection
                })
                // [END] Send AEPS Success transaction notification

                // Send Sms on Failed AEPS Transaction
                if (updateFields.transaction_status === 'F') {
                  const sendAepsSms = await this.sendSmsToAepsCustomer(transactionData.amount, transactionData.aggregator_order_id, transactionData.addedon, transactionData.mobile_number, updateFields.transaction_status, updateFields.transaction_type)
                }

                return updateTxn
              }
              // await mySQLWrapper.rollback(connection)
              return updateTxn
            } else {
              // await mySQLWrapper.rollback(connection)
              return { status: 400, message: 'fail : Amount Mismatch' }
            }
          } else {
            // await mySQLWrapper.rollback(connection)
            return { status: 400, message: 'fail : Transaction already updated' }
          }
        } else {
          // await mySQLWrapper.rollback(connection)
          return { status: 400, message: 'fail : Transaction Not Found' }
        }
      } else {
        // await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'fail : Invalid Transaction status' }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'updateAepsTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (isSet) {
        connection.release()
      }
    }
  }

  /**
   * This api is used to update Aadhaar Pay  Transaction
   * @param {*} _
   * @param {*} updateFields
   */
  static async updateADPAYTransaction (_, updateFields) {
    log.logger({ pagename: 'transactionController.js', action: 'updateADPAYTransaction', type: 'request', fields: updateFields })
    // console.log(updateFields)
    let isSet = true
    let connection = {}
    if (updateFields.connection) {
      connection = updateFields.connection
      isSet = false
    } else {
      connection = await mySQLWrapper.getConnectionFromPool()
    }
    try {
      updateFields.connection = connection
      await mySQLWrapper.beginTransaction(connection)
      if ((typeof updateFields.transaction_status !== 'undefined') && (updateFields.transaction_status === 'S' || updateFields.transaction_status === 'F' || updateFields.transaction_status === 'P')) {
        console.time('TIME_FOR_AEPS_SELECT_TRANSACTION')
        const sql = `SELECT transaction_status, amount, commission_amount, ma_transaction_master_id, aggregator_order_id, addedon, mobile_number FROM ma_transaction_master WHERE ma_user_id = ${updateFields.ma_user_id} AND aggregator_order_id = '${updateFields.orderid}' AND transaction_type = '${updateFields.transaction_type}' LIMIT 1`
        let transactionData = await this.rawQuery(sql, connection)
        console.timeEnd('TIME_FOR_AEPS_SELECT_TRANSACTION')
        console.log('sql', sql)
        console.log('queryresult', transactionData)
        if (transactionData.length > 0) {
          transactionData = transactionData[0]
          if (transactionData.transaction_status === 'I' || transactionData.transaction_status === 'P' || updateFields.ltsFlag) {
            if (transactionData.amount == updateFields.amount) {
              updateFields.bank_rrn = updateFields.rrn
              updateFields.aggregator_order_id = updateFields.orderid
              const userid = updateFields.userid
              delete updateFields.userid
              const ledgerCronId = updateFields.ledger_cron_id
              delete updateFields.ledger_cron_id
              let updateTxn = await this.updateTransaction(_, updateFields)
              if (updateTxn.status === 200) {
                // Save Bank RRN & Balance - Start
                let bank_rrn_val = ''
                let bank_balance_val = ''
                if (updateFields.rrn !== null) {
                  bank_rrn_val = updateFields.rrn
                }

                if (updateFields.bank_balance !== null) {
                  bank_balance_val = updateFields.bank_balance
                }

                const ma_transaction_master_id = transactionData.ma_transaction_master_id
                console.time('update rrn balance aggbnk and txn id query time >>>>')
                const updateTranDetailsSql = `UPDATE ma_transaction_master_details set bank_rrn='${bank_rrn_val}', bank_balance='${bank_balance_val}' where ma_transaction_master_id = '${ma_transaction_master_id}'`
                console.log('updateADPAYtransactiondetails', updateTranDetailsSql)
                await this.rawQuery(updateTranDetailsSql, connection)
                console.timeEnd('update rrn balance aggbnk and txn id query time >>>>')
                // End
                if (updateFields.transaction_status === 'S') {
                  // save order detaisl in adpay cron table - transaction_type = 10
                  // add entry in cron table
                  /* const saveAdpayOrderVerifyData = `Insert into ma_aeps_aadhaar_ledger_log
                                          (ma_user_id, userid, order_id, amount, transaction_type)
                                          VALUES (${updateFields.ma_user_id},${userid},'${updateFields.orderid}','${updateFields.amount}','${updateFields.transaction_type}')`
                  const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, connection)
                  log.logger({ pagename: require('path').basename(__filename), action: 'updateADPAYTransaction', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })

                  const ledgerCronId = saveAdpayOrderVerifyResult.insertId */
                  console.log('ledgerCronId-ADPAY>>>>>>>', ledgerCronId)

                  let ledgerEntries = await ledgerEntriesCont.collectmoneyLedgerEntries(updateFields, userid)
                  console.log('ledgerEntries>>>>>>>', ledgerEntries)
                  if (ledgerEntries.status === 200) {
                    // update ledger entries status to 'S' in adpay cron table -- Start
                    const sql = `UPDATE ma_aeps_aadhaar_ledger_log set ledger_status = 'S' where id = ${ledgerCronId}`
                    const update_transaction_amt_incentive_log = await this.rawQuery(sql, connection)
                    // End
                    const incentiveEntries = await collectMoneyDistribution.incentiveEntries(updateFields, userid)
                    if (incentiveEntries.status === 200) {
                      // update incentive entries status to 'S' in adpay cron table -- Start
                      const sql = `UPDATE ma_aeps_aadhaar_ledger_log set incentive_status = 'S', cron_status = 'V', remarks = 'REALTIME' where id = ${ledgerCronId}`
                      const update_transaction_amt_ledger_log = await this.rawQuery(sql, connection)
                      // End
                      // Call receipt api
                      const receiptDetails = await this.getADPayTransactionDetails(_, { ma_transaction_master_id: transactionData.ma_transaction_master_id, ma_status: updateFields.transaction_status, bank_rrn: updateFields.rrn, bank_balance: updateFields.bank_balance })
                      if (receiptDetails.status === 200) {
                        delete receiptDetails.status
                        delete receiptDetails.message
                        delete receiptDetails.respcode
                        receiptDetails.transaction_reason = (updateFields.transaction_reason) ? updateFields.transaction_reason : null
                        ledgerEntries = Object.assign(ledgerEntries, receiptDetails)
                      }
                      await mySQLWrapper.commit(connection)
                      log.logger({ pagename: 'transactionController.js', action: 'updateADPAYTransaction', type: 'response', fields: incentiveEntries })
                      // [START] Send ADPAY Success transaction notification
                      await common.sendNotification({
                        txnId: updateFields.aggregator_txn_id,
                        orderId: ledgerEntries.aggregator_order_id,
                        amount: ledgerEntries.amount,
                        txnStatus: ledgerEntries.transaction_status,
                        txnTime: ledgerEntries.transaction_time,
                        userId: ledgerEntries.userid,
                        instance: this,
                        connection
                      })
                      // [END] Send ADPAY Success transaction notification

                      const sendAepsSms = await this.sendSmsToADpayCustomer(transactionData.amount, transactionData.aggregator_order_id, transactionData.addedon, transactionData.mobile_number, updateFields.transaction_status, updateFields.transaction_type, transactionData.commission_amount)
                      return ledgerEntries
                    }
                  }
                  await mySQLWrapper.rollback(connection)
                  const receiptDetails = await this.getADPayTransactionDetails(_, { ma_transaction_master_id: transactionData.ma_transaction_master_id, ma_status: updateFields.transaction_status, bank_rrn: updateFields.rrn, bank_balance: updateFields.bank_balance })
                  if (receiptDetails.status === 200) {
                    delete receiptDetails.status
                    delete receiptDetails.message
                    delete receiptDetails.respcode
                    receiptDetails.transaction_reason = (updateFields.transaction_reason) ? updateFields.transaction_reason : null
                    ledgerEntries = Object.assign(ledgerEntries, receiptDetails)
                  }
                  // [START] Send ADPAY Success transaction notification
                  await common.sendNotification({
                    txnId: updateFields.aggregator_txn_id,
                    orderId: ledgerEntries.aggregator_order_id,
                    amount: ledgerEntries.amount,
                    txnStatus: ledgerEntries.transaction_status,
                    txnTime: ledgerEntries.transaction_time,
                    userId: ledgerEntries.userid,
                    instance: this,
                    connection
                  })
                  // [END] Send ADPAY Success transaction notification

                  const sendAepsSms = await this.sendSmsToADpayCustomer(transactionData.amount, transactionData.aggregator_order_id, transactionData.addedon, transactionData.mobile_number, updateFields.transaction_status, updateFields.transaction_type, transactionData.commission_amount)
                  return ledgerEntries
                }
                // Call receipt api
                const receiptDetails = await this.getADPayTransactionDetails(_, { ma_transaction_master_id: transactionData.ma_transaction_master_id, ma_status: updateFields.transaction_status, bank_rrn: updateFields.rrn, bank_balance: updateFields.bank_balance })
                if (receiptDetails.status === 200) {
                  delete receiptDetails.status
                  delete receiptDetails.message
                  delete receiptDetails.respcode
                  receiptDetails.transaction_reason = (updateFields.transaction_reason) ? updateFields.transaction_reason : null
                  updateTxn = Object.assign(updateTxn, receiptDetails)
                }
                await mySQLWrapper.commit(connection)
                log.logger({ pagename: 'transactionController.js', action: 'updateADPAYTransaction', type: 'response', fields: updateTxn })

                // [START] Send AEPS Success transaction notification
                await common.sendNotification({
                  txnId: updateFields.aggregator_txn_id,
                  orderId: updateTxn.aggregator_order_id,
                  amount: updateTxn.amount,
                  txnStatus: updateTxn.transaction_status,
                  txnTime: updateTxn.transaction_time,
                  userId: updateTxn.userid,
                  instance: this,
                  connection
                })
                // [END] Send AEPS Success transaction notification

                // Send Sms on Failed AEPS Transaction
                if (updateFields.transaction_status === 'F') {
                  const sendAepsSms = await this.sendSmsToADpayCustomer(transactionData.amount, transactionData.aggregator_order_id, transactionData.addedon, transactionData.mobile_number, updateFields.transaction_status, updateFields.transaction_type, transactionData.commission_amount)
                }

                return updateTxn
              }
              await mySQLWrapper.rollback(connection)
              return updateTxn
            } else {
              await mySQLWrapper.rollback(connection)
              return { status: 400, message: 'fail : Amount Mismatch' }
            }
          } else {
            await mySQLWrapper.rollback(connection)
            return { status: 400, message: 'fail : Transaction already updated' }
          }
        } else {
          await mySQLWrapper.rollback(connection)
          return { status: 400, message: 'fail : Transaction Not Found' }
        }
      } else {
        await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'fail : Invalid Transaction status' }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'updateADPAYTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (isSet) {
        connection.release()
      }
    }
  }

  /**
   * This api is used to update collect Money Transaction
   * @param {*} _
   * @param {*} updateFields
   */
  static async updateCollectMoneyTransaction (_, updateFields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      updateFields.connection = connection
      if ((typeof updateFields.transaction_status !== 'undefined') && (updateFields.transaction_status === 'P' || updateFields.transaction_status === 'F')) {
        const sql = `SELECT * FROM ma_transaction_master WHERE ma_user_id = ${updateFields.ma_user_id} AND aggregator_order_id = '${updateFields.aggregator_order_id}' AND transaction_type = '10'`
        let transactionData = await this.rawQuery(sql, connection)
        if (transactionData.length > 0) {
          transactionData = transactionData[0]
          if (transactionData.transaction_status === 'I') {
            if (transactionData.amount == updateFields.amount) {
              return this.updateTransaction(_, updateFields)
            } else {
              return { status: 400, message: 'fail : Amount Mismatch' }
            }
          } else {
            return { status: 400, message: 'fail : Transaction already updated' }
          }
        } else {
          return { status: 400, message: 'fail : Transaction Not Found' }
        }
      } else {
        return { status: 400, message: 'fail : Invalid Transaction status' }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'updateCollectMoneyTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async updateMasterTransfer (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateMasterTransfer', type: 'request', fields: fields })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {
      const transferSql = `SELECT 
              count(ma_transfers_id) as total_transfer_count,
              SUM(IF(transfer_status='S',1,0)) as success_transfer_count,
              SUM(IF(transfer_status='F',1,0)) as failure_transfer_count,
              SUM(IF(transfer_status='R',1,0)) as refund_transfer_count,
              SUM(IF(transfer_status='P',1,0)) as pending_transfer_count,
              SUM(IF(transfer_status='I',1,0)) as init_transfer_count,
              SUM(IF(refund_flag='Y',1,0)) as refund_flag_count
              FROM ma_transfers
              WHERE ma_transaction_master_id = ${fields.ma_transaction_master_id} `

      const queryResponse = await this.rawQuery(transferSql, connection)
      if (queryResponse.length > 0) {
        console.log('transferDataTotalCouunt', queryResponse)
        let data = { }
        const masterTransferData = queryResponse[0]
        if (masterTransferData.total_transfer_count === masterTransferData.success_transfer_count) {
          console.log('Full Success Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'S' }
        } else if (masterTransferData.total_transfer_count === masterTransferData.failure_transfer_count) {
          console.log('Full Failure Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'F' }
        } else if (masterTransferData.total_transfer_count === masterTransferData.pending_transfer_count) {
          console.log('Full Pending Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'P' }
        } else if (masterTransferData.total_transfer_count === masterTransferData.refund_transfer_count) {
          console.log('Full Refund Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'F' }
        } else if (masterTransferData.total_transfer_count === masterTransferData.refund_flag_count) {
          console.log('Full Refund Flag Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'F' }
        } else {
          console.log('Full Partial Case ::', fields.ma_transaction_master_id)
          if (masterTransferData.pending_transfer_count > 0 || masterTransferData.init_transfer_count > 0) {
            data = { transaction_status: 'P' }
          } else if (masterTransferData.success_transfer_count > 0) {
            data = { transaction_status: 'PS' }
          } else if (masterTransferData.refund_transfer_count > 0) {
            data = { transaction_status: 'F' }
          } else {
            data = { transaction_status: 'P' }
          }
        }

        if (Object.keys(data).length > 0) {
          // this.TABLE_NAME = 'ma_transaction_master'
          // const updateTransaction = await this.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
          /* RISK MANAGEMENT Changes */
          const updateTransactionResult = await this.updateWhereData(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
          log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

          /* if (updateTransaction.affectedRows <= 0) {
          // Rollback
            console.log('Transfer Data Update Failed')
            return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
          } */

          const transferRetryCtrl = require('../transfers/transferRetryController')
          let retrydata = { retry_status: 'pending' }
          if (data.transaction_status == 'S') {
            retrydata = { retry_status: 'success' }
          } else if (data.transaction_status == 'F') {
            retrydata = { retry_status: 'failed' }
          }

          console.log('retrydata update to ::', retrydata, ' ORDER ID::', fields.aggregator_order_id)

          const updateQueue = await transferRetryCtrl.updateWhereData(connection, { data: retrydata, id: fields.aggregator_order_id, where: 'orderid' })

          return { status: 200, message: 'success' }
        }
      } else {
        return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateMasterTransfer', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.message }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async updateMasterBillpay (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateMasterBillpay', type: 'request', fields: fields })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {
      const transferSql = `SELECT 
      count(ma_billpay_transactionid) as total_billpaytransaction_count,
      SUM(IF(transaction_status='S',1,0)) as success_billpaytransaction_count,
      SUM(IF(transaction_status='F',1,0)) as failure_billpaytransaction_count,
      SUM(IF(transaction_status='R',1,0)) as refund_billpaytransaction_count,
      SUM(IF(transaction_status='P',1,0)) as pending_billpaytransaction_count
      FROM ma_billpay_transaction_master
      WHERE ma_transaction_master_id= ${fields.ma_transaction_master_id} `

      const queryResponse = await this.rawQuery(transferSql, connection)
      if (queryResponse.length > 0) {
        console.log('transferDataTotalCouunt', queryResponse)
        let data = { }
        const masterBillpayData = queryResponse[0]
        if (masterBillpayData.total_billpaytransaction_count === masterBillpayData.success_billpaytransaction_count) {
          console.log('Full Success Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'S' }
        } else if (masterBillpayData.total_billpaytransaction_count === masterBillpayData.failure_billpaytransaction_count) {
          console.log('Full Failure Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'F' }
        } else if (masterBillpayData.total_billpaytransaction_count === masterBillpayData.refund_billpaytransaction_count) {
          console.log('Full Refund Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'F' }
        } else if (masterBillpayData.total_billpaytransaction_count === masterBillpayData.pending_billpaytransaction_count) {
          console.log('Full Pending Case ::', fields.ma_transaction_master_id)
          data = { transaction_status: 'P' }
        } else {

        }

        if (Object.keys(data).length > 0) {
          // this.TABLE_NAME = 'ma_transaction_master'
          const updateTransaction = await this.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })

          if (updateTransaction.affectedRows <= 0) {
          // Rollback
            console.log('Billpay Data Update Failed')
            return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
          }
          return { status: 200, message: 'success' }
        }
      } else {
        return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateMasterTransfer', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.message }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
   * This function is used to get the aeps transaction details for receipt
   * @param {*} _
   * @param {*} args
   */
  static async getAepsTransactionDetails (_, args) {
    log.logger({ pagename: 'transactionController.js', action: 'getAepsTransactionDetails', type: 'request', fields: args })
    // create a new connection for API
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let sqlCondition = ''
      if (args.ma_transaction_master_id) {
        sqlCondition = ` AND a.ma_transaction_master_id = '${args.ma_transaction_master_id}'`
      }

      if (args.aggregator_order_id) {
        sqlCondition = ` AND a.aggregator_order_id = '${args.aggregator_order_id}'`
      }
      const sql = `SELECT a.ma_transaction_master_id,
                          c.customer_name,
                          c.customer_aadhaar,
                          c.customer_virtualid,
                          b.address,
                          a.mobile_number AS customer_mobile,
                          a.amount,
                          a.aggregator_order_id,
                          CONCAT(b.firstname,' ',b.lastname) AS merchant_name,
                          b.mobile_id AS merchant_mobile,
                          FROM_UNIXTIME(UNIX_TIMESTAMP(a.addedon),'%d-%m-%Y %r') AS transaction_time,
                          IF (a.transaction_status = 'P', 'PND', a.transaction_status) AS transaction_status,
                          a.transaction_reason,
                          c.bank_name,
                          a.aggregator_txn_id,
                          a.userid,
                          c.bank_rrn,
                          c.bank_balance
                  FROM ma_transaction_master AS a
                  INNER JOIN ma_user_master AS b ON a.userid = b.userid AND a.ma_user_id = b.profileid
                  LEFT JOIN ma_transaction_master_details AS c ON a.ma_transaction_master_id = c.ma_transaction_master_id
                  WHERE a.transaction_type = '5' 
                  ${sqlCondition} LIMIT 1`
      const sqlResponse = await this.rawQuery(sql, connection)
      if (sqlResponse.length > 0) {
        log.logger({ pagename: 'transactionController.js', action: 'getAepsTransactionDetails', type: 'response', fields: sqlResponse[0] })
        const res = sqlResponse[0]
        var txnStatus = ''
        var txnBankRRN = ''
        var txnBankBalance = ''

        if (args.ma_status !== 'undefined' && args.ma_status != null) {
          txnStatus = args.ma_status
        } else {
          txnStatus = res.transaction_status
        }

        if (args.bank_rrn !== 'undefined' && args.bank_rrn != null) {
          txnBankRRN = args.bank_rrn
        } else {
          txnBankRRN = res.bank_rrn
        }

        if (args.bank_balance !== 'undefined' && args.bank_balance != null) {
          txnBankBalance = args.bank_balance
        } else {
          txnBankBalance = res.bank_balance
        }

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          customer_name: res.customer_name,
          customer_aadhaar: res.customer_aadhaar,
          customer_mobile: res.customer_mobile,
          address: res.address,
          amount: res.amount,
          aggregator_order_id: res.aggregator_order_id,
          merchant_name: res.merchant_name,
          merchant_mobile: res.merchant_mobile,
          transaction_time: res.transaction_time,
          transaction_status: txnStatus,
          transaction_reason: res.transaction_reason,
          bank_name: res.bank_name,
          aggregator_txn_id: res.aggregator_txn_id,
          userid: res.userid,
          bank_rrn: txnBankRRN,
          bank_balance: txnBankBalance
        }
      }
      log.logger({ pagename: 'transactionController.js', action: 'getAepsTransactionDetails', type: 'response', fields: {} })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getAepsTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * This function is used to get the aeps transaction details for receipt
   * @param {*} _
   * @param {*} args
   */
  static async getADPayTransactionDetails (_, args) {
    log.logger({ pagename: 'transactionController.js', action: 'getADPayTransactionDetails', type: 'request', fields: args })

    // create a new connection for API
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    console.log('env', env)
    const decryptionKey = util[env].encryptionKey
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let sqlCondition = ''
      if (args.ma_transaction_master_id) {
        sqlCondition = ` AND a.ma_transaction_master_id = '${args.ma_transaction_master_id}'`
      }

      if (args.aggregator_order_id) {
        sqlCondition = ` AND a.aggregator_order_id = '${args.aggregator_order_id}'`
      }
      const sql = `SELECT a.ma_transaction_master_id,
                          c.customer_name,
                          c.customer_aadhaar,
                          c.customer_email,
                          c.customer_virtualid,
                          b.address,
                          a.mobile_number AS customer_mobile,
                          (CASE WHEN a.transaction_type = 10 and c.utility_name !='AP' THEN a.amount
                          ELSE a.commission_amount + a.amount
                          END) as amount,
                          (CASE WHEN a.transaction_type = 10 and c.utility_name !='AP' THEN 0
                          ELSE a.commission_amount
                          END) as surcharge,
                          a.amount AS transaction_amount,
                          a.commission_amount,
                          a.aggregator_order_id,
                          CONCAT(b.firstname,' ',b.lastname) AS merchant_name,
                          b.mobile_id AS merchant_mobile,
                          FROM_UNIXTIME(UNIX_TIMESTAMP(a.addedon),'%d-%m-%Y %r') AS transaction_time,
                          IF (a.transaction_status = 'P', 'PND', a.transaction_status) AS transaction_status,
                          a.transaction_reason,
                          c.bank_name,
                          a.aggregator_txn_id,
                          a.userid,
                          a.bank_rrn,
                          c.bank_balance,
                          c.utility_name
                  FROM ma_transaction_master AS a
                  INNER JOIN ma_user_master AS b ON a.userid = b.userid AND a.ma_user_id = b.profileid
                  LEFT JOIN ma_transaction_master_details AS c ON a.ma_transaction_master_id = c.ma_transaction_master_id
                  WHERE a.transaction_type = '10' 
                  ${sqlCondition} LIMIT 1`
      const sqlResponse = await this.rawQuery(sql, connection)
      console.log('+++++++++++++++++sql', sql)
      console.log('response+++++++++++++++++++++', sqlResponse)
      if (sqlResponse.length > 0) {
        log.logger({ pagename: 'transactionController.js', action: 'getADPayTransactionDetails', type: 'response', fields: sqlResponse[0] })
        const res = sqlResponse[0]
        var txnStatus = ''
        var txnBankRRN = ''
        var txnBankBalance = ''
        let mode = ''
        if (res.utility_name != null) {
          mode = (res.utility_name).toLowerCase() == 'upi' ? 'UPI' : (res.utility_name).toLowerCase() == 'ap' ? 'Aadhaar Pay' : (res.utility_name).toLowerCase() == 'nb' ? 'Net Banking' : 'N/A'
        } else {
          mode = 'N/A'
        }
        if (args.ma_status !== 'undefined' && args.ma_status != null) {
          txnStatus = args.ma_status
        } else {
          txnStatus = res.transaction_status
        }

        if (args.bank_rrn !== 'undefined' && args.bank_rrn != null) {
          txnBankRRN = args.bank_rrn
        } else {
          txnBankRRN = res.bank_rrn
        }

        if (args.bank_balance !== 'undefined' && args.bank_balance != null) {
          txnBankBalance = args.bank_balance
        } else {
          txnBankBalance = res.bank_balance
        }

        const cust_email = res.utility_name == 'UPI' ? res.customer_virtualid : res.utility_name == 'AP' ? res.customer_email : res.customer_virtualid || res.customer_email

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          customer_name: res.customer_name,
          customer_aadhaar: res.customer_aadhaar,
          customer_mobile: res.customer_mobile,
          customer_email: cust_email || 'N/A',
          address: res.address,
          amount: res.amount,
          'surcharge (including GST)': res.surcharge,
          transaction_amount: res.transaction_amount,
          aggregator_order_id: res.aggregator_order_id,
          merchant_name: res.merchant_name,
          merchant_mobile: res.merchant_mobile,
          transaction_time: res.transaction_time,
          transaction_status: txnStatus,
          transaction_reason: res.transaction_reason,
          bank_name: res.bank_name,
          aggregator_txn_id: res.aggregator_txn_id,
          userid: res.userid,
          bank_rrn: txnBankRRN,
          bank_balance: txnBankBalance,
          customer_virtualid: res.customer_virtualid,
          mode: mode
        }
      }
      log.logger({ pagename: 'transactionController.js', action: 'getADPayTransactionDetails', type: 'response', fields: {} })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getADPayTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * This function is used to get the bbps transaction details for receipt
   * @param {*} _
   * @param {*} args
   */
  static async getBbpsTransactionDetails (_, args) {
    log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: 'request', fields: args })
    // create a new connection for API
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      let sqlCondition = ''
      if (args.ma_transaction_master_id) {
        sqlCondition = ` WHERE a.ma_transaction_master_id = '${args.ma_transaction_master_id}'`
      }

      if (args.aggregator_order_id) {
        sqlCondition = ` WHERE a.aggregator_order_id = '${args.aggregator_order_id}'`
      }

      const sql = `SELECT a.ma_transaction_master_id,
                          a.mobile_number AS customer_mobile,
                          (a.amount + a.commission_amount) AS amount,
                          a.commission_amount AS transaction_charges,
                          a.amount AS transaction_amount,
                          b.address,
                          a.aggregator_order_id,
                          CONCAT(b.firstname,' ',b.lastname) AS merchant_name,
                          b.mobile_id AS merchant_mobile,
                          FROM_UNIXTIME(UNIX_TIMESTAMP(a.addedon),'%d-%m-%Y %r') AS transaction_time,
                          IF((a.transaction_type = 6 OR a.transaction_type = 17) AND a.transaction_status = 'P','PND',a.transaction_status) as transaction_status,
                          c.provider_name,
                          c.utility_name,
                          c.bank_response,
                          d.api_request,
                          d.api_response,
                          d.bill_amount,
                          a.transaction_id,
                          a.transaction_type,
                          tmd.form_data
                    FROM ma_transaction_master AS a
                    INNER JOIN ma_user_master AS b ON a.userid = b.userid AND a.ma_user_id = b.profileid
                    INNER JOIN ma_transaction_master_details AS tmd ON a.ma_transaction_master_id = tmd.ma_transaction_master_id
                    INNER JOIN ma_billpay_transaction_master AS c ON a.ma_transaction_master_id = c.ma_transaction_master_id 
                    INNER JOIN ma_makepayment_requests AS d ON CAST(a.aggregator_order_id AS CHAR CHARACTER SET utf8) = CAST(d.aggregator_order_id AS CHAR CHARACTER SET utf8)
                    ${sqlCondition}`
      const sqlResponse = await this.rawQuery(sql, connection)
      if (sqlResponse.length > 0) {
        log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: ' sql response', fields: sqlResponse[0] })
        const res = sqlResponse[0]
        var txnStatus = ''
        if (args.ma_status !== 'undefined' && args.ma_status != null) {
          txnStatus = args.ma_status
        } else {
          txnStatus = res.transaction_status
        }

        // Bill pay response
        const billPayDetails = []
        const billPayAdditionalDetails = []
        
        const api_request = JSON.parse(((res || {}).api_request || {}))
        if (res.api_response != null) {
          let receiptFieldsToInsert = {}

          const api_response = JSON.parse(res.api_response)

          // Get details
          // OLD BBPS RECEIPT DETAILS
          if (validator.validateField(api_response.data) && validator.validateField(api_response.data.details)) {
            const details = JSON.parse(api_response.data.details)
            for (const key of Object.keys(details)) {
              if ((util.bbpsReceiptExclude).includes(key)) {
                continue
              }
              let keyFound = 0
              for (const paramKey of Object.keys(util.bbpsReceiptParams)) {
                if ((util.bbpsReceiptParams[paramKey]).includes(key)) {
                  keyFound = 1
                  receiptFieldsToInsert[paramKey] = details[key]
                }
              }
              // If key not found in util
              if (keyFound == 0) {
                receiptFieldsToInsert[key] = details[key] 
              }
            }
          }

          // BBPS REWORK/RECURRING-CHANGE RECEIPT DETAILS
          if (validator.validateField(api_request) && validator.validateField(api_request.bill_data) && validator.validateField(api_request.bill_data[0])) {
            const details = api_request.bill_data[0]
            const billDetailData = api_request?.bill_data?.[0]?.details|| {}
            const billDetails = api_response?.bill_response || api_response?.BILLS?.[0] || {}
            let bank_response = {}
            try {
              let parsed = JSON.parse(res.bank_response);
              if (typeof parsed === "string") {
                parsed = JSON.parse(parsed);
              }
              bank_response = parsed
            } catch (error) {
              console.error("Parsing error:", error);
            }

            const bbps_ref_no = bank_response?.response?.response?.bbps_ref_no;
            const paymentid = bank_response?.response?.response?.paymentid;
            const payment_type = bank_response?.response?.response?.payment_type;
            const biller_approval_code = bank_response?.response?.response?.biller_approval_code;
            const payment_method = bank_response?.response?.response?.payment_account?.payment_method;
            const billerId = bank_response?.response?.response?.billerid


            receiptFieldsToInsert["B-Connect Txn ID"] = bbps_ref_no || 'N/A'
            receiptFieldsToInsert["Payment ID"] = paymentid || 'N/A'
            receiptFieldsToInsert["Payment Channel"] = payment_type || 'N/A'
            receiptFieldsToInsert["Payment Method"] = payment_method || 'N/A'
            receiptFieldsToInsert["Bill Number"] = billDetails.billnumber|| 'N/A'
            receiptFieldsToInsert["Bill Date"] = billDetails.billdate || 'N/A'
            receiptFieldsToInsert["Bill Due Date"] = billDetails.billduedate || 'N/A'
            receiptFieldsToInsert["Bill Period"] =  billDetails.billperiod || 'N/A'
            receiptFieldsToInsert["Bill Approval Code"] = biller_approval_code || 'N/A'
            receiptFieldsToInsert['Biller ID'] = billerId || 'N/A'
            receiptFieldsToInsert['Transaction (Source) ID'] = details.requestid || 'N/A'
            receiptFieldsToInsert['Payee Mobile Number'] = details.mobile_number || 'N/A'
            receiptFieldsToInsert['Customer Mobile Number'] = details.mobile_number 
            receiptFieldsToInsert["Customer Name"] = billDetails.customer_name || 'N/A'


            for (let i in billDetailData) {
              if (/mobile\s*number/i.test(i)) {
                receiptFieldsToInsert['Customer Mobile Number'] = billDetailData[i]
              }
            }
            
            // For electricity Utility pass user input Consumer Number/K Number/Account Number/Consumer Id
            if (api_request.utility_id == 1){
              receiptFieldsToInsert = { ...receiptFieldsToInsert, ...(api_request.bill_details || {}) }
            }

          }
          
          for (const key in receiptFieldsToInsert) {
            billPayDetails.push({ parameter_name: key, value: receiptFieldsToInsert[key] })
          }
        }

        if (validator.validateField(res) && validator.validateField(res.form_data)) {
          try {
            let additional_details = JSON.parse(res.form_data).addition_details
            if (typeof additional_details == 'string') additional_details = JSON.parse(additional_details)
            if (Array.isArray(additional_details)) billPayAdditionalDetails.push(...(additional_details || []))
          } catch (error) {
            log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: 'json parse catcherror', fields: error })
          }
        }

        console.log('Beforebill_response64', billPayDetails)
        const bill_response64 = Buffer.from(JSON.stringify(billPayDetails), 'utf8').toString('base64')
        console.log('bill_response64', bill_response64)

        let providersDetails = [{}]
        if (validator.validateField(api_request.bill_provider_id)) {
          const providerDetailsQuery = `SELECT
              bpv.provider_name,
              bup.provider_name as utility_name
          FROM
            ma_bbps_providers_validations bpv
          JOIN
            ma_billpayment_utility_providers bup
          ON
            CAST(bup.provider_key AS CHAR) = bpv.utility_id
          WHERE
            provider_id = ${api_request.bill_provider_id || 0};`

          providersDetails = await this.rawQuery(providerDetailsQuery, connection)
          if (providersDetails.length <= 0) providersDetails = [{}]
        }

        const apiData = {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          ma_transaction_master_id: res.ma_transaction_master_id,
          cutomer_name: res.customer_name,
          customer_mobile: res.customer_mobile,
          address: res.address,
          amount: res.amount,
          aggregator_order_id: res.aggregator_order_id,
          merchant_name: res.merchant_name,
          merchant_mobile: res.merchant_mobile,
          transaction_time: res.transaction_time,
          transaction_status: txnStatus,
          provider_name: res.provider_name || providersDetails[0].provider_name,
          utility_name: res.utility_name || providersDetails[0].utility_name,
          transaction_charges: res.transaction_charges,
          transaction_amount: res.transaction_amount,
          bill_response: bill_response64,
          receipt_bill_response: billPayDetails,
          transaction_id: res.transaction_id,
          receipt_bill_additional_details: billPayAdditionalDetails
        }
        if (res.transaction_type == 17) {
          if (validator.validateField(api_request) && validator.validateField(api_request.__rechargeType)) {
            this.mergeShigrapayData(apiData, api_request)
          }
        }
        if (validator.validateField(api_request) && validator.validateField(api_request.receiptData)) {
          log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: 'api response', fields: { apiData, receiptData: api_request.receiptData } })
          this.mergeShigrapayData(apiData, api_request.receiptData)
          return { ...apiData, ...api_request.receiptData }
        }
        log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: 'api response', fields: apiData })
        return apiData
      }
      log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: 'api response', fields: {} })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getBbpsTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async updateLtsForAeps (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Get merchant details
      const userSql = `SELECT 
                        a.apikey,
                        a.username,
                        a.password,
                        b.amount
                      FROM ma_user_master AS a 
                      INNER JOIN ma_transaction_master AS b ON a.userid = b.userid
                      WHERE a.profileid = '${fields.ma_user_id}' 
                      AND a.userid = ${fields.userid}
                      AND b.aggregator_order_id = '${fields.orderid}'`
      const userResponse = await this.rawQuery(userSql, connection)
      if (userResponse.length <= 0) {
        log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'response', fields: {} })
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
      }
      const user = {
        secretKey: userResponse[0].apikey,
        userName: userResponse[0].username,
        password: userResponse[0].password
      }

      // Create hash private key for order confirmation api
      const keyData = `${user.secretKey}@${user.userName}:|:${user.password}`
      // console.log(keyData)
      const postParams = {
        privatekey: common.createHash(keyData),
        mercid: fields.ma_user_id,
        merchant_txnId: fields.orderid ? fields.orderid : ''
        // airpayId: fields.aggregator_txn_id ? fields.aggregator_txn_id : ''
      }

      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)
      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'order/verify.php',
        data: postData,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })

      if (response.status !== 200) {
        log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving transaction details' }
      }
      console.log(response.data)
      const transactionObj = parser.parse(response.data)
      // console.log(transactionObj)
      let transaction_status = ''
      let transaction_status_text = ''
      if (typeof transactionObj === 'object' && typeof transactionObj.RESPONSE === 'object') {
        let transaction_reason = ''
        const rrn = (transactionObj.RESPONSE.TRANSACTION.RRN) ? transactionObj.RESPONSE.TRANSACTION.RRN : ''
        const bank_balance = (transactionObj.RESPONSE.TRANSACTION.CUSTOMERBANKBAL) ? transactionObj.RESPONSE.TRANSACTION.CUSTOMERBANKBAL : 0.0
        // Update aeps transaction according to transaction status return from order confirmation
        if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 200) {
          // Transaction is success
          transaction_status = 'S'
          transaction_reason = 'Success'
          transaction_status_text = 'Success'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 201) {
          // Transaction in pending state
          transaction_status = 'I'
          transaction_reason = 'Initiated'
          transaction_status_text = 'Initiated'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 400) {
          // Transaction failed
          transaction_status = 'F'
          transaction_reason = (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON) ? transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON : 'Failed'
          transaction_status_text = 'Failed'
        }

        if (transaction_status) { // If transaction status in success,fail,pending
          // Call update aeps transaction
          const updateParams = {
            ma_user_id: fields.ma_user_id,
            orderid: fields.orderid,
            aggregator_txn_id: fields.aggregator_txn_id,
            transaction_status: transaction_status,
            amount: userResponse[0].amount,
            rrn: rrn,
            userid: fields.userid,
            transaction_reason,
            bank_balance,
            connection,
            ltsFlag: 1
          }
          const updateTransResp = await this.updateAepsTransaction(_, updateParams)
          if (updateTransResp.status === 200) {
            log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'response', fields: updateTransResp })
            let message = 'Your Previous Txn #txnid# is now #status#'
            message = message.replace('#txnid#', fields.orderid)
            message = message.replace('#status#', transaction_status_text)
            return { status: 200, respcode: 1000, message: message, transaction_status }
          }
          log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'response', fields: updateTransResp })
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + updateTransResp.message }
        }
        log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + transactionObj.RESPONSE.TRANSACTION.MESSAGE }
      }
      log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'response', fields: {} })
      return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'updateLtsForAeps', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * This function is used to get parent_transaction_master_id
   * @param String orderId
   * @param Object connection
   */
  static async getTransactionDetailsByParentId (orderId, connection) {
    try {
      const sql = `SELECT ma_transaction_master_id FROM ma_transaction_master WHERE parent_id = '${orderId}' AND parent_transaction_master_id = 0`
      const sqlResponse = await this.rawQuery(sql, connection)
      if (sqlResponse.length > 0) {
        return sqlResponse[0].ma_transaction_master_id
      }
      return 0
    } catch (err) {
      console.log(err)
      return 0
    }
  }

  /**
   * This function is used to get the available channels for transaction
   * @param {*} maUserId
   * @param {*} connection
   */
  static async getAllowedChannels (maUserId, connection, state = null, user_type) {
    log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'request', fields: { maUserId, connection, state, user_type } })
    try {
      if (state == null) {
        // Check user exists and get the state id
        const sql = `SELECT state,user_type FROM ma_user_master WHERE profileid = '${maUserId}'`
        const userData = await this.rawQuery(sql, connection)

        if (userData.length <= 0) {
          return { status: 400, respcode: 1028, message: 'User not found for channel master list.' }
        }
        state = userData[0].state
      }

      // Get channels by ma_user_id
      const custChannels = `SELECT channel_name 
                            FROM ma_channels_master
                            WHERE ma_user_id = '${maUserId}'
                            AND record_status = 'Y' AND user_type = '${user_type}' `
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'stateChannels', fields: custChannels })
      let custChannelsData = await this.rawQuery(custChannels, connection)
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'stateChannels', fields: custChannelsData })
      // console.log(custChannelsData)
      if (custChannelsData.length > 0) {
        custChannelsData = this.getChannelTxnType(custChannelsData)
        console.log('--- Merchant Channels ---')
        console.log(custChannelsData)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], channels: custChannelsData }
      }

      // Get channels by state
      const stateChannels = `SELECT channel_name 
                            FROM ma_channels_master
                            WHERE state_master_id = '${state}'
                            AND record_status = 'Y' AND user_type = '${user_type}' `
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'stateChannels', fields: stateChannels })
      let stateChannelsData = await this.rawQuery(stateChannels, connection)
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'stateChannelsData', fields: stateChannelsData })

      // console.log(stateChannelsData)
      if (stateChannelsData.length > 0) {
        stateChannelsData = this.getChannelTxnType(stateChannelsData)
        console.log('--- State Channels ---')
        console.log(stateChannelsData)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], channels: stateChannelsData }
      }

      // Get global channels
      const globalChannels = `SELECT channel_name 
                            FROM ma_channels_master
                            WHERE ma_user_id = '0'
                            AND state_master_id = '0'
                            AND record_status = 'Y' AND user_type = '${user_type}'`
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'globalChannels', fields: globalChannels })
      let globalChannelsData = await this.rawQuery(globalChannels, connection)
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'globalChannelsData', fields: globalChannelsData })

      // console.log(globalChannelsData)
      if (globalChannelsData.length > 0) {
        globalChannelsData = this.getChannelTxnType(globalChannelsData)
        console.log('--- Global Channels ---')
        console.log(globalChannelsData)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], channels: globalChannelsData }
      }

      // If no channels found
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], channels: [] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getAllowedChannels', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * This function is used to convert Channel master data to transaction type
   * @param {*} channels
   */
  static getChannelTxnType (channels) {
    const channelsType = []

    channels.forEach(channel => {
      switch (channel.channel_name) {
        case 'CASHINONLINE':
          channelsType.push('1')
          break
        /* case 'CASHINEVALUE':
          channelsType.push('1')
          break */
        case 'DMT':
          channelsType.push('2')
          channelsType.push('21')
          channelsType.push('38') // DMT KYC CHARGE
          break
        case 'WITHDRAWAL':
          channelsType.push('3')
          break
        case 'AEPS':
          channelsType.push('5')
          channelsType.push('40')
          channelsType.push('42')
          channelsType.push('64')
          channelsType.push('65')
          channelsType.push('66')
          break
        case 'BBPS':
          channelsType.push('6')
          channelsType.push('17')
          break
        case 'COLLECTMONEY':
          channelsType.push('10')
          break
        case 'INSURANCE':
          channelsType.push('15')
          break
        case 'CMS':
          channelsType.push('24')
          break
        case 'GOLD':
          channelsType.push('28')
          break
        case 'FASTAG':
          channelsType.push('31')
          channelsType.push('32')
          break
        case 'CASHBACKPROMOTION':
          channelsType.push('37')
          break
        case 'SHOPPING':
          channelsType.push('34')
          channelsType.push('35')
          channelsType.push('36')
          break
        case 'UPIQR':
          channelsType.push('10')
          break
        case 'RISCOVERYSACHET':
          channelsType.push('15')
          break
        case 'ONDC':
          channelsType.push('44')
          channelsType.push('45')
          break
        case 'DIGI_KHATA_DMT_PPI':
          // Digikhata Changes
          channelsType.push('75')
          channelsType.push('76')
          channelsType.push('77')
          channelsType.push('79')
          break
        /* FMT CHANGES */
        case 'FMT':
          channelsType.push('50')
          channelsType.push('51')
          channelsType.push('52')
          channelsType.push('53')
          channelsType.push('54')
          channelsType.push('55')
          channelsType.push('56')
          break
          case 'SOUNDBOX_ACTIVATION':
            // Digikhata Changes
            channelsType.push('81')
            break

      }
    })
    return channelsType
  }

  /**
   * This function is used to check the channel exists in allowed cahnnels
   */
  static async checkChannelExists (channel, maUserId, connection, state = null, user_type = 'RT') {
    log.logger({ pagename: 'transactionController.js', action: 'checkChannelExists', type: 'request', fields: { channel, maUserId, connection, state } })
    const channelsResponse = await this.getAllowedChannels(maUserId, connection, state, user_type)
    console.log(' --- channelsResponse ---')
    console.log(channelsResponse)
    if (channelsResponse.status === 400) {
      return channelsResponse
    }

    const allowedChannels = channelsResponse.channels
    channel = channel.toString()

    // Check channel exists in allowed channels
    return allowedChannels.includes(channel)
  }

  /**
   * Get Transaction types
   * @param {*} _
   * @param {*} fields
   * <AUTHOR> Hassan
   * @returns transaction_type_data
   */
  static async getTransactionTypes (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'getTransactionTypes', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let sqlCondition = ''
      if (fields.transaction_types == 'TRANSACTION_HISTORY') {
        sqlCondition = ` WHERE transaction_history_type = '${fields.transaction_types}'`
      }
      const sql = `SELECT * FROM ma_transaction_type_master${sqlCondition} ORDER BY display_name ASC`
      const transactionTypes = await this.rawQuery(sql, connection)
      console.log('transactionTypes', transactionTypes.length)
      if (transactionTypes.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_type_data: transactionTypes }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getTransactionTypes', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /** This function is used to send Sms for AEPS customer
    * @param Integer Transaction Amount
    * @param String Order id
    * @param DateTime transaction date time
    * @param Integer Mobile Number
    */

  static async sendSmsToAepsCustomer (transaction_amt, order_id, datetime, mobile_number, transaction_status, transaction_type) {
    try {
      // Send sms to customer after transaction is successful
      const smsParams = {
        transaction_amt: transaction_amt,
        orderid: order_id,
        datetime: datetime,
        mobile_number: mobile_number,
        transaction_status: transaction_status
      }
      log.logger({ pagename: 'transactionController.js', action: 'sendSmsToAepsCustomer', type: 'response', fields: { ...smsParams, transaction_type } })
      let commMessage = ''
      let template = ''
      if (transaction_status == 'S') {
        switch (transaction_type) {
          case '5':
            commMessage = util.communication.TRANSACTIONAEPSSUCCESS
            template = util.templateid.TRANSACTIONAEPSSUCCESS
            break
          case '40':
            commMessage = util.communication.TRANSACTIONAEPSMSSUCCESS
            template = util.templateid.TRANSACTIONAEPSMSSUCCESS
            break
          case '42':
            commMessage = util.communication.TRANSACTIONAEPSBESUCCESS
            template = util.templateid.TRANSACTIONAEPSBESUCCESS
            break
        }
      } else if (transaction_status == 'F') {
        switch (transaction_type) {
          case '5':
            transaction_amt = 'Rs.' + transaction_amt
            commMessage = util.communication.TRANSACTIONAEPSFAILURE
            template = util.templateid.TRANSACTIONAEPSFAILURE
            break
          case '40':
            commMessage = util.communication.TRANSACTIONAEPSMSFAILURE
            template = util.templateid.TRANSACTIONAEPSMSFAILURE
            break
          case '42':
            commMessage = util.communication.TRANSACTIONAEPSBEFAILURE
            template = util.templateid.TRANSACTIONAEPSBEFAILURE
            break
        }
      }

      var dt = new Date(datetime)
      const datetimeformat = dt.toLocaleString('en-IN')
      commMessage = commMessage.replace('<amount>', transaction_amt)
      commMessage = commMessage.replace('<orderid>', order_id)
      commMessage = commMessage.replace('<date>', datetimeformat)
      commMessage = commMessage.replace('<signature>', util.communication.Signature)
      await sms.sentSmsAsync(commMessage, mobile_number, template, 'sms', null, { aggregator_order_id: order_id })
      return 0
    } catch (err) {
      console.log(err)
      return 0
    }
  }

  /** This function is used to send Sms for AEPS customer
    * @param Integer Transaction Amount
    * @param String Order id
    * @param DateTime transaction date time
    * @param Integer Mobile Number
    */

  static async sendSmsToADpayCustomer (transaction_amt, order_id, datetime, mobile_number, transaction_status, transaction_type, surcharge) {
    try {
      // Send sms to customer after transaction is successful
      const smsParams = {
        transaction_amt: transaction_amt,
        orderid: order_id,
        datetime: datetime,
        mobile_number: mobile_number,
        transaction_status: transaction_status
      }
      log.logger({ pagename: 'transactionController.js', action: 'sendSmsToAepsCustomer', type: 'response', fields: { ...smsParams, transaction_type } })
      let commMessage = ''
      let template = ''
      if (transaction_status == 'S') {
        commMessage = util.communication.TRANSACTIONADPAYSUCCESS
        template = util.templateid.TRANSACTIONADPAYSUCCESS
      } else if (transaction_status == 'F') {
        commMessage = util.communication.TRANSACTIONADPAYFAILURE
        template = util.templateid.TRANSACTIONADPAYFAILURE
      }

      var dt = new Date(datetime)
      const datetimeformat = dt.toLocaleString('en-IN')
      commMessage = commMessage.replace('<amount>', transaction_amt)
      commMessage = commMessage.replace('<orderid>', order_id)
      commMessage = commMessage.replace('<date>', datetimeformat)
      await sms.sentSmsAsync(commMessage, mobile_number, template, 'sms', null, { aggregator_order_id: order_id })
      return 0
    } catch (err) {
      console.log(err)
      return 0
    }
  }

  static async revalidatePointsBank (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'revalidatePointsBank', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const generatedChecksum = checksum.checksum(fields.aggregator_order_id + fields.ma_user_id + fields.aggregator_txn_id)
      if (generatedChecksum === fields.checksum) {
        // var sql = `SELECT * from ma_billpay_transaction_master WHERE order_id = "${fields.aggregator_order_id}" limit 1`
        var sql = `SELECT t.*,b.payment_status,b.ma_billpay_transactionid from ma_transaction_master as t JOIN ma_billpay_transaction_master as b on b.ma_transaction_master_id = t.ma_transaction_master_id  where aggregator_order_id='${fields.aggregator_order_id}' limit 1`
        // var sql = `SEELCT * from ma_transaction_master limit 1`
        const data = await this.rawQuery(sql, connection)
        if (data.length > 0) {
          var pointssql = `SELECT * from ma_points_ledger_master where mode = 'dr' AND orderid='${fields.aggregator_order_id}'AND transaction_type = '${data[0].transaction_type}'`
          const getPointsDetails = await this.rawQuery(pointssql, connection)
          if (getPointsDetails.length <= 0) {
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1002] + ' Debit Entry not found !', amount: data[0].amount, transaction_status: 'F' }
          }

          let isWalletRevert = false
          var point2ssql = `SELECT * from ma_points_ledger_master where parent_id='${fields.aggregator_order_id}' AND ma_status IN ('R','REV')`
          const getPoints2Details = await this.rawQuery(point2ssql, connection)
          if (getPoints2Details.length > 0) {
            isWalletRevert = true
            return { status: 200, respcode: 1000, amount: data[0].amount, message: errorMsg.responseCode[1002] + ' Debit Entry not found !', transaction_status: 'F' }
          }

          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'S', amount: data[0].amount }
          // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: data[0].amount, transaction_status: data[0].payment_status }
        } else {
          return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
        }
      } else {
        return { status: 400, respcode: 1004, message: errorMsg.responseCode[1004] }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'revalidatePointsBank', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   *
   * @param {Promise<mySQLWrapper.getConnectionFromPool()>} connection
   * @param {{data:object,id:string,where:string}} param1
   * @returns
   */
  static async updateWhereData (connection, { data, id, where }) {
    log.logger({ pagename: 'transactionController.js', action: 'updateWhereData', type: 'request', fields: { data, id, where } })
    if (this.TABLE_NAME != 'ma_transaction_master') {
      this.TABLE_NAME = 'ma_transaction_master'
    }
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const updateResult = await this.updateWhere(conn, { data, id: id, where: where })

      log.logger({ pagename: 'transactionController.js', action: 'updateWhereData', type: 'updateResult', fields: updateResult })

      if (updateResult.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      /* RISK MANAGEMENT Changes */
      if ('transaction_status' in data) {
        const saveRiskAnalysisResp = await this.saveRiskAnalysisRecord({ where, id, transaction_status: data.transaction_status, connection: conn })
        log.logger({ pagename: 'transactionController.js', action: 'updateWhereData', type: 'saveRiskAnalysisRecord', fields: saveRiskAnalysisResp })
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.logger({ pagename: 'transactionController.js', action: 'updateWhereData', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   * This function is used to get the kotak/insurance transaction details for receipt
   * @param {*} _
   * @param {*} args
   */
  static async getInsuranceTransactionDetails (_, args) {
    log.logger({ pagename: 'transactionController.js', action: 'getInsuranceTransactionDetails', type: 'request', fields: args })
    // create a new connection for API
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let sqlCondition = ''
      if (args.ma_transaction_master_id) {
        sqlCondition = ` WHERE a.ma_transaction_master_id = '${args.ma_transaction_master_id}'`
      }

      if (args.aggregator_order_id) {
        sqlCondition = ` WHERE a.aggregator_order_id = '${args.aggregator_order_id}'`
      }

      const sql = `SELECT a.mobile_number AS customer_mobile,
                   d.policy_title,
                  c.policy_response,
                  a.transaction_status                  
                  FROM ma_transaction_master AS a
                  INNER JOIN ma_user_master AS b ON a.userid = b.userid AND a.ma_user_id = b.profileid
                  INNER JOIN ma_user_policy_details AS c ON a.ma_transaction_master_id = c.ma_transaction_master_id 
                  JOIN ma_policy_master AS d ON d.policy_id = c.policy_id
                  ${sqlCondition} AND a.transaction_status = 'S' AND c.policy_status IN ('VFY','PI')`
      const sqlResponse = await this.rawQuery(sql, connection)
      if (sqlResponse.length > 0) {
        log.logger({ pagename: 'transactionController.js', action: 'getInsuranceTransactionDetails', type: ' sql response', fields: sqlResponse[0] })
        const res = sqlResponse[0]
        var txnStatus = ''
        txnStatus = res.transaction_status

        // policy pay response
        const billPayDetails = []
        let coi = ''
        if (res.policy_response != null) {
          const policy_response = JSON.parse(res.policy_response)
          coi = policy_response.coi
        }

        const apiData = {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          customer_mobile: res.customer_mobile,
          transaction_status: txnStatus,
          coi: coi
        }

        log.logger({ pagename: 'transactionController.js', action: 'getInsuranceTransactionDetails', type: 'api response', fields: apiData })
        return apiData
      }
      log.logger({ pagename: 'transactionController.js', action: 'getInsuranceTransactionDetails', type: 'api response', fields: {} })
      return { status: 400, respcode: 1002, message: 'Receipt not found' }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getInsuranceTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async aepsWithdrawalIntegrated (fields, userid) {
    try {
      const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${userid}`
      const integratedMer = await this.rawQuery(userSQL, fields.connection)
      console.log("SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =" + userid)
      if (integratedMer.length > 0) {
        var integratedCode = await this.rawQuery(`SELECT integration_code from ma_integration_user_master where  ma_user_id= ${fields.ma_user_id}`, fields.connection)
        if (integratedCode.length > 0) {
          if (integratedCode[0].integration_code == 'EMITRA') {
            const neftcontroller = require('../neftDetails/neftDetailsController')
            var bankdata = await this.rawQuery(`SELECT * from ma_business_profiles_bank where  ma_user_id= ${fields.ma_user_id} AND bank_status = 'Y'`, fields.connection)
            // const bankid = ''
            await neftcontroller.neftProcess(fields.ma_user_id, fields.amount, fields.orderid, userid, bankdata[0].ma_business_profiles_bank_id)
            // neftcontroller.neftProcess (fields.ma_user_id,fields.amount,fields.orderid,userid,bankdata[0].ma_business_profiles_bank_id).then((data) => console.log('neftProcessData >>', data)).catch((error) => console.log('neftProcessError >>', error))
          }
        }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'aepsWithdrawalIntegrated', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * This function is used to get the BARCODE String for UPI QR from MA Side
   * @param {*} _
   * @param {*} args
  */
  static async getQrCodeForUpi (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'getQrCodeForUpi', type: 'request', fields: fields })
    try {
      const merchant_domain = Buffer.from(util.mer_domain).toString('base64')
      const postParams = {
        mid: fields.ma_user_id,
        userid: fields.userid,
        version: 'v1',
        tid: '',
        ref_url: '',
        checksum: '',
        mer_dom: merchant_domain,
        surcharge_amt: '',
        customvar: '',
        call_type: 'upiqr',
        upi_mode: 'qr_code'
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getQrCodeForUpi', type: 'postparams-request', fields: postParams })
      // Call payments api for invoice pay
      const postData = await generateOrderchecksum(this, postParams)
      if (postData.status != 200) {
        return { status: postData.status, respcode: postData.respcode, message: postData.message }
      }
      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'api/generateOrder.php',
        data: postData.data,
        headers: { 'Content-Type': 'application/json' }
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'getQrCodeForUpi', type: 'paymentapi-request', fields: postData })
      log.logger({ pagename: require('path').basename(__filename), action: 'getQrCodeForUpi', type: 'paymentapi-response', fields: response.data })
      var finalResp = {}
      if (response.status === 200 && response.data.status == '200') {
        finalResp.record_status = 'success'
        finalResp.barcode_string = response.data.BARCODE_STRING
        finalResp.status = 200
        finalResp.respcode = 1000
        finalResp.message = errorMsg.responseCode[1000]
      } else {
        finalResp.record_status = 'fail'
        finalResp.status = 400
        finalResp.respcode = 1001
        finalResp.message = response.data.error
      }
      return finalResp
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getQrCodeForUpi', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {

    }
  }

  static async getUpiQrCodeDetails (_, fields) {
    log.logger({ pagename: 'transactionController.js', action: 'getUpiQrCodeDetails', type: 'request', fields: fields })

    // Validate checksum ********************************
    const checksumFile = require('../../util/checksum')
    const crypto = require('crypto')

    // genaerate decrypted Hash
    let dateForHash = new Date()
    dateForHash = dateForHash.toISOString().split('T')[0]
    dateForHash = dateForHash.split('-')
    const decryptedHash = `${util.getBomQrEndpointCredentialForChecksum.username}#${util.getBomQrEndpointCredentialForChecksum.password}#${fields.ma_user_id}#${dateForHash.join('')}#${util.getBomQrEndpointCredentialForChecksum.salt}`
    log.logger({ pagename: 'transactionController.js', action: 'getUpiQrCodeDetails', type: 'decryptedHash', fields: decryptedHash })

    // generate encrypted Hash
    const encryptedHash = await checksumFile.checksum512(decryptedHash)
    log.logger({ pagename: 'transactionController.js', action: 'getUpiQrCodeDetails', type: 'encryptedHash', fields: encryptedHash })

    // validate the fields.checksum and encryptedHash
    if (fields.checksum != null && fields.checksum != 'null' && fields.checksum != '') {
      if (!crypto.timingSafeEqual(Buffer.from(fields.checksum), Buffer.from(encryptedHash))) return { status: 400, respcode: 1001, message: 'Invalid Checksum', ma_user_id: fields.ma_user_id, btqrString: null }
    } else {
      return { status: 400, respcode: 1001, message: 'Invalid Checksum', ma_user_id: fields.ma_user_id, btqrString: null }
    }
    // END Validate checksum ********************************

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // ma_user_id validation
      const merchantDetailsSql = `SELECT bt_qr_string, firstname, lastname FROM ma_user_master WHERE profileid = '${fields.ma_user_id}' AND mer_user = 'mer' LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getUpiQrCodeDetails', type: 'merchantDetailsSql', fields: merchantDetailsSql })
      const merchantDetailsSqlData = await this.rawQuery(merchantDetailsSql, connection)
      log.logger({ pagename: 'btqrController.js', action: 'getUpiQrCodeDetails', type: 'merchantDetailsSqlData', fields: merchantDetailsSqlData })
      if (merchantDetailsSqlData.length <= 0) return { status: 400, respcode: 1001, message: 'Merchant not found!', ma_user_id: fields.ma_user_id, btqrString: null }

      // Validate btqr string
      if (!validator.definedVal(merchantDetailsSqlData[0].bt_qr_string)) return { status: 400, respcode: 1001, message: 'BTQR not configured for this merchant.', ma_user_id: fields.ma_user_id, btqrString: merchantDetailsSqlData[0].bt_qr_string, merchant_name: `${merchantDetailsSqlData[0].firstname || ''} ${merchantDetailsSqlData[0].lastname || ''}` }

      // return success data
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_user_id: fields.ma_user_id, btqrString: merchantDetailsSqlData[0].bt_qr_string, merchant_name: `${merchantDetailsSqlData[0].firstname || ''} ${merchantDetailsSqlData[0].lastname || ''}` }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getUpiQrCodeDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * This function is used to merge recharge data
   * @param {*} apiData
   * @param {*} api_request
  */
  static mergeShigrapayData (apiData, api_request) {
    const bill = []
    apiData.recharge_type = api_request.__rechargeType
    apiData.plan_name = api_request.__planName
    apiData.plan_description = api_request.__planDescription
    apiData.circle_name = api_request.__circleName
    delete api_request.__rechargeType
    delete api_request.__planName
    delete api_request.__planDescription
    delete api_request.__circleName

    if (api_request.action == 'insurance' || api_request.action == 'electricity') {
      apiData.logo_url = 'https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png'
      bill.push({ parameter_name: 'Customer Name', value: api_request.customer_name })
      bill.push({ parameter_name: 'Mobile Number', value: api_request.mobile_number })
      bill.push({ parameter_name: 'Utility Name', value: api_request.utility_name })
      bill.push({ parameter_name: 'Provider Name', value: api_request.provider_name })
      // bill.push({ parameter_name: 'Action', value: api_request.action || 'recharge' })
      if (api_request.action == 'insurance') {
        bill.push({ parameter_name: 'Policy Number', value: api_request.policy_number })
      } else if (api_request.action == 'electricity') {
        bill.push({ parameter_name: 'Customer ID', value: api_request.customer_id })
        bill.push({ parameter_name: 'State', value: api_request.state })
      }
      apiData.bill_response = Buffer.from(JSON.stringify(bill), 'utf8').toString('base64')
    }
    if (apiData.recharge_type == 'DTH') {
      apiData.customer_id = apiData.customer_mobile
      delete apiData.customer_mobile
    }
    return apiData
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,transaction_type:string|number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async activationLevelCheck ({ ma_user_id, userid, transaction_type, state, user_type, transactionAmount, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'transactionController.js', action: 'activationLevelCheck', type: 'request', fields: { ma_user_id, userid, transaction_type, state, user_type } })
    try {
      transaction_type = transaction_type.toString()
      /* INGORE HATM,POS,ADHOC ,COLLECTMONEY, CHARGEBACK, CHARGEBACK REVERSAL & REFUNDED */
      if (['20', '23', '27', '10', '67', '68', '69'].includes(transaction_type)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }

      /* Transaction Limit check */
      const mccLevelSettingResult = await mccLevelSettingController.getMccLevelSetting({
        ma_user_id,
        userid,
        connection: conn
      })
      log.logger({ pagename: 'transactionController.js', action: 'activationLevelCheck', type: 'getLimit', fields: mccLevelSettingResult })
      if (mccLevelSettingResult.status != 200) return mccLevelSettingResult

      const { setting } = mccLevelSettingResult

      /* Check if the transaction channel is allowed  */
      const channels = setting.channels

      if (!channels) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      const channelList = channels.split(',').map(channel => ({ channel_name: channel.toUpperCase() }))

      const channelTxnTypeList = this.getChannelTxnType(channelList)

      const channelsResponse = await this.getAllowedChannels(ma_user_id, connection, state, user_type)

      log.logger({ pagename: 'transactionController.js', action: 'getMccLevelSetting', type: 'channelsResponse', fields: channelsResponse })

      if (channelsResponse.status != 200) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      const finalChannelList = channelTxnTypeList.filter(transactionType => channelsResponse.channels.includes(transactionType))

      log.logger({ pagename: 'transactionController.js', action: 'getMccLevelSetting', type: 'getChannelTxnType', fields: channelTxnTypeList })

      if (!finalChannelList.includes(transaction_type)) return { status: 400, respcode: 1028, message: 'Transaction not allowed as channel is not assigned', action_code: 1001 }

      /* LIMIT CHECK NOT REQUIRED FOR : COLLECT MONEY & AEPS */
      if (['10', '5'].includes(transaction_type)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }

      const totalSuccessTransactionAmountQuery = `SELECT SUM(amount) as total_amount FROM ma_transaction_master WHERE ma_user_id=${ma_user_id} AND userid=${userid} AND transaction_status IN ('S','P') AND transaction_type NOT IN ('1')`
      const totalSuccessTransactionAmountResult = await this.rawQuery(totalSuccessTransactionAmountQuery, conn)

      log.logger({ pagename: 'transactionController.js', action: 'activationLevelCheck', type: 'totalSuccessTransactionAmountResult', fields: totalSuccessTransactionAmountResult })

      let totalTransactionAmount = 0
      if (totalSuccessTransactionAmountResult.length > 0) {
        const { total_amount } = totalSuccessTransactionAmountResult[0]
        totalTransactionAmount = parseFloat(total_amount) || 0
      }

      totalTransactionAmount += parseFloat(transactionAmount)

      if (totalTransactionAmount > setting.transaction_limit) return { status: 400, message: `${errorMsg.responseCode[1028]} :transaction limit exceed`, respcode: 1001, action_code: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: 'transactionController.js', action: 'getMccLevelSetting', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{where:string,id:number,transaction_status:string,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   */
  static async saveRiskAnalysisRecord ({ where, id, transaction_status, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'saveRiskAnalysisRecord', type: 'request', fields: { where, id } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (!['F', 'S', 'PS'].includes(transaction_status)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }

      const isRiskAnalysisActive = await riskManagementController.isRiskAnalysisActive({ connectionRead: conn })
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'isRiskAnalysisActive', fields: isRiskAnalysisActive })
      if (isRiskAnalysisActive.status != 200) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      const isRiskSQSActive = await riskManagementController.isRiskSQSActive({ connectionRead: conn })
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'isRiskSQSActive', fields: isRiskSQSActive })

      if (this.TABLE_NAME != 'ma_transaction_master') {
        this.TABLE_NAME = 'ma_transaction_master'
      }
      const transactionResult = await this.findMatching('_', { [where]: id })

      log.logger({ pagename: require('path').basename(__filename), action: 'saveRiskAnalysisRecord', type: 'result', fields: transactionResult })

      if (transactionResult.length == 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 }

      const tranactionData = transactionResult[0]

      const fields = {
        ma_user_id: tranactionData.ma_user_id,
        userid: tranactionData.userid,
        transaction_type: tranactionData.transaction_type,
        aggregator_order_id: tranactionData.aggregator_order_id,
        transaction_status: transaction_status
      }

      if (isRiskSQSActive.status != 200) {
        const riskApiCallResp = await riskManagementController.calculateRiskScore({
          request: fields,
          connection: conn,
          connectionRead: conn
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'response', fields: riskApiCallResp })
      }
      /* SQS CALL */
      if (isRiskSQSActive.status == 200) {
        const riskSQSResp = await riskManagementController.doSQSPost({
          SQSDATA: [
            {
              queue_name: 'risk_score_messages',
              queue_msg_grp_id: `riskanalysis-${tranactionData.aggregator_order_id}-${transaction_status}`,
              queue_payload: {
                fields,
                functions: 'calculateRiskScore',
                controller: 'riskManagementController'
              }
            }
          ],
          connection: conn
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCallSQS', type: 'response', fields: riskSQSResp })
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'saveRiskAnalysisRecord', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * This function is used to get the aeps transaction details for receipt
   * @param {*} _
   * @param {*} args
   */
  static async getHatmTransactionDetails (_, args) {
    log.logger({ pagename: 'transactionController.js', action: 'getHatmTransactionDetails', type: 'request', fields: args })

    // create a new connection for API
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    console.log('env', env)
    const decryptionKey = util[env].encryptionKey
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let sqlCondition = ''
      if (args.ma_transaction_master_id) {
        sqlCondition = ` AND a.ma_transaction_master_id = '${args.ma_transaction_master_id}'`
      }

      if (args.aggregator_order_id) {
        sqlCondition = ` AND a.aggregator_order_id = '${args.aggregator_order_id}'`
      }
      const sql = `SELECT a.ma_transaction_master_id,
                            IF(c.customer_name IS NULL,'N/A',c.customer_name) as customer_name,
                            IF(c.customer_mobile IS NULL,'N/A',c.customer_mobile) as customer_mobile,
                            a.amount,
                            a.aggregator_order_id,
                            CONCAT(b.firstname,' ',b.lastname) AS merchant_name,
                            b.mobile_id AS merchant_mobile,
                            b.address,
                            FROM_UNIXTIME(UNIX_TIMESTAMP(a.addedon),'%d-%m-%Y %r') AS transaction_time,
                            IF (a.transaction_status = 'P', 'PND', a.transaction_status) AS transaction_status,
                            a.transaction_reason,
                            c.bank_name,
                            c.terminalid,
                            a.aggregator_txn_id,
                            a.userid,
                            a.bank_rrn,
                            mptl.receipt_data,
                            a.ma_user_id,
                            c.form_data
                    FROM ma_transaction_master AS a
                    INNER JOIN ma_user_master AS b ON a.userid = b.userid AND a.ma_user_id = b.profileid
                    LEFT JOIN ma_transaction_master_details AS c ON a.ma_transaction_master_id = c.ma_transaction_master_id
                    LEFT JOIN ma_pos_transaction_logs AS mptl ON mptl.airpay_id = a.aggregator_txn_id
                    WHERE a.transaction_type = '23' 
                    ${sqlCondition}`
      const sqlResponse = await this.rawQuery(sql, connection)
      console.log('+++++++++++++++++sql', sql)
      console.log('response+++++++++++++++++++++', sqlResponse)
      if (sqlResponse.length > 0) {
        log.logger({ pagename: 'transactionController.js', action: 'getHatmTransactionDetails', type: 'response', fields: sqlResponse[0] })
        const res = sqlResponse[0]
        var txnStatus = ''
        var txnBankRRN = ''
        var txnBankBalance = ''
        let bankMid = ''
        let bankTid = ''
        let authCode = ''
        let cardType = ''
        let merchantId = res.ma_user_id
        let card_num = ''

        if (res.receipt_data != null && res.receipt_data != undefined && res.receipt_data != '') {
          const receiptData = res.receipt_data.replace(/[']/g, '')
          const jdata = JSON.parse(receiptData)
          bankMid = jdata.bankMid
          bankTid = jdata.bankTid
          authCode = jdata.authCode
          cardType = jdata.cardIssuer
          merchantId = jdata.merchantId
        }
        if (res.form_data != '' && res.form_data != undefined && res.form_data != null) {
          const decodedValue = JSON.parse(res.form_data)
          card_num = decodedValue.card_num || 'N/A'
        }

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          customer_name: res.customer_name,
          customer_mobile: res.customer_mobile,
          address: res.address,
          amount: res.amount,
          aggregator_order_id: res.aggregator_order_id,
          merchant_name: res.merchant_name,
          merchant_mobile: res.merchant_mobile,
          transaction_time: res.transaction_time,
          transaction_status: res.transaction_status,
          transaction_reason: res.transaction_reason,
          bank_name: res.bank_name,
          aggregator_txn_id: res.aggregator_txn_id,
          userid: res.userid,
          RRN: res.bank_rrn,
          terminal_id: res.terminalid,
          bank_mid: bankMid,
          bank_tid: bankTid,
          auth_code: authCode,
          card_type: cardType,
          merchant_id: merchantId,
          card_number: card_num || 'N/A',
          card_name: res.customer_name
        }
      }
      log.logger({ pagename: 'transactionController.js', action: 'getHatmTransactionDetails', type: 'response', fields: {} })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'transactionController.js', action: 'getHatmTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async createIpnAdditionalInfo (_, fields, connection = null) {
    log.logger({ pagename: 'transactionController.js', action: 'createIpnAdditionalInfo', type: 'request', fields: fields })
    let isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const createIpnAdditionalInfoQuery = `INSERT INTO ma_ipn_additional_logs(ma_user_id, order_id,card_num,customer_name) values (${fields.ma_user_id}, '${fields.order_id}','${fields.card_num}','${fields.customer_name}')`
      const createIpnAdditionalInfoResult = await this.rawQuery(createIpnAdditionalInfoQuery, connection)
      log.logger({ pagename: 'transactionController.js', action: 'createIpnAdditionalInfo', type: 'SQL Respone', fields: createIpnAdditionalInfoResult })

      return { status: 200, respcode: 1000, response: createIpnAdditionalInfoResult }
    } catch (err) {
      console.log('error in createIpnAdditionalInfo--->', err)
      log.logger({ pagename: 'transactionController.js', action: 'createIpnAdditionalInfo', type: 'catcherror', fields: err })
      return { status: 400, message: 'Something Went wrong...Please try again !', respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }
}

module.exports = Transaction
