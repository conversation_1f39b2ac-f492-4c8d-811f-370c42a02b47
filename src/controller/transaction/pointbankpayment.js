const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common = require('../../util/common')
const transaction = require('./transactionController')

const qs = require('qs')
const parser = require('fast-xml-parser')
const axios = require('axios')

const transactionCtrl = require('./transactionController')
const billPayTransactionCtrl = require('../billpayTransaction/billpayTransaction')

const integrated = require('../integrated/integratedController')
const rechargeController = require('../transaction/rechargeController')
const BaseShigrapayController = require('../shigrapay/Integration/baseShigrapayController')

class Pointbankpayment extends DAO {
  static async getBuyersData (fields, connection) {
    // Get merchant details
    const merchantSql = `SELECT
    a.email_id,
    a.mobile_id,
    a.firstname,
    a.lastname,
    a.address,
    mcm.name as city,
    mcm.name as state,
    a.pincode,
    a.country,       
    a.apikey,
    a.username,
    a.password,
    a.userid
  FROM ma_user_master AS a 
  JOIN ma_states_master as msm on msm.id = a.state
  JOIN ma_cities_master as mcm on mcm.id = a.city
  WHERE a.user_status = 'Y' AND  a.profileid = '${fields.ma_user_id}' limit 1`
    const merchantResponse = await this.rawQuery(merchantSql, connection)

    console.log('merchantResponse', merchantResponse)
    return merchantResponse
    // return merchantResponse
  }

  static async callPayIndex (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'callPayIndex', type: 'request', fields: fields })
    let isSet = false
    if (connection == null) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {
      const merchantResponse = await this.getBuyersData(fields, connection)

      if (merchantResponse.length <= 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'callPayIndex', type: 'response', fields: {} })
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
      }

      // const mercid = fields.ma_user_id
      const paymentsUrl = util.paymentsUrl

      const { mer_dtls, transaction_amount, aggregator_order_id, bill_amount } = fields.makePaymentResponse

      const apiResponseData = JSON.parse(fields.makePaymentResponse.api_response)
      const { requestid, account_id, details, PAYMENTAMOUNT_VALIDATION, customerid, invoice_id, BILLER_MASTER_ID } = apiResponseData.data
      const mer_dtlsObj = JSON.parse(mer_dtls)

      fields.mer_dtlsObj = mer_dtlsObj
      fields.requestid = requestid
      fields.account_id = account_id
      fields.details = details
      fields.invoice_id = invoice_id
      fields.bill_amount = bill_amount
      fields.transaction_amount = transaction_amount
      fields.PAYMENTAMOUNT_VALIDATION = PAYMENTAMOUNT_VALIDATION
      fields.customerid = customerid

      const privatekey = await this.getPrivateKey(mer_dtlsObj)

      fields.privatekey = privatekey

      const buyerData = merchantResponse[0]
      const alldata = {
        email_id: buyerData.email_id,
        firstname: buyerData.firstname,
        lastname: buyerData.lastname,
        address: buyerData.address,
        city: buyerData.city,
        state: buyerData.state,
        country: buyerData.country,
        transaction_amount: transaction_amount,
        aggregator_order_id: aggregator_order_id,
        merchantDet: mer_dtlsObj
      }

      // $checksum = Checksum::calculateChecksum($alldata.date('Y-m-d'),$privatekey);
      // echo $checksum.'<br/>';
      // $hiddenmod = "apcrdt";
      const { md5checksum, payIndexchecksum } = require('./../../util/checksum.js')
      const postParams = {
        privatekey: privatekey,
        checksum: payIndexchecksum(alldata),
        mercid: mer_dtlsObj.mercid,
        orderid: aggregator_order_id,
        currency: '356',
        isocurrency: 'INR',
        chmod: 'apcrdt',
        channel: 'apcrdt',
        buyerEmail: buyerData.email_id,
        buyerPhone: buyerData.mobile_id,
        buyerFirstName: buyerData.firstname,
        buyerLastName: buyerData.lastname,
        buyerAddress: buyerData.address,
        buyerCity: buyerData.city,
        buyerState: buyerData.state,
        buyerCountry: buyerData.country,
        buyerPinCode: buyerData.pincode,
        amount: transaction_amount,
        apcrdtpin: fields.security_pin,
        customvar: '',
        mer_dom: Buffer.from('https://apmerchantapp.nowpay.co.in').toString('base64'),
        arpyVer: util.arpyVer
      }

      console.log('postParams >>', postParams)
      // console.log('payindexapi_liveUrl:::>>', paymentsUrl + 'pay/payindexapi_live.php')
      // Call payments api for order confirmation
      console.time('TIMER_POINTBANK_PAYMENT')
      const postData = qs.stringify(postParams)
      const response = await axios({
        method: 'post',
        url: paymentsUrl + 'pay/payindexapi.php',
        data: postData
      })
      console.timeEnd('TIMER_POINTBANK_PAYMENT')

      let bankStatusError = false
      if (response.status !== 200) {
        bankStatusError = true
        log.logger({ pagename: require('path').basename(__filename), action: 'callPayIndex', type: 'responseNot200', fields: response })
        // return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in point bank transaction' }
      }
      console.log('callPayIndexResponse:::>>', response.data)
      let transactionObj = {}
      try {
        transactionObj = parser.parse(response.data)
      } catch (error) {
        console.log('Response Parsing Error', error)
        transactionObj = {}
      }

      fields.buyerData = buyerData
      console.log('====================================')
      console.log('PointBankResponseObject==>', transactionObj)

      // const transactionCtrl = require('./transactionController')
      // const billPayTransactionCtrl = require('../billpayTransaction/billpayTransaction')
      let aggregator_txn_id = 0
      let bank_rrn = ''
      let message = ''
      let ponintbankResObj = {}
      let requireConfirm = false
      let successResponse = false
      let payment_status = 'P'
      let transaction_status = 'P'

      // If we got proper response from point bank
      if (typeof transactionObj === 'object' && Object.keys(transactionObj).length > 0 && (typeof transactionObj.RESPONSE === 'object' || typeof transactionObj.TRANSACTION === 'object')) {
        ponintbankResObj = transactionObj
        const TRANS_ORDER = (transactionObj.RESPONSE || transactionObj.TRANSACTION || {}).TRANSACTION
        const rrn = (TRANS_ORDER.RRN) ? TRANS_ORDER.RRN : ''
        bank_rrn = rrn
        aggregator_txn_id = TRANS_ORDER.APTRANSACTIONID
        message = TRANS_ORDER.MESSAGE

        // Success Case from Point Bank Payment Gateway
        if (TRANS_ORDER.TRANSACTIONSTATUS == 200) {
          log.logger({ pagename: require('path').basename(__filename), action: 'callPayIndex', type: 'response', fields: {} })
          successResponse = true
          payment_status = 'S'
          transaction_status = 'P'
        } else {
          // Other than Success Case from Point Bank Payment Gateway need to do confirmation
          requireConfirm = true
        }
      } else {
        // If we not got proper response from point bank need to confirm order again
        requireConfirm = true
      }

      if (requireConfirm) {
        const orderConfirmResponse = await this.orderConfirmation(_, fields)
        // Initted ..? Failure mark

        // To do orderConfirmation Requery Cron

        log.logger({ pagename: require('path').basename(__filename), action: 'callPayIndex', type: 'OuterresponseorderConfirmResponse', fields: orderConfirmResponse })

        if (orderConfirmResponse.status == 400) {
          return orderConfirmResponse
        }
        if (orderConfirmResponse.status == 200) {
          aggregator_txn_id = orderConfirmResponse.aggregator_txn_id
          bank_rrn = orderConfirmResponse.rrn
          message = orderConfirmResponse.transaction_reason
          ponintbankResObj = orderConfirmResponse.transactionObj

          if (orderConfirmResponse.transaction_status == 'S') {
            payment_status = 'S'
            transaction_status = 'P'
            successResponse = true
          } else if (orderConfirmResponse.transaction_status == 'F') {
            payment_status = 'F'
            transaction_status = 'F'
            successResponse = false
          }
        } else {
          console.log('orderConfirmResponse400 >>', orderConfirmResponse)
          return orderConfirmResponse
        }
      }

      // Testing
      // payment_status = 'S'
      // transaction_status = 'P'
      // successResponse = true
      // Testing

      return await this.updateBBPSTransaction({
        aggregator_order_id: aggregator_order_id,
        aggregator_txn_id: aggregator_txn_id,
        transaction_status: transaction_status,
        payment_status: payment_status,
        amount: bill_amount,
        bank_rrn: bank_rrn,
        message: message,
        ponintbankResObj: ponintbankResObj,
        ma_billpay_transactionid: fields.ma_billpay_transactionid,
        successResponse: successResponse,
        private_key: privatekey,
        mercid: mer_dtlsObj.mercid,
        action_type: fields.action_type,
        ma_user_id: fields.ma_user_id,
        requestid: fields.requestid,
        provider_id: fields.provider_id,
        buyerData: buyerData,
        account_id: fields.account_id,
        details: fields.details,
        mobile_number: fields.mobile_number,
        ma_transaction_master_id: fields.ma_transaction_master_id,
        ipnhit: false,
        PAYMENTAMOUNT_VALIDATION: fields.PAYMENTAMOUNT_VALIDATION,
        customerid: fields.customerid,
        invoice_id: invoice_id,
        BILLER_MASTER_ID: BILLER_MASTER_ID
      }, connection)
    } catch (err) {
      console.log('callPayIndexError', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'callPayIndex', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (isSet) connection.release()
    }
  }

  static async orderConfirmation (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const mer_dtlsObj = fields.mer_dtlsObj
      const privatekey = await this.getPrivateKey(mer_dtlsObj)

      const postParams = {
        privatekey: privatekey,
        mercid: mer_dtlsObj.mercid,
        merchant_txnId: fields.aggregator_order_id ? fields.aggregator_order_id : '',
        airpayId: ''
      }

      console.log('postParams>>', postParams)
      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)

      console.log('postDataStringify>>', postData)
      console.time('TIMER_POINTBANK_ORDER_CONFIRMATION')
      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'order/verify.php',
        data: postData,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })
      console.timeEnd('TIMER_POINTBANK_ORDER_CONFIRMATION')

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Order Confirmation Status ' + response.status }
      }
      // for local testing purpose
      // response = { RESPONSE: { TRANSACTION: { RRN: '', RISK: '0', CHMOD: 'apcrdt', AMOUNT: '10.00', MESSAGE: 'Success', BANKNAME: '', CARDTYPE: '', CUSTOMER: 'BACKEND3  RETAILER', CUSTOMVAR: '', SURCHARGE: '', CARDISSUER: '', CARDCOUNTRY: '', BILLEDAMOUNT: '10.00', CURRENCYCODE: '356', AP_SECUREHASH: '**********', CUSTOMEREMAIL: '<EMAIL>', CUSTOMERPHONE: '**********', MERCHANT_NAME: 'Airpay Demo', TRANSACTIONID: 'MAWEB98581625727167', WALLETBALANCE: '', ap_SecureHash: '**********', CONVERSIONRATE: '', APTRANSACTIONID: '********', SETTLEMENT_DATE: '', TRANSACTIONTIME: '08-07-2021 12:19:23', TRANSACTIONTYPE: '320', TRANSACTIONREASON: '', TRANSACTIONSTATUS: '200', TRANSACTIONPAYMENTSTATUS: 'SUCCESS' } } }
      // const transactionObj = response.data
      console.log('orderConfirmationResponse', response.data)
      const transactionObj = parser.parse(response.data)

      console.log('orderConfirmationResponseObj==>', transactionObj)
      // console.log(transactionObj)
      let transaction_status = ''
      let transaction_status_text = ''
      if (typeof transactionObj === 'object' && typeof transactionObj.RESPONSE === 'object') {
        let transaction_reason = ''
        const rrn = (transactionObj.RESPONSE.TRANSACTION.RRN) ? transactionObj.RESPONSE.TRANSACTION.RRN : ''
        const aggregator_txn_id = (transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID) ? transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID : 0
        // Update aeps transaction according to transaction status return from order confirmation
        if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 200) {
          // Transaction is success
          transaction_status = 'S'
          transaction_reason = 'Success'
          transaction_status_text = 'Success'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 201 || transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 211) {
          // Transaction in pending state
          transaction_status = 'P'
          transaction_reason = 'Initiated'
          transaction_status_text = 'Initiated'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 400) {
          // Transaction failed
          transaction_status = 'F'
          transaction_reason = (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON) ? transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON : 'Failed'
          transaction_status_text = 'Failed'
        } else if (fields.isCron && transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 503) {
          transaction_status = 'F'
          transaction_reason = (transactionObj.RESPONSE.TRANSACTION.MESSAGE) ? transactionObj.RESPONSE.TRANSACTION.MESSAGE : 'Failed'
          transaction_status_text = 'Failed'
        } else {
          // Transaction in pending state
          transaction_status = 'P'
          transaction_reason = 'Initiated'
          transaction_status_text = 'Initiated'
        }

        if (transaction_status) { // If transaction status in success,fail,pending
          const updateParams = {
            ma_user_id: fields.ma_user_id,
            orderid: fields.aggregator_order_id,
            aggregator_txn_id: aggregator_txn_id,
            transaction_status: transaction_status,
            amount: fields.transaction_amount,
            rrn: rrn,
            userid: fields.userid,
            transaction_reason
          }

          log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: updateParams, transaction_status })
          let message = 'Your Previous Txn #txnid# is now #status#'
          message = message.replace('#txnid#', fields.aggregator_order_id)
          message = message.replace('#status#', transaction_status_text)
          return { status: 200, respcode: 1000, message: message, updateParams, transaction_status, aggregator_txn_id, rrn, transaction_reason, transactionObj }
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + transactionObj.RESPONSE.TRANSACTION.MESSAGE }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: { notanobject: transactionObj } })
      return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
    } catch (err) {
      console.log('orderConfirmationError', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async makepayment (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'request', fields: fields })
    let isSet = false

    if (typeof (fields.makepayment_params) !== 'undefined' && fields.makepayment_params !== null && fields.makepayment_params !== '' && fields.makepayment_params !== 'undefined') {
      try {
        const makepaymentParamsJson = Buffer.from(fields.makepayment_params, 'base64').toString('utf8')
        console.log('makepaymentParamsJson', makepaymentParamsJson)
        fields.makepaymentParamsJsonArr = JSON.parse(makepaymentParamsJson)
        console.log(fields.makepaymentParamsJsonArr)
      } catch (error) {
        console.log('makepaymentParameterError', error)
        return { status: 400, respcode: 1129, message: errorMsg.responseCode[1129] + ' error' }
      }
    } else {
      return { status: 400, respcode: 1129, message: errorMsg.responseCode[1129] }
    }

    if (typeof connection === 'undefined' || connection === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }

    let makepayment_request_id = 0
    try {
      const makepaymentsUrl = util.makepaymentsUrl + 'bbps/bbpsapi/makepayment'

      const postParams = fields.makepaymentParamsJsonArr

      const makePaymentRequestCtrl = require('./makepayment_request')
      const insertRequest = await makePaymentRequestCtrl.insertData(connection, {
        data: {
          api_request: JSON.stringify(postParams),
          api_response: '{}',
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          aggregator_order_id: fields.aggregator_order_id,
          invoice_id: 0,
          mer_dtls: '{}',
          request_status: 'I',
          bill_amount: 0,
          transaction_amount: 0,
          surcharge_amount: 0,
          transaction_type: fields.transaction_type
        }
      })

      makepayment_request_id = insertRequest.insertId

      console.log('postParams >>', postParams)
      console.log('makepaymentsUrl:::>>', makepaymentsUrl)
      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)
      console.time('TIMER_MAKE_PAYMENT')
      const response = await axios({
        method: 'post',
        url: makepaymentsUrl,
        data: postData,
        timeout: 60000 // 60 secs timeout
      })
      console.timeEnd('TIMER_MAKE_PAYMENT')

      // check if invoice id already used
      const invoiceCheckQuery = `select invoice_id from ma_makepayment_requests where invoice_id = '${response.data.data.invoice_id}'`
      log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'invoiceCheckQuery', fields: invoiceCheckQuery })
      const invoiceCheck = await this.rawQuery(invoiceCheckQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'invoiceCheck', fields: invoiceCheck })

      if (response.status !== 200 || invoiceCheck.length > 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'response', fields: {} })
        await makePaymentRequestCtrl.updateWhereData(connection, {
          data: {
            api_response: JSON.stringify({ error: response.data }),
            request_status: 'E'
          },
          id: makepayment_request_id,
          where: 'makepayment_request_id'
        })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving payment details' }
      }

      console.log('makepaymentsUrlResponse:::>>', response.data)
      const makepaymentObj = typeof (response.data) === 'string' ? JSON.parse(response.data) : response.data
      console.log('====================================')
      console.log('makepaymentsUrlResponseObj==>', makepaymentObj)
      if (typeof makepaymentObj === 'object' && ('status' in makepaymentObj)) {
        log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'response', fields: makepaymentObj })

        if (makepaymentObj.status == 200) {
          let bill_response_base64 = null
          let billAmount = 0
          if (('bill_response' in makepaymentObj) && Object.keys(makepaymentObj.bill_response).length > 0) {
            bill_response_base64 = Buffer.from(JSON.stringify(makepaymentObj.bill_response)).toString('base64')
            billAmount = parseFloat(makepaymentObj.bill_response.billamount)
          } else {

          }

          let invoiceData = {}
          if (('data' in makepaymentObj) && Object.keys(makepaymentObj.data).length > 0) {
            invoiceData = makepaymentObj.data

            if (('mer_dtls' in invoiceData) && Object.keys(invoiceData.mer_dtls).length > 0) {
              await makePaymentRequestCtrl.updateWhereData(connection, {
                data: {
                  api_response: JSON.stringify(makepaymentObj),
                  request_status: 'S',
                  mer_dtls: JSON.stringify(invoiceData.mer_dtls),
                  invoice_id: invoiceData.invoice_id
                },
                id: makepayment_request_id,
                where: 'makepayment_request_id'
              })
            }

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bill_response_base64, invoiceData, billAmount, makepayment_request_id }
          } else {
            await makePaymentRequestCtrl.updateWhereData(connection, {
              data: {
                api_response: JSON.stringify({ failure: makepaymentObj }),
                request_status: 'F'
              },
              id: makepayment_request_id,
              where: 'makepayment_request_id'
            })
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': payment data not found!' }
          }
        } else {
          if ('msg' in makepaymentObj) {
            await makePaymentRequestCtrl.updateWhereData(connection, {
              data: {
                api_response: JSON.stringify({ failure: makepaymentObj }),
                request_status: 'F'
              },
              id: makepayment_request_id,
              where: 'makepayment_request_id'
            })
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ':Bill Response ' + makepaymentObj.msg }
          } else {
            await makePaymentRequestCtrl.updateWhereData(connection, {
              data: {
                api_response: JSON.stringify({ failure: makepaymentObj }),
                request_status: 'F'
              },
              id: makepayment_request_id,
              where: 'makepayment_request_id'
            })
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': payment response unknown!' }
          }
        }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'response', fields: {} })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ':Payment unknown response' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'response', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + err.message }
    } finally {
      // Release the connection
      // connection.release()
    }
  }

  static async getBillPaymentUtility (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentUtility', type: 'request', fields: fields })

    if (fields.ma_user_id <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid merchant id' }
    }

    if (fields.userid <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user id' }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const makepaymentsUrl = util.makepaymentsUrl + 'bbps/bbpsapi/get-utility'

      const { md5checksum } = require('./../../util/checksum')
      const md5String = md5checksum('airpay')
      const encodeStr = '' + md5String
      const checkSum = Buffer.from(encodeStr, 'utf8').toString('base64')
      // base64_encode(trim($utility_id).md5('airpay'))
      const postParams = {
        checksum: checkSum // ?
      }

      console.log('postParams >>', postParams)
      console.log('makepaymentsUrl:::>>', makepaymentsUrl)
      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)
      console.time('TIMER_BBPS_UTILITY')
      const response = await axios({
        method: 'post',
        url: makepaymentsUrl,
        data: postData,
        timeout: 60000 // 10 secs timeout
      })
      console.timeEnd('TIMER_BBPS_UTILITY')

      log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentProviders', type: 'response', fields: response.data })

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving utility details' }
      }

      console.log('getBillPaymentUtilityResponse:::>>', response.data)
      const getUtilityObj = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
      console.log('====================================')
      console.log('getBillPaymentUtilityResponseObj==>', getUtilityObj)
      if (typeof getUtilityObj === 'object' && ('status' in getUtilityObj)) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentUtility', type: 'response', fields: getUtilityObj })

        if (getUtilityObj.status == 200) {
          const bill_response_base64 = null
          const billAmount = 0

          let utility_data = []
          if (('data' in getUtilityObj) && Object.keys(getUtilityObj.data).length > 0) {
            utility_data = getUtilityObj.data

            // Remove Recharge from BBPS utility
            utility_data = utility_data.filter(function (params) {
              if (params.value != 'Recharge') {
                return params
              }
            })

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], utility_data: utility_data }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': utility data not found!' }
          }
        } else {
          if ('msg' in getUtilityObj) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ':Utility Response ' + getUtilityObj.msg }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Utility response unknown!' }
          }
        }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentUtility', type: 'response', fields: {} })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ':Payment unknown response' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentUtility', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async getBillPaymentProviders (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentProviders', type: 'request', fields: fields })

    if (fields.ma_user_id <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid merchant id' }
    }

    if (fields.userid <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user id' }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const makepaymentsUrl = util.makepaymentsUrl + 'bbps/bbpsapi/get-providers'

      const postParams = {
        checksum: '', // ?
        utility_id: fields.utility_id
      }

      let providerstr = ''
      if (fields.provider_id > 0) {
        postParams.provider_id = fields.provider_id
        providerstr = fields.provider_id
      }

      const { md5checksum } = require('./../../util/checksum')
      const md5String = md5checksum('airpay')
      const encodeStr = fields.utility_id + '' + providerstr + '' + md5String
      const checkSum = Buffer.from(encodeStr, 'utf8').toString('base64')
      // base64_encode(trim($utility_id).trim($provider_id).md5('airpay'));

      postParams.checksum = checkSum

      console.log('postParams >>', postParams)
      console.log('makepaymentsUrl:::>>', makepaymentsUrl)
      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)
      console.time('TIMER_BBPS_PROVIDERS')
      const response = await axios({
        method: 'post',
        url: makepaymentsUrl,
        data: postData,
        timeout: 60000 // 10 secs timeout
      })
      console.timeEnd('TIMER_BBPS_PROVIDERS')

      log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentProviders', type: 'response', fields: response.data })

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'makepayment', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving provider details' }
      }

      // console.log('getBillPaymentProvidersResponse:::>>', response.data)
      const getProviderObj = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
      console.log('====================================')
      // console.log('getBillPaymentProvidersResponseObj==>', getProviderObj)
      if (typeof getProviderObj === 'object' && ('status' in getProviderObj)) {
        if (getProviderObj.status == 200) {
          // let data_base64 = null
          const billAmount = 0

          let provider_data = []
          if (('data' in getProviderObj) && Object.keys(getProviderObj.data).length > 0) {
            // console.log('data_json', JSON.stringify(getProviderObj.data))
            // console.log('data_base64', Buffer.from(JSON.stringify(getProviderObj.data), 'utf8').toString('base64'))
            provider_data = Buffer.from(JSON.stringify(getProviderObj.data), 'utf8').toString('base64')
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], providers_data: provider_data }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': provider data not found!' }
          }
        } else {
          if ('msg' in getProviderObj) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + getProviderObj.msg }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': provider response unknown!' }
          }
        }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentProviders', type: 'response', fields: {} })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ':Payment unknown response' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBillPaymentProviders', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!!' }
      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async initMakePayment (_, { ma_user_id, transaction_type, amount, userid, makepayment_params, aggregator_order_id, action_type }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'initMakePayment', type: 'request', fields: { ma_user_id, transaction_type, amount, userid, makepayment_params, aggregator_order_id, action_type } })

    if (ma_user_id <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid merchant id' }
    }

    if (userid <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user id' }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if ((typeof (amount) === 'undefined' || amount == '' || isNaN(amount)) && action_type !== 'instapay') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid amount provided' }
      }

      const makePaymentResponse = await this.makepayment(null, {
        makepayment_params: makepayment_params,
        ma_user_id: ma_user_id,
        userid: userid,
        aggregator_order_id: aggregator_order_id,
        transaction_type: transaction_type
      },
      connection
      )

      console.log('makePaymentResponse>>', makePaymentResponse)

      if (makePaymentResponse.status == 400) {
        return makePaymentResponse
      }

      let bill_response = null
      let bill_response_json = null
      if (makePaymentResponse.bill_response_base64 && makePaymentResponse.bill_response_base64 != null && makePaymentResponse.billAmount > 0 && action_type == 'instapay') {
        console.log('billAmountAssigned >>', typeof makePaymentResponse.billAmount)
        amount = makePaymentResponse.billAmount
        bill_response = makePaymentResponse.bill_response_base64
        bill_response_json = JSON.parse(Buffer.from(makePaymentResponse.bill_response_base64, 'base64').toString('utf-8'))
      }
      const surcharge = 0

      const bbpsIncentiveCtrl = require('../incentive/bbpsIncentiveController')
      if (amount > 0) {
        const surchargeResponse = await bbpsIncentiveCtrl.calculateSurcharge(_, {
          ma_user_id: ma_user_id,
          transaction_type: transaction_type,
          amount: amount,
          userid: userid
        })

        console.log('surchargeResponse', surchargeResponse)
        if (surchargeResponse.status === 400) {
          return surchargeResponse
        }

        const surchargeS = surchargeResponse.surcharge
        const amountS = surchargeResponse.amount

        if (makePaymentResponse.makepayment_request_id > 0) {
          const makePaymentRequestCtrl = require('./makepayment_request')
          await makePaymentRequestCtrl.updateWhereData(connection, {
            data: {
              bill_amount: amount,
              transaction_amount: amountS,
              surcharge_amount: surchargeS
            },
            id: makePaymentResponse.makepayment_request_id,
            where: 'makepayment_request_id'
          })
        }
        // const checksum = require('../../util/checksum')
        // console.log('CheckSome==>', checksum.checksum('976668547600111758943415.00POSMAN53350333'))

        return {
          status: 200,
          message: errorMsg.responseCode[1000],
          respcode: 1000,
          surcharge: surchargeS,
          amount: amountS,
          makepayment_request_id: makePaymentResponse.makepayment_request_id,
          aggregator_order_id: aggregator_order_id,
          bill_response: bill_response,
          bill_response_json: bill_response_json
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': Unable to fetch bill amount please retry!', respcode: 1028 }
      }
    } catch (err) {
      console.log('initMakePaymentError', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'initMakePayment', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async doPointBankPayment (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doPointBankPayment', type: 'request', fields: fields })

    if (fields.ma_user_id <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid merchant id' }
    }

    if (fields.userid <= 0) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user id' }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const makePaymentRequestCtrl = require('./makepayment_request')
      const requestData = await makePaymentRequestCtrl.getRequestData(connection, fields.makepayment_request_id)
      if (requestData.length > 0) {
        const makepayrequestdata = requestData[0]
        if (makepayrequestdata.aggregator_order_id === fields.aggregator_order_id) {
          const apiRquest = JSON.parse(makepayrequestdata.api_request)
          if (apiRquest.utility_id != fields.utility_id) {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [2201]' }
          }

          if (apiRquest.provider_id != fields.provider_id) {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [2202]' }
          }

          if (apiRquest.action != fields.action_type) {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [2203]' }
          }

          if (makepayrequestdata.transaction_type != fields.transaction_type) {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [2204]' }
          }

          if (makepayrequestdata.request_status != 'S') {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [2205]' }
          }

          // Verify Pin Valid , Locked, Exired
          const securePinCtrl = require('../securityPin/securityPinController')
          const securePinData = await securePinCtrl.verifySecurePin(null, {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            security_pin: fields.security_pin,
            connection: connection
          })

          console.log('securePinData', securePinData)

          if (securePinData.status === 400) {
            return securePinData
          }

          var amountData = {}
          // To do Balance Check validation
          // Check balance of Retailer
          const balanceController = require('../balance/balanceController')
          const availableBalance = await balanceController.getWalletBalancesDirect(_, {
            ma_user_id: fields.ma_user_id,
            ma_status: 'ACTUAL',
            balance_flag: 'SUMMARY',
            connection
          })

          if (availableBalance.amount < makepayrequestdata.transaction_amount) {
            return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], amountData }
          }

          var sql = `SELECT * from ma_transaction_master where aggregator_order_id='${fields.aggregator_order_id}'`
          const getTxnDetails = await this.rawQuery(sql, connection)
          if (getTxnDetails.length > 0) {
            return { status: 400, respcode: 1133, message: errorMsg.responseCode[1133] }
          }

          // Create Transaction with pending status
          const transactionCtrl = require('./transactionController')
          const transactionData = await transactionCtrl.initiateTransaction(_, {
            connection: connection,
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            aggregator_order_id: fields.aggregator_order_id,
            amount: makepayrequestdata.bill_amount,
            transaction_type: fields.transaction_type,
            remarks: fields.remarks,
            mobile_number: fields.mobile_number,
            provider_id: fields.provider_id,
            provider_name: fields.provider_name,
            utility_id: fields.utility_id,
            utility_name: fields.utility_name,
            action_type: fields.action_type,
            transaction_status: 'P',
            makePaymentResponse: makepayrequestdata
          })

          if (transactionData.status === 400) {
            return transactionData
          }

          // return transactionData

          fields.makePaymentResponse = makepayrequestdata
          fields.ma_transaction_master_id = transactionData.transaction_id
          fields.ma_billpay_transactionid = transactionData.ma_billpay_transactionid

          // return transactionData // tempory for mobile and web development

          // Call Point Bank Operation , Point deduction , Pay Direct Bill End Point
          console.time('TIMER_POINTBANK_callPayIndex')
          let pointBankResponse = await this.callPayIndex(_, fields, connection)
          console.timeEnd('TIMER_POINTBANK_callPayIndex')

          // For testing Added here later can be removed
          /* const verifySecurePinCtrl = require('../authentication/otpController')
          const verifySecurePinResponse = await verifySecurePinCtrl.verifySecurePin(
            {
              mobile: fields.mobile_number,
              security_pin: fields.security_pin,
              aggregator_order_id: fields.aggregator_order_id,
              transaction_id: Math.floor(Math.random() * 100) + 1, // testing purpose
              amount: makepayrequestdata.transaction_amount,
              checksum: 'f240523c15fc1a5b0155aa6a2de81efefd0470a3000275580054a726d90bfc2c',
              testmode: true
            }
          )
          console.log('verifySecurePinResponse', verifySecurePinResponse)
          if (verifySecurePinResponse.status == 200) {
            // const addBBPSIncentiveRes = await this.addBBPSIncentive({
            //   aggregator_order_id: fields.aggregator_order_id,
            //   bill_amount: makepayrequestdata.bill_amount
            // }, connection)
            // console.log('addBBPSIncentiveRes>>', addBBPSIncentiveRes)

            // const reverseBBPSLedgersRes = await this.reverseBBPSLedgers({
            //   orderid: fields.aggregator_order_id
            // }, connection)
            // console.log('reverseBBPSLedgersResRes>>', reverseBBPSLedgersRes)
          }
          */
          console.log('poinyBankResponse', pointBankResponse)

          // Get bbps receipt
          console.time('TIMER_POINTBANK_getBbpsTransactionDetails')
          const receiptDetails = await transaction.getBbpsTransactionDetails(_, {
            aggregator_order_id: fields.aggregator_order_id,
            ma_user_id: fields.ma_user_id,
            userid: fields.userid
          })
          console.timeEnd('TIMER_POINTBANK_getBbpsTransactionDetails')

          console.log('receiptDetails', receiptDetails)
          if (receiptDetails.status === 200) {
            delete receiptDetails.status
            delete receiptDetails.message
            delete receiptDetails.respcode
            pointBankResponse = Object.assign(pointBankResponse, receiptDetails)
          }

          pointBankResponse.status = 200
          return pointBankResponse

          // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        } else {
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' found for order ' + fields.aggregator_order_id }
        }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' for payment request' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doPointBankPayment', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async callPaymentResponse (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'request', fields: fields })

    let isSet = false
    if (connection == null || connection == undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    const bank_response = {}
    try {
      const postParams = {
        private_key: fields.private_key,
        checksum: '',
        mercid: fields.mercid,
        callbackurl: util.bbpscallbackurl, // ??? to do this will changes recharge status
        api: 'makePayment',
        airpay_id: fields.aggregator_txn_id,
        amountsum: fields.amount,
        paymentamount_validation: fields.action_type == 'instapay' ? 'N' : 'Y', // recharge case
        action: fields.action_type,
        customerid: fields.ma_user_id,
        payment_type: fields.action_type == 'instapay' ? 'billpay' : 'instapay'
      }

      // billpayment case
      switch (fields.action_type) {
        case 'instapay':
          postParams.makepaymentdata = [
            {
              viewbillresponseid: fields.invoice_id,
              amount: fields.amount,
              billerid: fields.BILLER_MASTER_ID
            }
          ]
          break
        case 'register':
          postParams.makepaymentdata = [
            {
              viewbillresponseid: fields.aggregator_order_id || 0,
              amount: fields.amount,
              billerid: fields.BILLER_MASTER_ID
            }
          ]
          if (fields.bank_account_number && fields.ifsc_number) {
            postParams.makepaymentdata[0].account_number = fields.bank_account_number
            postParams.makepaymentdata[0].ifsc = fields.ifsc_number
            postParams.makepaymentdata[0].txntype = 'netbank'
          }
          break
        default:
          // recharge case
          postParams.makepaymentdata = [
            {
              billerid: fields.BILLER_MASTER_ID,
              rechargeid: '',
              amount: fields.amount,
              circle_name: '',
              circleid: '',
              planid: '',
              plan_category_name: '',
              first_name: fields.buyerData.firstname,
              last_name: fields.buyerData.lastname,
              email: fields.buyerData.email_id,
              account_id: fields.account_id,
              details: fields.details,
              mobile_number: fields.mobile_number
            }
          ]
      }
      // Call payments api for order confirmation

      const postUrl = util.paymentsUrl + 'bbps/bbps.php'
      console.log('postParams>>', postParams)
      console.log('postURL>>', postUrl)

      const postData = qs.stringify(postParams)
      console.log('postDataJSOn>>', JSON.stringify(postParams))
      console.time('TIMER_POINTBANK_BILL_PAYMENT')
      const response = await axios({
        method: 'post',
        url: postUrl,
        data: JSON.stringify(postParams),
        headers: { 'Content-Type': 'application/json' }
      })
      console.timeEnd('TIMER_POINTBANK_BILL_PAYMENT')

      // $err['200']               = "Payment successful";
      // $err['recharge_status'] = $payment_status; //PAID, PENDING....
      // $err['response']     = $curlresult;
      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'response', fields: { status: response.status } })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving payment response details', bank_response: { message: 'Server error', response: { status: response.status } }, billerStatus: 'P' }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'response', fields: response.data })

      const transactionObj = typeof response.data == 'object' ? response.data : JSON.parse(response.data)
      // console.log(transactionObj)
      let billerStatus = 'P'
      if (typeof transactionObj === 'object' && Object.keys(transactionObj).length > 0 && (('200' in transactionObj) || ('400' in transactionObj))) {
        log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'response', fields: {} })
        if (transactionObj['200']) {
          const message = transactionObj['200']
          if (transactionObj.recharge_status == 'PAID') {
            billerStatus = 'S'
          } else {
            billerStatus = 'P'
          }
          log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'response', fields: transactionObj })
          return { status: 200, respcode: 1000, message: transactionObj.msg, bank_response: { message: message, response: transactionObj }, billerStatus: billerStatus }
        } else {
          billerStatus = 'P' // immediate failure mark as pending
          const message = transactionObj['400']
          log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'response', fields: transactionObj })
          return { status: 200, respcode: 1131, message: errorMsg.responseCode[1131] + ': ' + message, bank_response: { message: message, response: transactionObj }, billerStatus: billerStatus }
        }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'response', fields: { notanobject: transactionObj } })
      return { status: 400, respcode: 1132, message: errorMsg.responseCode[1132], bank_response: { message: 'Unknown response', response: transactionObj }, billerStatus: billerStatus }
    } catch (err) {
      console.log('callPaymentResponseError', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'callPaymentResponse', type: 'catcherror', fields: { codeerror: err.message } })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], bank_response: { codeerror: err.message }, billerStatus: 'P' }
    } finally {
      // Release the connection
      if (isSet) connection.release()
    }
  }

  static async payInstantBill (_, fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'payInstantBill', type: 'request', fields: fields })

    let isSet = false
    if (connection == null || connection == undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }

    try {
      const payInstantBillData = await this.callPaymentResponse(_, fields, connection)

      console.log('payInstantBillDataRes>>', payInstantBillData)

      // 100 % Success
      let billpaymentstatus = 'P'
      let bank_response = {}

      if ('billerStatus' in payInstantBillData) {
        billpaymentstatus = payInstantBillData.billerStatus
      }
      bank_response = JSON.stringify(payInstantBillData.bank_response)

      // const billPayTransactionCtrl = require('../billpayTransaction/billpayTransaction')

      const updateTranResponse = await transactionCtrl.updateWhereData(connection, {
        data: {
          transaction_status: billpaymentstatus
        },
        id: fields.ma_transaction_master_id,
        where: 'ma_transaction_master_id'
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'payInstantBill', type: 'updateTranResponse', fields: updateTranResponse })

      const updateBillPayTranResponse = await billPayTransactionCtrl.updateWhereData(connection, {
        data: {
          transaction_status: billpaymentstatus,
          bank_response: JSON.stringify(bank_response)
        },
        id: fields.ma_billpay_transactionid,
        where: 'ma_billpay_transactionid'
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'payInstantBill', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })

      let billTxnDetail = {}
      // Send SMS in case of succss/failure
      if (billpaymentstatus == 'S' || billpaymentstatus == 'F') {
        var sql = `SELECT * from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}'`
        const getBillTxnDetails = await this.rawQuery(sql, connection)
        if (getBillTxnDetails.length > 0) {
          let message = ''
          let template = ''
          if (billpaymentstatus == 'S') {
            message = util.communication.BILLPAYSUCCESS
            template = util.templateid.BILLPAYSUCCESS
          } else {
            message = util.communication.BILLPAYFAILURE
            template = util.templateid.BILLPAYFAILURE
          }
          billTxnDetail = getBillTxnDetails[0]

          var dt = new Date(billTxnDetail.updatedon)
          const datetimeformat = dt.toLocaleString('en-IN')
          message = message.replace('<Provider name>', billTxnDetail.provider_name)
          message = message.replace('<amount>', billTxnDetail.amount)
          message = message.replace('<date time>', datetimeformat)
          message = message.replace('<orderid>', fields.aggregator_order_id)
          message = message.replace('<signature>', util.communication.Signature)

          const sms = require('../../util/sms')
          await sms.sentSmsAsync(message, fields.mobile_number, template)
        } else {
          console.log('SMSSkippedAsTransactionDetailsNotFound>>')
        }
      }

      if (billpaymentstatus == 'S') {
        // give incentive to this
        const addBBPSIncentiveResponse = await this.addBBPSIncentive({
          aggregator_order_id: fields.aggregator_order_id,
          bill_amount: fields.amount
        }, connection) // What if failed
        console.log('addBBPSIncentiveResponse >> ', addBBPSIncentiveResponse)
        console.log('billTxnDetail >> ', billTxnDetail)

        // Check if integrated merchant
        const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${billTxnDetail.userid}`
        const integratedMer = await this.rawQuery(userSQL, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'payInstantBill', type: 'response-int', fields: integratedMer })
        if (integratedMer.length > 0) {
          // Post receipt
          var receiptData = {}
          receiptData.action = 'POSTRECEIPT'
          receiptData.aggregator_user_id = integratedMer[0].aggregator_user_id
          receiptData.aggregator_order_id = fields.aggregator_order_id
          receiptData.ma_user_id = billTxnDetail.ma_user_id
          const responsePostReceipt = await integrated.index(receiptData, connection)
          console.log('integrated returned this', responsePostReceipt)
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else { // failed
        // revert ledgers incentive to this
        if (billpaymentstatus == 'F') {
          const reverseBBPSLedgersResponse = await this.reverseBBPSLedgers({
            orderid: fields.aggregator_order_id
          }, connection) // What if failed
          console.log('PayInstantBillReverseBBPSLedgers >> ', reverseBBPSLedgersResponse)
          return { status: 400, respcode: 1131, message: errorMsg.responseCode[1131] + ': Transaction failed' }
        } else {
          log.logger({ pagename: require('path').basename(__filename), action: 'payInstantBill', type: 'PendingCase', fields: payInstantBillData })
          return { status: 200, respcode: 1130, message: errorMsg.responseCode[1130] }
        }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'payInstantBill', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (isSet) connection.release()
    }
  }

  static async refundPointBank (fields) {
    fields.transaction_status = 'R'
    fields.skip_entry = true
    const refundCtrl = require('../settlement/refundController')
    return await refundCtrl.bbpsRefund(null, fields)
  }

  static async updateBBPSTransaction (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateBBPSTransaction', type: 'request', fields: fields })

    let isSet = false
    if (connection == null || connection == undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }

    try {
      var sql = `SELECT * from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}'`
      const getBillTxnDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateBBPSTransaction', type: 'getBillTxnDetails', fields: getBillTxnDetails })
      if (getBillTxnDetails.length <= 0) {
        console.log('BillpayTransactionNotFound')
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
      }

      const currenrPaymentStatus = getBillTxnDetails[0].payment_status
      log.logger({ pagename: require('path').basename(__filename), action: 'updateBBPSTransaction', type: 'currenrPaymentStatus === fields.payment_status ', fields: { currenrPaymentStatus, payment_status: fields.payment_status } })
      // Initially currenrPaymentStatus = I , Incoming expected P,S,F status
      if (currenrPaymentStatus == fields.payment_status) {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
      }
      /** *** Start */
      //
      // Update Transactions recevied Data
      const updateTranResponse = await transactionCtrl.updateTransaction(null, {
        aggregator_order_id: fields.aggregator_order_id,
        aggregator_txn_id: fields.aggregator_txn_id,
        transaction_status: fields.transaction_status, // as dicussed with anmol,yashwant final status update later
        amount: fields.amount,
        bank_rrn: fields.bank_rrn,
        connection: connection,
        transaction_reason: fields.message
      })

      console.log('updateTranResponse', updateTranResponse)

      const updateBillPayTranResponse = await billPayTransactionCtrl.updateWhereData(connection, {
        data: {
          transaction_status: fields.bill_transaction_status,
          payment_status: fields.payment_status,
          payment_response: JSON.stringify(fields.ponintbankResObj)
        },
        id: fields.ma_billpay_transactionid,
        where: 'ma_billpay_transactionid'
      })

      console.log('updateBillPayTranResponse', updateBillPayTranResponse)

      if (fields.successResponse === true && updateTranResponse.status == 200 && updateBillPayTranResponse.affectedRows >= 0) {
        console.log('Success Payment Calling')
        const paymentResponseData = await this.payInstantBill(null, fields, connection)
        console.log('payInstantBillResponse>>', paymentResponseData)
        return paymentResponseData
      } else {
        console.log('TRANSACTIONSTATUS != 200 Pending or Failure Case Payment,', fields.successResponse, updateTranResponse, updateBillPayTranResponse)
        if (fields.payment_status == 'F') {
          const reverseBBPSLedgersResponse = await this.reverseBBPSLedgers({
            orderid: fields.aggregator_order_id
          }, connection) // What if failed
          console.log('updateBBPSTransactionReverseBBPSLedgers >> ', reverseBBPSLedgersResponse)
          return { status: 400, respcode: 1028, message: fields.message }
          // to do 1001
        } else {
          return { status: 200, respcode: 1130, message: errorMsg.responseCode[1130] }
        }
      }

      /** **** End */
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateBBPSTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (isSet) connection.release()
    }
  }

  static async getPrivateKey (mer_dtlsObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'request', fields: mer_dtlsObj })
    try {
      // fields.
      const user = {
        secretKey: mer_dtlsObj.secret,
        userName: mer_dtlsObj.username,
        password: mer_dtlsObj.password
      }

      console.log('user>>', user)
      // Create hash private key for order confirmation api
      const keyData = `${user.secretKey}@${user.userName}:|:${user.password}`
      console.log(keyData)

      const privatekey = common.createHash(keyData)
      log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'resposne', fields: privatekey })
      return privatekey
    } catch (error) {
      console.log('getPrivateKeyError')
      return false
    }
  }

  static async addBBPSIncentive (fields, connection = null) {
    let isSet = false
    const response = {}
    console.log('addBBPSIncentiveAdding>>>', fields)
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const _user = await this.getOrderUser(fields.aggregator_order_id, connection)
      if (_user.length > 0) {
        var params = {}
        params = {
          aggregator_order_id: fields.aggregator_order_id
        }
        const transactionCtrl = require('./transactionController')
        const _data = await transactionCtrl.findMatching('', params)

        console.log('transactionDetails >>', _data)
        // if (_data[0].commission_amount <= 0) {
        //   response.status = 400
        //   response.message = errorMsg.responseCode[1028] + ' Invalid amount for add incentive'
        //   response.respcode = 1028
        //   return response
        // }
        const incfields = {
          commission_amount: _data[0].commission_amount,
          amount: fields.bill_amount,
          ma_user_id: _user[0].profileid,
          orderid: fields.aggregator_order_id,
          userid: _user[0].userid,
          user_type: _user[0].user_type,
          distributer_user_master_id: _user[0].distributer_user_master_id,
          stateid: _user[0].state,
          pan: _user[0].pan,
          connection
        }
        let incentive_distribution = {}
        const bbpsinecntive = require('../incentive/bbpsIncentiveController')
        const bbpsinsuranceIncentive = require('../incentive/bbpsinsuranceIncentiveController')
        const electricityIncentive = require('../incentive/electricityIncentiveController')
        const rehcargeIncentive = require('../incentive/rechargeIncentiveController')
        await mySQLWrapper.beginTransaction(connection)
        if (_data[0].transaction_type == 17) {
          switch (_data[0].remarks) {
            case 'LIC Insurance Transaction':
              incentive_distribution = await bbpsinsuranceIncentive.incentiveDistribution(incfields)
              break
            case 'Electricity':
              incentive_distribution = await electricityIncentive.incentiveDistribution(incfields)
              break
            case 'RECHARGE TRANSACTION':
              incentive_distribution = await rehcargeIncentive.incentiveDistribution(incfields)
              break
            default:
              incentive_distribution.status = 400
              incentive_distribution.message = 'Invalid transaction type'
              break
          }
        } else if (_data[0].transaction_type == 6) {
          incentive_distribution = await bbpsinecntive.incentiveDistribution(incfields)
        } else {
          incentive_distribution.status = 400
          incentive_distribution.message = 'Invalid transaction type'
        }

        if (incentive_distribution.status === 200) {
          response.status = '200'
          response.message = errorMsg.responseCode[1000]
          response.respcode = 1000
          await mySQLWrapper.commit(connection)
        } else {
          response.status = 400
          response.message = errorMsg.responseCode[1001]
          response.respcode = 1001
          await mySQLWrapper.rollback(connection)
        }
      } else {
        response.status = 400
        response.message = errorMsg.responseCode[1003]
        response.respcode = 1003
      }

      return response
    } catch (error) {
      console.log('addBBPSIncentiveError', error)
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + error.message }
    } finally {
      // Release the connection
      if (isSet) connection.release()
    }
  }

  /*
    Fetch user details from mobile number
  */
  static async getOrderUser (aggregator_order_id, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      const sql = `select mum.profileid,mum.userid,mum.distributer_user_master_id,mum.user_type,mum.state,CAST(AES_DECRYPT(mum.pan,'${decryptionKey}') AS CHAR) as pan from ma_transaction_master as tm  	
      JOIN ma_user_master as mum on tm.ma_user_id = mum.profileid 	
      where tm.aggregator_order_id='${aggregator_order_id}' AND mum.user_status = "Y"`
      // const sql = `select mum.profileid,mum.userid,mum.distributer_user_master_id,mum.user_type,mum.state,mum.pan from ma_transaction_master as tm
      // JOIN ma_user_master as mum on tm.ma_user_id = mum.profileid
      // where tm.aggregator_order_id='${aggregator_order_id}' AND mum.user_status = "Y"`
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      console.log('getOrderUserErr>>', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'getOrderUser', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async reverseBBPSLedgers (fields, connection = null) {
    var isSet = false
    console.log('reverseBBPSLedgers>>>', fields)
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      fields.transaction_status = 'REV'
      var sql = `SELECT t.*,b.payment_status,b.ma_billpay_transactionid from ma_transaction_master as t JOIN ma_billpay_transaction_master as b on b.ma_transaction_master_id = t.ma_transaction_master_id  where aggregator_order_id='${fields.orderid}' limit 1`
      const getTxnDetails = await this.rawQuery(sql, connection)
      if (getTxnDetails.length <= 0) {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
      }

      var pointssql = `SELECT * from ma_points_ledger_master where mode = 'dr' AND orderid='${fields.orderid}'AND transaction_type = '${getTxnDetails[0].transaction_type}'`
      const getPointsDetails = await this.rawQuery(pointssql, connection)
      // if (getPointsDetails.length <= 0) {
      //   return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' Debit Entry not found !' }
      // }

      // Check if integrated merchant
      const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${getTxnDetails[0].userid}`
      const integratedMer = await this.rawQuery(userSQL, connection)
      console.log('REFUND', userSQL)
      log.logger({ pagename: 'pointsbank.js', action: 'reverseBBPSLedgers', type: 'response', fields: integratedMer })
      if (integratedMer.length > 0) {
        var postdata = {}
        postdata.action = 'REFUND'
        postdata.aggregator_user_id = integratedMer[0].aggregator_user_id
        postdata.aggregator_order_id = fields.orderid
        const res = await integrated.index(postdata, connection)
        console.log('integrated returned this', res)
        if (res.status != 200) {
          return res
        }
      }

      const txnDetails = getTxnDetails
      const pointsLedger = require('../creditDebit/pointsLedgerController')
      if (getPointsDetails.length > 0) {
      // Reverse BBPS txn if failed in settlement
        const newFields = {
          distributorId: util.airpayUserId,
          retailerId: txnDetails[0].ma_user_id,
          amount: txnDetails[0].amount,
          userid: txnDetails[0].userid,
          orderid: fields.transaction_status + '-' + fields.orderid,
          commissionType: txnDetails[0].transaction_type,
          txnStatus: fields.transaction_status,
          connection: connection
        }

        const resp = await pointsLedger.sendMoney(null, newFields)
        console.log('sendMoneyRevert', resp)
        if (resp.status != 200) return resp
      }
      const common = require('../../util/common')
      // Reverse BBPS Surcharges, incentives and TDS txn if failed in settlement

      // connection replica changes
      const connectionReplica = await mySQLWrapper.getConnectionFromReadReplica()
      // var ledgerssql = `SELECT * from ma_points_ledger_master where orderid like '%${fields.orderid}' and transaction_type in (14) order by mode,ma_points_ledger_master_id desc`
      var ledgerssql = `SELECT * from ma_points_ledger_master where parent_id = '${fields.orderid}' and transaction_type in (14, 9, 11, 18) order by mode,ma_points_ledger_master_id desc`
      log.logger({ pagename: require('path').basename(__filename), action: 'reverseBBPSLedgers', type: 'LIKESQL', fields: ledgerssql })
      const incentiveDetails = await pointsLedger.rawQuery(ledgerssql, connectionReplica)
      log.logger({ pagename: require('path').basename(__filename), action: 'reverseBBPSLedgers', type: 'incentiveDetails', fields: incentiveDetails })
      connectionReplica.release()
      // connection replica changes end
      if (incentiveDetails.length > 0) {
        for (var i = 0; i < incentiveDetails.length; i++) {
          let descType = ''
          fields.transaction_type = incentiveDetails[i].transaction_type // reverse surcharge
          const descTypeObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.transaction_type }, connection)
          descType = descTypeObj.code_desc ? descTypeObj.code_desc : ''
          const newFields = {
            ma_user_id: incentiveDetails[i].ma_user_id,
            corresponding_id: incentiveDetails[i].corresponding_id,
            amount: -incentiveDetails[i].amount,
            userid: incentiveDetails[i].userid,
            orderid: fields.transaction_status + '-' + incentiveDetails[i].orderid,
            mode: 'cr',
            transaction_type: incentiveDetails[i].transaction_type,
            ma_status: fields.transaction_status,
            // description: (fields.transaction_status === 'R' ? 'Refunded - ' : 'Reversed - ') + incentiveDetails[i].description.replace('debited', 'credited'),
            description: 'Credit - ' + descType + (fields.transaction_status === 'R' ? ' - Refunded' : ' - Reversed'),
            connection: connection
          }
          const resppoint = await pointsLedger.createEntry(null, newFields)
          if (resppoint.status === 400) {
            return resppoint
          }
        }
      }
      // write code here reversePaymodeCharges
      let paymodeChargeLedgerQuery = `SELECT * from ma_points_ledger_master where parent_id  = '${fields.orderid }' and transaction_type in ('70','71','72') and ma_status = 'S' ORDER BY ma_points_ledger_master_id desc limit 3`

      const paymodeChargeLedgerQueryResult = await this.rawQuery(paymodeChargeLedgerQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'paymodeChargeLedgerQueryResult', fields: paymodeChargeLedgerQueryResult })

      if (paymodeChargeLedgerQueryResult.length > 0) {
        for (let i = 0; i < paymodeChargeLedgerQueryResult.length; i++) {
          const mode = paymodeChargeLedgerQueryResult[i].mode == 'cr' ? 'dr' : 'cr'
          const amount = mode == 'cr' ? paymodeChargeLedgerQueryResult[i].amount * -1 : paymodeChargeLedgerQueryResult[i].amount
          const reverseTxnDetails = {
            ma_user_id: paymodeChargeLedgerQueryResult[i].ma_user_id,
            corresponding_id: paymodeChargeLedgerQueryResult[i].corresponding_id,
            amount,
            mode,
            userid: paymodeChargeLedgerQueryResult[i].userid,
            orderid: 'REV -' + paymodeChargeLedgerQueryResult[i].orderid,
            transaction_type: paymodeChargeLedgerQueryResult[i].transaction_type,
            ma_status:fields.transaction_status,
            description: paymodeChargeLedgerQueryResult[i].description + '- Reversed',
            connection: connection
          }

          const resppoint = await pointsLedger.createEntry(null, reverseTxnDetails)
          if (resppoint.status === 400) return resppoint
        }
        const bankPaymodeChargesSql = `SELECT * from ma_bank_paymode_charges where order_id = '${fields.orderid}' ORDER BY ma_bank_paymode_charges_id limit 2`
        const bankPaymodeChargesSqlResult = await this.rawQuery(bankPaymodeChargesSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'bankPaymodeChargesSqlResult', fields: bankPaymodeChargesSqlResult })
  
        if (bankPaymodeChargesSqlResult?.length) {
          for (let i = 0; i < bankPaymodeChargesSqlResult.length; i++) {
            const sql = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
            const params = [bankPaymodeChargesSqlResult[i].ma_user_id, bankPaymodeChargesSqlResult[i].order_id, bankPaymodeChargesSqlResult[i].charges_type, bankPaymodeChargesSqlResult[i].bank_name, bankPaymodeChargesSqlResult[i].transaction_type, bankPaymodeChargesSqlResult[i].sub_transaction_type, bankPaymodeChargesSqlResult[i].transaction_amount, -bankPaymodeChargesSqlResult[i].charges, 'REV']
            await this.secureRawQuery(sql, { params, connection: connection })
          }
        }
 
      }


      // if (resp.status == 200) {
      console.log('mark R for point bank payment', txnDetails[0].ma_billpay_transactionid)
      const updateBillPayTranResponse = await billPayTransactionCtrl.updateWhereData(connection, {
        data: {
          payment_status: 'R'
        },
        id: txnDetails[0].ma_billpay_transactionid,
        where: 'ma_billpay_transactionid'
      })
      // }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      console.log('reverseBBPSLedgersErr>>', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'reverseBBPSLedgers', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
    
  }
  
  static async bbpsRequeryCron () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Get pending transactions with payment_status success

      // LIVE ISSUE BBPS Cron not fetch pending transactions ----- start
      const sql = `SELECT billpay.order_id,trans.aggregator_txn_id,billpay.ma_transaction_master_id
                  FROM ma_billpay_transaction_master AS billpay
                  INNER JOIN ma_transaction_master AS trans ON billpay.ma_transaction_master_id = trans.ma_transaction_master_id
                  WHERE trans.transaction_status = 'P' AND billpay.payment_status = 'S'
                  AND billpay.transaction_status = 'P'
                  AND TIMESTAMPDIFF(MINUTE,billpay.updatedon,CURRENT_TIMESTAMP) >= 15
                  `
      // switched to transaction_master transaction_status instead of billpay
      // added 'P' in billpay payment_status ~ elton 22-11-2021
      // LIVE ISSUE BBPS Cron ----- end

      const queryResult = await this.rawQuery(sql, connection)
      console.log('---CRON QUERY RESULT---', queryResult)
      // console.log(queryResult)
      if (queryResult.length > 0) {
        for (const result of queryResult) {
          console.log('result>>', result)
          // Call bbps requery api from payments
          // const postData = qs.stringify(postParams) S -******** D - 215808
          try {
            if (result.aggregator_txn_id > 0) {
              const postData = JSON.stringify({ txnId: result.aggregator_txn_id })
              console.log('---CURL URL---', util.paymentsUrl + 'bbps/bbps_requery.php')
              const transactionResp = await axios({
                method: 'post',
                url: util.paymentsUrl + 'bbps/bbps_requery.php',
                data: postData,
                headers: { 'Content-Type': 'application/json' }
              })

              if (transactionResp.status !== 200) {
                log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'curl response', fields: transactionResp.data })
                // return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving transaction details' }
                continue
              }
              console.log('---CURL RESPONSE---[' + result.order_id + ']', transactionResp.data)
              if (typeof transactionResp.data.data != 'undefined') {
                const transData = transactionResp.data.data
                // Transaction start
                // await mySQLWrapper.beginTransaction(connection)
                let transStatus = ''
                if (transData.FLAG == 'B') { // SUCCESS (Add incentive)
                  transStatus = 'S'
                  // aggregator_order_id, bill_amount
                  const incentiveParams = {
                    aggregator_order_id: result.order_id,
                    bill_amount: transData.PAYMENTAMOUNT
                  }
                  const incentiveResponse = await this.addBBPSIncentive(incentiveParams, connection)
                  // if (incentiveResponse.status === 200) {
                  //   // Rollback
                  //   await mySQLWrapper.rollback(connection)
                  // }
                } else if ((transData.FLAG == 'F' || transData.FLAG == 'RF') || (transData.status == 400 && transData.message.indexOf('Transaction details not found.') > -1)) { // Fail / Refund (Reverse wallet)
                  transStatus = 'F'
                  const reverseParams = {
                    orderid: result.order_id
                  }
                  const reverseResponse = await this.reverseBBPSLedgers(reverseParams, connection)
                  if (reverseResponse.status != 200) {
                  //   // Rollback
                  //   await mySQLWrapper.rollback(connection)
                    continue
                  }
                  console.log('reverseResponse>>[' + result.order_id + ']', reverseResponse)
                }

                // Update transaction response in db
                if (transStatus.length > 0) {
                  let updateBankResponse = {}
                  let bankResponse = (transData.RESPONSE || '').trim()
                  // bankResponse = '{"txnid":2230809,"operator_ref":"","status":1,"message":"Transaction Submitted Successfully Done, Check Status in Transaction Report, Thanks"}'
                  if (typeof bankResponse != 'undefined') {
                    try {
                      bankResponse = JSON.parse(bankResponse)
                    } catch (error) {
                      console.log('JsonParseError', error)
                    }

                    updateBankResponse.response = bankResponse
                    if (typeof bankResponse.message != 'undefined') {
                      updateBankResponse.message = bankResponse.message
                    }
                  }
                  updateBankResponse = JSON.stringify(updateBankResponse)
                  console.log('---BANK RESPONSE---')
                  console.log(updateBankResponse)

                  // const updateTrans = `UPDATE ma_billpay_transaction_master
                  //         SET transaction_status = '${transStatus}' , bank_response = '${updateBankResponse}'
                  //         WHERE order_id = '${result.order_id}'`
                  // const updateTransResult = await this.rawQuery(updateTrans, connection)

                  console.log('updateBillPayTranResponse>>', result.order_id, updateBankResponse)
                  const updateBillPayTranResponse = await billPayTransactionCtrl.updateWhereData(connection, {
                    data: {
                      transaction_status: transStatus,
                      bank_response: updateBankResponse
                    },
                    id: result.order_id,
                    where: 'order_id'
                  })

                  console.log('UpdateTransactionMaster>>', result.ma_transaction_master_id, transStatus)
                  const updateTranResponse = await transactionCtrl.updateWhereData(connection, {
                    data: {
                      transaction_status: transStatus
                    },
                    id: result.ma_transaction_master_id,
                    where: 'ma_transaction_master_id'
                  })
                }

                // Transaction end
                // await mySQLWrapper.commit(connection)
                console.log('-------Final Committed------')

                // If transaction status is fail then revert wallet/ledger entries
              } else {
                console.log('---DATA NOT FOUND IN RESPONSE---')
              }
            } else {
              log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'response', fields: { aggregator_txn_id: result.aggregator_txn_id, aggregator_order_id: result.order_id } })
            }
          } catch (error) {
            log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'catchErrorLoop[' + result.order_id + ']', fields: error })
          }
        }
        return { status: 200, respcode: 100, message: 'Cron executed successfully.' }
      } else {
        log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'query response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : No records found' }
      }
    } catch (error) {
      log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'catch response', fields: {} })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + error.message }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async bbpsPointBankRequeryCron () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Get pending transactions with payment_status initited
      const sql = `SELECT trans.transaction_type, billpay.order_id as aggregator_order_id,trans.aggregator_txn_id,billpay.ma_user_id,billpay.userid,requests.mer_dtls as mer_dtlsObj,requests.bill_amount,requests.transaction_amount,billpay.ma_billpay_transactionid,requests.api_response,requests.api_request,billpay.provider_id,billpay.action_type,billpay.customer_mobile_number,billpay.ma_transaction_master_id,billpay.payment_status,trans.transaction_status 
                  FROM ma_billpay_transaction_master AS billpay
                  INNER JOIN ma_transaction_master AS trans ON billpay.ma_transaction_master_id = trans.ma_transaction_master_id
                  INNER JOIN ma_makepayment_requests AS requests ON billpay.makepayment_request_id = requests.makepayment_request_id
                  WHERE billpay.payment_status IN ('I','P')
                  AND billpay.transaction_status = 'I' 
                  AND trans.transaction_status IN ('I','P') 
                  AND TIMESTAMPDIFF(MINUTE,billpay.updatedon,CURRENT_TIMESTAMP) >= 15
                   `
      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'bbpsPointBankRequeryCron', type: 'queryResult', fields: queryResult })

      // console.log(queryResult)
      const cronResult = []
      const buyerDataMaster = {}
      if (queryResult.length > 0) {
        for (const result of queryResult) {
          // Call bbps requery api from payments
          // const postData = qs.stringify(postParams) S -******** D - 215808
          console.log('result>>', result)
          let apiResponse = {}
          if (result.transaction_status == 'I' && result.payment_status == 'I') {
            // const fail_ma_transaction_status = `update ma_transaction_master set transaction_status='F',transaction_reason = 'Transaction not completed by merchant' where ma_transaction_master_id='${result.ma_transaction_master_id}'`
            // const fail_ma_resp = await this.rawQuery(fail_ma_transaction_status, connection)

            /* RISK MANAGEMENT Changes */
            const data = {
              transaction_reason: 'Transaction not completed by merchant',
              transaction_status: 'F'
            }

            const updateTransactionResult = await transaction.updateWhereData(connection, {
              data,
              id: result.aggregator_order_id,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            const fail_ma_billpay_status = `update ma_billpay_transaction_master set transaction_status='F', payment_status = 'F' where ma_transaction_master_id='${result.ma_transaction_master_id}'`
            const fail_ma_bill_resp = await this.rawQuery(fail_ma_billpay_status, connection)

            log.logger({ pagename: require('path').basename(__filename), action: 'bbpsPointBankRequeryCron', type: 'Failed_Transaction_Response', fields: fail_ma_bill_resp })
          } else {
            try {
              apiResponse = { ...JSON.parse(result.api_request || '{}'), ...JSON.parse(result.api_response || '{}') }
              apiResponse = apiResponse.data || apiResponse
              console.log('apiResponse>>', apiResponse)
            } catch (error) {
              log.logger({ pagename: require('path').basename(__filename), action: 'bbpsIpn', type: 'error', fields: error })
              console.log('apiResponseErrorIgnore', error)
              continue
            }

            const mer_dtlsObj = JSON.parse(result.mer_dtlsObj)
            const privatekey = await this.getPrivateKey(mer_dtlsObj)
            const orderConfirmResponse = await this.orderConfirmation(null, {
              mer_dtlsObj: JSON.parse(result.mer_dtlsObj),
              aggregator_order_id: result.aggregator_order_id,
              ma_user_id: result.ma_user_id,
              userid: result.userid,
              transaction_amount: result.transaction_amount,
              isCron: true
            })

            console.log('orderConfirmResponse>>', orderConfirmResponse)

            log.logger({ pagename: require('path').basename(__filename), action: 'bbpsPointBankRequeryCron', type: 'OuterresponseorderConfirmResponse', fields: orderConfirmResponse })

            if (orderConfirmResponse.status == 400) {
              console.log('orderConfirmResponse400IgnoreProcessing>>')
              continue
            }

            let aggregator_txn_id = 0
            let bank_rrn = ''
            let message = ''
            let ponintbankResObj = {}
            let successResponse = false
            let payment_status = 'P'
            let transaction_status = 'P'
            let bill_transaction_status = 'I'
            if (orderConfirmResponse.status == 200) {
              aggregator_txn_id = orderConfirmResponse.aggregator_txn_id
              bank_rrn = orderConfirmResponse.rrn
              message = orderConfirmResponse.transaction_reason
              ponintbankResObj = orderConfirmResponse.transactionObj

              let buyerData = {}
              if (result.ma_user_id in buyerDataMaster) {
                buyerData = buyerDataMaster[result.ma_user_id]
              } else {
                const buyerDataArr = await this.getBuyersData({
                  ma_user_id: result.ma_user_id
                }, connection)

                if (buyerDataArr.length <= 0) {
                  log.logger({ pagename: require('path').basename(__filename), action: 'bbpsPointBankRequeryCron', type: 'response', fields: 'buyerData not found' })
                }
                buyerDataMaster[result.ma_user_id] = {}
                buyerDataMaster[result.ma_user_id] = buyerDataArr[0]
                buyerData = buyerDataArr[0]
              }

              if (orderConfirmResponse.transaction_status == 'S') {
                payment_status = 'S'
                transaction_status = 'P'
                bill_transaction_status = 'I'
                successResponse = true
              } else if (orderConfirmResponse.transaction_status == 'F') {
                payment_status = 'F'
                transaction_status = 'F'
                bill_transaction_status = 'F'
                successResponse = false
              } else {
                payment_status = 'P'
                transaction_status = 'P'
                successResponse = false
              }

              // if (result.transaction_type == 17) {
              //   return await rechargeController.updateRechargeTransaction({
              //     ma_user_id: result.ma_user_id,
              //     userid: result.userid,
              //     aggregator_order_id: result.aggregator_order_id,
              //     aggregator_txn_id,
              //     payment_status,
              //     transaction_status,
              //     amount: result.bill_amount,
              //     bank_rrn,
              //     message,
              //     ponintbankResObj,
              //     ma_transaction_master_id: result.ma_transaction_master_id,
              //     ma_billpay_transactionid: result.ma_billpay_transactionid,
              //     successResponse,
              //     mobile: result.customer_mobile_number,
              //     PAYMENTAMOUNT_VALIDATION: apiResponse.PAYMENTAMOUNT_VALIDATION,
              //     BILLER_MASTER_ID: apiResponse.BILLER_MASTER_ID
              //   }, connection)
              // }
              const apiRquest = JSON.parse(result.api_request)
              let bbpsTransactionRes = {}
              if (apiRquest.shigrapay) {
                bbpsTransactionRes = await BaseShigrapayController.updateShigrapayTransaction({
                  ...apiRquest.receiptData,
                  ma_user_id: result.ma_user_id,
                  userid: result.userid,
                  aggregator_order_id: result.aggregator_order_id,
                  aggregator_txn_id,
                  payment_status,
                  transaction_status,
                  amount: result.bill_amount,
                  bank_rrn,
                  message,
                  ponintbankResObj,
                  ma_transaction_master_id: result.ma_transaction_master_id,
                  ma_billpay_transactionid: result.ma_billpay_transactionid,
                  successResponse,
                  mobile: result.customer_mobile_number,
                  PAYMENTAMOUNT_VALIDATION: apiResponse.PAYMENTAMOUNT_VALIDATION,
                  BILLER_MASTER_ID: apiResponse.BILLER_MASTER_ID,
                  bill_transaction_status: bill_transaction_status
                }, connection)
              } else {
                bbpsTransactionRes = await this.updateBBPSTransaction({
                  aggregator_order_id: result.aggregator_order_id,
                  aggregator_txn_id: aggregator_txn_id,
                  transaction_status: transaction_status,
                  payment_status: payment_status,
                  amount: result.bill_amount,
                  bank_rrn: bank_rrn,
                  message: message,
                  ponintbankResObj: ponintbankResObj,
                  ma_billpay_transactionid: result.ma_billpay_transactionid,
                  successResponse: successResponse,
                  private_key: privatekey,
                  mercid: mer_dtlsObj.mercid,
                  action_type: result.action_type,
                  ma_user_id: result.ma_user_id,
                  requestid: apiResponse.requestid || result.aggregator_order_id,
                  provider_id: result.provider_id,
                  buyerData: buyerData,
                  account_id: apiResponse.account_id,
                  details: apiResponse.details || apiRquest.bill_details,
                  mobile_number: result.customer_mobile_number,
                  ma_transaction_master_id: result.ma_transaction_master_id,
                  ipnhit: false,
                  PAYMENTAMOUNT_VALIDATION: apiResponse.PAYMENTAMOUNT_VALIDATION || 'N',
                  customerid: apiResponse.customerid,
                  invoice_id: apiResponse.invoice_id || 0,
                  BILLER_MASTER_ID: apiResponse.BILLER_MASTER_ID,
                  bill_transaction_status: bill_transaction_status,
                  bank_account_number: apiRquest.bank_account_number,
                  ifsc_number: apiRquest.ifsc_number,
                  ma_bbps_register_bill_details_id: apiRquest.ma_bbps_register_bill_details_id
                }, connection)
              }

              console.log('bbpsTransactionRes >>', bbpsTransactionRes)
              cronResult.push({
                aggregator_order_id: result.aggregator_order_id,
                response: JSON.stringify(bbpsTransactionRes)
              })
            } else {
              console.log('orderConfirmResponse400Ignore >>', orderConfirmResponse)
            // return orderConfirmResponse
            }
          }
        }
        return { status: 200, respcode: 100, message: 'Cron executed successfully.', cronResult }
      } else {
        log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'query response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : No records found' }
      }
    } catch (error) {
      log.logger({ pagename: 'pointbankpayment.js', action: 'bbpsRequeryCron', type: 'catch response', fields: {} })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + error.message }
    } finally {
      // Release the connection
      connection.release()
    }
  }
}

module.exports = Pointbankpayment
