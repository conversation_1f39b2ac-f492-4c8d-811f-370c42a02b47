const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const common_fns = require('../../util/common_fns')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const axios = require('axios')
const { generateOrderchecksum } = require('../../util/checksum')
const util = require('../../util/util')
const parser = require('fast-xml-parser')
const qs = require('qs')
const common = require('../../util/common')
const transactionCtrl = require('./transactionController')
const refundTransactionController = require('../refund/refundtransactionController')
const collectMoneyDistribution = require('../incentive/collectMoneyDistributionController')
const SoundBoxManagementController = require('.././soundBoxManagement/soundBoxManagementController')

class collectMoneyController extends DAO {
  /**
   * Create Aadhaar pay transaction
   * @param {*} _
   * @param {*} fields
   * @returns updateADPAYResponse
   */
  static async createAdhaarPayOrder (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createAdhaarPayOrder', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let transaction_status = 'P'
      const captureResponseDecodeResult = Buffer.from(fields.captureResponse, 'base64').toString()
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'captureResponseDecodeResult', fields: captureResponseDecodeResult })

      const jsonObjectResult = JSON.parse(captureResponseDecodeResult)

      let authentication_type = ''

      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'jsonObjectResult', fields: jsonObjectResult })
      console.log('fauth value', jsonObjectResult.fauth)

      if (typeof jsonObjectResult === 'object' && jsonObjectResult != '') {
        authentication_type = jsonObjectResult.fauth ? jsonObjectResult.fauth : ''
      } else {
        authentication_type = ''
      }

      // check hard code lat and long
      fields.latitude = fields.latitude ? fields.latitude : ''
      fields.longitude = fields.longitude ? fields.longitude : ''
      if (fields.latitude == '19.0760' && fields.longitude == '72.8777') {
        const getDynamicLatLong = await this.getLatLongFromUserDetails({ ma_user_id: fields.ma_user_id })
        log.logger({ pagename: require('path').basename(__filename), action: 'createAdhaarPayOrder', type: 'response_getDynamicLatLong', fields: getDynamicLatLong })
        if (getDynamicLatLong.status == 200) {
          fields.latitude = getDynamicLatLong.lat_val
          fields.longitude = getDynamicLatLong.long_val
        }
      }

      // Step 1 :
      console.log('---STEP 1 : initiateTransaction (createAdhaarPayOrder) : Start---')
      // Create ADPAY transaction in Pending State
      console.time('TIME_FOR_ADPAY_INITIATE_TRANSACTION')
      const createTransaResponse = await transactionCtrl.initiateTransaction(_, fields.createTransactionParams)
      console.timeEnd('TIME_FOR_ADPAY_INITIATE_TRANSACTION')
      console.log('---Create ADPAY trans response---', createTransaResponse)
      if (createTransaResponse.status === 400 || createTransaResponse.status === 401) {
        console.log('---STEP 1 : initiateTransaction (doAepsCashWithdrawal) : Failed---')
        createTransaResponse.action_code = 1001
        return createTransaResponse // Return if error
      }
      const authTypeUpdateQuery = `UPDATE ma_aeps_transaction_logs SET authentication_type = '${authentication_type}' where order_id = '${fields.aggregator_order_id}';`
      log.logger({ pagename: require('path').basename(__filename), action: 'createAdhaarPayOrder', type: 'authTypeUpdateQuery', fields: authTypeUpdateQuery })
      const authTypeUpdateRes = await this.rawQuery(authTypeUpdateQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'createAdhaarPayOrder', type: 'authTypeUpdateRes', fields: authTypeUpdateRes })

      // add entry in cron table
      console.log('--- insert transaction details in ma_aeps_aadhaar_ledger_log able (doAepsCashWithdrawal) : Start---')
      const saveAdpayOrderVerifyData = `Insert into ma_aeps_aadhaar_ledger_log (ma_user_id, userid, order_id, amount, transaction_type) VALUES (${fields.ma_user_id},${fields.userid},'${fields.aggregator_order_id}','${fields.amount}','10')`
      const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doAepsCashWithdrawal', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })
      const ledgerCronId = saveAdpayOrderVerifyResult.insertId
      console.log('--- End---')

      console.log('---STEP 2 : callGenerateOrder (createAdhaarPayOrder) : Start---')
      // Call Generate order Payments API
      const response = await this.callGenerateOrder(fields)

      console.log('---Generate order response---', (response.data) ? response.data : response)
      if (response.status != 200) {
        console.log('---STEP 2 : callGenerateOrder (createAdhaarPayOrder) : Failed---')

        // Step 3
        console.log('---STEP 3 : orderConfirmation (createAdhaarPayOrder) : Start---')
        // Call order confirmation
        console.time('TIME_FOR_AEPS_ORDER_CONFIRMATION')
        const orderConfirmation = await this.orderConfirmation(fields)
        console.timeEnd('TIME_FOR_AEPS_ORDER_CONFIRMATION')
        console.log('---Order Confirmation response---', orderConfirmation)
        const updateOrderParams = {
          transactionid: 0,
          rrn: null,
          message: '',
          bankbalance: 0,
          ledger_cron_id: ledgerCronId
        }
        console.log(JSON.stringify(orderConfirmation.transactionObj))
        if (orderConfirmation.status === 200) {
          console.log('---STEP 3 : orderConfirmation (createAdhaarPayOrder) : Success---')
          transaction_status = orderConfirmation.transaction_status
          updateOrderParams.transactionid = orderConfirmation.aggregator_txn_id
          updateOrderParams.rrn = orderConfirmation.rrn
          updateOrderParams.message = orderConfirmation.transaction_reason == 'ERR:Suspected fraud' ? errorMsg.responseCode[12364] : orderConfirmation.transaction_reason
          updateOrderParams.bankbalance = orderConfirmation.bankbalance
          updateOrderParams.bank_code = fields.bank_code
          updateOrderParams.surcharge = fields.surcharge
          // updateOrderParams.transaction_mode = transaction_mode
          // Converting Pending transaction to Failed
          console.log('Transaction status --- ', transaction_status)
          if (transaction_status == 'P') {
            console.log('---STEP 3 : orderConfirmation (createAdhaarPayOrder) : Success--- returning P')
            var refund = await refundTransactionController.refundAeps('_', {
              ma_user_id: fields.ma_user_id,
              userid: fields.userid,
              amount: fields.total_amount,
              aggregator_order_id: orderConfirmation.aggregator_txn_id
            })
            console.log('---Refund Response---', refund)
            if (refund.status == '200') {
              transaction_status = 'F'
            }
          }
        } else {
          console.log(`---Refund : orderConfirmation (createAdhaarPayOrder) : Failure (${orderConfirmation})---`)
          transaction_status = 'F'
          console.log(`---Failed : ma_aeps_failed_temp (createAdhaarPayOrder) : On Failure (${JSON.stringify(orderConfirmation)})---`)
          var temptable = {}
          temptable.ma_transaction_master_id = ''
          temptable.ma_user_id = fields.ma_user_id
          temptable.aggregator_txn_id = fields.aggregator_order_id
          temptable.order_id = fields.aggregator_order_id
          temptable.ma_transaction_master_id = createTransaResponse.transaction_id
          temptable.amount = fields.total_amount
          temptable.status = 'I'
          temptable.attempts = '1'
          temptable.remark = orderConfirmation.message
          // comment below code - 31-12-2024, to avoid refund entries (executing from admin cron).
          this.TABLE_NAME = 'ma_aeps_failed_temp'
          const _result = await this.insert(connection, {
            data: temptable
          })
          /* comment below code as per discussion with compliance team - 08-01-2025
          // save order detaisl in adpay cron table - transaction_type = 10
          const status_code = orderConfirmation.status_code ? orderConfirmation.status_code : 211
          const saveAdpayOrderVerifyData = `Insert into ma_aadhaar_order_verify_log
                               (ma_user_id, userid, airpay_id, ma_transaction_master_id, order_id, amount, status_code, transaction_type)
                               VALUES (${fields.ma_user_id},${fields.userid},'',${createTransaResponse.transaction_id},'${fields.aggregator_order_id}','${fields.amount}','${status_code}',10)`
          const saveAdpayOrderVerifyResult = await this.rawQuery(saveAdpayOrderVerifyData, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'saveAdpayOrderVerifyResult', type: 'Adpay log result', fields: { saveAdpayOrderVerifyResult } })
          */
        }
        console.log(`---STEP 3 : orderConfirmation (createAdhaarPayOrder) : Success (${orderConfirmation.status})---`)

        // Step 4
        console.log('---STEP 4 : updateAdpayTransaction (createAdhaarPayOrder) : Start---')
        // Update the ADPAY transaction
        console.time('TIME_FOR_UPDATE_ADPAY_TRANSACTION')
        const updateADPAYResponse = await this.updateAdpayTransaction(fields, transaction_status, updateOrderParams)
        console.timeEnd('TIME_FOR_UPDATE_ADPAY_TRANSACTION')
        console.log('---Update ADPAY Transaction response---', updateADPAYResponse)

        if (updateADPAYResponse.status === 400) {
          console.log('---STEP 4 : updateAdpayTransaction (createAdhaarPayOrder) : Failed---')
        } else {
          console.log('---STEP 4 : updateAdpayTransaction (createAdhaarPayOrder) : Success---')
        }
      }

      // Generate order response
      const generateOrderResp = response.data
      //  generateOrderResp.transaction_mode = transaction_mode
      if (generateOrderResp.status == 400) {
        if (response.data.message == null || response.data.message == 'undefined') {
          response.data.message = errorMsg.responseCode[1001]
        }
        if (generateOrderResp.responseCode == '0059') {
          generateOrderResp.message = errorMsg.responseCode[12364]
        } else if (generateOrderResp.message.toLowerCase() == 'Please try next transaction after 30 minutes'.toLowerCase()) {
          generateOrderResp.message = errorMsg.responseCode[12365]
        }
        console.log('---STEP 2 : callGenerateOrder (createAdhaarPayOrder) : Success (Transaction Failed)---')
        transaction_status = 'F'
      } else if (generateOrderResp.status === 200) {
        console.log('---STEP 2 : callGenerateOrder (createAdhaarPayOrder) : Success (Transaction Success)---')
        transaction_status = 'S'
      }
      console.log('Final transaction status : ' + transaction_status)
      if (transaction_status == 'S') {
        /* SOUNDBOX CHANGES */
        const isPresent = await SoundBoxManagementController.checkTransactionTypeIsAllowed(fields.ma_user_id, 10)
        log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'isPresent', fields: isPresent })
        if (isPresent) {
          const sendSoundBoxNotificationResp = await SoundBoxManagementController.sendSoundBoxNotification({
            ma_user_id: fields.ma_user_id,
            amount: fields.amount,
            txnType: 'ADPAY',
            orderid: fields.aggregator_order_id,
            connectionRead: connection
          })
          log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'response', fields: sendSoundBoxNotificationResp })
        }
      }

      // Step 3
      console.log('---STEP 3 : updateAdpayTransaction (createAdhaarPayOrder) : Start---')
      // Update the ADPAY transaction
      console.time('TIME_FOR_UPDATE_ADPAY_TRANSACTION')
      generateOrderResp.ledger_cron_id = ledgerCronId
      const updateADPAYResponse = await this.updateAdpayTransaction(fields, transaction_status, generateOrderResp)
      console.timeEnd('TIME_FOR_UPDATE_ADPAY_TRANSACTION')
      console.log('---Update ADPAY Transaction response---', updateADPAYResponse)

      if (updateADPAYResponse.status === 400) {
        console.log('---STEP 3 : updateAdpayTransaction (createAdhaarPayOrder) : Failed---')
      }
      return updateADPAYResponse
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createAdhaarPayOrder', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async updateAdpayTransaction (fields, transaction_status, orderResponse) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateAdpayTransaction', type: 'Request', fields: { fields, transaction_status, orderResponse } })
    try {
      // Update Adpay transaction
      transaction_status = transaction_status == 'P' ? 'F' : transaction_status
      const updateTransParams = {
        ma_user_id: fields.ma_user_id,
        orderid: fields.aggregator_order_id,
        aggregator_txn_id: orderResponse.transactionid ? orderResponse.transactionid : 0,
        transaction_status: transaction_status,
        amount: fields.amount,
        rrn: orderResponse.rrn ? orderResponse.rrn : null,
        userid: fields.userid,
        transaction_reason: orderResponse.message,
        bank_balance: orderResponse.bankbalance ? orderResponse.bankbalance : 0.00,
        transaction_type: '10',
        transaction_mode: 'AP',
        surcharge: fields.surcharge_amount,
        aggregator_bank: fields.bank_code,
        ledger_cron_id: orderResponse.ledger_cron_id
      }
      const AdpaytransactionLog = await transactionCtrl.updateAEPSTransactionLog({}, {
        order_id: updateTransParams.orderid,
        airpay_id: updateTransParams.aggregator_txn_id,
        ma_status: updateTransParams.transaction_status,
        no_of_attempts: 1,
        bank_response: JSON.stringify(orderResponse)
      })
      console.log('---AepstransactionResponse---', AdpaytransactionLog)
      if (AdpaytransactionLog.status == 400) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      const updateTransResponse = await transactionCtrl.updateADPAYTransaction({}, updateTransParams)
      console.log('---updateAepsTransResponse---', updateTransResponse)
      updateTransResponse.respcode = 1000
      updateTransResponse.action_code = 1001
      return updateTransResponse
    } catch (err) {
      return { status: 400, respcode: 1028, message: err.message, action_code: 1001 }
    }
  }

  /**
   * Get surcharge amount for Aadhaar Pay
   * @param {*} _
   * @param {*} fields
   * @returns updateADPAYResponse
   */
  static async getADPaySurcharge (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getADPaySurcharge', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.connection = connection
      const sql = `SELECT ma_user_master_id,state,user_type FROM ma_user_master WHERE profileid = '${fields.ma_user_id}' AND userid = '${fields.userid} LIMIT 1'`
      const userData = await this.rawQuery(sql, connection)
      console.timeEnd('user validation query time >>>>')
      if (userData.length <= 0) {
        return { status: 400, message: 'fail : User does not exists.' }
      }
      fields.state_master_id = userData[0].state
      const cmDistribution = await collectMoneyDistribution.getDistribution(fields)
      console.log('cmDistribution', cmDistribution)
      if (cmDistribution.status == 200) {
        const customer_charge = cmDistribution[0].customer_charges
        const surcharge = cmDistribution[0].customer_charges_applied_type == 2 ? (fields.amount * (customer_charge / 100)) : customer_charge
        const adpay_limit = await common.getSystemCodes(this, util.adpay_max_limit, connection)
        var cal_total_amount = (+fields.amount) + (+surcharge)
        if (cal_total_amount > adpay_limit) {
          return { status: 400, respcode: 1002, message: 'Maximum amount should be less than 10000 (Inclusive of Surcharges).', action_code: 1003 }
        }
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          action_code: 1000,
          surcharge: surcharge,
          amount: fields.amount,
          total_amount: cal_total_amount
        }
      }
      return { status: 400, respcode: 1002, message: 'Fail: No Configurations found, Please contact the customer care.', action_code: 1003 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getADPaySurcharge', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async getPrivateKey (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'request', fields })
    try {
      // Get merchant details
      const userSql = `SELECT 
                        a.apikey,
                        a.username,
                        a.password,
                        b.amount
                      FROM ma_user_master AS a 
                      INNER JOIN ma_transaction_master AS b ON a.userid = b.userid
                      WHERE a.profileid = '${fields.ma_user_id}' 
                      AND a.userid = ${fields.userid}
                      AND b.aggregator_order_id = '${fields.orderid}' LIMIT 1`
      // console.log(userSql)
      const userResponse = await this.rawQuery(userSql, fields.connection)
      if (userResponse.length <= 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'response', fields: userResponse })
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
      }
      const user = {
        secretKey: userResponse[0].apikey,
        userName: userResponse[0].username,
        password: userResponse[0].password
      }

      console.log('user>>', user)
      // Create hash private key for order confirmation api
      const keyData = `${user.secretKey}@${user.userName}:|:${user.password}`
      console.log(keyData)

      const privatekey = common.createHash(keyData)
      log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'response', fields: privatekey })
      return privatekey
    } catch (error) {
      console.log('getPrivateKeyError')
      return false
    }
  }

  static async orderConfirmation (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'request', fields: fields })
    try {
      const privatekey = await this.getPrivateKey(fields)
      if (privatekey.status && privatekey.status === 400) {
        return privatekey
      }

      const postParams = {
        privatekey: privatekey,
        mercid: fields.ma_user_id,
        merchant_txnId: fields.aggregator_order_id ? fields.aggregator_order_id : '',
        airpayId: ''
      }

      console.log('---postParams orderconfirmation---', postParams)
      // Call payments api for order confirmation
      const postData = qs.stringify(postParams)

      console.log('---postDataStringify orderconfirmation---', postData)
      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'order/verify.php',
        data: postData,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 30000 // 30 secs
      })

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Order Confirmation Status ' + response.status }
      }
      console.log('---orderConfirmationResponse---', response.data)
      const transactionObj = parser.parse(response.data)

      console.log('---orderConfirmationResponseObj---', transactionObj)
      // console.log(transactionObj)
      let transaction_status = ''
      let transaction_status_text = ''
      if (typeof transactionObj === 'object' && typeof transactionObj.RESPONSE === 'object') {
        let transaction_reason = ''
        const rrn = (transactionObj.RESPONSE.TRANSACTION.RRN) ? transactionObj.RESPONSE.TRANSACTION.RRN : ''
        const aggregator_txn_id = (transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID) ? transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID : 0
        const bankbalance = (transactionObj.RESPONSE.TRANSACTION.CUSTOMERBANKBAL) ? transactionObj.RESPONSE.TRANSACTION.CUSTOMERBANKBAL : 0.0
        // Update aeps transaction according to transaction status return from order confirmation
        if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 200) {
          // Transaction is success
          transaction_status = 'S'
          transaction_reason = 'Success'
          transaction_status_text = 'Success'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 201) {
          // Transaction in pending state
          transaction_status = 'P'
          transaction_reason = 'Initiated'
          transaction_status_text = 'Initiated'
        } else if (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS == 400) {
          // Transaction failed
          transaction_status = 'F'
          transaction_reason = (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON) ? transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON : 'Failed'
          transaction_status_text = 'Failed'
        }

        if (transaction_status) { // If transaction status in success,fail,pending
          const updateParams = {
            ma_user_id: fields.ma_user_id,
            orderid: fields.aggregator_order_id,
            aggregator_txn_id: aggregator_txn_id,
            transaction_status: transaction_status,
            amount: fields.transaction_amount,
            rrn: rrn,
            userid: fields.userid,
            transaction_reason,
            bankbalance
          }

          log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: updateParams, transaction_status })
          let message = 'Your Previous Txn #txnid# is now #status#'
          message = message.replace('#txnid#', fields.aggregator_order_id)
          message = message.replace('#status#', transaction_status_text)
          return { status: 200, respcode: 1000, message: message, updateParams, transaction_status, aggregator_txn_id, rrn, transaction_reason, transactionObj, bankbalance }
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + transactionObj.RESPONSE.TRANSACTION.MESSAGE }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'response', fields: { notanobject: transactionObj } })
      return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'orderConfirmation', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    }
  }

  /**
   * Do a collect money transaction
   * @param {*} _
   * @param {*} fields
   */
  static async doCollectMoneyTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doCollectMoneyTransaction', type: 'request', fields: fields })
    try {
      console.log('---STEP 2 : callGenerateOrder (doCOLLECTMONEYTransaction) : Start---')
      const response = await this.callGenerateOrder(fields, { call_type: fields.call_type == 'invoicepay' ? fields.call_type : null })
      console.log('---Generate order response---', (response.data) ? response.data : response)
      if (response.status != 200) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
      const generateOrderResp = response.data
      var generateOrderMessage = generateOrderResp.message
      if (generateOrderResp.status == 200) {
        const returnData = {}
        switch (fields.call_type) {
          case 'invoicepay':
            returnData.invoice_number = generateOrderResp.invoice_number
            returnData.payment_url = generateOrderResp.payment_url
            generateOrderMessage = 'Invoice has been generated and sent to the customer\'s mobile/email id'
            break

          case 'upi':
          case 'upiqr':
            returnData.barcode_string = generateOrderResp.BARCODE_STRING
            returnData.mid = generateOrderResp.MID
            generateOrderMessage = `UPI request sent to the ${fields.buyer_vpa} . Login to your PSP application and accept the payment.1`
            break

          case 'btqr':
            returnData.barcode_string = generateOrderResp.BARCODE_STRING
            returnData.mid = generateOrderResp.MID
            returnData.rid = generateOrderResp.RID
            generateOrderMessage = 'Success'
            break

          default:
            break
        }

        console.log('return data', returnData)
        return { status: 200, respcode: 1000, message: generateOrderMessage, ...returnData }
      } else {
        let message = errorMsg.responseCode[1001]
        if (generateOrderResp.message) {
          message = generateOrderResp.message
        } else if (generateOrderResp.err_msg) {
          message = generateOrderResp.err_msg
        } else if (generateOrderResp.error) {
          message = generateOrderResp.error
        } else {
          message = errorMsg.responseCode[1001]
        }
        return { status: 400, respcode: 1001, message: message }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doCollectMoneyTransaction', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * Call generate Order API
   * @param {*} _
   * @param {*} fields
   * other field added for collect money txn
   */
  static async callGenerateOrder (fields, other = {}) {
    log.logger({ pagename: require('path').basename(__filename), action: 'callGenerateOrder', type: 'request', fields: { fields, other } })
    try {
      console.time('TIME_FOR_COLLECTMONEY_GENERATE_ORDER')
      let postParams
      if (fields.call_type != 'undefined' && fields.call_type != 'null' && fields.call_type == 'adpay') {
        const sqlUserReq = `SELECT um.state, um.pincode as pincode, um.city, sm.two_digit as state_code, cm.name as district, CONCAT(IFNULL(um.firstname,''),IFNULL(um.lastname,'')) AS merchant_name FROM ma_user_master um LEFT JOIN ma_states_master sm ON um.state = sm.id 
        LEFT JOIN ma_cities_master cm ON um.city = cm.id WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} limit 1`
        const dataUserRes = await this.rawQuery(sqlUserReq, fields.connection)
        // Call generate order for Adhaar Pay
        postParams = {
          merchantTransactionId: fields.aggregator_order_id,
          merchantTranId: fields.aggregator_order_id,
          captureResponse: JSON.parse(Buffer.from(fields.captureResponse, 'base64').toString('utf-8')),
          cardnumberORUID: JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8')),
          languageCode: fields.languageCode,
          latitude: fields.latitude,
          longitude: fields.longitude,
          mobileNumber: fields.mobile_number,
          paymentType: fields.paymentType,
          requestRemarks: fields.requestRemarks,
          timestamp: fields.timestamp,
          transactionAmount: Number(fields.amount) + Number(fields.surcharge_amount),
          transactionType: 'AP',
          merchantAggName: dataUserRes[0].merchant_name,
          merchantPin: fields.merchantPin,
          subMerchantId: fields.subMerchantId,
          call_type: 'adpay',
          email: fields.email,
          userid: fields.userid,
          privatekey: fields.privatekey,
          mercid: fields.ma_user_id,
          bankCode: fields.bank_code ? fields.bank_code : null,
          mer_dom: Buffer.from((Buffer.from(fields.mer_dom, 'base64').toString('utf-8')).replace(/(^"|"$)/g, '')).toString('base64'),
          arpyVer: 3,
          BcDistrict: dataUserRes[0].district ? dataUserRes[0].district : null,
          BcPincode: dataUserRes[0].pincode ? dataUserRes[0].pincode : null,
          BcState: dataUserRes[0].state_code ? dataUserRes[0].state_code : null,
          BcCountry: 'IN'
        }
        console.log('---Generate Order Params---', { ...postParams, cardnumberORUID: common_fns.maskValue(postParams.cardnumberORUID, 'adhaarNumber') })
      } else {
        // Call generate order
        postParams = {
          mid: fields.ma_user_id,
          userid: fields.userid,
          order_id: fields.aggregator_order_id,
          amount: fields.amount,
          version: fields.version,
          tid: fields.tid,
          contact_no: fields.customer_mobile,
          email: fields.customer_email,
          buyer_vpa: fields.buyer_vpa,
          ref_url: fields.ref_url,
          mer_dom: fields.mer_dom,
          surcharge_amount: fields.surcharge_amount,
          customvar: fields.customvar,
          call_type: fields.call_type
        }
      }
      if ('call_type' in other && other.call_type) { // This condition for collect money txn (for UPI transaction only)
        const moment = require('moment-timezone')
        const common = require('../../util/common')
        const currentDateTime = moment().tz('Asia/Kolkata')
        const mySQLWrapper = require('../../lib/mysqlWrapper')
        const connection = await mySQLWrapper.getConnectionFromReadReplica()
        const collect_money_link_expiry_time = await common.getSystemCodes(this, util.collect_money_link_expiry_time, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'callGenerateOrder', type: 'collect_money_link_expiry_time', fields: { collect_money_link_expiry_time_value: collect_money_link_expiry_time } })
        connection.release()
        const nextTwentyMinDateTime = currentDateTime.add(collect_money_link_expiry_time, 'minutes').format('YYYY-MM-DD  HH:mm:ss')

        log.logger({ pagename: require('path').basename(__filename), action: 'callGenerateOrder', type: 'nextTwentyMinDateTime', fields: nextTwentyMinDateTime })
        postParams.expiry_date = nextTwentyMinDateTime
      }
      console.log('---Generate Order Params---', postParams)
      const postData = await generateOrderchecksum(this, postParams)
      log.logger({ pagename: require('path').basename(__filename), action: 'callGenerateOrder', type: 'generateOrderchecksum', fields: postData })
      if (postData.status != 200) {
        return { status: postData.status, respcode: postData.respcode, message: postData.message }
      }

      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'api/generateOrder.php',
        data: postData.data,
        headers: { 'Content-Type': 'application/json' },
        timeout: 60000
      })
      console.timeEnd('TIME_FOR_COLLECTMONEY_GENERATE_ORDER')

      console.log('--- Generate order response from core', response)
      return response
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'callGenerateOrder', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    }
  }

  static async getLatLongFromUserDetails (fields) {
    const conn = await mySQLWrapper.getConnectionFromPool()
    try {
      const sqlPindcodeQry1 = `SELECT IF(latitude  is not null,
      IF(latitude != '' && latitude != 'undefined',latitude ,0),0) as latitude, IF(longitude  is not null,
        IF(longitude != '' && longitude != 'undefined',longitude ,0),0) as longitude, IF(pincode  is not null,
          IF(pincode != '',pincode ,0),0) as pincode FROM ma_user_master WHERE profileid = ${fields.ma_user_id} limit 1`
      const userDetailsResult1 = await this.rawQuery(sqlPindcodeQry1, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'userDetailsResult1', fields: userDetailsResult1 })
      let merchantPincode = 0
      let latitude = 0
      let longitude = 0
      if (userDetailsResult1.length > 0) {
        merchantPincode = userDetailsResult1[0].pincode
        latitude = userDetailsResult1[0].latitude
        longitude = userDetailsResult1[0].longitude
      }

      if (latitude && longitude == '0' && merchantPincode == 0) {
        return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Found, Please try again later', respcode: 1028 }
      }

      // if lat & long not equal to 0 then send
      if (latitude != 0 && longitude != 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, lat_val: latitude, long_val: longitude }
      }
      if (merchantPincode > 0) {
        let latitude_val = 0
        let longitude_val = 0
        const fetch_lat_long_sql1 = `SELECT latitude, longitude FROM ma_pincode_mapping WHERE pincode = ${merchantPincode} ORDER BY ma_pincode_mapping_id desc limit 1 `
        const fetch_lat_long_sql_resp1 = await this.rawQuery(fetch_lat_long_sql1, conn)
        log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'fetch_lat_long_sql_resp1', fields: fetch_lat_long_sql_resp1 })

        if (fetch_lat_long_sql_resp1.length > 0) {
          latitude_val = fetch_lat_long_sql_resp1[0].latitude
          longitude_val = fetch_lat_long_sql_resp1[0].longitude
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, lat_val: latitude_val, long_val: longitude_val }
        } else {
        // Call google api to check
          const responseLatLong = await axios({
            method: 'get',
            url: `https://maps.googleapis.com/maps/api/geocode/json?address=${merchantPincode}&&key=AIzaSyA2hm7fs2plQ0bhLGN6EQW5nMCMfAw_6qA`,
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 20000
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'responseLatLong_API', fields: responseLatLong })

          if (responseLatLong != '' && responseLatLong.status === 200) {
            const responseData = typeof (responseLatLong.data) == 'string' ? JSON.parse(responseLatLong.data) : responseLatLong.data
            log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'responseData', fields: responseData })
            if (responseData.status == 'OK') {
              if (responseData.results != '' && responseData.results[0].geometry != '' && responseData.results[0].geometry.location != '' && responseData.results[0].geometry.location.lat != '') {
                latitude_val = responseData.results[0].geometry.location.lat
              }
              if (responseData.results[0] != '' && responseData.results[0].geometry != '' && responseData.results[0].geometry.location != '' && responseData.results[0].geometry.location.lng != '') {
                longitude_val = responseData.results[0].geometry.location.lng
              }
              console.log('lat ', latitude_val)
              console.log('long ', longitude_val)
              if (latitude_val != 0 && longitude_val != 0) {
                const areaAddress = responseData.results[0].formatted_address ? responseData.results[0].formatted_address : ''

                // Insert lat long data in ma_pincode_mapping table
                const insertPincodeMappingSql = `INSERT INTO ma_pincode_mapping (area, pincode, latitude, longitude) VALUES ('${areaAddress}','${merchantPincode}','${latitude_val}','${longitude_val}')`
                const insertPincodeMappingSqlRes = await this.rawQuery(insertPincodeMappingSql, conn)
                log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'insertPincodeMappingSqlRes', fields: insertPincodeMappingSqlRes })

                // latitude & longitude in user master table - comment below code for now
                /* const updatePincodeMappingSql = `UPDATE ma_user_master SET latitude = '${latitude_val}' and longitude = '${longitude_val}' WHERE profileid = '${fields.ma_user_id}'`
                const updatePincodeMappingSqlRes = await this.rawQuery(updatePincodeMappingSql, conn)
                log.logger({ pagename: require('path').basename(__filename), action: 'getLatLongFromUserDetails', type: 'updatePincodeMappingSqlRes', fields: updatePincodeMappingSqlRes }) */

                return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, lat_val: latitude_val, long_val: longitude_val }
              }
            } else {
              return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028 }
            }
          } else {
            return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Geo Location Response, Please try again later', respcode: 1028 }
          }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      conn.release()
    }
  }
}
module.exports = collectMoneyController
