const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common_fns = require('../../util/common_fns')

class Transaction extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_transaction_master_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_transaction_master_details_id'
  }

  /**
    * Returns a transaction by its ID
    */
  static getByID (_, { id }) {
    return this.find(id)
  }

  static async findMatching (_, fields) {
    log.logger({ pagename: 'transactionDetailsController.js', action: 'findMatching', type: 'request', fields: fields })
    // Returns early with all transactions if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching transactions
    const res = await this.findByFields({
      fields
    })
    log.logger({ pagename: 'transactionDetailsController.js', action: 'findMatching', type: 'response', fields: res })
    return res
  }

  /**
     * Creates a transaction details
     */
  static async createTransactionDetails (_, fields, connection = null) {
    log.logger({ pagename: 'transactionDetailsController.js', action: 'createTransactionDetails', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      let maskedAdhaar = null
      if (fields.customer_aadhaar) {
        // const sql = `SELECT AES_ENCRYPT('${fields.customer_aadhaar}', '${encryptionKey}') AS aadhaar`
        // const encryptedData = await this.rawQuery(sql, connection)
        if (fields.customer_aadhaar.length > 0) {
          maskedAdhaar = fields.customer_aadhaar
          maskedAdhaar = maskedAdhaar.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, 'X')
        }
      }

      const insertObj = {
        ma_transaction_master_id: fields.ma_transaction_master_id,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        transaction_id: fields.transaction_id ? fields.transaction_id : fields.aggregator_order_id ? fields.aggregator_order_id : null,
        customer_name: fields.customer_name ? fields.customer_name : null,
        customer_aadhaar: maskedAdhaar,
        customer_mobile: fields.customer_mobile ? fields.customer_mobile : null,
        customer_email: fields.customer_email ? fields.customer_email : null,
        bank_name: fields.bank_name ? fields.bank_name : null,
        terminalid: fields.terminalid ? fields.terminalid : null,
        customer_virtualid: fields.customer_virtualid ? fields.customer_virtualid : null,
        provider_name: fields.provider_name ? fields.provider_name : null,
        utility_name: fields.utility_name ? fields.utility_name : null,
        uuid: fields.uuid ? fields.uuid : null,
        cms_unique_id: fields.cms_unique_id ? fields.cms_unique_id : null,
        cms_ma_user_id: fields.cms_ma_user_id ? fields.cms_ma_user_id : null,
        affiliated_id: fields.affiliated_id ? fields.affiliated_id : 0,
        category_master_id: fields.category_master_id ? fields.category_master_id : 0,
        subcategory_code: fields.subcategory_code ? fields.subcategory_code : null,
        company_id: fields.company_id ? fields.company_id : 0,
        ma_cms_merchant_on_boarding_id: fields.ma_cms_merchant_on_boarding_id ? fields.ma_cms_merchant_on_boarding_id : 0,
        cms_client_id: fields.cms_client_id ? fields.cms_client_id : 0,
        cms_merchant_type: fields.cms_merchant_type ? fields.cms_merchant_type : null,
        cms_partner_id: fields.cms_partner_id ? fields.cms_partner_id : null,
        cms_parent_partner_id: fields.cms_parent_partner_id ? fields.cms_parent_partner_id : null,
        cms_userid: fields.cms_userid ? fields.cms_userid : null,
        form_data: fields.form_data ? fields.form_data : null,
        receiver_account_number: fields.receiver_account_number || null,
        receiver_name: fields.receiver_name || null,
        receiver_mobile_number: fields.receiver_mobile_number || null
      }
      const _result = await this.insert(connection, {
        data: insertObj
      })

      log.logger({ pagename: 'transactionDetailsController.js', action: 'createTransactionDetails', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transaction_details_id: _result.insertId }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028], respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionDetailsController.js', action: 'createTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
   * This function is used to update the transaction_master_details table by aggregator_order_id
   */
  static async updateTransactionDetails (updateFields, connection) {
    log.logger({ pagename: 'transactionDetailsController.js', action: 'updateTransactionDetails', type: 'request', fields: updateFields })
    try {
      if (typeof updateFields.aggregator_order_id != 'undefined') {
        const aggregator_order_id = updateFields.aggregator_order_id
        // Delete aggregator_order_id from update fields
        delete updateFields.aggregator_order_id

        const transactionMasterSql = `SELECT ma_transaction_master_id FROM ma_transaction_master WHERE aggregator_order_id = '${aggregator_order_id}'`
        const transactionMasterData = await this.rawQuery(transactionMasterSql, connection)

        if (transactionMasterData.length > 0) {
          const _result = await this.updateWhere(connection, {
            id: transactionMasterData[0].ma_transaction_master_id,
            where: 'ma_transaction_master_id',
            data: updateFields
          })

          log.logger({ pagename: 'transactionDetailsController.js', action: 'updateTransactionDetails', type: 'response', fields: _result })
        } else {
          return { status: 400, respcode: 1028, message: 'Fail: Transaction not found.' }
        }
        return { status: 200, respcode: 1000, message: 'success' }
      } else {
        return { status: 400, respcode: 1028, message: 'Fail: Order id is required' }
      }
    } catch (err) {
      log.logger({ pagename: 'transactionDetailsController.js', action: 'updateTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}

module.exports = Transaction
