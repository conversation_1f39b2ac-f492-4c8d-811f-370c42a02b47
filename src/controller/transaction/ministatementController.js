const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common = require('../../util/common')

const common_fns = require('../../util/common_fns')
const redis = require('redis')
const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
const redisHost = util[env].redisHostIp
const client = redis.createClient({ port: 6379, host: redisHost })
const moment = require('moment')

class ministatementController extends DAO {
  static async extractLastRecordData (lastRecordsArray, bankName, aggregatorBank) {
    log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { lastRecordsArray, bankName,aggregatorBank } })
    try {
      let returnArray = []
      let balance = ''
      let structure_type = false
      if(aggregatorBank == 'credopay' && bankName == 'Bank Of Maharashtra'){
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { lastRecordsArray: lastRecordsArray } })
        console.log('--- credo pay Bank Of Maharashtra filter ---')
        const balanceLine = lastRecordsArray[lastRecordsArray.length - 1]
        const balanceMatch = balanceLine.match(/0{3,}(\d+)(?:\.00)?\s*(CR|DR)/i)
        let balance = null
        if (balanceMatch) {
          balance = parseInt(balanceMatch[1])
          const type = balanceMatch[2].toUpperCase()
          console.log(`Final Balance: ${balance} ${type}`)
        }
        for (const lastRecordArray of lastRecordsArray) {
          if (Object.keys(lastRecordArray).length > 0) {
            const returnRecord = {}
            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { lastrecord: lastRecordsArray } })
            const lastRecords = lastRecordArray.split(' ')
            const records = lastRecords.filter(function (e) {
              log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { e: e } })
              return e != '' && e != '0.00'
            })
            console.log('records}}}}}}}}}}}}', records)
            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { split_array: records } })
            if (aggregatorBank === 'credopay' && bankName == 'Bank Of Maharashtra') {
              switch (bankName) {
                case 'Bank Of Maharashtra':
                  console.log('Reached credopay Bank Of Maharashtra Filter')
                  // Filter out Date and change format
                  var day = records[0].substr(0, 2)
                  var month = records[0].substr(3, 2)
                  var year = records[0].substr(6, 2)
                  var date = day + '/' + month + '/' + year
                  returnRecord.Date = moment(date, 'DD/MM/YY').format('DD/MM/YYYY')
                  returnRecord.Type = records[0].substr(8, 2) == 'DR' ? 'Dr' : 'Cr'
                  const rawAmount = records[records.length - 1];
                  returnRecord.Amount = !isNaN(rawAmount)
                  ? (Number(rawAmount) / 100).toFixed(2)
                  : 'NaN';
                  returnRecord.Mode = records[1] || 'N/A'
                  structure_type = true
                  break
               }
               returnArray.push(returnRecord)
             }
            }
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnArray: returnArray } })
        returnArray = returnArray.filter(function (e) {
          return Object.keys(e).length > 0
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnArray: returnArray, balance: balance } })
        return { returnArray: returnArray, balance: balance, structure_type: structure_type }
      }
      else if (aggregatorBank == 'credopay') {
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { lastRecordsArray: lastRecordsArray } })
        console.log('--- credo pay filter ---')
        let returnRecord = {}
        const balanceRegex = /Balance:([+-]?\d+\.\d+)/;
        const balanceMatch = lastRecordsArray.match(balanceRegex);
        let balance = null;
        if (balanceMatch && balanceMatch.length > 1) {
            balance = parseFloat(balanceMatch[1]);
        }
        
       console.log(balance);
        const regex = /\d{2}\/\d{2}.*?\s+\d+\.\d{2}/g;
        const lastRecords = lastRecordsArray.match(regex) || [];
        console.log("records",lastRecords)
        for (const lastRecord of lastRecords) {
          if (Object.keys(lastRecord).length > 0) {
            const lastRecords = lastRecord.split(' ')
          const records = lastRecords.filter(function (e) {
            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { e: e } })
            return e != '' 
          })
          console.log('records}}}}}}}}}}}}', records)
          switch(bankName){
            case 'State Bank Of India':
              console.log('Reached credopay State Bank of india Filter')
              console.log("records[0]",records[0]);   
              var datesbi = records[0]
              returnRecord.Date = records[0]
              console.log("returndate",returnRecord.Date);
              returnRecord.Amount = parseFloat(records[3]).toFixed(2)
              returnRecord.Type = records[2]
              returnRecord.Mode = records[1]
              log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
              structure_type = true
              break
                case 'Bank Of Maharashtra':
                  console.log('Reached credopay Bank Of Maharashtra Filter')
                  console.log("records[0]",records[0]);   
                  var datesbi = records[0]
                  returnRecord.Date = records[0]
                  console.log("returndate",returnRecord.Date);
                  returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                  returnRecord.Type = records[2]
                  returnRecord.Mode = records[1]
                  log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                  structure_type = true
                  break
                  case 'Hdfc Bank':
                    console.log('Reached credopay Hdfc Bank Filter')
                    console.log("records[0]",records[0]);   
                    var datesbi = records[0]
                    returnRecord.Date = records[0]
                    console.log("returndate",returnRecord.Date);
                    returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                    returnRecord.Type = records[2]
                    returnRecord.Mode = records[1]
                    log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                    structure_type = true
                    break
                    case 'Airtel Payment Bank':
                      console.log('Reached credopay Airtel Payment Bank Filter')
                      console.log("records[0]",records[0]);   
                      var datesbi = records[0]
                      returnRecord.Date = records[0]
                      console.log("returndate",returnRecord.Date);
                      returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                      returnRecord.Type = records[2]
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
                      case 'Federal Bank':
                        console.log('Reached credopay Federal Bank Filter')
                        console.log("records[0]",records[0]);   
                        var datesbi = records[0]
                        returnRecord.Date = records[0]
                        console.log("returndate",returnRecord.Date);
                        returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                        returnRecord.Type = records[2]
                        returnRecord.Mode = records[1]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                        case 'Icici Bank':
                          console.log('Reached credopay Icici Bank Filter')
                          console.log("records[0]",records[0]);   
                          var datesbi = records[0]
                          returnRecord.Date = records[0]
                          console.log("returndate",returnRecord.Date);
                          returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                          returnRecord.Type = records[2]
                          returnRecord.Mode = records[1]
                          log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                          structure_type = true
                          break
                          case 'Canara Bank Erstwhile Syndicate Bank':
                            console.log('Reached credopay Canara Bank Erstwhile Syndicate Bank Filter')
                            console.log("records[0]",records[0]);   
                            var datesbi = records[0]
                            returnRecord.Date = records[0]
                            console.log("returndate",returnRecord.Date);
                            returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                            returnRecord.Type = records[2]
                            returnRecord.Mode = records[1]
                            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                            structure_type = true
                            break
                            case 'Idbi Bank':
                              console.log('Reached credopay Idbi Bank Filter')
                              console.log("records[0]",records[0]);   
                              var datesbi = records[0]
                              returnRecord.Date = records[0]
                              console.log("returndate",returnRecord.Date);
                              returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                              returnRecord.Type = records[2]
                              returnRecord.Mode = records[1]
                              log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                              structure_type = true
                              break
                              case 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank':
                                console.log('Reached credopay Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank Filter')
                                console.log("records[0]",records[0]);   
                                var datesbi = records[0]
                                returnRecord.Date = records[0]
                                console.log("returndate",returnRecord.Date);
                                returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                returnRecord.Type = records[2]
                                returnRecord.Mode = records[1]
                                log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                structure_type = true
                                break
                                case 'Central Bank Of India':
                                  console.log('Reached credopay Central Bank Of India Filter')
                                  console.log("records[0]",records[0]);   
                                  var datesbi = records[0]
                                  returnRecord.Date = records[0]
                                  console.log("returndate",returnRecord.Date);
                                  returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                  returnRecord.Type = records[2]
                                  returnRecord.Mode = records[1]
                                  log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                  structure_type = true
                                  break
                                  case 'Punjab National Bank Erstwhile Oriental Bank Of Commerce':
                                    console.log('Reached credopay Punjab National Bank Erstwhile Oriental Bank Of Commerce Filter')
                                    console.log("records[0]",records[0]);   
                                    var datesbi = records[0]
                                    returnRecord.Date = records[0]
                                    console.log("returndate",returnRecord.Date);
                                    returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                    returnRecord.Type = records[2]
                                    returnRecord.Mode = records[1]
                                    log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                    structure_type = true
                                    break
                                    case 'Andhra Pragathi Grameena Bank':
                                      console.log('Reached credopay Andhra Pragathi Grameena Bank Filter')
                                      console.log("records[0]",records[0]);   
                                      var datesbi = records[0]
                                      returnRecord.Date = records[0]
                                      console.log("returndate",returnRecord.Date);
                                      returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                      returnRecord.Type = records[2]
                                      returnRecord.Mode = records[1]
                                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                      structure_type = true
                                      break
                                      case 'Lakshmi Vilas Bank':
                                        console.log('Reached credopay Lakshmi Vilas Bank Filter')
                                        console.log("records[0]",records[0]);   
                                        var datesbi = records[0]
                                        returnRecord.Date = records[0]
                                        console.log("returndate",returnRecord.Date);
                                        returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                        returnRecord.Type = records[2]
                                        returnRecord.Mode = records[1]
                                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                        structure_type = true
                                        break
                                        case 'Paytm Payments Bank':
                                          console.log('Reached credopay Paytm Payments Bank Filter')
                                          console.log("records[0]",records[0]);   
                                          var datesbi = records[0]
                                          returnRecord.Date = records[0]
                                          console.log("returndate",returnRecord.Date);
                                          returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                          returnRecord.Type = records[2]
                                          returnRecord.Mode = records[1]
                                          log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                          structure_type = true
                                          break
                                          case 'Bank Of India':
                                            console.log('Reached credopay Bank Of India Filter')
                                            console.log("records[0]",records[0]);   
                                            var datesbi = records[0]
                                            returnRecord.Date = records[0]
                                            console.log("returndate",returnRecord.Date);
                                            returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                            returnRecord.Type = records[2]
                                            returnRecord.Mode = records[1]
                                            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                            structure_type = true
                                            break
                                            case 'Fino Payments Bank':
                                              console.log('Reached credopay Fino Payments Bank Filter')
                                              console.log("records[0]",records[0]);   
                                              var datesbi = records[0]
                                              returnRecord.Date = records[0]
                                              console.log("returndate",returnRecord.Date);
                                              returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                              returnRecord.Type = records[2]
                                              returnRecord.Mode = records[1]
                                              log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                              structure_type = true
                                              break
                                              case 'Fino Payments Bank':
                                                console.log('Reached credopay Fino Payments Bank Filter')
                                                console.log("records[0]",records[0]);   
                                                var datesbi = records[0]
                                                returnRecord.Date = records[0]
                                                console.log("returndate",returnRecord.Date);
                                                returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                                                returnRecord.Type = records[2]
                                                returnRecord.Mode = records[1]
                                                log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                                                structure_type = true
                                                break
                        default:
                          console.log('credopay Else case -><><><>')
                          structure_type = false
                          returnArray = lastRecordsArray
                          break
            
          }
          returnArray.push(returnRecord)
          returnRecord = {}
        }
       }
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnArray: returnArray } })
        returnArray = returnArray.filter(function (e) {
          return Object.keys(e).length > 0
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnArray: returnArray, balance: balance } })
        return { returnArray: returnArray, balance: balance, structure_type: structure_type }
      }else{
        for (const lastRecordArray of lastRecordsArray) {
          if (Object.keys(lastRecordArray).length > 0) {
            const returnRecord = {}
            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { lastrecord: lastRecordsArray } })
            const lastRecords = lastRecordArray.split(' ')
            const records = lastRecords.filter(function (e) {
              log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { e: e } })
              return e != '' && e != '0.00'
            })
            console.log('records}}}}}}}}}}}}', records)
            log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { split_array: records } })
            if (aggregatorBank === 'nsdl') {
              if (Object.keys(records).length > 0) {
                if (records[0] === '#LEDGER' || records[0] === '#AVAILABLE' || records[0] === 'AVAILABLE' || records[0] === 'AVL.BAL' || records[0] === 'AVL' || records[0] === 'Balance' || records[0] === 'AVAIL' || records[1] === 'BAL' || records[0].startsWith('Balance') || records[0].substr(10, 7) === 'Balance' || records[1] === 'BalanceAmt') {
                  if (records[0] === '#AVAILABLE' || records[0] === 'AVAILABLE' || records[0] === 'Balance' || records[0] === 'AVL.BAL' || records[0] === 'AVAIL' || records[1] === 'BAL' || records[0].startsWith('Balance') || records[0].substr(10, 7) === 'Balance' || records[1] === 'BalanceAmt' || records[0] === 'AVL') {
                    structure_type = true
                    if (bankName === 'State Bank Of India' || bankName === 'Utkal Gramin Bank' || bankName === 'Chhattisgarh Rajya Gramin Bank' ||
                      bankName === 'Indian Bank' || bankName === 'Rajasthan Marudhara Gramin Bank' || bankName === 'Madhyanchal Gramin Bank' ||
                      bankName === 'Purvanchal Gramin Bank' || bankName === 'Jharkhand Rajya Gramin Bank Erstwhile Vananchal Gramin Bank' ||
                      bankName === 'Uttarakhand Gramin Bank') {
                      console.log('reached balance length - 2', records[records.length - 2])
                      balance = Number(records[records.length - 2]).toString() // records.length - 2 not possible since appnded with CR
                    } else if (bankName === 'Saptagiri Grameena Bank') {
                      balance = Number(records[records.length - 2]).toFixed(2)
                    } else if (bankName === 'Bangiya Gramin Vikash Bank') {
                      balance = records[records.length - 1] // typecast not possible since it includes RS
                    } else if (bankName === 'Hdfc Bank' || bankName === 'Lakshmi Vilas Bank' || bankName === 'Andhra Pradesh Grameena Vikash Bank' ||
                      bankName === 'Indusind Bank' || bankName === 'Karnataka Gramin Bank Erstwhile Pragathi Krishna Gramin Bank' ||
                      bankName === 'The Tiruchirapalli Dist. Cent Cooperative Bank Ltd' || bankName === 'Fincare Small Finance Bank' ||
                      bankName === 'Equitas Small Finance Bank') {
                      if ((bankName === 'Indusind Bank' && records[records.length - 1] === 'AVL.BAL') ||
                      (bankName === 'Equitas Small Finance Bank' && records[records.length - 1] === 'Rs.')) { // 0.00 filtered out initially
                        balance = '0.00'
                      } else {
                        balance = records[records.length - 1] // getting formated amount
                      }
                    } else if (bankName === 'Airtel Payment Bank' || bankName === 'Paytm Payments Bank' || bankName === 'India Post Payment Bank' ||
                      bankName === 'Suryoday Small Fianance Bank') {
                      balance = records[records.length - 1] // getting formated amount
                    } else if (bankName === 'Canara Bank Erstwhile Syndicate Bank' || bankName === 'Punjab National Bank Erstwhile Oriental Bank Of Commerce' ||
                      bankName === 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank' || bankName === 'Idbi Bank' ||
                      bankName === 'Prathma Up Gramin Bank Erstwhile Sarva Up Gramin Bank' || bankName === 'Sarva Haryana Gramin Bank') {
                      balance = Number(records[records.length - 1].split(':')[1]).toFixed(2) == 'NaN' ? 'Not Available' : Number(records[records.length - 1].split(':')[1]).toFixed(2)
                    } else if (bankName === 'Central Bank Of India' || bankName === 'Odisha Gramya Bank') {
                      balance = Number(records[records.length - 1]) // decimal with pre-0
                  }else if( bankName === 'Indian Overseas Bank'){
                    const balanceString = records[records.length - 1];
                      const balanceParts = balanceString.split(':');
                      balance = balanceParts.length === 2 ? balanceParts[1].trim(): null;
                      console.log("bank balance",balance);
                  } else if (bankName === 'Bank Of India' || bankName === 'Aryavart Bank Erstwhile Gramin Bank Of Aryavart' ||
                      bankName === 'Madhya Pradesh Gramin Bank Erstwhile Narmada Jhabua Gramin Bank') {
                      balance = (Number(records[records.length - 1].split(':')[1]) / 100).toFixed(2)
                    } else if (bankName === 'Baroda Uttar Pradesh Gramin Bank' || bankName === 'Bank Of Maharashtra' || bankName === 'Axis Bank' ||
                      bankName === 'Chaitanya Godavari Gramin Bank' || bankName === 'Baroda Gujarat Gramin Bank' || bankName === 'City Union Bank') {
                      balance = (Number(records[records.length - 1]) / 100).toFixed(2)
                    } else {
                      console.log('reached balance else', records[records.length - 1])
                      balance = (Number(records[records.length - 1]) / 100).toString()
                    }
                  }
                  log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { bank_balance: balance } })
                } else {
                  log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { switch: bankName, aggregator_bank_name: 'NSDL' } })
                  switch (bankName) {
                    case 'Bank Of Maharashtra':
                      console.log('Reached nsdl Bank Of Maharashtra Filter')
                      // Filter out Date and change format
                      var day = records[0].substr(4, 2)
                      var month = records[0].substr(2, 2)
                      var year = records[0].substr(0, 2)
                      var date = day + '/' + month + '/' + year
                      returnRecord.Date = moment(date, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = (Number(records[records.length - 1]) / 100).toFixed(2)
                      returnRecord.Mode = records[1]
                      structure_type = true
                      break
  
                    case 'Hdfc Bank':
                      console.log('Reached nsdl Hdfc Bank Filter')
                      returnRecord.Date = records[0]
                      returnRecord.Type = records[2] == 'D' ? 'Dr' : 'Cr'
                      returnRecord.Mode = records[1]
                      returnRecord.Amount = records[3]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Airtel Payment Bank':
                      console.log('Reached nsdl Airtel Payment Bank Filter')
                      returnRecord.Date = records[0].substr(4, 5)
                      returnRecord.Type = records[2] == 'D' ? 'Dr' : 'Cr'
                      returnRecord.Mode = records[1]
                      returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Federal Bank':
                      console.log('Reached nsdl Federal Bank Filter')
                      // Filter out Date and change format
                      var dayF = records[0].substr(4, 2)
                      var monthF = records[0].substr(2, 2)
                      var yearF = records[0].substr(0, 2)
                      var dateF = dayF + '/' + monthF + '/' + yearF
                      returnRecord.Date = moment(dateF, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) === 'DR' ? 'Dr' : 'Cr'
                      // Filter out mode
                      var mode = records.length > 2 ? records[1] + ' ' + records[2] : records[1]
                      returnRecord.Mode = mode.slice(0, mode.indexOf('/'))
                      // Filter out amount given in pos style
                      var amount = parseFloat(Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      returnRecord.Amount = amount
                      structure_type = true
                      break
  
                    case 'State Bank Of India':
                      // Filter out Date and change
                      console.log('Reached nsdl State Bank of india Filter')
                      var dsbi = records[0].substr(0, 2)
                      var msbi = records[0].substr(2, 2)
                      var ysbi = records[0].substr(4, 2)
                      var datesbi = dsbi + '/' + msbi + '/' + ysbi
                      returnRecord.Date = moment(datesbi, 'DD/MM/YY').format('DD/MM/YYYY');
                      console.log("returndate",returnRecord.Date);
                      // Filter out amount given in pos style
                      var amountsbi = Number(records[records.length - 1].slice(-12)) / 100
                      returnRecord.Amount = parseFloat(amountsbi).toFixed(2)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) === 'CR' ? 'Cr' : 'Dr'
                      // Filter out mode
                      var modesbi = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      returnRecord.Mode = modesbi
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
                    case 'Icici Bank':
                      console.log('Reached nsdl Icici Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount
                      returnRecord.Amount = records[2]
                      // Filter out mode
                      returnRecord.Mode = records[3]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Canara Bank Erstwhile Syndicate Bank':
                      console.log('Reached nsdl Canara Bank Erstwhile Syndicate Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Idbi Bank':
                      console.log('Reached nsdl Idbi Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank':
                      console.log('Reached nsdl Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Central Bank Of India':
                      console.log('Reached nsdl Central Bank Of India Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Punjab National Bank Erstwhile Oriental Bank Of Commerce':
                      console.log('Reached nsdl Punjab National Bank Erstwhile Oriental Bank Of Commerce Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Andhra Pragathi Grameena Bank':
                      console.log('Reached nsdl Andhra Pragathi Grameena Bank Filter')
                      // Filter out Date
                      var dAndran = records[0].substr(4, 2)
                      var mAndran = records[0].substr(2, 2)
                      var yAndran = records[0].substr(0, 2)
                      var dateAndrac = dAndran + '/' + mAndran + '/' + yAndran
                      returnRecord.Date = moment(dateAndrac, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(5, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1]) / 100) // .toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Lakshmi Vilas Bank':
                      console.log('Reached nsdl Lakshmi Vilas Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Utkal Gramin Bank':
                      console.log('Reached nsdl Utkal Gramin Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      var amountUtkal = Number(records[records.length - 1].slice(-12)) / 100
                      returnRecord.Amount = parseFloat(amountUtkal).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Paytm Payments Bank':
                      console.log('Reached nsdl Paytm Payments Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[1] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Bank Of India':
                      console.log('Reached nsdl Bank Of India Filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Andhra Pradesh Grameena Vikash Bank':
                      console.log('Reached nsdl Andhra Pradesh Grameena Vikash Bank Filter')
                      // Filter out Date
                      // Filter out Date
                      var dAndraVikasn = records[0].substr(0, 2)
                      var mAndraVikasn = records[0].substr(2, 2)
                      var yAndraVikasn = records[0].substr(4, 2)
                      var dateAndraVikasc = dAndraVikasn + '/' + mAndraVikasn + '/' + yAndraVikasn
                      returnRecord.Date = moment(dateAndraVikasc, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Chhattisgarh Rajya Gramin Bank':
                      console.log('Reached nsdl Chhattisgarh Rajya Gramin Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      var amountChhattis = Number(records[records.length - 1].slice(-12)) / 100
                      returnRecord.Amount = parseFloat(amountChhattis).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'India Post Payment Bank':
                      console.log('Reached nsdl India Post Payment Bank filter')
                      // Filter out Date
                      var dIndiaPostN = records[0].substr(6, 2)
                      var mIndiaPostN = records[0].substr(4, 2)
                      var yIndiaPostN = records[0].substr(0, 4)
                      var dateIndiaPostN = dIndiaPostN + '/' + mIndiaPostN + '/' + yIndiaPostN
                      returnRecord.Date = dateIndiaPostN
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Aryavart Bank Erstwhile Gramin Bank Of Aryavart':
                      console.log('Reached nsdl Aryavart Bank Erstwhile Gramin Bank Of Aryavart filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Baroda Rajasthan Kshetriya Gramin Bank':
                      console.log('Reached nsdl Baroda Rajasthan Kshetriya Gramin Bank filter')
                      // Filter out Date
                      var dBarodaRjN = records[0].substr(4, 2)
                      var mBarodaRjN = records[0].substr(2, 2)
                      var yBarodaRjN = records[0].substr(0, 2)
                      var dateBarodaRjN = dBarodaRjN + '/' + mBarodaRjN + '/' + yBarodaRjN
                      returnRecord.Date = moment(dateBarodaRjN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Bangiya Gramin Vikash Bank':
                      console.log('Reached nsdl Bangiya Gramin Vikash Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Fino Payments Bank':
                      console.log('Reached nsdl Fino Payments Bank filter')
                      // Filter out Date
                      var dFinoN = records[0].substr(2, 2)
                      var mFinoN = records[0].substr(0, 2)
                      var yFinoN = records[0].substr(4, 2)
                      var dateFinoN = dFinoN + '/' + mFinoN + '/' + yFinoN
                      returnRecord.Date = moment(dateFinoN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Karnataka Vikas Grameena Bank':
                      console.log('Reached nsdl Karnataka Vikas Grameena Bank filter')
                      // Filter out Date
                      var dKAVikasN = records[0].substr(4, 2)
                      var mKAVikasN = records[0].substr(2, 2)
                      var yKAVikasN = records[0].substr(0, 2)
                      var dateKAVikasN = dKAVikasN + '/' + mKAVikasN + '/' + yKAVikasN
                      returnRecord.Date = moment(dateKAVikasN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indian Overseas Bank':
                      console.log('Reached nsdl Indian Overseas Bank filter')
                      // Filter out Date
                      var dIOBN = records[0].substr(6, 2)
                      var mIOBN = records[0].substr(4, 2)
                      var yIOBN = records[0].substr(0, 4)
                      var dateIOBN = dIOBN + '/' + mIOBN + '/' + yIOBN
                      returnRecord.Date = dateIOBN
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Baroda Uttar Pradesh Gramin Bank':
                      console.log('Reached nsdl Baroda Uttar Pradesh Gramin Bank filter')
                      // Filter out Date
                      var dBUPGBN = records[0].substr(4, 2)
                      var mBUPGBN = records[0].substr(2, 2)
                      var yBUPGBN = records[0].substr(0, 2)
                      var dateBUPGBN = dBUPGBN + '/' + mBUPGBN + '/' + yBUPGBN
                      returnRecord.Date = moment(dateBUPGBN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indian Bank':
                      console.log('Reached nsdl Indian Bank filter')
                      if (Object.values(records).toString().length > 4) {
                        // Filter out Date
                        var dateIBN = records[0].substr((records[0].indexOf('-') - 4), 10)
                        returnRecord.Date = moment(dateIBN, 'DD-MM-YYYY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[0].substr(records[0].indexOf('-') + 5, 2) == 'CR' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[1]
                        // Filter out mode
                        returnRecord.Mode = records[records.length - 1]
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Prathma Up Gramin Bank Erstwhile Sarva Up Gramin Bank':
                      console.log('Reached nsdl Prathma Up Gramin Bank Erstwhile Sarva Up Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Rajasthan Marudhara Gramin Bank':
                      console.log('Reached nsdl Rajasthan Marudhara Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Madhyanchal Gramin Bank':
                      console.log('Reached nsdl Madhyanchal Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Madhya Pradesh Gramin Bank Erstwhile Narmada Jhabua Gramin Bank':
                      console.log('Reached nsdl Madhya Pradesh Gramin Bank Erstwhile Narmada Jhabua Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indusind Bank':
                      console.log('Reached nsdl Indusind Bank filter')
                      if (!records[0].includes('-----')) {
                        // Filter out Date
                        returnRecord.Date = moment(records[0], 'DD-MM-YYYY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[records.length - 1]
                        // Filter out mode
                        returnRecord.Mode = ''
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Axis Bank':
                      console.log('Reached nsdl Axis Bank filter')
                      // Filter out Date
                      var dAxisN = records[0].substr(0, 2)
                      var mAxisN = records[0].substr(2, 2)
                      var yAxisN = records[0].substr(4, 2)
                      var dateAxisN = dAxisN + '/' + mAxisN + '/' + yAxisN
                      returnRecord.Date = moment(dateAxisN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[0].substr(8, records[0].length - 8) + ' ' + records[1] : records[0].substr(8, records[0].length - 8)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Au Small Finance Bank':
                      console.log('Reached nsdl Au Small Finance Bank filter')
                      // Filter out Date
                      var dAUN = records[0].substr(4, 2)
                      var mAUN = records[0].substr(2, 2)
                      var yAUN = records[0].substr(0, 2)
                      var dateAUN = dAUN + '/' + mAUN + '/' + yAUN
                      returnRecord.Date = moment(dateAUN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[1] : records[1].substr(0, records[1].length - 12)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Purvanchal Gramin Bank':
                      console.log('Reached nsdl Purvanchal Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Odisha Gramya Bank':
                      console.log('Reached nsdl Odisha Gramya Bank filter')
                      // Filter out Date
                      var dOGBN = records[0].substr(6, 2)
                      var mOGBN = records[0].substr(4, 2)
                      var yOGBN = records[0].substr(0, 4)
                      var dateOGBN = dOGBN + '/' + mOGBN + '/' + yOGBN
                      returnRecord.Date = dateOGBN
                      // Filter out type
                      returnRecord.Type = records[1].substr(0, 1) == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Karnataka Gramin Bank Erstwhile Pragathi Krishna Gramin Bank':
                      console.log('Reached nsdl Karnataka Gramin Bank Erstwhile Pragathi Krishna Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'The Tiruchirapalli Dist. Cent Cooperative Bank Ltd':
                      console.log('Reached nsdl The Tiruchirapalli Dist. Cent Cooperative Bank Ltd filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[1] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Sarva Haryana Gramin Bank':
                      console.log('Reached nsdl Sarva Haryana Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Chaitanya Godavari Gramin Bank':
                      console.log('Reached nsdl Chaitanya Godavari Gramin Bank filter')
                      // Filter out Date
                      var dCGGN = records[0].substr(0, 2)
                      var mCGGN = records[0].substr(2, 2)
                      var yCGGN = records[0].substr(4, 2)
                      var dateCGGN = dCGGN + '/' + mCGGN + '/' + yCGGN
                      returnRecord.Date = moment(dateCGGN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Fincare Small Finance Bank':
                      console.log('Reached nsdl Fincare Small Finance Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'YYYY-MM-DD').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1]) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[2] + ' ' + records[3] : records[2]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Jharkhand Rajya Gramin Bank Erstwhile Vananchal Gramin Bank':
                      console.log('Reached nsdl Jharkhand Rajya Gramin Bank Erstwhile Vananchal Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) === 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1]) / 100).toFixed(2)
                      // Filter out mode
                      if (records.length > 4) {
                        returnRecord.Mode = records[1] + ' ' + records[2] + ' ' + records[3]
                      } else if (records.length > 3) {
                        returnRecord.Mode = records[1] + ' ' + records[2]
                      } else {
                        returnRecord.Mode = records[1]
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Suryoday Small Fianance Bank':
                      console.log('Reached nsdl Suryoday Small Fianance Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'YYYY-MM-DD').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[2] + ' ' + records[3] : records[2]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Equitas Small Finance Bank':
                      console.log('Reached nsdl Equitas Small Finance Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 1] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 2]) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.toString().substr(6, 20)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Saptagiri Grameena Bank':
                      console.log('Reached nsdl Saptagiri Grameena Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'YYYY-MM-YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 2]
                      // Filter out mode
                      returnRecord.Mode = records[records.length - 1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Baroda Gujarat Gramin Bank':
                      console.log('Reached nsdl Baroda Gujarat Gramin Bank filter')
                      // Filter out Date
                      var dBGGBN = records[0].substr(4, 2)
                      var mBGGBN = records[0].substr(2, 2)
                      var yBGGBN = records[0].substr(0, 2)
                      var dateBGGBN = dBGGBN + '/' + mBGGBN + '/' + yBGGBN
                      returnRecord.Date = moment(dateBGGBN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - (records[records.length - 1].length + 8)))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                      // case 'Indian Bank Erstwhile Allahabad Bank':
                      //   console.log('Reached nsdl Indian Bank Erstwhile Allahabad Bank filter')
                      //   // Filter out Date
                      //   returnRecord.Date = moment(records[0], 'YYYY-MM-YY').format('DD/MM/YYYY')
                      //   // Filter out type
                      //   returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                      //   // Filter out amount
                      //   returnRecord.Amount = records[records.length - 2]
                      //   // Filter out mode
                      //   returnRecord.Mode = records[records.length - 1]
                      //   log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      //   structure_type = true
                      //   break
                      case 'Indian Bank Erstwhile Allahabad Bank':
                        console.log('Reached nsdl Indian Bank Erstwhile Allahabad Bank filter')
                        // Filter out Date
                        returnRecord.Date = moment(records[0],'DD/MM').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[records.length - 2]
                        // Filter out mode
                        returnRecord.Mode = records[records.length - 1]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
  
                    case 'City Union Bank':
                      console.log('Reached nsdl City Union Bank filter')
                      // Filter out Date
                      var dCUBN = records[0].substr(2, 2)
                      var mCUBN = records[0].substr(0, 2)
                      var yCUBN = records[0].substr(4, 2)
                      var dateCUBN = dCUBN + '/' + mCUBN + '/' + yCUBN
                      returnRecord.Date = moment(dateCUBN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - 20))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Uttarakhand Gramin Bank':
                      console.log('Reached nsdl Uttarakhand Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - 20))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    default:
                      console.log('NSDL Else case -><><><>')
                      returnArray = lastRecordsArray
                      break
                  }
                  returnArray.push(returnRecord)
                }
              }
            } else if (aggregatorBank === 'csb') {
              if (Object.keys(records).length > 1 || Object.values(records).toString().startsWith('Balance')) {
                if (typeof records[1] == 'undefined') {
                  records[1] = ''
                }
                if (records[0] === 'Balance' || records[0] === 'AVAIL' || records[1] === 'BAL' || records[0].endsWith('Balance') || records[1].endsWith('Balance') || records[0].startsWith('Balance') || records[1].startsWith('Balance')) {
                  structure_type = true
                  console.log('bankNAme******************', bankName)
                  if (bankName === 'Airtel Payment Bank' || bankName === 'Saptagiri Grameena Bank') {
                    balance = records[records.length - 1] // getting formated amount
                  } else if (bankName === 'State Bank Of India' || bankName === 'Rajasthan Marudhara Gramin Bank') {
                    console.log('reached balance State Bank Of India', records[records.length - 2])
                    balance = (Number(records[records.length - 2])).toString()
                  } else if (bankName === 'Hdfc Bank') {
                    console.log('reached balance Hdfc Bank', records[records.length - 1])
                    balance = records[records.length - 1]
                  } else if (bankName === 'Idbi Bank' || bankName === 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank' ||
                  bankName === 'Canara Bank Erstwhile Syndicate Bank' || bankName === 'Punjab National Bank Erstwhile Oriental Bank Of Commerce') {
                    console.log('reached balance Idbi/Union/canara/central Bank', records, records[records.length - 1])
                    let bal = ''
                    if (records[records.length - 1] == '' || records[records.length - 1] == 'undefined') {
                      if (records[records.length - 2] == '' || records[records.length - 2] == 'undefined') {
                        bal = ''
                      } else {
                        bal = records[records.length - 2]
                      }
                    } else {
                      bal = records[records.length - 1]
                    }
                    balance = Number(bal.split(':')[1]).toFixed(2) == 'NaN' ? 'Not Available' : Number(bal.split(':')[1]).toFixed(2)
                  } else if (bankName === 'Andhra Pragathi Grameena Bank' || bankName === 'Indian Bank') {
                    console.log('reached balance Andhra Pragathi Grameena Bank Bank')
                    balance = Number(records[records.length - 1]).toFixed(2)
                  } else if (bankName === 'Baroda Uttar Pradesh Gramin Bank' || bankName === 'Axis Bank' || bankName === 'Bank Of Maharashtra') {
                    balance = (Number(records[records.length - 1]) / 100).toFixed(2)
                  } else {
                    console.log('reached balance else', records[records.length - 1])
                    balance = (Number(records[records.length - 1]) / 100).toString()
                  }
                } else {
                  log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { switch: bankName, aggregator_bank_name: 'CSB' } })
                  switch (bankName) {
                    case 'Airtel Payment Bank':
                      console.log('Reached csb Airtel Payment Bank Filter')
                      returnRecord.Date = records[0].substr(4, 5)
                      returnRecord.Type = records[2] == 'D' ? 'Dr' : 'Cr'
                      returnRecord.Mode = records[1]
                      returnRecord.Amount = parseFloat(records[3]).toFixed(2)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Hdfc Bank':
                      console.log('Reached csb Hdfc Bank Filter')
                      returnRecord.Date = records[0]
                      returnRecord.Type = records[2] == 'D' ? 'Dr' : 'Cr'
                      returnRecord.Mode = records[1]
                      returnRecord.Amount = records[3]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Icici Bank':
                      console.log('Reached csb Icici Bank Filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr((records[0].indexOf('/') - 2), 10)
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount
                      returnRecord.Amount = records[2]
                      // Filter out mode
                      returnRecord.Mode = records[3]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                      // case 'Punjab National Bank Erstwhile Oriental Bank Of Commerce':
                      //   console.log('Reached csb Punjab National Bank Erstwhile Oriental Bank Of Commerce Filter', isNaN(Number(records[records.length - 1])))
                      //   // Filter out Date
                      //   returnRecord.Date = records[0].substr((records[0].indexOf('/') - 2), 5)
                      //   // Filter out type
                      //   if (isNaN(Number(records[records.length - 1]))) {
                      //     console.log(records[records.length - 1])
                      //     returnRecord.Type = records[records.length - 1] === 'C' ? 'Cr' : 'Dr'
                      //   } else {
                      //     console.log(records[records.length - 2])
                      //     returnRecord.Type = records[records.length - 2] === 'C' ? 'Cr' : 'Dr'
                      //   }
                      //   // Filter out amount
                      //   returnRecord.Amount = records[0].substr(0, (records[0].indexOf('/') - 2))
                      //   // Filter out mode
                      //   returnRecord.Mode = records[1]
                      //   log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      //   structure_type = true
                      //   break
  
                    case 'State Bank Of India':
                      // Filter out Date and change
                      console.log('Reached csb State Bank of india Filter', records.length)
                      var dcsbi = records[0].substr(4, 2)
                      var mcsbi = records[0].substr(6, 2)
                      var ycsbi = records[0].substr(8, 2)
                      var datecsbi = dcsbi + '/' + mcsbi + '/' + ycsbi
                      returnRecord.Date = moment(datecsbi, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out amount given in pos style
                      var amountcsbi = Number(records[records.length - 1].slice(-8)) // 100
                      returnRecord.Amount = parseFloat(amountcsbi).toFixed(2)
                      // Filter out type
                      returnRecord.Type = records[0].substr(11, 2) === 'CR' ? 'Cr' : 'Dr'
                      // Filter out mode
                      var modecsbi = ''
                      if (records.length > 5) {
                        modecsbi = records[1] + ' ' + records[2] + ' ' + records[3] + ' ' + records[4]
                      } else if (records.length == 4) {
                        modecsbi = records[1] + ' ' + records[2]
                      } else {
                        modecsbi = records[1]
                      }
  
                      returnRecord.Mode = modecsbi
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                      // case 'Bank Of Maharashtra':
                      //   var dcbom = records[0].substr(8, 2)
                      //   var mcbom = records[0].substr(6, 2)
                      //   var ycbom = records[0].substr(4, 2)
                      //   var datecbom = dcbom + '/' + mcbom + '/' + ycbom
                      //   returnRecord.Date = moment(datecbom, 'DD/MM/YY').format('DD/MM/YYYY')
                      //   returnRecord.Type = records[0].substr(10, 2) == 'DR' ? 'Dr' : 'Cr'
                      //   returnRecord.Mode = records[1]
                      //   returnRecord.Amount = records[0].substr(0, records[0].length - 8)
                      //   structure_type = true
                      //   break
  
                    case 'Idbi Bank':
                      console.log('Reached csb Idbi Bank Filter', records)
                      // Filter out Date
                      returnRecord.Date = records[0].substr((records[0].indexOf('/') - 2), 5)
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount
                      returnRecord.Amount = parseFloat(Number(records[records.length - 1])).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Central Bank Of India':
                      console.log('Reached csb Central Bank Of India Filter', records)
                      // Filter out Date
                      returnRecord.Date = records[0].substr(4, 4)
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount
                      returnRecord.Amount = parseFloat(Number(records[records.length - 1])).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank':
                      console.log('Reached csb Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank Filter', records)
                      // Filter out Date
                      returnRecord.Date = records[0].substr((records[0].indexOf('/') - 2), 5)
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount
                      returnRecord.Amount = parseFloat(Number(records[records.length - 1])).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Canara Bank Erstwhile Syndicate Bank':
                      console.log('Reached csb Canara Bank Erstwhile Syndicate Bank Filter', records)
                      // Filter out Date
                      returnRecord.Date = records[0].substr((records[0].indexOf('/') - 2), 5)
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount
                      returnRecord.Amount = parseFloat(Number(records[records.length - 1])).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Andhra Pragathi Grameena Bank':
                      console.log('Reached csb Andhra Pragathi Grameena Bank filter')
                      // Filter out Date
                      var dAndrac = records[0].substr(4, 2)
                      var mAndrac = records[0].substr(6, 2)
                      var yAndrac = records[0].substr(8, 2)
                      var dateAndran = dAndrac + '/' + mAndrac + '/' + yAndrac
                      returnRecord.Date = moment(dateAndran, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(10, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount
                      returnRecord.Amount = parseFloat(Number(records[records.length - 1])).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Saptagiri Grameena Bank':
                      console.log('Reached csb Saptagiri Grameena Bank Filter')
                      if (Object.values(records).toString().length > 4) {
                      // Filter out Date
                        var dateSGBC = records[0].substr((records[0].indexOf('-') - 4), 10)
                        returnRecord.Date = moment(dateSGBC, 'DD-MM-YYYY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[0].substr(records[0].indexOf('-') + 5, 2) == 'CR' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[1]
                        // Filter out mode
                        returnRecord.Mode = records[records.length - 1]
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                      // case 'India Post Payment Bank':
                      //   console.log('Reached csb India Post Payment Bank filter')
                      //   // Filter out Date
                      //   var dIndPostc = records[0].substr(10, 2)
                      //   var mIndPostc = records[0].substr(8, 2)
                      //   var yIndPostc = records[0].substr(4, 4)
                      //   var dateIndPostn = dIndPostc + '/' + mIndPostc + '/' + yIndPostc
                      //   returnRecord.Date = moment(dateIndPostn, 'DD/MM/YY').format('DD/MM/YYYY')
                      //   // Filter out type
                      //   returnRecord.Type = records[0].substr(10, 2) == 'DR' ? 'Dr' : 'Cr'
                      //   // Filter out amount
                      //   returnRecord.Amount = parseFloat(Number(records[records.length - 1])).toFixed(2)
                      //   // Filter out mode
                      //   returnRecord.Mode = ''
                      //   log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      //   structure_type = true
                      //   break
  
                    case 'Baroda Uttar Pradesh Gramin Bank':
                      console.log('Reached csb Baroda Uttar Pradesh Gramin Bank filter')
                      // Filter out Date
                      var dBUPGBC = records[0].substr(8, 2)
                      var mBUPGBC = records[0].substr(6, 2)
                      var yBUPGBC = records[0].substr(4, 2)
                      var dateBUPGBC = dBUPGBC + '/' + mBUPGBC + '/' + yBUPGBC
                      returnRecord.Date = moment(dateBUPGBC, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indian Bank':
                      console.log('Reached csb Indian Bank filter')
                      if (Object.values(records).toString().length > 4) {
                        // Filter out Date
                        var dateIBC = records[0].substr((records[0].indexOf('-') - 4), 10)
                        returnRecord.Date = moment(dateIBC, 'DD-MM-YYYY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[0].substr(records[0].indexOf('-') + 5, 2) == 'CR' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[1]
                        // Filter out mode
                        returnRecord.Mode = records[records.length - 1]
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Rajasthan Marudhara Gramin Bank':
                      console.log('Reached csb Rajasthan Marudhara Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(4, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-8)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Axis Bank':
                      console.log('Reached csb Axis Bank filter')
                      // Filter out Date
                      var dAxisC = records[0].substr(4, 2)
                      var mAxisC = records[0].substr(6, 2)
                      var yAxisC = records[0].substr(8, 2)
                      var dateAxisC = dAxisC + '/' + mAxisC + '/' + yAxisC
                      returnRecord.Date = moment(dateAxisC, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-8)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[0].substr(12, records[0].length - 12) + ' ' + records[1] : records[0].substr(12, records[0].length - 12)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indian Bank Erstwhile Allahabad Bank':
                      console.log('Reached csb Indian Bank Erstwhile Allahabad Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0].substr(4, 10), 'YYYY-MM-YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(14, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 2]
                      // Filter out mode
                      returnRecord.Mode = records[records.length - 1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    default:
                      console.log('CSB Else case -><><><>')
                      returnArray = lastRecordsArray
                      break
                  }
                  returnArray.push(returnRecord)
                }
              }
            } else if (aggregatorBank === 'fingpay') {
              returnArray = lastRecordsArray
              structure_type = true
            }else if(aggregatorBank === 'credopay'){
                console.log("inside credopay filter")
                returnArray = lastRecordsArray
                structure_type = false
            } else if (aggregatorBank === 'finobank') {
              if (Object.keys(records).length > 0) {
                console.log('<<<<<<<<<', records[0])
                if (!(['AVAIL', 'Balance'].includes(records[0]) || records[0].includes('Balance'))) {
                  switch (bankName) {
                    case 'Federal Bank':
                      console.log('Reached Fino Federal Bank Filter')
                      // Filter out Date and change format
                      var dayFinoFed = records[0].substr(4, 2)
                      var monthFinoFed = records[0].substr(2, 2)
                      var yearFinoFed = records[0].substr(0, 2)
                      var dateFinoFed = dayFinoFed + '/' + monthFinoFed + '/' + yearFinoFed
                      returnRecord.Date = moment(dateFinoFed, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = Number(records[records.length - 1].slice(-12)) / 100
                      returnRecord.Mode = records.length > 2 ? records[1] + ' ' + records[2].substr(0, records[2].length - 12) : records[1].substr(0, records[1].length - 12)
                      structure_type = true
                      break
  
                    case 'Airtel Payment Bank':
                      console.log('Reached Fino Airtel Payment Bank Filter')
                      // Filter out Date and change format
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = records[records.length - 1]
                      returnRecord.Mode = records[1]
                      structure_type = true
                      break
  
                    case 'Bank Of Maharashtra':
                      console.log('Reached Fino Bank Of Maharashtra Filter')
                      // Filter out Date and change format
                      var dayFinoBOM = records[0].substr(4, 2)
                      var monthFinoBOM = records[0].substr(2, 2)
                      var yearFinoBOM = records[0].substr(0, 2)
                      var dateFinoBOM = dayFinoBOM + '/' + monthFinoBOM + '/' + yearFinoBOM
                      returnRecord.Date = moment(dateFinoBOM, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(0, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      returnRecord.Mode = records[1]
                      structure_type = true
                      break
  
                    case 'Hdfc Bank':
                      console.log('Reached Fino Hdfc Bank Filter')
                      // Filter out Date and change format
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = records[records.length - 1]
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      structure_type = true
                      break
  
                    case 'State Bank Of India':
                      console.log('Reached Fino State Bank Of India Filter')
                      // Filter out Date and change format
                      var dayFinoSBI = records[0].substr(0, 2)
                      var monthFinoSBI = records[0].substr(2, 2)
                      var yearFinoSBI = records[0].substr(4, 2)
                      var dateFinoSBI = dayFinoSBI + '/' + monthFinoSBI + '/' + yearFinoSBI
                      returnRecord.Date = moment(dateFinoSBI, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      structure_type = true
                      break
  
                    case 'Icici Bank':
                      console.log('Reached Fino Icici Bank Filter')
                      // Filter out Date and change format
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount given in pos style
                      returnRecord.Amount = records[2]
                      returnRecord.Mode = records.length > 4 ? records[3] + ' ' + records[4] : records[3]
                      structure_type = true
                      break
  
                    case 'Paytm Payments Bank':
                      console.log('Reached Fino Paytm Payments Bank Filter')
                      // Filter out Date and change format
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[1] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = records[records.length - 1]
                      returnRecord.Mode = ''
                      structure_type = true
                      break
  
                    case 'Axis Bank':
                      console.log('Reached Fino Axis Bank Filter')
                      // Filter out Date and change format
                      var dayFinoAxis = records[0].substr(0, 2)
                      var monthFinoAxis = records[0].substr(2, 2)
                      var yearFinoAxis = records[0].substr(4, 2)
                      var dateFinoAxis = dayFinoAxis + '/' + monthFinoAxis + '/' + yearFinoAxis
                      returnRecord.Date = moment(dateFinoAxis, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'DR' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = Number(records[records.length - 1].slice(-12)) / 100
                      returnRecord.Mode = records[0].substr(8, records[0].length - 1)
                      structure_type = true
                      break
  
                    case 'Canara Bank Erstwhile Syndicate Bank':
                    case 'Idbi Bank':
                      console.log('Reached Fino Canara Bank Erstwhile Syndicate/Idbi Bank Filter')
                      // Filter out Date and change format
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      returnRecord.Mode = records[1]
                      structure_type = true
                      break
  
                    case 'Central Bank Of India':
                      console.log('Reached Fino Central Bank Of India Filter')
                      // Filter out Date and change format
                      var dayFinoCBI = records[0].substr(0, 2)
                      var monthFinoCBI = records[0].substr(2, 2)
                      returnRecord.Date = dayFinoCBI + '/' + monthFinoCBI
                      // Filter out type
                      returnRecord.Type = records[1].substr(0, 1) == 'D' ? 'Dr' : 'Cr'
                      // Filter out amount given in pos style
                      returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                      returnRecord.Mode = records[1]
                      structure_type = true
                      break
                    case 'Indian Bank Erstwhile Allahabad Bank':
                        console.log('Reached fino Indian Bank Erstwhile Allahabad Bank filter')
                        // Parse the date
                        returnRecord.Date = moment(records[0].split(' ')[0], 'DD/MM').format('DD/MM/YYYY');
                        // Parse the type
                        returnRecord.Type = records[0].substr(19, 2).trim() === 'CR' ? 'Cr' : 'Dr';
                        // Parse the amount
                        returnRecord.Amount = records[records.length - 1]
                        // Parse the mode
                        returnRecord.Mode = records[records.length - 3]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank':
                        console.log('Reached fino Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank')
                        // Parse the date
                        returnRecord.Date = moment(records[0].split(' ')[0], 'DD/MM').format('DD/MM/YYYY');
                        // Parse the type
                        // returnRecord.Type = records[0].substr(19, 2).trim() === 'CR' ? 'Cr' : 'Dr';
                        returnRecord.Type = records[2].trim() === 'C' ? 'Cr' : 'Dr';
                        // Parse the amount
                        returnRecord.Amount = records[records.length - 1]
                        // Parse the mode
                        returnRecord.Mode = records[records.length - 3]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Indian Overseas Bank':
                        console.log('Reached fino Indian Overseas Bank filter')
                        // Parse the date
                        returnRecord.Date = moment(records[0].split(' ')[0], 'DD/MM').format('DD/MM/YYYY');
                        // Parse the type
                        returnRecord.Type = records[0].substr(19, 2).trim() === 'CR' ? 'Cr' : 'Dr';
                        // Parse the amount
                        returnRecord.Amount = records[records.length - 1]
                        // Parse the mode
                        returnRecord.Mode = records[records.length - 3]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Punjab National Bank Erstwhile Oriental Bank Of Commerce':
                          console.log('Reached fino Punjab National Bank Erstwhile Oriental Bank Of Commerce Filter')
                          // Filter out Date
                          returnRecord.Date = records[0]
                          // Filter out type
                          returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                          // Filter out amount
                          returnRecord.Amount = Number(records[records.length - 1]).toFixed(2)
                          // Filter out mode
                          returnRecord.Mode = records[1]
                          log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                          structure_type = true
                          break
                    case 'Andhra Pragathi Grameena Bank':
                          console.log('Reached fino Andhra Pragathi Grameena Bank Filter')
                          // Filter out Date
                          var dAndran = records[0].substr(4, 2)
                          var mAndran = records[0].substr(2, 2)
                          var yAndran = records[0].substr(0, 2)
                          var dateAndrac = dAndran + '/' + mAndran + '/' + yAndran
                          returnRecord.Date = moment(dateAndrac, 'DD/MM/YY').format('DD/MM/YYYY')
                          // Filter out type
                          returnRecord.Type = records[0].substr(5, 2) == 'DR' ? 'Dr' : 'Cr'
                          // Filter out amount
                          returnRecord.Amount = (Number(records[records.length - 1]) / 100) // .toFixed(2)
                          // Filter out mode
                          returnRecord.Mode = ''
                          log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                          structure_type = true
                          break  
                    case 'Lakshmi Vilas Bank':
                          console.log('Reached fino Lakshmi Vilas Bank Filter')
                          // Filter out Date
                          returnRecord.Date = records[0]
                          // Filter out type
                          returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                          // Filter out amount
                          returnRecord.Amount = records[records.length - 1]
                          // Filter out mode
                          returnRecord.Mode = records[1]
                          log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                          structure_type = true
                          break
                    case 'Utkal Gramin Bank':
                          console.log('Reached fino Utkal Gramin Bank Filter')
                          // Filter out Date
                          returnRecord.Date = records[0].substr(0, 5)
                          // Filter out type
                          returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                          // Filter out amount
                          var amountUtkal = Number(records[records.length - 1].slice(-12)) / 100
                          returnRecord.Amount = parseFloat(amountUtkal).toFixed(2)
                          // Filter out mode
                          returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                          log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                          structure_type = true
                          break
                    case 'Bank Of India':
                        console.log('Reached fino Bank Of India Filter')
                        // Filter out Date
                        returnRecord.Date = records[0]
                        // Filter out type
                        returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                        // Filter out amount
                         returnRecord.Amount = records[records.length - 1]
                         // Filter out mode
                        returnRecord.Mode = records[1]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Andhra Pradesh Grameena Vikash Bank':
                        console.log('Reached fino Andhra Pradesh Grameena Vikash Bank Filter')
                        // Filter out Date
                        var dAndraVikasn = records[0].substr(0, 2)
                        var mAndraVikasn = records[0].substr(2, 2)
                        var yAndraVikasn = records[0].substr(4, 2)
                        var dateAndraVikasc = dAndraVikasn + '/' + mAndraVikasn + '/' + yAndraVikasn
                        returnRecord.Date = moment(dateAndraVikasc, 'DD/MM/YY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[records.length - 1]
                        // Filter out mode
                        returnRecord.Mode = ''
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Chhattisgarh Rajya Gramin Bank':
                        console.log('Reached fino Chhattisgarh Rajya Gramin Bank Filter')
                        // Filter out Date
                        returnRecord.Date = records[0].substr(0, 5)
                        // Filter out type
                        returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                        // Filter out amount
                        var amountChhattis = Number(records[records.length - 1].slice(-12)) / 100
                        returnRecord.Amount = parseFloat(amountChhattis).toFixed(2)
                        // Filter out mode
                        returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Aryavart Bank Erstwhile Gramin Bank Of Aryavart':
                        console.log('Reached fino Aryavart Bank Erstwhile Gramin Bank Of Aryavart filter')
                        // Filter out Date
                        returnRecord.Date = records[0]
                        // Filter out type
                        returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[records.length - 1]
                        // Filter out mode
                        returnRecord.Mode = records[1]
                        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                        structure_type = true
                        break
                    case 'Baroda Rajasthan Kshetriya Gramin Bank':
                      console.log('Reached fino Baroda Rajasthan Kshetriya Gramin Bank filter')
                      // Filter out Date
                      var dBarodaRjN = records[0].substr(4, 2)
                      var mBarodaRjN = records[0].substr(2, 2)
                      var yBarodaRjN = records[0].substr(0, 2)
                      var dateBarodaRjN = dBarodaRjN + '/' + mBarodaRjN + '/' + yBarodaRjN
                      returnRecord.Date = moment(dateBarodaRjN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Bangiya Gramin Vikash Bank':
                      console.log('Reached fino Bangiya Gramin Vikash Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Fino Payments Bank':
                      console.log('Reached fino Fino Payments Bank filter')
                      // Filter out Date
                      var dFinoN = records[0].substr(2, 2)
                      var mFinoN = records[0].substr(0, 2)
                      var yFinoN = records[0].substr(4, 2)
                      var dateFinoN = dFinoN + '/' + mFinoN + '/' + yFinoN
                      returnRecord.Date = moment(dateFinoN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Karnataka Vikas Grameena Bank':
                      console.log('Reached fino Karnataka Vikas Grameena Bank filter')
                      // Filter out Date
                      var dKAVikasN = records[0].substr(4, 2)
                      var mKAVikasN = records[0].substr(2, 2)
                      var yKAVikasN = records[0].substr(0, 2)
                      var dateKAVikasN = dKAVikasN + '/' + mKAVikasN + '/' + yKAVikasN
                      returnRecord.Date = moment(dateKAVikasN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
                    case 'Baroda Uttar Pradesh Gramin Bank':
                      console.log('Reached fino Baroda Uttar Pradesh Gramin Bank filter')
                      // Filter out Date
                      var dBUPGBN = records[0].substr(4, 2)
                      var mBUPGBN = records[0].substr(2, 2)
                      var yBUPGBN = records[0].substr(0, 2)
                      var dateBUPGBN = dBUPGBN + '/' + mBUPGBN + '/' + yBUPGBN
                      returnRecord.Date = moment(dateBUPGBN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indian Bank':
                      console.log('Reached fino Indian Bank filter')
                      if (Object.values(records).toString().length > 4) {
                        // Filter out Date
                        var dateIBN = records[0].substr((records[0].indexOf('-') - 4), 10)
                        returnRecord.Date = moment(dateIBN, 'DD-MM-YYYY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[0].substr(records[0].indexOf('-') + 5, 2) == 'CR' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[1]
                        // Filter out mode
                        returnRecord.Mode = records[records.length - 1]
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Prathma Up Gramin Bank Erstwhile Sarva Up Gramin Bank':
                      console.log('Reached fino Prathma Up Gramin Bank Erstwhile Sarva Up Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Rajasthan Marudhara Gramin Bank':
                      console.log('Reached fino Rajasthan Marudhara Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Madhyanchal Gramin Bank':
                      console.log('Reached fino Madhyanchal Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Madhya Pradesh Gramin Bank Erstwhile Narmada Jhabua Gramin Bank':
                      console.log('Reached fino Madhya Pradesh Gramin Bank Erstwhile Narmada Jhabua Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Indusind Bank':
                      console.log('Reached fino Indusind Bank filter')
                      if (!records[0].includes('-----')) {
                        // Filter out Date
                        returnRecord.Date = moment(records[0], 'DD-MM-YYYY').format('DD/MM/YYYY')
                        // Filter out type
                        returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                        // Filter out amount
                        returnRecord.Amount = records[records.length - 1]
                        // Filter out mode
                        returnRecord.Mode = ''
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
                    case 'Au Small Finance Bank':
                      console.log('Reached fino Au Small Finance Bank filter')
                      // Filter out Date
                      var dAUN = records[0].substr(4, 2)
                      var mAUN = records[0].substr(2, 2)
                      var yAUN = records[0].substr(0, 2)
                      var dateAUN = dAUN + '/' + mAUN + '/' + yAUN
                      returnRecord.Date = moment(dateAUN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 2 ? records[1] : records[1].substr(0, records[1].length - 12)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Purvanchal Gramin Bank':
                      console.log('Reached fino Purvanchal Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 3 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Odisha Gramya Bank':
                      console.log('Reached fino Odisha Gramya Bank filter')
                      // Filter out Date
                      var dOGBN = records[0].substr(6, 2)
                      var mOGBN = records[0].substr(4, 2)
                      var yOGBN = records[0].substr(0, 4)
                      var dateOGBN = dOGBN + '/' + mOGBN + '/' + yOGBN
                      returnRecord.Date = dateOGBN
                      // Filter out type
                      returnRecord.Type = records[1].substr(0, 1) == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Karnataka Gramin Bank Erstwhile Pragathi Krishna Gramin Bank':
                      console.log('Reached fino Karnataka Gramin Bank Erstwhile Pragathi Krishna Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'The Tiruchirapalli Dist. Cent Cooperative Bank Ltd':
                      console.log('Reached fino The Tiruchirapalli Dist. Cent Cooperative Bank Ltd filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[1] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = ''
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Sarva Haryana Gramin Bank':
                      console.log('Reached fino Sarva Haryana Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Chaitanya Godavari Gramin Bank':
                      console.log('Reached fino Chaitanya Godavari Gramin Bank filter')
                      // Filter out Date
                      var dCGGN = records[0].substr(0, 2)
                      var mCGGN = records[0].substr(2, 2)
                      var yCGGN = records[0].substr(4, 2)
                      var dateCGGN = dCGGN + '/' + mCGGN + '/' + yCGGN
                      returnRecord.Date = moment(dateCGGN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[records.length - 2] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[1] + ' ' + records[2] : records[1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Fincare Small Finance Bank':
                      console.log('Reached fino Fincare Small Finance Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'YYYY-MM-DD').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1]) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[2] + ' ' + records[3] : records[2]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Jharkhand Rajya Gramin Bank Erstwhile Vananchal Gramin Bank':
                      console.log('Reached fino Jharkhand Rajya Gramin Bank Erstwhile Vananchal Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) === 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1]) / 100).toFixed(2)
                      // Filter out mode
                      if (records.length > 4) {
                        returnRecord.Mode = records[1] + ' ' + records[2] + ' ' + records[3]
                      } else if (records.length > 3) {
                        returnRecord.Mode = records[1] + ' ' + records[2]
                      } else {
                        returnRecord.Mode = records[1]
                      }
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Suryoday Small Fianance Bank':
                      console.log('Reached fino Suryoday Small Fianance Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'YYYY-MM-DD').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[1]
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.length > 4 ? records[2] + ' ' + records[3] : records[2]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Equitas Small Finance Bank':
                      console.log('Reached fino Equitas Small Finance Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0]
                      // Filter out type
                      returnRecord.Type = records[records.length - 1] == 'C' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 2]) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.toString().substr(6, 20)
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Saptagiri Grameena Bank':
                      console.log('Reached fino Saptagiri Grameena Bank filter')
                      // Filter out Date
                      returnRecord.Date = moment(records[0], 'YYYY-MM-YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(10, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 2]
                      // Filter out mode
                      returnRecord.Mode = records[records.length - 1]
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Baroda Gujarat Gramin Bank':
                      console.log('Reached fino Baroda Gujarat Gramin Bank filter')
                      // Filter out Date
                      var dBGGBN = records[0].substr(4, 2)
                      var mBGGBN = records[0].substr(2, 2)
                      var yBGGBN = records[0].substr(0, 2)
                      var dateBGGBN = dBGGBN + '/' + mBGGBN + '/' + yBGGBN
                      returnRecord.Date = moment(dateBGGBN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = records[records.length - 1]
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - (records[records.length - 1].length + 8)))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
                    case 'City Union Bank':
                      console.log('Reached fino City Union Bank filter')
                      // Filter out Date
                      var dCUBN = records[0].substr(2, 2)
                      var mCUBN = records[0].substr(0, 2)
                      var yCUBN = records[0].substr(4, 2)
                      var dateCUBN = dCUBN + '/' + mCUBN + '/' + yCUBN
                      returnRecord.Date = moment(dateCUBN, 'DD/MM/YY').format('DD/MM/YYYY')
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - 20))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
  
                    case 'Uttarakhand Gramin Bank':
                      console.log('Reached fino Uttarakhand Gramin Bank filter')
                      // Filter out Date
                      returnRecord.Date = records[0].substr(0, 5)
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - 20))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break
                    case 'Punjab And Sind Bank':
                      console.log('Reached fino Punjab And Sind Bank filter')
                      // Filter out Date
                      var dPSN = 0
                      var mPSN = records[0].substr(4, 2)
                      var yPSN = records[0].substr(0, 4)
                      var datePSN = mPSN + '/' + yPSN
                      returnRecord.Date = datePSN
                      // Filter out type
                      returnRecord.Type = records[0].substr(6, 2) == 'CR' ? 'Cr' : 'Dr'
                      // Filter out amount
                      returnRecord.Amount = (Number(records[records.length - 1].slice(-12)) / 100).toFixed(2)
                      // Filter out mode
                      returnRecord.Mode = records.join(' ').substr(8, (records.toString().length - 20))
                      log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnRecord: returnRecord } })
                      structure_type = true
                      break             
                    default:
                      console.log('FINO Else case -><><><>')
                      returnArray = lastRecordsArray
                      break
                  }
                  returnArray.push(returnRecord)
                } else {
                  if (bankName === 'State Bank Of India') {
                    balance = Number(records[records.length - 2]).toFixed(2)
                  } else if (bankName === 'Canara Bank Erstwhile Syndicate Bank' || bankName == 'Idbi Bank') {
                    balance = records[0].split(':')[1]
                  } else if (bankName === 'Federal Bank' || bankName === 'Bank Of Maharashtra' ||
                    bankName == 'Icici Bank' || bankName == 'Axis Bank') {
                    balance = (Number(records[records.length - 1]) / 100).toFixed(2)
                  } else if (bankName === 'Central Bank Of India') {
                    balance = Number(records[records.length - 1]).toFixed(2)
                  } else {
                    balance = records[records.length - 1]
                  }
                }
              }
            }else {
              console.log('fino / other structure change')
              returnArray = lastRecordsArray
              structure_type = false
            }
          }
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnArray: returnArray } })
        returnArray = returnArray.filter(function (e) {
          return Object.keys(e).length > 0
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'extractLastRecordData', type: 'request', fields: { returnArray: returnArray, balance: balance } })
        // split balance
        const substring1 = 'Balance:+'
        const substring2 = 'Balance:'
        const substring3 = '+'
        const substring4 = 'Balance:-'
        let myArray = ''
        if (balance != '' && balance != 'NaN' && balance.includes(substring1)) {
          console.log('exist')
          myArray = balance.split(':')
          balance = myArray[1].slice(1)
        } else if (balance != '' && balance != 'NaN' && balance.includes(substring4)) {
          myArray = balance.split(':')
          balance = myArray[1]
        } else if (balance != '' && balance != 'NaN' && balance.includes(substring2)) {
          myArray = balance.split(':')
          balance = myArray[1]
          // console.log('Welcome to Programiz!', myArray[1])
        } else if (balance != '' && balance != 'NaN' && balance.includes(substring3)) {
          myArray = balance.split(':')
          balance = myArray[1].slice(1)
        }
        return { returnArray: returnArray, balance: balance, structure_type: structure_type }
        
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMiniStatement', type: 'catcherror', fields: err })
    } finally {
      // Release the connection
      // connection.release()
    }
  }
}

module.exports = ministatementController
