const mySQLWrapper = require('../../lib/mysqlWrapper')
const DAO = require('../../lib/dao')

class MakePaymentRequest extends DAO {
  /**
       * Overrides TABLE_NAME with this class' backing table at MySQL
       */
  get TABLE_NAME () {
    return 'ma_makepayment_requests'
  }

  get PRIMARY_KEY () {
    return 'makepayment_request_id'
  }

  static async insertData (connection, insertObject) {
    this.TABLE_NAME = 'ma_makepayment_requests'
    console.log('insertObject', insertObject)
    const result = await this.insert(connection, insertObject)
    return result
  }

  static async getRequestData (connection, makepayment_request_id) {
    this.TABLE_NAME = 'ma_makepayment_requests'
    let queueData = []
    try {
      const sql = `SELECT * FROM ma_makepayment_requests WHERE makepayment_request_id ='${makepayment_request_id}' limit 1`
      queueData = await this.rawQuery(sql, connection)
      console.log('getRequestData>> ', queueData)
      if (queueData.length > 0) {
        return queueData
      }
    } catch (error) {
      console.log('getRequestDataError>>', error)
    }
    return queueData
  }

  static async getRequestDataWithOrderID (connection, orderid) {
    this.TABLE_NAME = 'ma_makepayment_requests'
    let queueData = []
    try {
      const sql = `SELECT * FROM ma_makepayment_requests WHERE aggregator_order_id ='${orderid}' limit 1`
      queueData = await this.rawQuery(sql, connection)
      console.log('getRequestData>> ', queueData)
      if (queueData.length > 0) {
        return queueData
      }
    } catch (error) {
      console.log('getRequestDataError>>', error)
    }
    return queueData
  }

  static async updateWhereData (connection, { data, id, where }) {
    this.TABLE_NAME = 'ma_makepayment_requests'
    const updateQueue = await this.updateWhere(connection, { data, id: id, where: where })
    return updateQueue
  }
}

module.exports = MakePaymentRequest
