const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const util = require('../../../util/util')
const balance = require('../balance/balanceController')
const transaction = require('../transaction/transactionController')
const sms = require('../../util/sms')
const jwt = require('../../util/token')
const comission = require('../commission/commissionController')
const pointsrate = require('../pointsRate/pointsRateController')
const pointsledger = require('../creditDebit/pointsLedgerController')
const log = require('../../util/log')
const errorMsg = require('../../util/error')

class Otp extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_otp_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_otp_master_id'
  }

  /**
     * Returns a CashLedger by its ID
     */
  //   static async getByID (_, { id }) {
  //     console.log('CashLedger entry with id', id)
  //     return await this.find(id)
  //   }

  static async getUser (mobile, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      // const sql = 'Select profileid from ma_user_master where mobile_id=' + mobile + ' AND user_status = "Y"'
      const sql = `Select profileid,userid,distributer_user_master_id,user_type,state, CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan from ma_user_master where mobile_id=${mobile} AND user_status = "Y"`

      // const sql = 'Select profileid from ma_user_master where mobile_id=' + mobile + ' AND user_status = "Y"'
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getUser', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getVerifyData (aggregator_order_id, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'SELECT * FROM ma_otp_master WHERE aggregator_order_id = "' + aggregator_order_id + '"  AND expiry > CURRENT_TIMESTAMP ORDER BY ma_otp_master_id DESC LIMIT 1'
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getVerifyData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getResentData (mobile, aggregator_order_id, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'SELECT otp,expiry FROM ma_otp_master WHERE aggregator_order_id = "' + aggregator_order_id + '" AND mobile = ' + mobile + ' AND expiry > CURRENT_TIMESTAMP ORDER BY ma_otp_master_id DESC LIMIT 1'
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getResentData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
     * Creates a new  entry
     */
  static async createEntry (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)
      const _result = await this.insert(connection, {
        data: fields
      })
      await mySQLWrapper.commit(connection)
      return _result
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: 'otpController.js', action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: 'Something went wrong.', respcode: 1001 }
    } finally {
      // Releases the connection
    //   if (connection != null) connection.release()
      connection.release()
    }
  }

  static async updateEntry (_, { aggregator_order_id, retry_count, flag }) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)
      const _result = await this.updateWhere(connection, {
        id: aggregator_order_id,
        where: 'aggregator_order_id',
        data: {
          retry_count,
          flag
        }
      })
      await mySQLWrapper.commit(connection)
      if (_result.affectedRows > 0) {
        return { status: 200, message: 'success' }
      } else {
        return { status: 400, message: 'fail' }
      }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: 'otpController.js', action: 'updateEntry', type: 'catcherror', fields: err })
      return { status: 400, message: 'Something went wrong.', respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection._pool._freeConnections.indexOf(connection) === -1) connection.release()
    }
  }

  static async sentOtp (_, fields) {
    // console.log('hers')
    const _user = await this.getUser(fields.mobile)
    console.log(fields)
    const response = {}
    if (_user.length > 0) {
      // const rand = await this.generateOTP(8)
      // const date = await  this.getExpiry('transaction')
      // const transaction_id = `MA${date}${rand}`
      const comission_val = await comission.getCommission('', { ma_user_id: _user[0].profileid, ma_commission_type: '5', amount: fields.amount })
      console.log('comission', comission_val)
      const pointsrate_val = await pointsrate.getGlobalPointsRate()
      console.log('points val' + pointsrate_val.points_value)
      const transaction_data = {
        ma_user_id: _user[0].profileid,
        aggregator_txn_id: fields.aggregator_txn_id,
        aggregator_order_id: fields.aggregator_order_id,
        transaction_id: fields.aggregator_order_id,
        amount: fields.amount,
        commission_amount: comission_val.commissionVal,
        transaction_status: 'I',
        points_factor: pointsrate_val.points_value,
        bank_rrn: fields.aggregator_order_id

      }
      console.log('trans data', transaction_data)
      const trans_result = await transaction.createTransaction('', transaction_data)
      console.log('trans result ', trans_result.status)

      if (trans_result.status == 200) {
        const otp = await this.generateOTP(6)
        const expiry = await this.getExpiry('expiry')
        const _data = await this.createEntry(_, {
          ma_user_id: _user[0].profileid,
          otp: otp,
          mobile: fields.mobile,
          expiry: expiry,
          transaction_id: fields.aggregator_order_id,
          retry_count: 0,
          flag: '1'
        })
        if (_data.affectedRows > 0) {
          // send otp and generate token
          const message = ' Dear Customer,The OTP to perform transaction is: ' + otp + '. Valid till' + expiry + ' (IST). Team Airpay Vyaapaar!'
          await sms.sentSmsAsync(message, fields.mobile)
          const publickey = _user[0].profileid + fields.mobile
          const token = await jwt.signToken(publickey)
          console.log('token:', token)

          response.status = 200
          response.message = 'success'
          response.token = token
          response.transaction = trans_result
        } else {
          response.status = 400
          response.message = 'Someting went wrong'
        }
      } else {
        response.status = 400
        response.message = 'Error in creating transaction'
      }
    } else {
      // console.log('fdfd'_user.length)
      response.status = 400
      response.message = 'User does not exist'
    }
    console.log('resp', response)
    return response
  }

  static async verifyOtp (headers, fields) {
    const response = {}
    const _user = await this.getUser(fields.mobile)
    // console.log( 'HEADERS', headers.authorization)
    // console.log( 'HEADERS CONTENT', headers.content-type)
    if (_user.length > 0) {
      console.log(_user)
      // if (!headers.authorization) {
      //   response.status = '401'
      //   response.message = 'missing authorization header'
      // response.respcode = ''
      // }else{
      // const publickey = _user[0].profileid + fields.mobile
      // const token = headers.authorization
      // console.log('token',token)
      // const payload = await jwt.verifyToken(headers.authorization)
      // console.log(''headers.authorization)
      // console.log(publickey)
      // if (payload.publickey) {
      const _verify = await this.getVerifyData(fields.aggregator_order_id)
      var transaction_data = {}
      console.log(_verify)
      if (_verify.length > 0) {
        if (_verify[0].otp == fields.otp) {
          var data = {
            ma_user_id: _user[0].profileid
          }
          const _balance = await balance.getPointsBalance('', data)
          const comission_val = await comission.getCommission('', { ma_user_id: _user[0].profileid, ma_commission_type: '5', amount: fields.amount })
          console.log('l;ll;l', _balance)

          if (_balance.amount >= (fields.amount + comission_val.commissionVal)) {
            // debit creidt api
            // update txn
            const _creddeb = await pointsledger.sendMoney('', { distributorId: _user[0].profileid, retailerId: '99999', amount: fields.amount, orderid: fields.aggregator_order_id, commissionType: 5 })
            console.log('resp send money', _creddeb)
            transaction_data = {
              aggregator_order_id: fields.aggregator_order_id,
              transaction_status: 'S',
              remarks: 'success'

            }
            response.status = '200'
            response.message = 'success'
            response.respcode = ''

            // success
          } else {
            // update txn
            // insufficient funds
            transaction_data = {
              aggregator_order_id: fields.aggregator_order_id,
              transaction_status: 'F',
              remarks: 'Insufficient balance'

            }
            response.status = '400'
            response.message = 'Insufficient balance'
            response.respcode = ''
          }
          const trans_result = await transaction.updateTransaction('', transaction_data)
          this.updateEntry('', { aggregator_order_id: fields.aggregator_order_id, retry_count: _verify[0].retry_count + 1, flag: '2' })
          response.transaction = trans_result[0]
        } else {
          if (_verify[0].retry_count >= 3) {
            const transaction_data = {
              aggregator_order_id: fields.aggregator_order_id,
              transaction_status: 'F',
              remarks: 'OTP Attempts exceeded'

            }
            const trans_result = await transaction.updateTransaction('', transaction_data)
            console.log('update data', trans_result)
            response.status = '400'
            response.message = 'OTP Attempts exceeded'
            response.respcode = ''
            response.transaction = trans_result[0]
            // update txn to failed
          } else {
            // update retry & status
            this.updateEntry('', { aggregator_order_id: fields.aggregator_order_id, retry_count: _verify[0].retry_count + 1, flag: '3' })
            response.status = '400'
            response.message = 'wrong OTP'
            response.respcode = ''
          }
        }
      } else {
        console.log('this else')
        const transaction_data = {
          aggregator_order_id: fields.aggregator_order_id,
          transaction_status: 'F',
          remarks: 'OTP has expired'

        }
        const trans_result = await transaction.updateTransaction('', transaction_data)
        console.log('update data', trans_result)
        response.status = '400'
        response.message = 'OTP has expired'
        response.respcode = ''
        response.transaction = trans_result[0]
      }
      // }else{
      //         response.status = '401'
      //         response.message = 'invalid token'
      //          response.respcode = ''
      // }
    // }
    } else {
      // console.log('fdfd'_user.length)
      response.status = '400'
      response.message = 'User does not exist'
      response.respcode = ''
    }
    return response
  }

  static async resentOtp (headers, fields) {
    const response = {}
    const _user = await this.getUser(fields.mobile)
    if (_user.length > 0) {
      console.log(_user)
      // if (!headers.authorization) {
      //   response.status = '401'
      //   response.message = 'missing authorization header'
      // }
      // const publickey = _user[0].profileid + fields.mobile
      // const token = req.headers.authorization
      // const payload = jwt.verify(headers.authorization, util.jwtKey)
      // if (payload.publickey) {
      const _resent = await this.getResentData(fields.mobile, fields.aggregator_order_id)
      console.log(_resent)
      if (_resent.length > 0) {
        const message = ' Dear Customer,The OTP to perform transaction is: ' + _resent[0].otp + '. Valid till' + _resent[0].expiry + ' (IST). Team Airpay Vyaapaar!'
        await sms.sentSmsAsync(message, fields.mobile)
        response.status = '200'
        response.message = 'success'
      } else {
        response.status = '400'
        response.message = 'Something went wrong'
      }
      // }else{
      //         response.status = '401'
      //   response.message = 'invalid token'
      // }
    } else {
      // console.log('fdfd'_user.length)
      response.status = '400'
      response.message = 'User does not exist'
    }
    return response
  }

  static async generateOTP (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  static async getExpiry (type) {
    console.log(type)
    const today = new Date()
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    if (type == 'expiry') {
      console.log('inside type')
      today.setTime(today.getTime() + (5 * 60 * 1000))
      const min = today.getMinutes()
      const sec = today.getSeconds()
      const hh = today.getHours()
      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`
    } else {
      return `${dd}${mm}${yyyy}`
    }
  }
}
module.exports = Otp
