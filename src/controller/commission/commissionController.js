/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
class Commission extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_cash_account'
  }

  static get PRIMARY_KEY () {
    return 'ma_cash_account_id'
  }

  static async getCommission (_, { ma_user_id, ma_commission_type, ma_deduction_type, amount, connection = null }) {
    log.logger({ pagename: 'commissionController.js', action: 'getCommission', type: 'request', fields: { ma_user_id, ma_commission_type, ma_deduction_type, amount } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // Check for retailer specific details
      const retailerCommissionDetails = await this.getCommissionValue(_, {
        ma_user_id,
        state_master_id: 0,
        ma_commission_type,
        amount,
        ma_deduction_type,
        connection,
        get_commission_ma_user_id: ma_user_id
      })
      log.logger({ pagename: 'commissionController.js', action: 'getCommission', type: 'retailerCommissionDetails', fields: retailerCommissionDetails })

      if ((retailerCommissionDetails.status === 200 && retailerCommissionDetails.commissionVal == 0) ||
      retailerCommissionDetails.status === 400) {
        const sql = `select state from ma_user_master where profileid=${ma_user_id}`
        const state = await this.rawQuery(sql, connection)
        console.log('State kya aaya bhai', state)
        if (state.length > 0) {
          // State specific
          const stateCommissionDetails = await this.getCommissionValue(_, {
            ma_user_id: 0,
            state_master_id: state[0].state,
            ma_commission_type,
            amount,
            ma_deduction_type,
            connection,
            get_commission_ma_user_id: ma_user_id
          })
          log.logger({ pagename: 'commissionController.js', action: 'getCommission', type: 'stateCommissionDetails', fields: stateCommissionDetails })

          if ((stateCommissionDetails.status === 200 && stateCommissionDetails.commissionVal == 0) ||
          stateCommissionDetails.status === 400
          ) {
            // Default
            const commissionDetails = await this.getCommissionValue(_, {
              ma_user_id: 0,
              state_master_id: 0,
              ma_commission_type,
              amount,
              ma_deduction_type,
              connection,
              get_commission_ma_user_id: ma_user_id
            })
            log.logger({ pagename: 'commissionController.js', action: 'getCommission', type: 'commissionDetails1', fields: commissionDetails })
            console.log('Default Commission Entry')
            return commissionDetails
          }
          console.log('State Commission Entry')
          return stateCommissionDetails
        }
        // Default
        const commissionDetails = await this.getCommissionValue(_, {
          ma_user_id: 0,
          state_master_id: 0,
          ma_commission_type,
          amount,
          ma_deduction_type,
          connection,
          get_commission_ma_user_id: ma_user_id
        })
        log.logger({ pagename: 'commissionController.js', action: 'getCommission', type: 'commissionDetails2', fields: commissionDetails })
        console.log('Default Commission Entry')
        return commissionDetails
      }
      console.log('Retailer Commission Entry')
      return retailerCommissionDetails
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCommission', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getCommissionValue (_, { ma_user_id, ma_commission_type, ma_deduction_type, amount, connection = null, state_master_id = 0, get_commission_ma_user_id }) {
    log.logger({ pagename: 'commissionController.js', action: 'getCommissionValue', type: 'request', fields: { ma_user_id, ma_commission_type, ma_deduction_type, amount, state_master_id, get_commission_ma_user_id } })
    var isSet = false
    var sql
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      // checking incentive_status(ma_commission_type) of ma_transaction_type_master tbls. If YES then status = 200 else status = 400
      const getIncentiveStatus = await this.getIncentiveStatus('_', { ma_commission_type, connection })

      // For TDS value calculation as per ma_tds_merchant_master table it is necessary that the incentive_status should be YES for respsective transaction_type(ma_commission_type) in ma_transaction_type_master Table
      if (getIncentiveStatus.status == 200) {
        const masterTDS = await this.getMasterCommissionvalue('_', { ma_user_id, amount, connection })
        if (masterTDS.status == 200) {
          log.logger({ pagename: 'commissionController.js', action: 'getCommissionValue', type: 'masterTDSApplied[' + ma_user_id + '][' + amount + ' ]', fields: masterTDS })
          return masterTDS
        }
      }

      if (state_master_id > 0) {
        sql = `select ma_commission_master_id,applied_type,ma_fixed_commission_amount,ma_commission_percentage,slab_flag from ma_commission_master where commission_status='Y' and ma_deduction_type=${ma_deduction_type} and ma_commission_type='${ma_commission_type}' and state_master_id=${state_master_id}`
      } else {
        sql = `select ma_commission_master_id,applied_type,ma_fixed_commission_amount,ma_commission_percentage,slab_flag from ma_commission_master where commission_status='Y' and ma_deduction_type=${ma_deduction_type} and ma_commission_type='${ma_commission_type}' and ma_user_id=${ma_user_id}`
      }
      var _commssion = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'commissionController.js', action: 'getCommissionValue', type: 'response', fields: _commssion })

      if (_commssion.length > 0) {
        // Calculate commission based on applied type and amount
        var commissionVal
        var appliedCommission = ''
        if (_commssion[0].slab_flag === 'Y') {
          const sqlSlab = `select * from ma_commission_slabs where ma_commission_id=${_commssion[0].ma_commission_master_id} and min_amount<=${amount} and max_amount>=${amount}`
          const commssionSlab = await this.rawQuery(sqlSlab, connection)
          log.logger({ pagename: 'commissionController.js', action: 'getCommissionValue', type: 'response', fields: commssionSlab })
          if (commssionSlab.length > 0) {
            // Calculate commission based on applied type and amount
            if (commssionSlab[0].applied_type === '3') {
              commissionVal = parseFloat((commssionSlab[0].commission_fixed_amount +
                commssionSlab[0].commission_percentage * amount / 100).toFixed(4))
              appliedCommission = commssionSlab[0].commission_fixed_amount + ',' + commssionSlab[0].commission_percentage + '%'
            } else {
              commissionVal =
                commssionSlab[0].applied_type === '1'
                  ? parseFloat((commssionSlab[0].commission_fixed_amount).toFixed(4))
                  : parseFloat((commssionSlab[0].commission_percentage * amount / 100).toFixed(4))
              appliedCommission = commssionSlab[0].applied_type === '1'
                ? commssionSlab[0].commission_fixed_amount
                : commssionSlab[0].commission_percentage + '%'
            }
          } else {
            commissionVal = 0
          }
        } else {
          if (_commssion[0].applied_type === '3') {
            commissionVal = parseFloat((_commssion[0].ma_fixed_commission_amount +
              _commssion[0].ma_commission_percentage * amount / 100).toFixed(4))
            appliedCommission = _commssion[0].ma_fixed_commission_amount + ',' + _commssion[0].ma_commission_percentage + '%'
          } else {
            commissionVal =
              _commssion[0].applied_type === '1'
                ? parseFloat((_commssion[0].ma_fixed_commission_amount).toFixed(4))
                : parseFloat((_commssion[0].ma_commission_percentage * amount / 100).toFixed(4))
            appliedCommission = _commssion[0].applied_type === '1'
              ? _commssion[0].ma_fixed_commission_amount
              : _commssion[0].ma_commission_percentage + '%'
          }
        }
      } else {
        commissionVal = 0
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], commissionVal, appliedCommission }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCommissionValue', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  // checking incentive_status(ma_commission_type) of ma_transaction_type_master tbls. If YES then status = 200 else 400
  static async getIncentiveStatus (_, { ma_commission_type, connection = null }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveStatus', type: 'request', fields: { ma_commission_type } })
    let isSet = false
    try {
    // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      const sql = `SELECT ma_transaction_type_master_id
      FROM ma_transaction_type_master 
      WHERE transaction_type = ${ma_commission_type} 
      AND incentive_status = 'YES' LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveStatus', type: 'sql', fields: sql })
      const incentiveStatus = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveStatus', type: 'incentiveStatus', fields: incentiveStatus })

      if (incentiveStatus.length > 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
      return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveStatus', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getMasterCommissionvalue (_, { ma_user_id, amount, connection = null }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getMasterCommissionvalue', type: 'request', fields: { ma_user_id, amount } })
    let isSet = false
    try {
    // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      const sql = `SELECT applied_type,tds_value
    FROM ma_tds_merchant_master 
    WHERE tds_status = 'active' 
    AND ma_user_id = ${ma_user_id}`

      const commssionData = await this.rawQuery(sql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'getMasterCommissionvalue', type: 'commssionData', fields: commssionData })

      let commissionVal = 0
      let appliedCommission = ''

      if (commssionData.length > 0) {
        commissionVal = commssionData[0].applied_type == 1 ? parseFloat((commssionData[0].tds_value).toFixed(4)) : parseFloat((commssionData[0].tds_value * amount / 100).toFixed(4))

        appliedCommission = commssionData[0].applied_type == 1
          ? commssionData[0].tds_value
          : commssionData[0].tds_value + '%'
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          commissionType: 'MASTER',
          commissionVal,
          appliedCommission
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMasterCommissionvalue', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }
}
module.exports = Commission
