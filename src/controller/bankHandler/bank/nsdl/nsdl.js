const { BANK_API_URL, TRANSFER_MODES, TIMEOUT_VALS, HIDE_REMARKS, HIDE_REMARKS_REPLACE, KYC_TYPE, NSDL_EKYC_CALLBACK_URL, ENC_PASS, CH<PERSON><PERSON><PERSON>_ID, PARTNER_ID, BANK_API_URL_NEW, BANK_API_ENDPOINTS, PARTNER_REF_URL, PARTNER_CALLBACK_URL, CUST_ONBOARDING_UID_DTLS_FETCH, CUST_ONBOARDING_BIO_DTLS, DETAILS_CONFIRM, REFUND_BANK_API_URL } = require('./config').BANK_CONSTANTS
const { apiXmlRequest } = require('../../../../util/xmlrequest')
const log = require('../../../../util/log')
const { propExist, de } = require('../../bankcommon')
const bankOnBoardDetails = require('./../../bankOnBoardingDetails')
const errorMsg = require('../../../../util/error')
const axios = require('axios')
// const validator = require('../../../../util/validator')

const bankErrorMsg = require('./error')
const nsdlrules = require('./inputRule')
const apiValidator = require('../../apiValidator')
const sms = require('../../../../util/sms')
const otp = require('../../../otp/otpController')
const common_fns = require('../../../../util/common_fns')
const { apiJsonRequest } = require('../../../../util/jsonrequest')
const TransferController = require('../../../transfers/transfersController')
const crypto = require('crypto')

/* remitter name validation changes   */
const AirtelBank = require('../airtel/airtel')

const bankInstance = new AirtelBank.BANK()
class NSDLBank {
  constructor (conn = null) {
    this.conn = conn
    this.sessionData = null
  }

  getSessionDateTime (datestr) {
    const today = new Date(datestr)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }

    const min = today.getMinutes()
    const sec = today.getSeconds()
    const hh = today.getHours()
    return `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`
  }

  async agentLogin (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'request', fields: reqDataObj })
    try {
      /**
       * Get agent login details From DB ma_user_on_boarding_bank_mapping
       */
      const agentLoginData = {}

      let agentDetails = null
      if ('sessionData' in reqDataObj) {
        const tmpSessionData = reqDataObj.sessionData
        agentDetails = tmpSessionData
      } else {
        agentDetails = await bankOnBoardDetails.getSessionDetails(null, { ma_user_id: reqDataObj.ma_user_id, ma_bank_on_boarding_id: reqDataObj.ma_bank_on_boarding_id }, connection)
      }

      if (agentDetails.status === 200 && agentDetails.data.length > 0) {
        const locationidData = agentDetails.data
        const locationidStr = locationidData[0].locationid
        // To do Decypt logins
        const secretLoginArr = locationidStr.split('::')
        agentLoginData.username = secretLoginArr[0] + '1'
        agentLoginData.password = secretLoginArr[1]
        agentLoginData.bcagent = secretLoginArr[2]
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'response', fields: bankErrorMsg.responseCode[10003] })
        return { status: 400, respcode: 10003, message: bankErrorMsg.responseCode[10003] }
      }

      const payLoadJson = {
        username: agentLoginData.username,
        //  password: getSha1(reqDataObj.password),
        password: agentLoginData.password,
        bcagent: agentLoginData.bcagent
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'request', fields: payLoadJson })
      const requestStart = process.hrtime()
      console.time('TIMER_NSDL_AGENT_LOGIN')
      const responseBankAuth = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'bcpartnerloginreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.LOGIN })
      console.timeEnd('TIMER_NSDL_AGENT_LOGIN')
      log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'response', fields: JSON.stringify(responseBankAuth.data) })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_AGENT_LOGIN_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseBankAuth.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseBankAuth)) {
        if (propExist('bcpartnerloginres', responseBankAuth.data) && propExist('status', responseBankAuth.data.bcpartnerloginres)) {
          if (responseBankAuth.data.bcpartnerloginres.status === 1) {
            const { sessionid, timeout } = responseBankAuth.data.bcpartnerloginres

            const loginResp = {
              sessionid: sessionid,
              sessionexpiry: this.getSessionDateTime(timeout),
              ma_user_id: reqDataObj.ma_user_id,
              bcagent: agentLoginData.bcagent,
              ma_bank_on_boarding_id: reqDataObj.ma_bank_on_boarding_id,
              connection: reqDataObj.connection
            }

            // await bankOnBoardDetails.storeAgentSession(null, loginResp)
            log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'response', fields: loginResp })
            return loginResp
          } else {
            return { status: 400, respcode: 10002, message: bankErrorMsg.responseCode[10002] }
          }
        } else {
          if (propExist('errorres', responseBankAuth.data) && propExist('status', responseBankAuth.data.errorres)) {
            return { status: 400, respcode: 10002, message: bankErrorMsg.responseCode[10002] + ' || ' + responseBankAuth.data.errorres.description }
          } else {
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'auth', type: 'error', fields: { error: error.toString() } })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_AGENT_LOGIN_' + reqDataObj.ma_user_id)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'login' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitterSendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id: reqDataObj.aggregator_order_id || null })
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitterReSendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj
      const aggregator_order_id = reqDataObj.aggregator_order_id || null
      // For provider id store in ma_sent_sms_master TBL
      const getProviderIdForSms = await otp.getProviderIdForSms(sendermobilenumber, aggregator_order_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      const getProviderId = getProviderIdForSms.data.provider_id || null

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id, provider_id: getProviderId })

      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitter (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'request', fields: reqDataObj || '' })
    try {
      const payLoadJson = {
        header: {
          sessionid: ''
        },
        bcagent: '',
        sendermobilenumber: reqDataObj.sendermobilenumber,
        sendername: reqDataObj.sendername,
        senderaddress1: reqDataObj.senderaddress1.replace(/[^a-z0-9,]+/gi, ' '),
        senderaddress2: reqDataObj.senderaddress2.replace(/[^a-z0-9,]+/gi, ' '),
        pincode: reqDataObj.pincode,
        cityname: reqDataObj.cityname,
        statename: reqDataObj.statename,
        bcpartnerrefno: reqDataObj.bcpartnerrefno,
        dob: reqDataObj.dob
      }

      /***
       * Merge Custom fields
       */
      // console.log('reqDataObj.customFields', reqDataObj.customFields)
      for (const [key, value] of Object.entries(reqDataObj.customFields)) {
        // log.logger(`${key}: ${value}`)
        if (key in payLoadJson) {
          payLoadJson[key] = value
        }
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      // const validationRes = await this.validateAPIInput('ADD_REMITTER', payLoadJson)
      // if (validationRes.status !== 200) {
      //   return validationRes
      // }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status === 200) {
        payLoadJson.header.sessionid = currentSessionId.data.sessionid
        payLoadJson.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/
      console.time('TIMER_NSDL_ADD_REMITTER')
      const requestStart = process.hrtime()
      const responseRemitter = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'senderregistrationrmreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.REMITTER })
      console.timeEnd('TIMER_NSDL_ADD_REMITTER')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseRemitter.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseRemitter)) {
        if (propExist('senderregistrationrmres', responseRemitter.data) && propExist('status', responseRemitter.data.senderregistrationrmres)) {
          if (responseRemitter.data.senderregistrationrmres.status === 1) {
            const { bcagent, senderid } = responseRemitter.data.senderregistrationrmres

            const addSenderResp = {
              senderid: senderid,
              bcagent: bcagent,
              remitter_name: reqDataObj.sendername,
              status: 200,
              message: bankErrorMsg.responseCode[10000],
              respcode: 10000
            }
            return addSenderResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: responseRemitter.data })
            return { status: 400, respcode: 1080, message: errorMsg.responseCode[1080], error: { message: 'Session not created!' } }
          }
        } else {
          if (propExist('errorres', responseRemitter.data) && propExist('status', responseRemitter.data.errorres) && propExist('description', responseRemitter.data.errorres)) {
            // To do
            // Call Remiiter Details and get Remitter id here
            if (responseRemitter.data.errorres.description.indexOf('MOBILE NUMBER IS ALREADY REGISTERED') > -1 || responseRemitter.data.errorres.status === 102) {
              const remitterDetails = await this.syncRemitterId(reqDataObj, sessionRQ, connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'remitterDetails', fields: remitterDetails })
              if (('senderid' in remitterDetails)) {
                return remitterDetails
              }
              const allReadyExitsResp = { status: 400, respcode: 10006, message: bankErrorMsg.responseCode[10006] + ':::' + responseRemitter.data.errorres.description, error: responseRemitter.data }
              return allReadyExitsResp
            }
            log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: responseRemitter.data })
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + ':::' + responseRemitter.data.errorres.description }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: responseRemitter.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error.toString() } })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_ADD_REMITTER_' + reqDataObj.sendermobilenumber)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'addremitter' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: reqDataObj || '' })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        senderid: reqDataObj.senderid,
        receivername: reqDataObj.receivername,
        receivermobilenumber: reqDataObj.receivermobilenumber,
        receiveremailid: reqDataObj.receiveremailid,
        bank: reqDataObj.bank,
        state: reqDataObj.state,
        city: reqDataObj.city,
        branch: reqDataObj.branch,
        address: reqDataObj.address,
        ifscode: reqDataObj.ifscode,
        accountnumber: reqDataObj.accountnumber,
        flag: 2, // To do Only IMPS supported NEFT Required
        bcpartnerrefno: reqDataObj.bcpartnerrefno
      }

      console.time('TIMER_NSDL_ADD_BENEFICIARY')
      const requestStart = process.hrtime()
      const responseBeneficiary = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'receiverregistrationreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.BENE })
      console.timeEnd('TIMER_NSDL_ADD_BENEFICIARY')
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: JSON.stringify(responseBeneficiary.data) })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_ADD_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseBeneficiary.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseBeneficiary)) {
        if (propExist('receiverregistrationres', responseBeneficiary.data) && propExist('status', responseBeneficiary.data.receiverregistrationres)) {
          if (responseBeneficiary.data.receiverregistrationres.status === 1) {
            const { bcagent, senderid, beneficiaryid } = responseBeneficiary.data.receiverregistrationres

            const addBeneficaryResp = {
              senderid: senderid,
              bcagent: bcagent,
              beneficiaryid: beneficiaryid,
              maBeneficiaryId: reqDataObj.beneficiaryId,
              status: 200,
              respcode: 10000,
              message: errorMsg.responseCode[10000]
            }
            // bankOnBoardDetails.storeBeneficiaryId(null, addBeneficaryResp).then((data) => console.log('storeBeneficiaryId>>', data)).catch((error) => console.log('storeBeneficiaryIdError>>', error))
            return addBeneficaryResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: responseBeneficiary.data })
            return { status: 400, respcode: 1084, message: errorMsg.responseCode[1084], error: { message: 'Session not created!' } }
          }
        } else {
          if (propExist('errorres', responseBeneficiary.data) && propExist('status', responseBeneficiary.data.errorres) && propExist('description', responseBeneficiary.data.errorres)) {
            // To do
            // Call Add Beneficiary Details and get beneficiaryid id here

            if (responseBeneficiary.data.errorres.description.indexOf('ACCOUNT NUMBER IS ALREADY REGISTERED') > -1 || responseBeneficiary.data.errorres.description.indexOf('RECEIVERNAME ALREADY REGISTERED') > -1) {
              const beneficaryDetails = await this.syncBeneficaryId(reqDataObj, sessionRQ, connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: beneficaryDetails })
              let bankBranchCtrl = null
              if (beneficaryDetails && ('beneficiarydetail' in beneficaryDetails) && ('beneficiary' in beneficaryDetails.beneficiarydetail)) {
                const isObjectArray = Object.prototype.toString.call(beneficaryDetails.beneficiarydetail.beneficiary) === '[object Array]'
                let bankSideBeneficiaris = []
                if (!isObjectArray && ('beneficiaryid' in beneficaryDetails.beneficiarydetail.beneficiary)) {
                  bankSideBeneficiaris.push(beneficaryDetails.beneficiarydetail.beneficiary)
                } else {
                  bankSideBeneficiaris = beneficaryDetails.beneficiarydetail.beneficiary
                }
                for (let index = 0; index < bankSideBeneficiaris.length; index++) {
                  const currentBene = bankSideBeneficiaris[index]
                  if (('beneficiaryid' in currentBene) && parseInt(currentBene.beneficiaryid) > 0 && currentBene.beneficiarystatus === 1) {
                    if (String(currentBene.accountnumber).toLowerCase() == String(reqDataObj.accountnumber).toLowerCase() && currentBene.ifscode.toLowerCase() == reqDataObj.ifscode.toLowerCase()) {
                      const addBeneficaryResp = {
                        senderid: beneficaryDetails.senderid,
                        bcagent: sessionData.bcagent,
                        beneficiaryid: currentBene.beneficiaryid,
                        maBeneficiaryId: reqDataObj.beneficiaryId,
                        allReadyExist: true,
                        status: 200,
                        respcode: 10000,
                        message: errorMsg.responseCode[10000]
                      }

                      if (bankBranchCtrl === null) {
                        bankBranchCtrl = require('../../../bankDetails/bankBranchController')
                      }

                      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'addBeneficaryResp', fields: addBeneficaryResp })
                      const ifscRes = await bankBranchCtrl.getBranchByIfscForBank(null, { ifsc: currentBene.ifscode })
                      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'ifscRes', fields: ifscRes })
                      if (ifscRes.status === 200) {
                        // bankOnBoardDetails.storeBeneficiaryId(null, addBeneficaryResp).then((data) => console.log('storeBeneficiaryId>>', data)).catch((error) => console.log('storeBeneficiaryIdError>>', error))
                        return addBeneficaryResp
                      } else {
                        return { status: 400, respcode: 10026, message: bankErrorMsg.responseCode[10026] + currentBene.ifscode, error: responseBeneficiary.data }
                      }
                    }
                  }
                }
              }
              const allReadyExitsResp = { status: 400, respcode: 10007, message: bankErrorMsg.responseCode[10007] + ':::' + responseBeneficiary.data.errorres.description, error: responseBeneficiary.data }
              return allReadyExitsResp
            }

            let message = ''
            if (propExist('description', responseBeneficiary.data.errorres)) {
              message = responseBeneficiary.data.errorres.description
            }
            log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: responseBeneficiary.data })
            // reqDataObj.bcpartnerrefno
            return { status: 400, respcode: 10007, message: bankErrorMsg.responseCode[10007] + '::' + message, error: responseBeneficiary.data }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: responseBeneficiary.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004], error: { message: 'Response Data Error From API!' } }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      //  console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: { error: error.toString() } })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_ADD_BENEFICIARY' + reqDataObj.accountnumber)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'addbeneficiary' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiarySendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id: reqDataObj.aggregator_order_id || null })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiaryReSendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj
      const aggregator_order_id = reqDataObj.aggregator_order_id || null
      // For provider id store in ma_sent_sms_master TBL
      const getProviderIdForSms = await otp.getProviderIdForSms(sendermobilenumber, aggregator_order_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      const getProviderId = getProviderIdForSms.data.provider_id || null

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id, provider_id: getProviderId })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: reqDataObj || '' })
    try {
      const payLoadJson = {
        header: {
          sessionid: ''
        },
        senderid: reqDataObj.senderid,
        receiverid: reqDataObj.receiverid
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const validationRes = await this.validateAPIInput('DELETE_BENEFICIARY', payLoadJson)
      if (validationRes.status !== 200) {
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status === 200) {
        payLoadJson.header.sessionid = currentSessionId.data.sessionid
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/
      console.time('TIMER_NSDL_DELETE_BENEFICIARY')
      const requestStart = process.hrtime()
      const responseDelBeneficiary = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'receiverdeletereq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.DBENE })
      console.timeEnd('TIMER_NSDL_DELETE_BENEFICIARY')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_DELETE_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseDelBeneficiary.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (e) {
        console.log(e)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'response', fields: JSON.stringify(responseDelBeneficiary.data) })

      if (propExist('data', responseDelBeneficiary)) {
        if (propExist('receiverdeleteres', responseDelBeneficiary.data) && propExist('status', responseDelBeneficiary.data.receiverdeleteres)) {
          if (responseDelBeneficiary.data.receiverdeleteres.status === 1) {
            const { receiverid } = responseDelBeneficiary.data.receiverdeleteres

            const delBeneficaryResp = {
              receiverid: receiverid,
              maBeneficiaryId: reqDataObj.beneficiaryId,
              status: 200,
              message: bankErrorMsg.responseCode[10000],
              respcode: 10000
            }
            // await bankOnBoardDetails.deleteBeneficiaryId(null, delBeneficaryResp)
            return delBeneficaryResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'response', fields: responseDelBeneficiary.data })
            return { status: 400, respcode: 10028, message: errorMsg.responseCode[10028] }
          }
        } else {
          if (propExist('errorres', responseDelBeneficiary.data) && propExist('status', responseDelBeneficiary.data.errorres) && propExist('description', responseDelBeneficiary.data.errorres)) {
            // To do
            // Call Add Beneficiary Details and get beneficiaryid id here
            let message = ''
            if (propExist('description', responseDelBeneficiary.data.errorres)) {
              message = responseDelBeneficiary.data.errorres.description
            }
            log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'response', fields: responseDelBeneficiary.data })
            // reqDataObj.bcpartnerrefno
            return { status: 400, respcode: 10028, message: bankErrorMsg.responseCode[10028] + '::' + message }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'response', fields: responseDelBeneficiary.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'error', fields: { error: error.message } })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_DELETE_BENEFICIARY' + reqDataObj.receiverid)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'addbeneficiary' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] + '::' + error.message }
    }
  }

  async initTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'request', fields: reqDataObj || '' })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/
      const requestNumber = reqDataObj.request_number || 0
      const mobileNumber = reqDataObj.request_data[requestNumber].mobile_number || 0
      reqDataObj.userid = reqDataObj.request_data[requestNumber].userid || 0
      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        senderid: reqDataObj.senderid,
        receiverid: reqDataObj.receiverid,
        amount: reqDataObj.amount,
        remarks: reqDataObj.remarks,
        bcpartnerrefno: reqDataObj.otp_ref_id,
        flag: TRANSFER_MODES[reqDataObj.flag], // Describes the mode of payment. 0-neft 2-imps
        otp: reqDataObj.otp
      }

      let TransferResp = {}
      const resultTransactionArr = []

      console.time('TIMER_NSDL_INIT_TRANSFER')
      const requestStart = process.hrtime()
      const responseTransaction = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'transactionreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.TSINGLE })
      console.timeEnd('TIMER_NSDL_INIT_TRANSFER')
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: responseTransaction.data })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_INIT_TRANSFER_DATA_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseTransaction.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      try {
        console.log('NSDL_INIT_TRANSFER_DATA_[' + reqDataObj.amount + ']_[' + JSON.stringify({ transactionreq: payLoadJson }) + ']_[' + JSON.stringify(responseTransaction.data) + ']')
      } catch (e) {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'errorlogdata', fields: e })
      }
      /**
       * SAVE THE API DETAILS TO DATABASE
       */
      reqDataObj.bank_name = 'NSDL'
      reqDataObj.mobile_number = reqDataObj.beneMobNo == '0' ? mobileNumber : reqDataObj.beneMobNo || reqDataObj.MerchantMobileNo || 0
      reqDataObj.userid = reqDataObj.userid || 0
      reqDataObj.sessionRQ = sessionRQ
      reqDataObj.request_type = 'INIT_TRANSFER_TRANSACTION'
      reqDataObj.request = JSON.stringify({ transactionreq: payLoadJson })
      reqDataObj.response = JSON.stringify(responseTransaction.data)
      reqDataObj.url = BANK_API_URL
      reqDataObj.api_status = responseTransaction.status || '00'
      const saveLogsResp = await TransferController.saveApiLogDmt(reqDataObj, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'INIT TRANSACTION', type: 'saveLogsResp', fields: saveLogsResp })

      if (propExist('data', responseTransaction)) {
        if (propExist('transactionres', responseTransaction.data) && propExist('status', responseTransaction.data.transactionres)) {
          if (responseTransaction.data.transactionres.status === 1) {
            const { bcpartnerrefno, amount, servicecharge, gst, grossamount, bankrefno, nsdltransactionid, remarks, rrn, benename } = responseTransaction.data.transactionres

            TransferResp = {
              bcpartnerrefno: bcpartnerrefno,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gst: gst,
              bank_gross_amount: grossamount,
              bankrefno: bankrefno,
              bankTransactionid: nsdltransactionid,
              transactionId: reqDataObj.ma_transfers_id,
              status: 1,
              message: bankErrorMsg.responseCode[10000],
              rrn: rrn,
              benename: benename
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }

            if (de(remarks)) {
              TransferResp.message = remarks
              if (HIDE_REMARKS.indexOf(remarks) > -1) {
                const replaceWIthIndex = HIDE_REMARKS.indexOf(remarks)
                TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
              }
            }
            // await bankOnBoardDetails.storeTransferDetails(null, TransferResp, prevConn)
            // return TransferResp
          } else if (responseTransaction.data.transactionres.status === 0) {
            const { bcpartnerrefno, amount, servicecharge, gst, grossamount, bankrefno, nsdltransactionid, rrn, benename } = responseTransaction.data.transactionres
            TransferResp = {
              request_number: bcpartnerrefno,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gst: gst,
              bank_gross_amount: grossamount,
              bankrefno: bankrefno,
              bankTransactionid: nsdltransactionid,
              transactionId: reqDataObj.ma_transfers_id,
              status: 0,
              message: bankErrorMsg.responseCode[10012],
              rrn: rrn,
              benename: benename
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }

            if (propExist('remarks', responseTransaction.data.transactionres) && (responseTransaction.data.transactionres.remarks !== null || responseTransaction.data.transactionres.remarks !== undefined) && responseTransaction.data.transactionres.remarks != '') {
              TransferResp.message += responseTransaction.data.transactionres.remarks
              if (HIDE_REMARKS.indexOf(responseTransaction.data.transactionres.remarks) > -1) {
                const replaceWIthIndex = HIDE_REMARKS.indexOf(responseTransaction.data.transactionres.remarks)
                TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
              }
            }

            // return TransferResp
          } else {
            if (responseTransaction.data.transactionres.status === -1) {
              TransferResp = {
                status: -1,
                message: bankErrorMsg.responseCode[10013]
              }

              if (propExist('remarks', responseTransaction.data.transactionres) && (responseTransaction.data.transactionres.remarks !== null || responseTransaction.data.transactionres.remarks !== undefined) && responseTransaction.data.transactionres.remarks != '') {
                TransferResp.message += '~' + responseTransaction.data.transactionres.remarks
                if (HIDE_REMARKS.indexOf(responseTransaction.data.transactionres.remarks) > -1) {
                  const replaceWIthIndex = HIDE_REMARKS.indexOf(responseTransaction.data.transactionres.remarks)
                  TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
                }
              }
              // await bankOnBoardDetails.storeTransferDetails(null, TransferResp, prevConn)
              // return TransferResp
            } else {
              // Unknown Status Received here
              TransferResp = {
                status: -1,
                message: 'Unknow Status'
              }

              if (propExist('remarks', responseTransaction.data.transactionres) && (responseTransaction.data.transactionres.remarks !== null || responseTransaction.data.transactionres.remarks !== undefined) && responseTransaction.data.transactionres.remarks != '') {
                TransferResp.message += '~' + responseTransaction.data.transactionres.remarks
                if (HIDE_REMARKS.indexOf(responseTransaction.data.transactionres.remarks) > -1) {
                  const replaceWIthIndex = HIDE_REMARKS.indexOf(responseTransaction.data.transactionres.remarks)
                  TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
                }
              }
            }

            // log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: responseTransaction.data })
            // return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Session not created!' } }
          }

          TransferResp.request_number = reqDataObj.request_number
          resultTransactionArr.push(TransferResp)
          log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })
          return {
            status: 200,
            respcode: 1000,
            message: errorMsg.responseCode[1000],
            bulkData: {
              finalStatus: 1,
              transferBulkData: resultTransactionArr
            }
          }
        } else {
          if (propExist('errorres', responseTransaction.data) && propExist('status', responseTransaction.data.errorres) && propExist('status', responseTransaction.data.errorres)) {
            // To do
            // Call If Transaction Timeout
            // Failed The Transaction On Below Scenario
            if (responseTransaction.data.errorres.status == '2024' || responseTransaction.data.errorres.description == 'Invalid OTP' || responseTransaction.data.errorres.status == 0) {
              return {
                status: 400,
                transaction_status: 0,
                respcode: 10001,
                message: responseTransaction.data.errorres.description || 'Failed'
              }
            }
            const TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10013]
            }

            if (propExist('description', responseTransaction.data.errorres)) {
              TransferResp.message = responseTransaction.data.errorres.description
              if (de(responseTransaction.data.errorres) && HIDE_REMARKS.indexOf(responseTransaction.data.errorres) > -1) {
                const replaceWIthIndex = HIDE_REMARKS.indexOf(responseTransaction.data.errorres)
                TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
              }
            }

            if (responseTransaction.data.errorres.status === -1) {
              TransferResp.status = -1
              // return TransferResp
            } else if (responseTransaction.data.errorres.status === 0) {
              TransferResp.status = -1
              // return TransferResp
            } else if (responseTransaction.data.errorres.status === 91) {
              TransferResp.status = -1
              // return TransferResp
            } else {
              // return TransferResp
            }

            TransferResp.request_number = reqDataObj.request_number

            resultTransactionArr.push(TransferResp)

            return {
              status: 200,
              respcode: 10012,
              message: errorMsg.responseCode[10012],
              bulkData: {
                finalStatus: 0,
                transferBulkData: resultTransactionArr
              }
            }
            // log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: responseTransaction.data })
            // reqDataObj.bcpartnerrefno
            // return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: responseTransaction.data }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: responseTransaction.data })
            return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error From API!' } }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error!' } }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'error', fields: { error: error.toString() } })

      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_INIT_TRANSFER' + reqDataObj.remarks)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'Transfer' }
      }
      return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085] }
    }
  }

  async bulkTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'request', fields: reqDataObj || '' })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        senderid: reqDataObj.senderid,
        receiverid: reqDataObj.receiverid,
        amount: reqDataObj.amount,
        remarks: reqDataObj.remarks,
        bcpartnerrefno: reqDataObj.request_number,
        flag: TRANSFER_MODES[reqDataObj.flag] // Describes the mode of payment. 0-neft 2-imps
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'payload', fields: payLoadJson })

      console.time('TIMER_NSDL_BULK_TRANSFER')
      const requestStart = process.hrtime()
      const responseBulkTransaction = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'bundletransactionreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.TBULK })
      console.timeEnd('TIMER_NSDL_BULK_TRANSFER')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_BULK_TRANSFER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseBulkTransaction.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      try {
        console.log('NSDL_BULK_TRANSFER_DATA_[' + reqDataObj.amount + ']_[' + JSON.stringify({ bundletransactionreq: payLoadJson }) + ']_[' + JSON.stringify(responseBulkTransaction.data) + ']')
      } catch (e) {
        log.logger({ msg: 'consoleerror >>', e })
      }

      if (propExist('data', responseBulkTransaction)) {
        if (propExist('bundletransactionres', responseBulkTransaction.data) && propExist('transaction', responseBulkTransaction.data.bundletransactionres)) {
          const transactionList = responseBulkTransaction.data.bundletransactionres.transaction
          const resultTransactionArr = []
          if (transactionList.length > 0) {
            for (let index = 0; index < transactionList.length; index++) {
              const currentTransaction = transactionList[index]
              const { bcpartnerrefno, amount, servicecharge, gst, grossamount, bankrefno, nsdltransactionid, remarks, rrn, benename } = currentTransaction

              const TransferResp = {
                request_number: bcpartnerrefno,
                amount: amount,
                bank_service_charges: servicecharge,
                bank_gst: gst,
                bank_gross_amount: grossamount,
                bankrefno: bankrefno,
                bankTransactionid: nsdltransactionid,
                transactionId: reqDataObj.ma_transfers_id,
                status: -1,
                message: bankErrorMsg.responseCode[10013],
                rrn: rrn,
                benename: benename
              }

              if (!de(rrn)) {
                TransferResp.rrn = ''
              }

              if (!de(benename)) {
                TransferResp.benename = ''
              }

              if (currentTransaction.status === -1) {
                TransferResp.status = -1
                TransferResp.message = bankErrorMsg.responseCode[10013]
              } else if (currentTransaction.status === 1) {
                TransferResp.status = 1
                TransferResp.message = bankErrorMsg.responseCode[10000]
              } else if (currentTransaction.status === 0) {
                TransferResp.status = 0
                TransferResp.message = bankErrorMsg.responseCode[10012]
              } else if (currentTransaction.status === 91) {
                TransferResp.status = -1
              } else {
                TransferResp.status = -1
              }

              if (de(remarks)) {
                TransferResp.message = remarks
                if (HIDE_REMARKS.indexOf(remarks) > -1) {
                  const replaceWIthIndex = HIDE_REMARKS.indexOf(remarks)
                  TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
                }
              }
              resultTransactionArr.push(TransferResp)
            }
            return {
              status: 200,
              respcode: 1000,
              message: errorMsg.responseCode[1000],
              bulkData: {
                finalStatus: 1,
                transferBulkData: resultTransactionArr
              }
            }
          } else {
            return { status: 400, respcode: 10030, message: errorMsg.responseCode[10030] }
          }
        } else {
          if (propExist('errorres', responseBulkTransaction.data) && propExist('status', responseBulkTransaction.data.errorres) && propExist('status', responseBulkTransaction.data.errorres)) {
            const TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10012]
            }

            if (propExist('description', responseBulkTransaction.data.errorres)) {
              TransferResp.message = responseBulkTransaction.data.errorres.description
              if (de(responseBulkTransaction.data.errorres.description) && HIDE_REMARKS.indexOf(responseBulkTransaction.data.errorres.description) > -1) {
                const replaceWIthIndex = HIDE_REMARKS.indexOf(responseBulkTransaction.data.errorres.description)
                TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
              }
            }

            if (responseBulkTransaction.data.errorres.status === -1) {
              TransferResp.status = -1
            } else if (responseBulkTransaction.data.errorres.status === 0) {
              TransferResp.status = -1
            } else if (responseBulkTransaction.data.errorres.status === 91) {
              TransferResp.status = -1
            }

            const resultTransactionArr = []
            const requestNumberArray = reqDataObj.request_number.split(',')
            for (let index = 0; index < requestNumberArray.length; index++) {
              const currenRequestNo = requestNumberArray[index]
              const tempTransferData = { ...TransferResp, request_number: currenRequestNo }
              resultTransactionArr.push(tempTransferData)
            }
            return {
              status: 200,
              respcode: 10012,
              message: errorMsg.responseCode[10012],
              bulkData: {
                finalStatus: 0,
                transferBulkData: resultTransactionArr
              }
            }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'response', fields: responseBulkTransaction.data })
            return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error From API!' } }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error!' } }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'error', fields: JSON.stringify(error.message) })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_BULK_TRANSFER' + reqDataObj.remarks)
        return { status: 400, respcode: 10031, message: bankErrorMsg.responseCode[10031] + 'BulkTransfer' }
      }
      return { status: 400, message: 'Bulk Transaction to NSDL Bank Failed!', error: error.message }
    }
  }

  async doubleVerify (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: reqDataObj })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        bcpartnerrefno: reqDataObj.bcpartnerrefno
      }

      console.time('TIMER_NSDL_DOUBLE_VERIFICATION')
      const requestStart = process.hrtime()
      const responseDoubleVerify = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'transactionrequeryreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.DVERIFY })
      console.timeEnd('TIMER_NSDL_DOUBLE_VERIFICATION')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_DOUBLE_VERIFICATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseDoubleVerify.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseDoubleVerify)) {
        if (propExist('transactionrequeryres', responseDoubleVerify.data) && propExist('status', responseDoubleVerify.data.transactionrequeryres)) {
          if (responseDoubleVerify.data.transactionrequeryres.status === 1) {
            const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responseDoubleVerify.data.transactionrequeryres

            const TransferResp = {
              bcpartnerrefno: bcpartnerrefno,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gst: gst,
              bank_gross_amount: grossamount,
              bankrefno: bankrefno,
              benename: benename,
              bankTransactionid: nsdltransactionid,
              transactionId: reqDataObj.ma_transfers_id,
              status: -1,
              message: 'Pending',
              refund: false,
              rrn: rrn,
              remarks: remarks || ''
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }

            return TransferResp
          } else {
            const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responseDoubleVerify.data.transactionrequeryres
            const TransferResp = {
              bcpartnerrefno: bcpartnerrefno,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gst: gst,
              bank_gross_amount: grossamount,
              bankrefno: bankrefno,
              benename: benename,
              bankTransactionid: nsdltransactionid,
              transactionId: reqDataObj.ma_transfers_id,
              status: -1,
              message: bankErrorMsg.responseCode[10025],
              refund: false,
              autorefundcredit: false,
              rrn: rrn,
              remarks: remarks || ''
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }

            // 0 - Pending -1 -Unknown
            if (responseDoubleVerify.data.transactionrequeryres.status === -1 || responseDoubleVerify.data.transactionrequeryres.status === 0) {
              TransferResp.status = -1
              TransferResp.message = TransferResp.remarks + '~Pending or Unknown'
              return TransferResp
            } else if (responseDoubleVerify.data.transactionrequeryres.status === 6) {
              TransferResp.status = 0
              TransferResp.message = TransferResp.remarks + '~Failure'
              TransferResp.autorefundcredit = true
              return TransferResp
            } else if (responseDoubleVerify.data.transactionrequeryres.status === 4) {
              TransferResp.status = 0
              TransferResp.message = TransferResp.remarks + '~Refund Processed'
              TransferResp.refunded = true
              return TransferResp
            } else if (responseDoubleVerify.data.transactionrequeryres.status === 3) {
              TransferResp.status = 0
              TransferResp.message = TransferResp.remarks + '~Please initiate refund'
              TransferResp.refund = true
              return TransferResp
            } else if (responseDoubleVerify.data.transactionrequeryres.status === 2) {
              TransferResp.status = 1
              TransferResp.message = TransferResp.remarks + '~Credited'
              return TransferResp
            } else {
              return TransferResp
            }

            //  log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: responseTransaction.data })
            // return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Session not created!' } }
          }
        } else {
          if (propExist('errorres', responseDoubleVerify.data) && propExist('status', responseDoubleVerify.data.errorres)) {
            const TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10025]
            }

            if (propExist('description', responseDoubleVerify.data.errorres)) {
              TransferResp.message = responseDoubleVerify.data.errorres.description
              TransferResp.remarks = responseDoubleVerify.data.errorres.description
            }

            return TransferResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: {} })
            return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error From API!' } }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error!' } }
      }
    } catch (error) {
      log.logger({ msg: 'doubleVerify', error })
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'error', fields: JSON.stringify(error) })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_DOUBLE_VERIFICATION' + reqDataObj.bcpartnerrefno)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'doubleverify' }
      }
      return { status: 400, message: 'Double verification to NSDL Bank Failed!', error: error.message }
    }
  }

  async refund (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'request', fields: reqDataObj || '' })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        nsdlbanktransactionid: reqDataObj.utr_number
      }
      console.time('TIMER_NSDL_REFUND')
      const requestStart = process.hrtime()
      const responseRefundOtp = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'refundotpreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.REFUND })
      console.timeEnd('TIMER_NSDL_REFUND')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_REFUND_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseRefundOtp.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseRefundOtp)) {
        if (propExist('refundotpres', responseRefundOtp.data) && propExist('status', responseRefundOtp.data.refundotpres)) {
          if (responseRefundOtp.data.refundotpres.status === 1) {
            const refundResp = {
              status: 1
            }
            return refundResp
          } else {
            // const refundResp = {
            //   status: 0
            // }
            return { status: 400, respcode: 10008, message: bankErrorMsg.responseCode[10008] }
          }
        } else {
          if (propExist('errorres', responseRefundOtp.data) && propExist('status', responseRefundOtp.data.errorres) && propExist('status', responseRefundOtp.data.errorres)) {
            const refundResp = {
              status: 0,
              message: 'otp failed'
            }

            if (propExist('description', responseRefundOtp.data.errorres)) {
              refundResp.message = responseRefundOtp.data.errorres.description
            }
            return { status: 400, respcode: 10008, message: bankErrorMsg.responseCode[10008] + refundResp.message }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'response', fields: responseRefundOtp.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'error', fields: error })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_REFUND' + reqDataObj.utr_number)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'refund' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async verifyOTPRefund (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'request', fields: reqDataObj || '' })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        nsdlbanktransactionid: reqDataObj.utr_number,
        bcpartnerrefno: reqDataObj.bcpartnerrefno,
        verficationcode: reqDataObj.verficationcode
      }

      console.time('TIMER_NSDL_VERIFY_REFUND')
      const requestStart = process.hrtime()
      const responseVerifyRefundOtp = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'refundreq', payLoad: payLoadJson })
      console.timeEnd('TIMER_NSDL_VERIFY_REFUND')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_VERIFY_REFUND_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseVerifyRefundOtp.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseVerifyRefundOtp)) {
        if (propExist('refundres', responseVerifyRefundOtp.data) && propExist('status', responseVerifyRefundOtp.data.refundres)) {
          if (responseVerifyRefundOtp.data.refundres.status === 1) {
            const refundResp = {
              status: 1
            }
            return refundResp
          } else {
            const refundResp = {
              status: 0,
              message: 'Verify otp failed From bank'
            }
            if (propExist('description', responseVerifyRefundOtp.data.errorres)) {
              refundResp.message = responseVerifyRefundOtp.data.errorres.description
            }
            return { status: 400, respcode: 10009, message: bankErrorMsg.responseCode[10009] + refundResp.message }
          }
        } else {
          if (propExist('errorres', responseVerifyRefundOtp.data) && propExist('status', responseVerifyRefundOtp.data.errorres) && propExist('status', responseVerifyRefundOtp.data.errorres)) {
            const refundResp = {
              status: 0,
              message: 'Verify otp failed From bank'
            }
            if (propExist('description', responseVerifyRefundOtp.data.errorres)) {
              refundResp.message = responseVerifyRefundOtp.data.errorres.description
            }
            return { status: 400, respcode: 10008, message: bankErrorMsg.responseCode[10008] + refundResp.message }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'response', fields: responseVerifyRefundOtp.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'error', fields: JSON.stringify(error) })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_VERIFY_REFUND' + reqDataObj.bcpartnerrefno)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'verifyrefund' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  /**
   * NEW CHANGES : NSDL REFUND WITHOUT OTP
   * @param {Object} reqDataObj
   * @param {String} sessionRQ
   * @param {Promise<mySQLWrapper.getConnectionFromPool()>} connection
   * @returns
   */
  async refundVerify (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refundVerify', type: 'response', fields: reqDataObj })
    try {
      /* Get/Set Agent session */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'response', fields: currentSessionId })
        return currentSessionId
      }

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        nsdlbanktransactionid: reqDataObj.utr_number,
        bcpartnerrefno: reqDataObj.bcpartnerrefno
      }

      console.time('TIMER_NSDL_REFUND_VERIFY')
      const requestStart = process.hrtime()
      const responseRefundVerify = await apiXmlRequest.post(REFUND_BANK_API_URL, { wrapIn: 'refundwootpreq', payLoad: payLoadJson })
      console.timeEnd('TIMER_NSDL_REFUND_VERIFY')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_VERIFY_REFUND_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseRefundVerify.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (!propExist('data', responseRefundVerify)) {
        log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      if (propExist('errorres', responseRefundVerify.data)) {
        const refundResp = {
          message: bankErrorMsg.responseCode[10001]
        }
        // 146 - REFUND AMOUNT MISMATCH/REFUND ALREADY PROCESSED
        if (responseRefundVerify.data.errorres.status == 146) {
          return { status: 1 }
        }
        if (propExist('description', responseRefundVerify.data.errorres)) {
          refundResp.message = responseRefundVerify.data.errorres.description
        }
        return { status: 400, respcode: 10008, message: bankErrorMsg.responseCode[10001] + refundResp.message }
      }
      if (propExist('refundwootpres', responseRefundVerify.data) && propExist('status', responseRefundVerify.data.refundwootpres)) {
        if (responseRefundVerify.data.refundwootpres.status == 1) {
          return { status: 1 }
        }

        if (responseRefundVerify.data.refundwootpres.status != 1) {
          return { status: 400, respcode: 10034, message: bankErrorMsg.responseCode[10034] }
        }
      }

      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundVerify', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getRemitterDetails (reqDataObj, sessionRQ, connection) {
    // log.logger(require('path').basename(__filename), 'NSDLBANK', 'getRemitterDetails', arguments)
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterDetails', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        mobilenumber: reqDataObj.sendermobilenumber
      }

      const requestStart = process.hrtime()
      console.time('TIMER_NSDL_AGENT_DETAIL')
      const responseRemitterDetails = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'senderdetailsreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.AGENTDETAIL })
      console.timeEnd('TIMER_NSDL_AGENT_DETAIL')
      // log.logger(responseRemitterDetails.data)

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_AGENT_DETAIL_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responseRemitterDetails.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responseRemitterDetails)) {
        if (propExist('senderdetailsres', responseRemitterDetails.data) && propExist('status', responseRemitterDetails.data.senderdetailsres)) {
          if (responseRemitterDetails.data.senderdetailsres.status === 1) {
            const { bcagent, senderdetail, beneficiarydetail } = responseRemitterDetails.data.senderdetailsres

            const addSenderResp = {
              senderid: senderdetail.senderid,
              remitter_name: senderdetail.sendername,
              bcagent: bcagent,
              senderdetail: senderdetail,
              beneficiarydetail: beneficiarydetail || {},
              remitterDetail: true,
              consumedLimit: senderdetail.TransactionDone,
              remainingLimit: senderdetail.AviableLimit,
              status: 200,
              message: bankErrorMsg.responseCode[10000],
              respcode: 10000
            }
            // console.log('addSenderResp:::', JSON.stringify(addSenderResp))
            return addSenderResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: responseRemitterDetails.data })
            return { status: 400, respcode: 1080, message: errorMsg.responseCode[1080], error: { message: 'Session not created!' } }
          }
        } else {
          if (propExist('errorres', responseRemitterDetails.data) && propExist('status', responseRemitterDetails.data.errorres) && propExist('description', responseRemitterDetails.data.errorres)) {
            // To do
            // Call Remiiter Details and get Remitter id here
            const allReadyExitsResp = { status: 400, respcode: 10006, message: bankErrorMsg.responseCode[10006] + ':::' + responseRemitterDetails.data.errorres.description, error: responseRemitterDetails.data }
            return allReadyExitsResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: responseRemitterDetails.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterDetails', type: 'error', fields: error })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_AGENT_DETAIL_' + reqDataObj.sendermobilenumber)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'remitterdetails' }
      }
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  /**
   * To do
   */
  async syncRemitterId (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'request', fields: reqDataObj || '' })
    try {
      //
      const remitterDetails = await this.getRemitterDetails(reqDataObj, sessionRQ, connection)
      return remitterDetails
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'error', fields: error })
    }
  }

  /**
   * To do
   */
  async syncBeneficaryId (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'request', fields: reqDataObj || '' })
    try {
      const remitterDetails = await this.getRemitterDetails(reqDataObj, sessionRQ, connection)
      return remitterDetails
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'error', fields: error })
    }
  }

  async getSessionId (maUserId, maBankOnBoardingId, prevConn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'request', fields: { maUserId, maBankOnBoardingId } })
    try {
      this.sessionData = null
      if (this.sessionData !== null && Object.keys(this.sessionData).length > 0) {
        return this.sessionData
      }
      const sessionData = await bankOnBoardDetails.getSessionDetails(null, { ma_user_id: maUserId, ma_bank_on_boarding_id: maBankOnBoardingId }, prevConn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'response', fields: sessionData })
      if (sessionData.status === 200 && Object.keys(sessionData.data).length > 0) {
        const tmpSessionData = sessionData.data[0]
        const locationidStr = tmpSessionData.locationid
        if (locationidStr === undefined || locationidStr === null || locationidStr === '') {
          return { status: 400, respcode: 10027, message: bankErrorMsg.responseCode[10027] }
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'sessionData', fields: sessionData })
        const secretLoginArr = locationidStr.split('::')
        const bcagent = secretLoginArr[2]
        if (tmpSessionData.sessionStatus === 1) {
          this.sessionData = { status: 200, respcode: 10000, data: { sessionid: tmpSessionData.sessionid, bcagent: bcagent } }
          return this.sessionData
        } else {
          const agentLogin = await this.agentLogin({ ma_user_id: maUserId, ma_bank_on_boarding_id: maBankOnBoardingId, connection: prevConn, sessionData: sessionData }, maUserId, prevConn)
          if ('sessionid' in agentLogin) {
            this.sessionData = { status: 200, respcode: 10000, data: { sessionid: agentLogin.sessionid, bcagent: agentLogin.bcagent } }
            return this.sessionData
          } else {
            return agentLogin // return agent login error
          }
        }
      } else {
        return { status: 400, respcode: 10003, message: bankErrorMsg.responseCode[10003] }
      }
    } catch (error) {
      console.log(error)
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async validateBeneficiaryBeforeAdd (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'request', fields: reqDataObj })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        senderid: reqDataObj.senderid,
        ifsccode: reqDataObj.ifsccode,
        accountnumber: reqDataObj.accountnumber,
        receivername: reqDataObj.receivername,
        receivermobilenumber: reqDataObj.receivermobilenumber,
        receiveremailid: reqDataObj.receiveremailid,
        bcpartnerrefno: reqDataObj.request_number
      }

      console.time('TIMER_NSDL_VALIDATE_BENEFICARY')
      const requestStart = process.hrtime()
      const responseBeneficaryTransaction = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'beneficiaryvalidationwobenereq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.BENEVERIFY })
      console.timeEnd('TIMER_NSDL_VALIDATE_BENEFICARY')
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'response', fields: responseBeneficaryTransaction.data })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_VALIDATE_BENEFICARY_DATA_REQUEST_[' + JSON.stringify({ transactionreq: payLoadJson }) + ']_RESPONSE_[' + JSON.stringify(responseBeneficaryTransaction.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (e) {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'catcherrorjson', fields: e })
      }

      if (propExist('data', responseBeneficaryTransaction)) {
        if (propExist('beneficiaryvalidationres', responseBeneficaryTransaction.data) && propExist('status', responseBeneficaryTransaction.data.beneficiaryvalidationres)) {
          const beneficiaryvalidationres = responseBeneficaryTransaction.data.beneficiaryvalidationres
          // Success check
          let TransferResp = {}
          if (beneficiaryvalidationres.status === 1) {
            const { channelpartnerrefno, nsdlbanktransactionid, amount, servicecharge, grossamount, kycstatus, benename, rrn } = beneficiaryvalidationres

            TransferResp = {
              request_number: channelpartnerrefno,
              bankTransactionid: nsdlbanktransactionid,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gross_amount: grossamount,
              benename: benename,
              status: 1,
              kycstatus: kycstatus,
              message: bankErrorMsg.responseCode[10000],
              rrn: rrn
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }
          } else if (beneficiaryvalidationres.status === 0) {
            TransferResp = {
              status: 0,
              message: bankErrorMsg.responseCode[10029]
            }

            if (propExist('remarks', beneficiaryvalidationres) && beneficiaryvalidationres.remarks) {
              TransferResp.message += beneficiaryvalidationres.remarks
              if (HIDE_REMARKS.indexOf(beneficiaryvalidationres.remarks) > -1) {
                const replaceWIthIndex = HIDE_REMARKS.indexOf(beneficiaryvalidationres.remarks)
                TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
              }
            }
          } else if (beneficiaryvalidationres.status === -1) {
            const { channelpartnerrefno, nsdlbanktransactionid, amount, servicecharge, grossamount, kycstatus, benename } = beneficiaryvalidationres

            TransferResp = {
              request_number: channelpartnerrefno,
              bankTransactionid: nsdlbanktransactionid,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gross_amount: grossamount,
              benename: benename,
              status: -1,
              kycstatus: kycstatus,
              message: bankErrorMsg.responseCode[10013]
            }

            // console.time('TIMER_NSDL_VALIDATE_BENEFICARY_REQUERY')
            // const res = await this.doubleVerify(payLoadJson, prevConn)
            // console.timeEnd('TIMER_NSDL_VALIDATE_BENEFICARY_REQUERY')

            // if (res.status != 400) {
            //   TransferResp.status = res.status
            // }
          } else if (beneficiaryvalidationres.status === 91) {
            // Case 91
            TransferResp = {
              status: -1,
              message: 'Pending or Unknown'
            }
          } else {
            TransferResp = {
              status: -1,
              message: 'Other Status Received'
            }
          }
          TransferResp.apiFailed = false
          TransferResp.bank_response = beneficiaryvalidationres
          return TransferResp
        } else {
          if (propExist('errorres', responseBeneficaryTransaction.data) && propExist('status', responseBeneficaryTransaction.data.errorres)) {
            const errorres = responseBeneficaryTransaction.data.errorres
            // To do
            // Call If Transaction Timeout

            const TransferResp = {
              status: 0,
              message: bankErrorMsg.responseCode[10029],
              apiFailed: true
            }

            if (propExist('description', errorres)) {
              TransferResp.message += errorres.description
              if (HIDE_REMARKS.indexOf(errorres.description) > -1) {
                const replaceWIthIndex = HIDE_REMARKS.indexOf(errorres.description)
                TransferResp.message = HIDE_REMARKS_REPLACE[replaceWIthIndex]
              }
            }
            TransferResp.bank_response = errorres
            return TransferResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'response', fields: responseBeneficaryTransaction.data })
            return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004], error: { message: 'Response Data Error From API!' } }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004], error: { message: 'Response Data Error!' } }
      }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error.toString() } })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_VALIDATE_BENEFICARY')
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'verifybeneficiary' }
      }
      return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
    }
  }

  async validateAPIInput (actionType, reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateAPIInput', type: 'request', fields: { validateAPIInput: 'validateAPIInput', actionType, reqDataObj } })
    const defaultResponse = {
      status: 200,
      respcode: 1001,
      message: 'API valid input'
    }
    if (typeof (actionType) !== 'undefined' && typeof (reqDataObj) !== 'undefined') {
      if (nsdlrules !== null && Object.keys(nsdlrules).length > 0 && (actionType in nsdlrules)) {
        const currentRules = nsdlrules[actionType]
        /**
         *  Loop through fields
         */
        for (const [field, fieldRules] of Object.entries(currentRules)) {
          // console.log('currentRules fields', field, fieldRules)
          /**
           * Check Rule field exist in reqDataObj if exist then validate
           */
          if (field in reqDataObj) {
            /**
           * Loop throgh field Rule
           */
            // console.log('fieldRules fields', Object.entries(fieldRules))
            for (const rulename in fieldRules) {
              const rulevalue = fieldRules[rulename]
              // for (var (rulename, rulevalue) in fieldRules ) {
              //  console.log('currentRules fields', rulename, rulevalue)
              const isCurrentFieldValid = apiValidator.validInput(rulename, reqDataObj[field], rulevalue)
              // log.logger(field, rulename, reqDataObj[field], rulevalue, isCurrentFieldValid)
              if (isCurrentFieldValid === false) {
                const invValidMsg = `::: ${field} invalid value ${reqDataObj[field]} for rule ${rulename} `
                defaultResponse.status = 400
                defaultResponse.respcode = 10011
                defaultResponse.message = bankErrorMsg.responseCode[10011] + invValidMsg
                break
              }
            }
          }
        }
      }
    }
    return defaultResponse
  }

  async getRemitterAvailableBalance (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterAvailableBalance', type: 'request', fields: reqDataObj })
    try {
      const remitterDetails = await this.getRemitterDetails(reqDataObj, sessionRQ, connection)
      return remitterDetails
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterAvailableBalance', type: 'error', fields: error })
      return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
    }
  }

  async preValidateRemitter (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: reqDataObj || '' })
    try {
      //
      const remitterDetails = await this.getRemitterDetails(reqDataObj, sessionRQ, connection)
      return remitterDetails
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'error', fields: error })
      return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
    }
  }

  async doubleVerifyBene (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'request', fields: reqDataObj })
    try {
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      const sessionData = {}
      if (currentSessionId.status === 200) {
        sessionData.sessionId = currentSessionId.data.sessionid
        sessionData.bcagent = currentSessionId.data.bcagent
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        header: {
          sessionid: sessionData.sessionId
        },
        bcagent: sessionData.bcagent,
        bcpartnerrefno: reqDataObj.bcpartnerrefno
      }

      console.time('TIMER_NSDL_DOUBLE_VERIFICATION')
      const requestStart = process.hrtime()
      const responsedoubleVerifyBene = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'transactionrequeryreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.DVERIFY })
      console.timeEnd('TIMER_NSDL_DOUBLE_VERIFICATION')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_DOUBLE_VERIFICATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(responsedoubleVerifyBene.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (propExist('data', responsedoubleVerifyBene)) {
        if (propExist('transactionrequeryres', responsedoubleVerifyBene.data) && propExist('status', responsedoubleVerifyBene.data.transactionrequeryres)) {
          if (responsedoubleVerifyBene.data.transactionrequeryres.status === 1) {
            const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responsedoubleVerifyBene.data.transactionrequeryres

            const TransferResp = {
              bcpartnerrefno: bcpartnerrefno,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gst: gst,
              bank_gross_amount: grossamount,
              bankrefno: bankrefno,
              benename: benename,
              bankTransactionid: nsdltransactionid,
              transactionId: reqDataObj.ma_transfers_id,
              status: -1,
              message: 'Pending',
              refund: false,
              rrn: rrn,
              remarks: remarks || ''
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }

            return TransferResp
          } else {
            const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responsedoubleVerifyBene.data.transactionrequeryres
            const TransferResp = {
              bcpartnerrefno: bcpartnerrefno,
              amount: amount,
              bank_service_charges: servicecharge,
              bank_gst: gst,
              bank_gross_amount: grossamount,
              bankrefno: bankrefno,
              benename: benename,
              bankTransactionid: nsdltransactionid,
              transactionId: reqDataObj.ma_transfers_id,
              status: -1,
              message: bankErrorMsg.responseCode[10025],
              refund: false,
              autorefundcredit: false,
              rrn: rrn,
              remarks: remarks || ''
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(benename)) {
              TransferResp.benename = ''
            }

            // 0 - Pending -1 -Unknown
            if (responsedoubleVerifyBene.data.transactionrequeryres.status === -1 || responsedoubleVerifyBene.data.transactionrequeryres.status === 0) {
              TransferResp.status = -1
              TransferResp.message = 'Pending or Unknown'
              return TransferResp
            } else if (responsedoubleVerifyBene.data.transactionrequeryres.status === 6) {
              TransferResp.status = 0
              TransferResp.message = 'Failure'
              TransferResp.autorefundcredit = true
              return TransferResp
            } else if (responsedoubleVerifyBene.data.transactionrequeryres.status === 4) {
              TransferResp.status = 0
              TransferResp.message = 'Refund Processed'
              TransferResp.refunded = true
              return TransferResp
            } else if (responsedoubleVerifyBene.data.transactionrequeryres.status === 3) {
              TransferResp.status = 0
              TransferResp.message = 'Please initiate refund'
              TransferResp.refund = true
              return TransferResp
            } else if (responsedoubleVerifyBene.data.transactionrequeryres.status === 2) {
              TransferResp.status = 1
              TransferResp.message = 'Credited'
              return TransferResp
            } else {
              return TransferResp
            }

            //  log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: responseTransaction.data })
            // return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Session not created!' } }
          }
        } else {
          if (propExist('errorres', responsedoubleVerifyBene.data) && propExist('status', responsedoubleVerifyBene.data.errorres)) {
            const TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10025]
            }

            if (propExist('description', responsedoubleVerifyBene.data.errorres)) {
              TransferResp.message = responsedoubleVerifyBene.data.errorres.description
              TransferResp.remarks = responsedoubleVerifyBene.data.errorres.description
            }

            return TransferResp
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: {} })
            return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error From API!' } }
          }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: { message: 'Response Data Error!' } })
        return { status: 400, respcode: 1085, message: errorMsg.responseCode[1085], error: { message: 'Response Data Error!' } }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'error', fields: error })
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('TIMEOUT_NSDL_DOUBLE_VERIFICATION' + reqDataObj.bcpartnerrefno)
        return { status: 400, respcode: 1028, message: bankErrorMsg.responseCode[10031] + 'doubleVerifyBene' }
      }
      return { status: 400, message: 'Double verification to NSDL Bank Failed!', error: error.message }
    }
  }

  async getBeneExist (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBeneExist', type: 'reqDataObj', fields: reqDataObj })

    try {
      const beneficaryDetails = await this.getRemitterDetails(reqDataObj, sessionRQ, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneExist', type: 'beneficaryDetails', fields: beneficaryDetails })

      if (beneficaryDetails && ('beneficiarydetail' in beneficaryDetails) && ('beneficiary' in beneficaryDetails.beneficiarydetail)) {
        const isObjectArray = Object.prototype.toString.call(beneficaryDetails.beneficiarydetail.beneficiary) === '[object Array]'
        let bankSideBeneficiaris = []
        if (!isObjectArray && ('beneficiaryid' in beneficaryDetails.beneficiarydetail.beneficiary)) {
          bankSideBeneficiaris.push(beneficaryDetails.beneficiarydetail.beneficiary)
        } else {
          bankSideBeneficiaris = beneficaryDetails.beneficiarydetail.beneficiary
        }

        const matchAccnts = bankSideBeneficiaris.filter((currentBank) => (String(currentBank.accountnumber).toLowerCase() == String(reqDataObj.accountnumber).toLowerCase()) && (currentBank.beneficiarystatus === 1))

        if (matchAccnts.length > 0) {
          let ifsCodeMatched = matchAccnts.find((matchBene) => matchBene.ifscode.toLowerCase() == reqDataObj.ifscode.toLowerCase())
          const addBeneficaryResp = {
            status: 200,
            respcode: 10000,
            existWithDiffIfsc: false,
            message: bankErrorMsg.responseCode[10000]
          }
          if (typeof (ifsCodeMatched) == 'undefined') {
            ifsCodeMatched = matchAccnts[0]
            addBeneficaryResp.existWithDiffIfsc = true
            addBeneficaryResp.senderid = ifsCodeMatched.senderid
            addBeneficaryResp.beneficiaryid = ifsCodeMatched.beneficiaryid
            addBeneficaryResp.beneficiaryname = ifsCodeMatched.beneficiaryname
            addBeneficaryResp.ifscode = ifsCodeMatched.ifscode
            addBeneficaryResp.beneficiarymobilenumber = ifsCodeMatched.beneficiarymobilenumber
            addBeneficaryResp.accountnumber = ifsCodeMatched.accountnumber
            addBeneficaryResp.bank = ifsCodeMatched.bank
            addBeneficaryResp.ben_mobile_number = ifsCodeMatched.beneficiarymobilenumber
          } else {
            addBeneficaryResp.senderid = ifsCodeMatched.senderid
            addBeneficaryResp.beneficiaryid = ifsCodeMatched.beneficiaryid
            addBeneficaryResp.beneficiaryname = ifsCodeMatched.beneficiaryname
            addBeneficaryResp.ifscode = ifsCodeMatched.ifscode
            addBeneficaryResp.beneficiarymobilenumber = ifsCodeMatched.beneficiarymobilenumber
            addBeneficaryResp.accountnumber = ifsCodeMatched.accountnumber
            addBeneficaryResp.bank = ifsCodeMatched.bank
            addBeneficaryResp.ben_mobile_number = ifsCodeMatched.beneficiarymobilenumber
          }

          log.logger({ pagename: require('path').basename(__filename), action: 'getBeneExist', type: 'addBeneficaryResp', fields: addBeneficaryResp })
          const bankBranchCtrl = require('../../../bankDetails/bankBranchController')
          const ifscRes = await bankBranchCtrl.getBranchByIfscForBank(null, { ifsc: addBeneficaryResp.ifscode })
          log.logger({ pagename: require('path').basename(__filename), action: 'getBeneExist', type: 'ifscRes', fields: ifscRes })
          if (ifscRes.status === 200) {
            return addBeneficaryResp
          } else {
            return { status: 400, respcode: 10026, message: bankErrorMsg.responseCode[10026] + addBeneficaryResp.ifscode }
          }
        }
      }

      return { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000], existWithDiffIfsc: false }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneExist', type: 'error', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getBeneficiaryList (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'request', fields: reqDataObj || '' })
    try {
      const remitterDetails = await this.getRemitterDetails(reqDataObj, sessionRQ, connection)

      if (remitterDetails.status != 200) {
        return remitterDetails
      }

      const beneList = []
      let beneListLength = 0

      if (remitterDetails.beneficiarydetail.beneficiary && remitterDetails.beneficiarydetail.beneficiary.constructor.name == 'Object' && Object.keys(remitterDetails.beneficiarydetail.beneficiary).length > 0) {
        remitterDetails.beneficiarydetail.beneficiary = [remitterDetails.beneficiarydetail.beneficiary]
      }

      if (remitterDetails.beneficiarydetail.beneficiary && remitterDetails.beneficiarydetail.beneficiary.constructor.name == 'Array') {
        beneListLength = remitterDetails.beneficiarydetail.beneficiary.length
      }

      const beneficiaries = remitterDetails.beneficiarydetail.beneficiary

      for (let index = 0; index < beneListLength; index++) {
        /* bene name blank case handle while sync bene */
        if (
          beneficiaries[index].beneficiaryname != '' &&
          beneficiaries[index].accountnumber != '' &&
          beneficiaries[index].ifscode != ''
        ) {
          beneList.push({
            accountNumber: beneficiaries[index].accountnumber,
            ifscCode: beneficiaries[index].ifscode,
            beneficiaryMobileNumber: beneficiaries[index].beneficiarymobilenumber || 0,
            beneficiaryName: beneficiaries[index].beneficiaryname,
            bankName: beneficiaries[index].bank,
            beneficiaryId: beneficiaries[index].beneficiaryid
          })
        }
      }

      return {
        status: 200,
        respcode: 1000,
        beneList: beneList
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async remitterNameValidation (reqDataObj, sessionRQ, connection) {
    try {
      /* request changes : workdround for call airtel api */
      reqDataObj.ma_bank_on_boarding_id = 10
      const result = await bankInstance.remitterNameValidation(reqDataObj, sessionRQ, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'airtel-result', fields: result })

      if (result.status != 200) {
        return result
      }

      return result
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10033] }
    }
  }

  async registerNewRemitterKyc (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'kycRegistration', type: 'response', fields: reqDataObj })
    try {
      const requestPayLoad = this.getKycRequestData(reqDataObj)

      if (requestPayLoad.status != 200) {
        return requestPayLoad
      }
      const payLoadJson = requestPayLoad.data

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      payLoadJson.header.sessionid = currentSessionId.data.sessionid
      payLoadJson.bcagent = currentSessionId.data.bcagent

      console.time('TIMER_NSDL_KYC_REGISTRATION')
      const requestStart = process.hrtime()
      const response = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'senderregistrationkycreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.REMITTER })
      console.timeEnd('TIMER_NSDL_KYC_REGISTRATION')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        const payLoadJsonMask = payLoadJson
        payLoadJsonMask.pannumber = common_fns.maskValue(payLoadJsonMask, 'pannumber')
        console.log('API_[' + sessionRQ + ']_NSDL_KYC_REGISTRATION_REQUEST_[' + JSON.stringify(payLoadJsonMask) + ']_RESPONSE_[' + JSON.stringify(response.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }
      if (!('data' in response)) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      const BankResponse = response.data
      /* Error */
      if ('errorres' in BankResponse &&
        'status' in BankResponse.errorres &&
        'description' in BankResponse.errorres
      ) {
        return {
          status: 400,
          respcode: 10032,
          message: `${bankErrorMsg.responseCode[10032]} ::: ${BankResponse.errorres.description}`
        }
      }

      if (!('senderupdatekycres' in BankResponse) ||
        !('status' in BankResponse.senderupdatekycres)
      ) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      let kycStatus = false
      // kycstatus = 0 (FAILURE)
      if (BankResponse.senderupdatekycres.status == 0) {
        kycStatus = false
      }
      // kycstatus = 1 (SUCCESS)
      if (BankResponse.senderupdatekycres.status == 1) {
        kycStatus = true
      }

      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        kycStatus: kycStatus
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'kycRegistration', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async registerRemitterKyc (reqDataObj, sessionRQ, connection) {
    const maskResponse = reqDataObj
    log.logger({ pagename: require('path').basename(__filename), action: 'registerRemitterKyc', type: 'response', fields: common_fns.maskValue(maskResponse.customFields, 'pannumber') })
    try {
      const requestPayLoad = await this.getKycRequestData(reqDataObj)

      if (requestPayLoad.status != 200) {
        return requestPayLoad
      }
      const payLoadJson = requestPayLoad.data

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'registerRemitterKyc', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      payLoadJson.header.sessionid = currentSessionId.data.sessionid
      payLoadJson.bcagent = currentSessionId.data.bcagent

      console.time('TIMER_NSDL_KYC_UPDATE')
      const requestStart = process.hrtime()
      const response = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'senderupdatekycreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.REMITTER })
      console.timeEnd('TIMER_NSDL_KYC_UPDATE')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        // masking
        const payLoadJsonMask = payLoadJson
        payLoadJsonMask.pannumber = common_fns.maskValue(payLoadJsonMask, 'pannumber')
        console.log('API_[' + sessionRQ + ']_NSDL_KYC_UPDATE_REQUEST_[' + JSON.stringify(payLoadJsonMask) + ']_RESPONSE_[' + JSON.stringify(response.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (!('data' in response)) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      const BankResponse = response.data
      /* Error */
      if ('errorres' in BankResponse &&
        'status' in BankResponse.errorres &&
        'description' in BankResponse.errorres
      ) {
        return {
          status: 400,
          respcode: 10032,
          message: `${bankErrorMsg.responseCode[10032]} ::: ${BankResponse.errorres.description}`
        }
      }

      if (!('senderupdatekycres' in BankResponse) ||
        !('status' in BankResponse.senderupdatekycres)
      ) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      let kycStatus = false
      // kycstatus = 0 (FAILURE)
      if (BankResponse.senderupdatekycres.status == 0) {
        kycStatus = false
      }
      // kycstatus = 1 (SUCCESS)
      if (BankResponse.senderupdatekycres.status == 1) {
        kycStatus = true
      }

      if (BankResponse.senderupdatekycres.kycurl == '') {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }

      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        kycStatus: kycStatus,
        link: BankResponse.senderupdatekycres.kycurl
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'registerRemitterKyc', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getKycRequestData (reqDataObj) {
    try {
      const customPayload = {
        senderid: reqDataObj.senderid,
        kyctype: reqDataObj.kycType
      }
      const validationRes = await this.validateAPIInput('KYC_UPDATE', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      if (!KYC_TYPE[customPayload.kyctype]) {
        return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
      }

      if (customPayload.kyctype == 'PAN') {
        const validationRes = await this.validateAPIInput('KYC_UPDATE_PAN', {
          pan: reqDataObj.customFields.pannumber
        })
        if (validationRes.status !== 200) {
          return validationRes
        }
        customPayload.pannumber = reqDataObj.customFields.pannumber
      }

      if (customPayload.kyctype == 'BIO') {
        const validationRes = await this.validateAPIInput('KYC_UPDATE_BIO', {})
        if (validationRes.status !== 200) {
          return validationRes
        }
      }

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        data: {
          header: {
            sessionid: ''
          },
          bcagent: '',
          senderid: customPayload.senderid,
          kyctype: KYC_TYPE[customPayload.kyctype],
          pannumber: customPayload.pannumber || '',
          bccallbackkycurl: NSDL_EKYC_CALLBACK_URL,
          extra1: '',
          extra2: ''
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'registerRemitterKyc', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async requestCustomerOnboardingDmt (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'Request', fields: reqDataObj })
    try {
      // Header Value
      const headerReqId = await this.generateRequestID()
      const headers = {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'REQDMTCUSTONBOARD',
        requestid: headerReqId,
        requesttype: 'web'
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'NSDL headers', fields: headers })

      // Requestbody to be encrypted and send in encdata
      const encDataReq = {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        customerreferenceno: reqDataObj.mobile_no,
        mobileno: reqDataObj.mobile_no,
        email: reqDataObj.email,
        accesstype: [
          'DMT'
        ],
        device_serial_no: reqDataObj.device_serial_no,
        device_model: reqDataObj.device_model,
        device_manufacturer: reqDataObj.device_manufacturer,
        device_rdversion: reqDataObj.device_rdversion,
        onboarding_location_details: reqDataObj.location,
        onboarding_location_longitude: reqDataObj.latitude,
        onboarding_location_latitude: reqDataObj.longitude,
        partner_ref_url: PARTNER_REF_URL,
        partner_callback_url: PARTNER_CALLBACK_URL,
        requestreferenceno: reqDataObj.uniqueId,
        requestedby: reqDataObj.requested_by
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'encDataReq', fields: encDataReq })
      // encrypt encDataReq value
      const encResult = await this.encryptString(JSON.stringify(encDataReq))
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'encResult', fields: encResult })

      const payLoadJson =
      {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'REQDMTCUSTONBOARD',
        encdata: encResult
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'payLoadJson', fields: payLoadJson })
      const URL = `${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.CUST_ONBOARDING}`
      console.log('CustomerOnboarding URL--->', URL)
      console.time('TIMER_NSDL_REQ_CUST_ONBOARDING')
      const requestStart = process.hrtime()
      const response = await apiJsonRequest.post(URL, payLoadJson, { headers: headers })

      console.timeEnd('TIMER_NSDL_REQ_CUST_ONBOARDING')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_REQ_CUST_ONBOARDING_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(response.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      /**
       * SAVE THE API DETAILS TO DATABASE
       */
      reqDataObj.bank_name = 'NSDL'
      reqDataObj.mobile_number = reqDataObj.mobile_no || 0
      reqDataObj.sessionRQ = sessionRQ || 0
      reqDataObj.request_type = 'REQ_CUST_ONBOARDING'
      reqDataObj.request = JSON.stringify({ ...payLoadJson, ...encDataReq, ...headers })
      reqDataObj.response = typeof response.data == 'object' ? JSON.stringify(response.data) : `${response.data}`
      reqDataObj.url = URL || ''
      reqDataObj.api_status = response.status || ''
      const saveLogsResp = await TransferController.saveApiLogDmt(reqDataObj, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'saveLogsResp', fields: saveLogsResp })

      if (!('data' in response)) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      const BankResponse = response.data
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'BankResponse', fields: BankResponse })

      /* Error */
      if ('errorres' in BankResponse && 'status' in BankResponse.errorres && 'description' in BankResponse.errorres) {
        return {
          status: 400,
          respcode: 10032,
          message: `${bankErrorMsg.responseCode[10032]} ::: ${BankResponse.errorres.description}`
        }
      }
      if (BankResponse.response == 'SUCCESS' && BankResponse.responsecode == '00' && BankResponse.responsedata.response == 'Success' && BankResponse.responsedata.respcode == '00') {
        console.log('Success Response-->', JSON.stringify({
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          data: BankResponse
        }))
        return {
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          data: BankResponse
        }
      } else if (BankResponse.response == 'Failed' || BankResponse.responsecode == '99') {
        return {
          status: 400,
          respcode: 10000,
          message: BankResponse.responsedata.response || bankErrorMsg.responseCode[10005],
          data: BankResponse
        }
      }
      return {
        status: 400,
        respcode: 10000,
        message: BankResponse.responsedata.response || bankErrorMsg.responseCode[10005]
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCustomerOnboardingDmt', type: 'error', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async generateRequestID () {
    try {
      const timestamp = Date.now().toString()
      const random = Math.floor(100 + Math.random() * 900)
      const result = `UNQREQ${timestamp}${random}`
      return result
    } catch (err) {
      console.error(err)
      return null
    }
  }

  async getRemitterKycStatus (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterKycStatus', type: 'response', fields: reqDataObj })
    try {
      const customPayload = { mobilenumber: reqDataObj.mobile_number, senderid: reqDataObj.senderid }

      const validationRes = await this.validateAPIInput('KYC_STATUS_CHECK', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const payLoadJson = {
        header: {
          sessionid: ''
        },
        bcagent: '',
        mobilenumber: customPayload.mobilenumber,
        extra1: '',
        extra2: ''
      }

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterKycStatus', type: 'response', fields: currentSessionId })
        return currentSessionId
      }
      payLoadJson.header.sessionid = currentSessionId.data.sessionid
      payLoadJson.bcagent = currentSessionId.data.bcagent

      console.time('TIMER_NSDL_KYC_STATUS')
      const requestStart = process.hrtime()
      const response = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'senderdetailskycreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.REMITTER })
      console.timeEnd('TIMER_NSDL_KYC_STATUS')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_NSDL_KYC_STATUS_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(response.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (!('data' in response)) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      const BankResponse = response.data
      /* Error */
      if ('errorres' in BankResponse &&
        'status' in BankResponse.errorres &&
        'description' in BankResponse.errorres
      ) {
        return {
          status: 400,
          respcode: 10032,
          message: `${bankErrorMsg.responseCode[10032]} ::: ${BankResponse.errorres.description}`
        }
      }

      if (!('senderdetailskycres' in BankResponse) ||
        !('senderdetail' in BankResponse.senderdetailskycres) ||
        !('kycstatus' in BankResponse.senderdetailskycres.senderdetail)
      ) {
        return { status: 400, respcode: 10004, message: bankErrorMsg.responseCode[10004] }
      }
      let kycStatus = false

      // kycstatus = 0 (PENDING)
      // kycstatus = 1 (UNDER PROCESS)
      // kycstatus = 3 (Bank Rejected)
      const kycPendingCode = [0, 1, 3]

      if (kycPendingCode.includes(BankResponse.senderdetailskycres.senderdetail.kycstatus)) {
        kycStatus = false
      }
      // kycstatus = 2 (BANK APPROVED)
      if (BankResponse.senderdetailskycres.senderdetail.kycstatus == 2) {
        kycStatus = true
      }

      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        kycStatus: kycStatus
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterKycStatus', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async confirmOnboardingDetails (reqDataObj, sessionRQ, connection) {
    try {
      // Log request data
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'confirmOnboardingDetails',
        type: 'request',
        fields: reqDataObj.referenceid
      })

      const serviceType = 'CONFIRM'
      const header = {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: serviceType,
        requestid: await this.generateRequestID()
      }   
      const payload = {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: serviceType,
        encdata: await this.encryptString(
          JSON.stringify({
            referenceid: reqDataObj.referenceid,
            statusmode: serviceType,
            statusreason: 'Confirmed for Submission'
          })
        )
      }
      // Log request data
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'confirmOnboardingDetails',
        type: 'request',
        fields: { header, payload }
      })
      // Make API request
      const responseData = await apiJsonRequest.post(`${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.DETAILS_CONFIRM}`, payload, { headers: header })
      // Log response data
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'confirmOnboardingDetails',
        type: 'response',
        fields: responseData
      })

      // Code for saving logs
      const saveLogsData = {
        ...reqDataObj,
        bank_name: 'NSDL',
        request_type:'CONFIRM_ONBOARDING',
        request:  JSON.stringify({payload, header}),
        response: typeof responseData.data == 'object' ? JSON.stringify(responseData.data) : `${responseData.data}`,
        url: `${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.DETAILS_CONFIRM}`,
        api_status: responseData.status,
        sessionRQ
      }
  
      const savelogResponse = await TransferController.saveApiLogDmt(saveLogsData, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'saveApiLog', type: 'response', fields: savelogResponse })

      // Check if the response is successful
      if (responseData.status == 200 && responseData.data && responseData.data.responsecode == '00') {
        return { status: 200, respcode: 1000, message: 'Remitter onboarded successfully' }
      } else {
        return { status: 400, respcode: 1001, message: 'Remitter onboarding failed', displayMessage: responseData?.data?.responsedata?.response }
      }
    } catch (error) {
      // Log error data
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'confirmOnboardingDetails',
        type: 'error',
        fields: error
      })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getCustomerOnboardingDetails (reqDataObj, requestNo) {
    try {
      const requestId = await this.generateRequestID()
      console.log('requestId>>>', requestId)

      console.log('number>>>',reqDataObj.mobile_number)
      const value = JSON.stringify({
        partnerid: PARTNER_ID,
        mobileno: reqDataObj.mobile_number
      })
      const EncryptedValue = await this.encryptString(value)
      console.log('EncryptedValue>>>>', EncryptedValue)
      const data = {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'GETCUSTONBOARDDTLS',
        encdata: EncryptedValue
      }
      
      const config = {
        method: 'post',
        url:`${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.GET_CUST_ONBRD_DETAILS}`,
        headers: {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'GETCUSTONBOARDDTLS',
        requestid: requestId,
        // 'Content-Type': 'application/json',
        requesttype: 'web'
        },
        data
      }
      const resdata = await axios(config)
      console.log('resdata>>>', resdata)
      const BankResponse = resdata.data
      log.logger({ pagename: require('path').basename(__filename), action: 'nsdlcustKycOnboarding', type: 'BankResponse', fields: BankResponse })
      const fields = { ...reqDataObj, request: JSON.stringify(config) || '', response: JSON.stringify(BankResponse) || '', api_status:BankResponse.responsecode == '00'?200:400, request_type: BANK_API_ENDPOINTS.GET_CUST_ONBRD_DETAILS, url:`${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.GET_CUST_ONBRD_DETAILS}` }
      const result = await TransferController.saveApiLogDmt(fields)
      log.logger({ pagename: 'customerController.js', action: 'getCustomerOnboardingDetails', type: 'result', fields: result })
    
      /* Error */
      if ('errorres' in BankResponse && 'status' in BankResponse.errorres && 'description' in BankResponse.errorres) {
        return {
          status: 400,
          respcode: 10032,
          message: `${bankErrorMsg.responseCode[10032]} ::: ${BankResponse.errorres.description}`
        }
      }
      if (BankResponse.response == 'SUCCESS' && BankResponse.responsecode == '00') {
        console.log('Success Response-->', JSON.stringify({
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          data: BankResponse
        }))
      if(BankResponse.response == 'SUCCESS' && BankResponse.responsecode == '00' && BankResponse.responsedata.dmtcustomer_registration_status == 'Success'){
        return {
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          data: BankResponse
        }
      }
      return {
        status: 400,
        respcode: 1000,
        message: BankResponse.responsedata.response || bankErrorMsg.responseCode[10005],
        data: BankResponse
      }
      }
      else if (BankResponse.response == 'Failed' || BankResponse.responsecode == '99' && BankResponse.responsedata.respcode=='99') {
        return {
          status: 400,
          respcode: 1000,
          message: BankResponse.responsedata.response || bankErrorMsg.responseCode[10005],
          data: BankResponse
        }
      }
      return {
        status: 400,
        respcode: 1001,
        message:BankResponse.responsedata.response|| bankErrorMsg.responseCode[10001]
      }
    } catch (err) {
      log.logger({ pagename: 'customerController.js', action: 'nsdlgetCustomerOnboardingDetailsApi', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  async nsdlcustKycOnboarding (reqDataObj, requestNo) {
    try {
      const headerReqId = await this.generateRequestID()
      const encdata = await this.encryptString(JSON.stringify(reqDataObj.referenceid))
      const data = {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'GETREQCUSTONBOARDDTLS',
        encdata: encdata
      }
    const urlValue = `${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.CUST_KYC_ONBOARDING}`
    console.log('url>>>>',urlValue)
      var config = {
        method: 'post',
        url:urlValue,
        headers: {
          chanid: CHANNEL_ID,
          partnerid: PARTNER_ID,
          servicetype: 'GETREQCUSTONBOARDDTLS',
          requestid: headerReqId,
          'Content-Type': 'application/json',
          requesttype: 'web'
        },
        data
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'nsdlcustKycOnboarding', type: 'config', fields: config })

      const nsdlResponse = await axios(config)
      console.log('nsdlResponse>>', JSON.stringify(nsdlResponse.data))
      const BankResponse = nsdlResponse.data
      log.logger({ pagename: require('path').basename(__filename), action: 'nsdlcustKycOnboarding', type: 'BankResponse', fields: BankResponse })
      /* save logs */
        const fields = { ...reqDataObj, request: JSON.stringify(config) || '', response: JSON.stringify(nsdlResponse.data) || '', api_status:BankResponse.responsecode == '00'?200:400, request_type: BANK_API_ENDPOINTS.CUST_KYC_ONBOARDING, url:urlValue }
        const result = await TransferController.saveApiLogDmt(fields)
        log.logger({ pagename: require('path').basename(__filename), action: 'nsdlcustKycOnboarding', type: 'result', fields: result })
      
      /* Error */
      if ('errorres' in BankResponse && 'status' in BankResponse.errorres && 'description' in BankResponse.errorres) {
        return {
          status: 400,
          respcode: 10032,
          message: `${bankErrorMsg.responseCode[10032]} ::: ${BankResponse.errorres.description}`
        }
      }
      if (BankResponse.response == 'SUCCESS' && BankResponse.responsecode == '00') {
        console.log('Success Response-->', JSON.stringify({
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          data: BankResponse
        }))
        return {
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          data: BankResponse
        }
      }
      else if (BankResponse.response == 'Failed' || BankResponse.responsecode == '99' && BankResponse.responsedata.respcode=='99') {
        return {
          status: 400,
          respcode: 1000,
          message: BankResponse.responsedata.response || bankErrorMsg.responseCode[10005],
          data: BankResponse
        }
      }
      return {
        status: 400,
        respcode: 1001,
        message:BankResponse.responsedata.response || bankErrorMsg.responseCode[10001]
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'airtelGetCustomerApi', type: 'error', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async encryptString (value) {
    try {
      const key = Buffer.from(ENC_PASS.substring(0, 32), 'utf8')
      const iv = Buffer.from(ENC_PASS.substring(0, 16), 'utf8')
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
      let encrypted = cipher.update(value, 'utf8', 'base64')
      encrypted += cipher.final('base64')
      return encrypted
    } catch (err) {
      console.error(err)
      return null
    }
  }

  async decryptString (encryptedValue) {
    try {
      const key = Buffer.from(ENC_PASS.substring(0, 32), 'utf8')
      const iv = Buffer.from(ENC_PASS.substring(0, 16), 'utf8')
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
      let decrypted = decipher.update(encryptedValue, 'base64', 'utf8')
      decrypted += decipher.final('utf8')
      return decrypted
    } catch (err) {
      console.error(err)
      return null
    }
  }


  async processCustomerEKYC (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'request', fields: reqDataObj })
    try {
      // Get session id
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const { ma_user_id, userid, ma_bank_on_boarding_id, ma_bank_name, remitter_mobile_number, referenceid, encrypted_aadhaar } = reqDataObj
      // const { channelid, partnerid, servicetype, encdata } = rest

      const headerReqId = await this.generateRequestID()

      // request headers
      const headers = {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'VERIFYCUSTONBOARDUIDFTECH',
        requestid: headerReqId
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'NSDL headers', fields: headers })

      const payload = {
        referenceid: referenceid,
        uid: encrypted_aadhaar
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'NSDL payload', fields: payload })

      // encrypt payload
      const encryptedPayload = await this.encryptString(JSON.stringify(payload))
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'encrypted Payload', fields: encryptedPayload })

      const reqBody = {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'VERIFYCUSTONBOARDUIDFTECH',
        encdata: encryptedPayload
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'req body', fields: reqBody })

      // fetch NSDL Bank API url
      const URL = `${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.CUST_ONBOARDING_UID_DTLS_FETCH}`

      const requestStart = process.hrtime()
      console.time('TIMER_PROCESS_CUSTOMER_EKYC')
      const response = await apiJsonRequest.post(URL, reqBody, { headers: headers })
      const apiResponse = response.data
      console.log('API Result: ', apiResponse)
      console.timeEnd('TIMER_PROCESS_CUSTOMER_EKYC')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_PROCESS_CUSTOMER_EKYC_[' + JSON.stringify(payload) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'bankResponse', fields: apiResponse })

      // store logs
      const logObj = {
        ma_user_id: ma_user_id,
        userid: userid,
        bank_name: ma_bank_name,
        mobile_number: remitter_mobile_number,
        sessionRQ: sessionRQ,
        request_type: 'PROCESS_CUSTOMER_EKYC',
        request: JSON.stringify({...payload, ...headers}),
        response: JSON.stringify(apiResponse),
        url: BANK_API_URL_NEW + BANK_API_ENDPOINTS.CUST_ONBOARDING_UID_DTLS_FETCH,
        api_status: apiResponse.responsecode == '00' ? 200 : 400
      }
      const storeLogs = await TransferController.saveApiLogDmt(logObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'store logs', fields: storeLogs })
      
      if (apiResponse.responsecode == '00') {
        return {
          status: 200,
          respcode: 1000,
          action_code: 1000,
          requestID: apiResponse.requestId || '',
          message: 'Aadhaar verified successfully',
          displayMessage: 'Aadhaar verified successfully',
          navigation: 'show_biometric',
          sessionRQ: sessionRQ || '',
          bankID: ma_bank_on_boarding_id || '',
          bankName: ma_bank_name || '',
          acToken: ''
        }
      } else {
        return {
          status: 400,
          respcode: 1001,
          action_code: 1001,
          requestID: apiResponse.requestId || '',
          message: 'Fail: NSDL - ' + apiResponse.responsedata.response,
          displayMessage: 'EKYC registration failed',
          navigation: 'show_aadhar',
          sessionRQ: sessionRQ,
          bankID: ma_bank_on_boarding_id || '',
          bankName: ma_bank_name || '',
          acToken: ''
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'catcherror', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async processCustomerBiometric (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'request', fields: reqDataObj })
    try {
      // Get session id
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const { ma_user_id, userid, ma_bank_on_boarding_id, ma_bank_name, remitter_mobile_number, referenceid, rawPayloadData } = reqDataObj

      const headerReqId = await this.generateRequestID()

      // request headers
      const headers = {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'CUSTONBOARDUIDFETCHKYC',
        requestid: headerReqId
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'NSDL headers', fields: headers })

      // const payload = {
      //   referenceid: referenceid,
      //   uid: rawPayloadData
      // }
      const payload = rawPayloadData
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'NSDL payload', fields: payload })

      // encrypt payload
      const encryptedPayload = await this.encryptString(JSON.stringify(payload))
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'encrypted Payload', fields: encryptedPayload })

      const reqBody = {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'CUSTONBOARDUIDFETCHKYC',
        encdata: encryptedPayload
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'req body', fields: reqBody })

      // fetch NSDL Bank API url
      const URL = `${BANK_API_URL_NEW}${BANK_API_ENDPOINTS.CUST_ONBOARDING_BIO_DTLS}`

      const requestStart = process.hrtime()
      console.time('TIMER_PROCESS_CUSTOMER_BIOMETRIC')
      const response = await apiJsonRequest.post(URL, reqBody, { headers: headers })
      const apiResponse = response.data
      console.log('API Result: ', apiResponse)
      console.timeEnd('TIMER_PROCESS_CUSTOMER_BIOMETRIC')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_PROCESS_CUSTOMER_BIOMETRIC_[' + JSON.stringify(payload) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'bankResponse', fields: apiResponse })

      // store logs
      const logObj = {
        ma_user_id: ma_user_id,
        userid: userid,
        bank_name: ma_bank_name,
        mobile_number: remitter_mobile_number,
        sessionRQ: sessionRQ,
        request_type: 'PROCESS_CUSTOMER_BIOMETRIC',
        request: JSON.stringify({...payload, ...headers}),
        response: JSON.stringify(apiResponse),
        url: BANK_API_URL_NEW + BANK_API_ENDPOINTS.CUST_ONBOARDING_BIO_DTLS,
        api_status: apiResponse.responsecode == '00' ? 200 : 400
      }
      const storeLogs = await TransferController.saveApiLogDmt(logObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'store logs', fields: storeLogs })

      if (apiResponse.responsecode == '00') {
        return {
          status: 200,
          respcode: 1000,
          action_code: 1000,
          requestID: apiResponse.requestId || '',
          message: apiResponse.responsedata.response || '',
          displayMessage: apiResponse.responsedata.response || '',
          navigation: 'show_beneficiary_list',
          sessionRQ: sessionRQ || '',
          bankID: ma_bank_on_boarding_id || '',
          bankName: ma_bank_name || ''
        }
      } else {
        return {
          status: 400,
          respcode: 1001,
          action_code: 1001,
          requestID: apiResponse.requestId || '',
          message: 'Fail: NSDL - ' + apiResponse.responsedata.response,
          displayMessage: 'EKYC registration failed',
          navigation: 'show_aadhar',
          sessionRQ: sessionRQ,
          bankID: ma_bank_on_boarding_id || '',
          bankName: ma_bank_name || ''
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerBiometric', type: 'catcherror', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async customerGenerateOTP (reqDataObj, sessionRQ, connection) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'request', fields: reqDataObj })
      if (!reqDataObj.otp_type || (reqDataObj.otp_type != 1 && reqDataObj.otp_type != 5)) {
        return { status: 400, message: 'OTP Type is not valid', respcode: 1001 }
      }
      if (reqDataObj.otp_type == 5 && !reqDataObj.amount) {
        return { status: 400, message: 'Amount is required for Transaction OTP', respcode: 1001 }
      }

      if (reqDataObj.otp_type == 1) {
        const headerReqId = await this.generateRequestID()
        const header = {
          chanid: CHANNEL_ID,
          partnerid: PARTNER_ID,
          servicetype: 'VERIFYCUSTONBOARDMOBILENO_REQOTP',
          requestid: headerReqId
        }
        const obj = {
          referenceid: reqDataObj.referenceid
        }
        const encdata = await this.encryptString(JSON.stringify(obj))
        const payload = {
          channelid: CHANNEL_ID,
          partnerid: PARTNER_ID,
          servicetype: 'VERIFYCUSTONBOARDMOBILENO_REQOTP',
          encdata: encdata
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'requestObj', fields: { payload: payload, headers: header } })
        const response = await apiJsonRequest.post(BANK_API_URL_NEW + BANK_API_ENDPOINTS.GENRATE_OTP, payload, { headers: header })
        log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'requestObj', fields: response.data })
        // Code for saving logs
        reqDataObj.bank_name = 'NSDL'
        reqDataObj.request_type = 'GENERATE_CUST_OTP'
        reqDataObj.request = JSON.stringify({ ...payload, ...header })
        reqDataObj.response = typeof response.data == 'object' ? JSON.stringify(response.data) : `${response.data}`
        reqDataObj.url = BANK_API_URL_NEW + BANK_API_ENDPOINTS.GENRATE_OTP
        reqDataObj.api_status = response.data.responsecode == '00' ? 200 : 400

        const savelogRes = await TransferController.saveApiLogDmt(reqDataObj)
        log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'saveApiLog', fields: savelogRes })

        if (response.data.responsecode == '00') {
          return {
            status: 200,
            respcode: 10001,
            message: 'OTP generate successfully',
            otp_id: response.data.responsedata.otpreqid
          }
        } else {
          return {
            status: 400,
            respcode: 10000,
            message: response.data.response
          }
        }
      } else if (reqDataObj.otp_type == 5) {
        const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
        const sessionData = {}
        if (currentSessionId.status === 200) {
          sessionData.sessionId = currentSessionId.data.sessionid
          sessionData.bcagent = currentSessionId.data.bcagent
        } else {
          log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'response', fields: currentSessionId })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'response', fields: currentSessionId })
          return currentSessionId
        }
        /** ** Get/Set Agent session **/

        const payLoadJson = {
          header: {
            sessionid: sessionData.sessionId
          },
          bcagent: sessionData.bcagent,
          sendermobilenumber: reqDataObj.mobile_number,
          // accountnumber: reqDataObj.accountnumber,
          // ifscode: reqDataObj.ifscode,
          // amount: reqDataObj.amount,
          amount: (reqDataObj.amount).split('.')[0],
          bcpartnerrefno: reqDataObj.request_number
        }

        const response = await apiXmlRequest.post(BANK_API_URL, { wrapIn: 'transactionotpreq', payLoad: payLoadJson }, { timeout: TIMEOUT_VALS.TSINGLE })
        // console.log('response', response)
        log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'apiresponse', fields: response.data })
        // Code for saving logs
        reqDataObj.bank_name = 'NSDL'
        reqDataObj.request_type = 'GENERATE_TRANSACTION_OTP'
        reqDataObj.request = JSON.stringify(payLoadJson)
        reqDataObj.response = typeof response.data == 'object' ? JSON.stringify(response.data) : `${response.data}`
        reqDataObj.url = BANK_API_URL
        reqDataObj.api_status = 200

        const savelogRes = await TransferController.saveApiLogDmt(reqDataObj)
        log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'saveApiLog', fields: savelogRes })
        return {
          status: 200,
          respcode: 10001,
          message: response.data.transactionotpres.OTPStatus,
          // otp_id: response.data.transactionotpres.Senderid
          otp_id: reqDataObj.request_number
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  // async encryptString (value) {
  //   try {
  //     const key = Buffer.from(ENC_PASS.substring(0, 32), 'utf8')
  //     const iv = Buffer.from(ENC_PASS.substring(0, 16), 'utf8')
  //     const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
  //     let encrypted = cipher.update(value, 'utf8', 'base64')
  //     encrypted += cipher.final('base64')
  //     return encrypted
  //   } catch (err) {
  //     console.error(err)
  //     return null
  //   }
  // }

  async customerVerifyOTP (reqDataObj, sessionRQ, connection) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'request', fields: reqDataObj })
      const headerReqId = await this.generateRequestID()
      const header = {
        chanid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'VERIFYCUSTONBOARDMOBILENO_VEROTP',
        requestid: headerReqId
      }
      const obj = {
        referenceid: reqDataObj.referenceid,
        otpreqid: reqDataObj.otp_id,
        otphash: await this.getOTPenc(reqDataObj.otp_pin)
        // otphash: 'ujJTh2rta8ItSm/1PYQGxq2GQZXtFEq1yHYhtsIztUi66uaVbfNG7IwX9eoQ817jy8UUeX7X3dMUVGTioLq0Ew=='
      }
      // console.log('obj', obj)
      // console.log('test', await this.gEtOTPenc())
      const encdata = await this.encryptString(JSON.stringify(obj))
      const payload = {
        channelid: CHANNEL_ID,
        partnerid: PARTNER_ID,
        servicetype: 'VERIFYCUSTONBOARDMOBILENO_VEROTP',
        encdata: encdata
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'requestObj', fields: { payload: payload, headers: header } })
      const response = await apiJsonRequest.post(BANK_API_URL_NEW + BANK_API_ENDPOINTS.VERIFY_OTP, payload, { headers: header })
      console.log('res', response.data)
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'responseObj', fields: response.data })

      // Code for saving logs
      reqDataObj.bank_name = 'NSDL'
      reqDataObj.request_type = 'VERIFY_CUST_OTP'
      reqDataObj.request = JSON.stringify({ ...payload, ...header })
      reqDataObj.response = JSON.stringify(response.data)
      reqDataObj.url = BANK_API_URL_NEW + BANK_API_ENDPOINTS.VERIFY_OTP
      reqDataObj.api_status = response.data.responsecode == '00' ? 200 : 400

      const savelogRes = await TransferController.saveApiLogDmt(reqDataObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'saveApiLog', fields: savelogRes })

      if (response.data.responsecode == '00') {
        return {
          status: 200,
          respcode: 10001,
          message: response.data.responsedata.response
          // token: response.data.responsedata.otpreqid
        }
      } else {
        return {
          status: 400,
          respcode: 10000,
          message: response.data.responsedata.response
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getOTPenc (otp) {
    // const otp = '123456'
    // Compute SHA-512 hash
    const hash = crypto.createHash('sha512')
    hash.update(otp, 'utf8') // Update with the OTP string (in UTF-8 encoding)
    const hashBytes = hash.digest() // Get the hash as a buffer

    // Convert to Base64
    const base64Hash = hashBytes.toString('base64')
    return base64Hash
  }
}

module.exports = {
  BANK: NSDLBank
}
