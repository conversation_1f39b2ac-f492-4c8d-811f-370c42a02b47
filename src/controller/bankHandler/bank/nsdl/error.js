const responseCode = {
  10000: 'Success',
  10001: 'Fail: NSDL API Error ',
  10002: 'Fail: NSDL Session Error ',
  10003: 'Fail: NSDL Session Details record not found ',
  10004: 'Fail: NSDL Undefined Response ',
  10005: 'Fail: NSDL Bank Api returns Failure ',
  10006: 'Fail: NSDL Add Remitter ',
  10007: 'Fail: NSDL Add Beneficiary ',
  10008: 'Fail: NSDL Refund send otp Failure ',
  10009: 'Fail: NSDL Refund verify send otp Failure ',
  10010: 'Fail: NSDL Sender details not found ',
  10011: 'Fail: NSDL Invalid input provided ',
  10012: 'Fail: NSDL Transfer failed ',
  10013: 'Awaiting response from bank',
  10014: 'Fail: NSDL Transfer Requery failed ',
  10025: 'Fail: NSDL Transfer Requery Unknow status ',
  10026: 'Fail: NSDL Account Already Exists With Diffrent Ifsc code ',
  10027: 'Fail: NSDL Agent Login Details Missing ',
  10028: 'Fail: NSDL Delete Beneficary ',
  10029: 'Fail: NSDL Beneficiary validation Failed ',
  10030: 'Fail: Transaction not found!',
  10031: 'Fail: NSDL Bank Timeout @',
  10032: 'Fail: NSDL KYC',
  10033: 'Fail: API Service Down',
  10034: 'Fail: Refund Failed try after sometime'
}

module.exports = { responseCode }
