module.exports = {
  ADD_REMITTER: {
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    otp: {
      required: true
    },
    state: {
      required: true
    },
    firstName: {
      required: true,
      paytmname: true
    },
    lastName: {
      required: true,
      paytmname: true
    },
    address1: {
      required: true,
      paytmaddress: true,
      minlength: 2,
      maxlength: 500
    },
    address2: {
      required: true,
      paytmaddress: true,
      minlength: 2,
      maxlength: 500
    },
    city: {
      required: true,
      alphanumericsinglespace: true,
      minlength: 1,
      maxlength: 100
    },
    country: {
      required: true,
      alphanumericsinglespace: true,
      minlength: 1,
      maxlength: 100
    },
    State: {
      required: true,
      alphanumericsinglespace: true,
      minlength: 1,
      maxlength: 100
    },
    pin: {
      required: true,
      indianpincode: true
    }
  },
  SEND_OTP_ADD_REMITTER: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  VALIDATE_BENEFICIARY_BEFORE_ADD: {
    accountNumber: {
      required: true,
      alphanumeric: true
    },
    bankName: {
      required: true
    },
    benIfsc: {
      required: true,
      indiabankifsc: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    txnReqId: {
      required: true,
      alphanumeric: true,
      maxlength: 20
    }
  },
  SEND_OTP_ADD_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  RESEND_OTP_ADD_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  ADD_BENEFICIARY: {
    accountNumber: {
      required: true,
      alphanumeric: true
    },
    bankName: {
      required: true
    },
    benIfsc: {
      required: true,
      indiabankifsc: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    name: {
      required: true,
      paytmname: true
    },
    nickName: {
      required: true,
      paytmname: true
    }
  },
  INIT_TRANSACTION: {
    amount: {
      required: true
    },
    beneficiaryId: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    txnReqId: {
      required: true,
      alphanumeric: true,
      maxlength: 20
    },
    mode: {
      required: true
    }
  },
  BULK_TRANSFER: {
    amount: {
      required: true
    },
    beneficiaryId: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    txnReqId: {
      required: true,
      alphanumeric: true,
      maxlength: 20
    },
    mode: {
      required: true
    }
  },
  DOUBLE_VERIFICATION: {
    txnReqId: {
      required: true,
      alphanumeric: true,
      maxlength: 20
    }
  },
  SEND_OTP_DELETE_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  GET_REMITTER_BALANCE: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  PRE_VALIDATE: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  VIEW_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  }
}
