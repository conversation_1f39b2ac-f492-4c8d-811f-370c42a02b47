const util = require('../../../../util/util')

const BANK_CONSTANTS = {
  BANK_API_URL: util.isProduction() ? 'https://pass-api.paytmbank.com/' : 'https://pass-api-ite.paytmbank.com',
  BANK_API_ENDPOINTS: {
    ADD_REMITTER: '/api/tops/remittance/v1/user/register',
    SEND_OTP_ADD_REMITTER: '/api/tops/remittance/v1/send-otp',
    VALIDATE_BENEFICIARY_BEFORE_ADD: '/api/tops/remittance/v2/penny-drop',
    ADD_BENEFICIARY: '/api/tops/remittance/v1/user/add-beneficiary',
    INIT_TRANSACTION: '/api/tops/remittance/v2/fund-transfer',
    BULK_TRANSFER: '/api/tops/remittance/v2/fund-transfer',
    DOUBLE_VERIFICATION: '/api/tops/remittance/v1/status',
    SEND_OTP_ADD_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
    SEND_OTP_DELETE_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
    RESEND_OTP_DELETE_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
    GET_REMITTER_BALANCE: '/api/tops/remittance/v1/user/amount-limit',
    PRE_VALIDATE: '/api/tops/remittance/v1/user/pre-validate',
    DELETE_BENEFICIARY: '/api/tops/remittance/v1/user/delete-beneficiary',
    VIEW_BENEFICIARY: '/api/tops/remittance/v1/user/beneficiaries'
  },
  BANK_API_ENDPOINTS_TIMEOUT: {
    ADD_REMITTER: 60000,
    SEND_OTP_ADD_REMITTER: 60000,
    VALIDATE_BENEFICIARY_BEFORE_ADD: 60000,
    SEND_OTP_ADD_BENEFICIARY: 60000,
    ADD_BENEFICIARY: 60000,
    INIT_TRANSACTION: 60000,
    BULK_TRANSFER: 60000,
    DOUBLE_VERIFICATION: 60000,
    SEND_OTP_DELETE_BENEFICIARY: 60000,
    RESEND_OTP_DELETE_BENEFICIARY: 60000,
    GET_REMITTER_BALANCE: 60000,
    PRE_VALIDATE: 60000,
    DELETE_BENEFICIARY: 60000,
    VIEW_BENEFICIARY: 60000
  },
  PAYMENT_MODES: {
    0: 'neft',
    2: 'imps'
  },
  TRANSFER_MODES: {
    NEFT: 'neft',
    IMPS: 'imps'
  },
  TRANSFER_DETAILS_MODE: {
    0: 'all',
    1: 'neft',
    2: 'imps'
  },
  TRANSACTION_TYPE: {
    B: 'Success',
    R: 'Rejection',
    C: 'Refund',
    F: 'Failure',
    BV: 'Bene Validation'
  },
  STATUS: {
    0: 'FAILURE',
    1: 'SUCCESS',
    '-1': 'TIMEOUT',
    91: 'UNKNOWN'
  },
  REMITTERSTATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  BENEFICIARY_STATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  IMPS_STATUS: {
    0: 'pending',
    1: 'active'
  },
  TRANSSTATUS: {
    0: 'Pending',
    1: 'Process',
    2: 'Credited',
    3: 'Rejected or Refund Processing',
    4: 'Refund Success or refund Processed',
    5: 'remitter Registration',
    6: 'failure',
    '-1': 'Unknown'
  },
  PAYMENTSTATUS: {
    0: 'pending',
    1: 'processing',
    2: 'credited',
    3: 'rejected',
    4: 'refund process',
    6: 'failure'
  },
  BANK_LIMIT: 25000,
  transfer_status_code: {
    status_200: {
      0: 'success',
      1023: 'unknown',
      1030: 'failure',
      1032: 'failure',
      1033: 'failure',
      1043: 'duplicate',
      1061: 'failure',
      1062: 'failure',
      1064: 'failure',
      1065: 'failure',
      1068: 'failure',
      1105: 'failure',
      1106: 'failure',
      1107: 'pending',
      1108: 'failure',
      1207: 'failure',
      1304: 'pending',
      2003: 'unknown',
      8003: 'pending'
    },
    status_400: {
      0: 'failure',
      1039: 'failure',
      1070: 'failure',
      1208: 'failure'
    },
    status_401: {
      2001: 'failure',
      2004: 'failure'
    },
    status_500: {
      2002: 'unknown'
    }
  },
  verify_bene_status_code: {
    status_200: {
      0: 'success',
      1023: 'unknown',
      1030: 'failure',
      1032: 'failure',
      1033: 'failure',
      1043: 'duplicate',
      1064: 'failure',
      1065: 'failure',
      1103: 'failure',
      1104: 'failure',
      1107: 'pending',
      1108: 'failure',
      2003: 'unknown',
      8003: 'pending'
    },
    status_400: {
      0: 'failure'
    },
    status_401: {
      2001: 'failure'
    },
    status_500: {
      2002: 'unknown'
    }
  },
  HIDE_REMARKS: { 1067: 'Transaction Failed~[INSB]' } // response_code
}

module.exports = {
  BANK_CONSTANTS: BANK_CONSTANTS
}
