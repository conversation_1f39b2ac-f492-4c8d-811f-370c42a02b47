const { TRANSFER_MODES, BANK_API_ENDPOINTS, CLIENT_ID, CLIENT_SECRET, PROXY_URL } = require('./config').BANK_CONSTANTS
const log = require('../../../../util/log')
const { propExist, de, getHash512 } = require('../../bankcommon')
const bankOnBoardDetails = require('../../bankOnBoardingDetails')
const errorMsg = require('../../../../util/error')
const bankErrorMsg = require('./error')
const paytmrules = require('./inputRule')
const apiValidator = require('../../apiValidator')
const AirtelRequest = require('./airtelrequest')
const sms = require('../../../../util/sms')
const commonFunction = require('../../../../util/common.js')
const TransferController = require('../../../transfers/transfersController')

class AIRTELBank {
  constructor (conn = null) {
    this.conn = conn
    this.sessionData = null
  }

  async addRemitterSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber,
        firstName: reqDataObj.firstName,
        lastName: reqDataObj.lastName,
        dob: reqDataObj.dob || '',
        pincode: reqDataObj.pincode,
        address: reqDataObj.senderaddress1
      }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryReSendOtp', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      // final payload format
      const payLoadJson = {
        custMsisdn: customPayload.customerMobile,
        firstName: customPayload.firstName,
        lastName: customPayload.lastName,
        dob: customPayload.dob,
        partnerId: currentSessionId.partnerId,
        agentId: currentSessionId.agentId,
        pinCode: customPayload.pincode,
        address: customPayload.address.replace(/[^a-z0-9,-/]+/gi, ' ')
      }

      const hashDataString = [payLoadJson.custMsisdn, payLoadJson.firstName, payLoadJson.dob, payLoadJson.pinCode, currentSessionId.salt].map((e) => e.toString()).join('#')

      payLoadJson.hash = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'SEND_OTP_ADD_REMITTER',
        payLoadJson,
        {
          channel: 'EXTP',
          fesessionid: this.getUnique('RMO')
        })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_SEND_OTP_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, description } = apiResponse.response.meta

      if (status != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      return {
        status: 200,
        respcode: 1000,
        message: description,
        request: payLoadJson,
        response: apiResponse.response
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitterReSendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { ...reqDataObj.request }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getSessionId', fields: { error: currentSessionId } })
        return currentSessionId
      }

      // final payload format
      const payLoadJson = {
        custMsisdn: customPayload.custMsisdn,
        firstName: customPayload.firstName,
        lastName: customPayload.lastName,
        dob: customPayload.dob,
        partnerId: currentSessionId.partnerId,
        agentId: currentSessionId.agentId,
        pinCode: customPayload.pinCode,
        address: customPayload.address.replace(/[^a-z0-9,-/]+/gi, ' ')
      }

      const hashDataString = [payLoadJson.custMsisdn, payLoadJson.firstName, payLoadJson.dob, payLoadJson.pinCode, currentSessionId.salt].map((e) => e.toString()).join('#')

      payLoadJson.hash = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'SEND_OTP_ADD_REMITTER',
        payLoadJson,
        {
          channel: 'EXTP',
          fesessionid: this.getUnique('RMO')
        })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_RESEND_OTP_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, description } = apiResponse.response.meta

      if (status != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      return {
        status: 200,
        respcode: 1000,
        message: description,
        request: payLoadJson,
        response: apiResponse.response
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitter (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      const customPayload = {
        otp: reqDataObj.otp,
        customerMobile: reqDataObj.sendermobilenumber
      }

      const validationRes = await this.validateAPIInput('ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const payLoadJson = {
        otp: customPayload.otp,
        partnerId: currentSessionId.partnerId
      }

      const urlParams = {
        CustomerMobileNumber: customPayload.customerMobile
      }

      const hashDataString = [payLoadJson.otp, payLoadJson.partnerId, currentSessionId.salt].map((e) => e.toString()).join('#')

      payLoadJson.hash = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'ADD_REMITTER',
        payLoadJson,
        {
          channel: 'EXTP',
          fesessionid: this.getUnique('RMA')
        },
        urlParams)

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'bankResponse', fields: apiResponse })
      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, description } = apiResponse.response.meta

      if (status != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      return {
        status: 200,
        respcode: 1000,
        message: description,
        remitter_name: `${reqDataObj.firstName} ${reqDataObj.lastName}`,
        senderid: customPayload.customerMobile
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiarySendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = {
        name: reqDataObj.beneficiary_name,
        accountNumber: reqDataObj.account_number,
        bankName: reqDataObj.bank_name,
        benIfsc: reqDataObj.ifsc_code,
        beneMobileNumber: reqDataObj.ben_mobile_number,
        isVerified: reqDataObj.isVerified || true, // to do
        customerMobile: reqDataObj.mobile_number
      }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // final payload format
      const payLoadJson = {
        beneName: customPayload.name,
        beneAccount: customPayload.accountNumber,
        beneBankName: customPayload.bankName,
        beneIfscCode: customPayload.benIfsc,
        beneMobileNumber: customPayload.beneMobileNumber,
        isVerified: customPayload.isVerified,
        agentId: currentSessionId.agentId,
        partnerId: currentSessionId.partnerId
      }

      const urlParams = {
        CustomerMobileNumber: customPayload.customerMobile
      }
      // partnerId#beneAccount#beneIfscCode#Salt
      const hashDataString = [currentSessionId.partnerId, customPayload.accountNumber, customPayload.benIfsc, currentSessionId.salt].map((e) => e.toString()).join('#')

      payLoadJson.hash = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'SEND_OTP_ADD_BENEFICIARY',
        payLoadJson,
        {
          'cache-control': 'no-cache',
          channel: 'EXTP',
          fesessionid: this.getUnique('BEO')
        },
        urlParams)

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_SEND_OTP_ADD_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'bankResponse', fields: apiResponse })
      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, description } = apiResponse.response.meta

      if (status != 0 || typeof apiResponse.response.data != 'object') {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      return {
        status: 200,
        respcode: 1000,
        message: description,
        request: payLoadJson,
        response: apiResponse.response
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiaryReSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { ...reqDataObj }

      const validationRes = await this.validateAPIInput('RESEND_OTP_ADD_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryReSendOtp', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryReSendOtp', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // final payload format
      const payLoadJson = {
        beneName: customPayload.beneName,
        beneAccount: customPayload.beneAccount,
        beneBankName: customPayload.beneBankName,
        beneIfscCode: customPayload.beneIfscCode,
        beneMobileNumber: customPayload.beneMobileNumber,
        isVerified: customPayload.isVerified,
        agentId: currentSessionId.agentId,
        partnerId: currentSessionId.partnerId
      }

      const urlParams = {
        CustomerMobileNumber: reqDataObj.mobile_number
      }

      const hashDataString = [currentSessionId.partnerId, customPayload.beneAccount, customPayload.beneIfscCode, currentSessionId.salt].map((e) => e.toString()).join('#')

      payLoadJson.hash = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'RESEND_OTP_ADD_BENEFICIARY',
        payLoadJson,
        {
          'cache-control': 'no-cache',
          channel: 'EXTP',
          fesessionid: this.getUnique('BEO')
        },
        urlParams)

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_RESEND_OTP_ADD_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryReSendOtp', type: 'bankResponse', fields: apiResponse })
      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, description } = apiResponse.response.meta

      if (status != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      return {
        status: 200,
        respcode: 1000,
        message: description,
        request: payLoadJson,
        response: apiResponse.response
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: reqDataObj })
    try {
      // if bene already exist at bank end
      // if (reqDataObj.response &&
      //   reqDataObj.response.data &&
      //   reqDataObj.response.data.beneExist == true) {
      //   return await this.syncBeneficaryId(reqDataObj, sessionRQ, connection)
      // }

      // const customPayload = {
      //   otp: reqDataObj.otp,
      //   customerMobile: reqDataObj.sendermobilenumber,
      //   BeneficiaryId: reqDataObj.response.data.beneId // to do
      // }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      // const validationRes = await this.validateAPIInput('ADD_BENEFICIARY', customPayload)
      // if (validationRes.status !== 200) {
      //   log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'validateAPIInput', fields: validationRes })
      //   return validationRes
      // }

      /**
      * Get/Set Agent session
      */
      // const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      // if (currentSessionId.status != 200) {
      //   log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'getSessionId', fields: currentSessionId })
      //   return currentSessionId
      // }
      /** ** Get/Set Agent session **/

      // final payload format
      // const payLoadJson = {
      //   otp: customPayload.otp,
      //   partnerId: currentSessionId.partnerId
      // }

      // const urlParams = {
      //   BeneficiaryId: customPayload.BeneficiaryId,
      //   CustomerMobileNumber: customPayload.customerMobile
      // }
      // otp#partnerId#Salt
      // const hashDataString = [customPayload.otp, currentSessionId.partnerId, currentSessionId.salt].map((e) => e.toString()).join('#')

      // payLoadJson.hash = getHash512(hashDataString)

      // // OP_NAME, PARAMS, { TOKENDATA }
      // const requestStart = process.hrtime()
      // const apiResponse = await AirtelRequest.post('ADD_BENEFICIARY',
      //   payLoadJson,
      //   {
      //     channel: 'EXTP',
      //     fesessionid: this.getUnique('ABF')
      //   },
      //   urlParams)

      // try {
      //   const requestEnd = process.hrtime(requestStart)
      //   const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      //   console.log('API_[' + sessionRQ + ']_AIRTEL_ADD_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      // } catch (error) {
      //   console.log(error)
      // }

      // log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'bankResponse', fields: apiResponse })

      // if (apiResponse.status != 200) {
      //   apiResponse.status = 400
      //   return apiResponse
      // }
      // const { status, description } = apiResponse.response.meta
      // if (status != 0) {
      //   return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      // }

      return {
        status: 200,
        respcode: 1000,
        message: 'Success',
        senderid: reqDataObj.sendermobilenumber,
        // beneficiaryid: reqDataObj.BeneficiaryId
        beneficiaryid: reqDataObj.beneficiaryId
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiarySendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'customPayload', fields: customPayload })

      const validationRes = await this.validateAPIInput('SEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiaryReSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'customPayload', fields: customPayload })

      const validationRes = await this.validateAPIInput('RESEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */
      return {
        status: 200,
        message: bankErrorMsg.responseCode[10000],
        respcode: 10000
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  // Single Transaction
  async initTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'request', fields: reqDataObj })
    

    const { ifsc_code, bank_name, request_number, userid, bank_charges } = reqDataObj.request_data[reqDataObj.request_number]
    let  benMobileNumber = reqDataObj.beneMobNo
    if((benMobileNumber == null) || (benMobileNumber == '0') || (benMobileNumber == 'undefined' )){
      benMobileNumber = reqDataObj.senderid  || reqDataObj.MerchantMobileNo
    }

    try {
      const customPayload = {
        amount: reqDataObj.amount,
        customerMobile: reqDataObj.senderid,
        name: reqDataObj.remitter_name,
        custPincode: reqDataObj.pincode,
        custAddress: reqDataObj.address,
        bankName: bank_name,
        ifsc: ifsc_code,
        beneAccNo: reqDataObj.beneAccNo,
        beneMobNo: benMobileNumber,
        beneficiaryId: reqDataObj.receiverid
      }

      // const validationRes = await this.validateAPIInput('INIT_TRANSACTION', customPayload)
      // if (validationRes.status !== 200) {
      //   log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'validateAPIInput', fields: validationRes })
      //   // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'validateAPIInput', fields: validationRes })
      //   return validationRes
      // }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const headersData = {
        token: reqDataObj.token
      }

      const requestData = {
        partnerId: currentSessionId.partnerId,
        agentId: reqDataObj.ma_user_id,
        customerId: customPayload.customerMobile,
        amount: customPayload.amount,
        bankName: customPayload.bankName,
        ifsc: customPayload.ifsc,
        beneAccNo: customPayload.beneAccNo,
        beneMobNo: customPayload.beneMobNo,
        externalRefNo: request_number  
      }

      const apiResult = await AirtelRequest.proxyRequest('imps', requestData, headersData)
      console.log('--apiResult --', apiResult)
      const parsedData = JSON.parse(apiResult)
      console.log('parsedData>>>>',parsedData)
      const response = parsedData.airtelResponse
      const fields = { ma_user_id: reqDataObj.ma_user_id || 0, userid: userid || 0, bank_name: "AIRTEL", mobile_number: reqDataObj.senderid || 0, sessionRQ: sessionRQ || 0, request: JSON.stringify({ ...requestData, ...headersData }) || '', response: JSON.stringify(response) || '', api_status: response.meta.status == 0 ? 200 : 400, request_type: 'imps', url: PROXY_URL }
      const result = await TransferController.saveApiLogDmt(fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'result', fields: result })

      // const { firstName: custFirstName, lastName: custLastName } = commonFunction.splitRemitterNameWithSpace(customPayload.name)

      // const payLoadJson = {
      //   ver: '2.0',
      //   feSessionId: this.getUnique('TRF'),
      //   channel: 'EXTP',
      //   apiMode: 'P',
      //   partnerId: currentSessionId.partnerId,
      //   customerId: customPayload.customerMobile,
      //   amount: customPayload.amount,
      //   bankName: customPayload.bankName,
      //   ifsc: customPayload.ifsc,
      //   beneAccNo: customPayload.beneAccNo,
      //   beneMobNo: customPayload.beneMobNo,
      //   externalRefNo: request_number,
      //   reference1: reqDataObj.ma_user_id,
      //   reference2: reqDataObj.senderid,
      //   reference3: reqDataObj.remarks,
      //   reference4: '',
      //   reference5: '',
      //   custFirstName: custFirstName,
      //   custLastName: custLastName,
      //   custPincode: customPayload.custPincode,
      //   custAddress: customPayload.custPincode,
      //   custDob: customPayload.custDob || '',
      //   stateCode: customPayload.stateCode || ''

      // }

      // const hashDataString = [payLoadJson.channel, payLoadJson.partnerId, payLoadJson.customerId, payLoadJson.amount, payLoadJson.ifsc, payLoadJson.beneAccNo, currentSessionId.salt].map((e) => e.toString()).join('#')

      // payLoadJson.hash = getHash512(hashDataString)

      // const requestStart = process.hrtime()
      // const apiResponse = await AirtelRequest.post(
      //   'INIT_TRANSACTION',
      //   payLoadJson,
      //   {},
      //   {})

      // try {
      //   const requestEnd = process.hrtime(requestStart)
      //   const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      //   console.log('API_[' + sessionRQ + ']_AIRTEL_INIT_TRANSACTION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      // } catch (error) {
      //   console.log(error)
      // }

      // log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'bankResponse', fields: apiResponse })
      // // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'bankResponse', fields: apiResponse })

      // if (apiResponse.status != 200) {
      //   apiResponse.status = 400
      //   return apiResponse
      // }

      // const { code, messageText, amount, externalRefNo, rrn, charges, tranId, beneName, feSessionId } = apiResponse.response

      /* status : Success  */
      if (response.meta.status == 0) {
        const { responseTimestamp, externalRefNo, tranId, rrn, amount, charges, beneName } = response.data
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: 1,
              request_number: externalRefNo,
              amount: amount,
              bank_service_charges: charges,
              bank_gst: 0,
              bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
              bankrefno: tranId,
              // bankTransactionid: feSessionId,
              bankTransactionid: '',
              transactionId: reqDataObj.ma_transfers_id,
              message: response.meta.description || '',
              rrn: rrn,
              benename: beneName
            }]
          }
        }
      }
      /* status : Failure  */
      if (response.meta.status == 1) {
        return {
          status: 200,
          respcode: 1012,
          message: errorMsg.responseCode[1012],
          bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: 0,
              request_number: request_number,
              amount: reqDataObj.amount,
              bank_service_charges: bank_charges,
              bank_gst: 0,
              bank_gross_amount: ((parseFloat(reqDataObj.amount) || 0) + (parseFloat(bank_charges) || 0)),
              bankrefno: '',
              // bankTransactionid: feSessionId,
              bankTransactionid: '',
              transactionId: reqDataObj.ma_transfers_id,
              message: response.meta.description || bankErrorMsg.responseCode[10001],
              rrn: '',
              benename: reqDataObj.receivername || ''
            }]
          }
        }
      }
      /* status : Timeout  */
      if (response.meta.status== 2) {
        const { responseTimestamp, externalRefNo, tranId, rrn, amount, charges, beneName } = response.data
        return {
          status: 200,
          respcode: 10013,
          message: bankErrorMsg.responseCode[10013],
          bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: -1,
              request_number: externalRefNo,
              amount: amount,
              bank_service_charges: charges,
              bank_gst: 0,
              bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
              bankrefno: tranId,
              // bankTransactionid: feSessionId,
              bankTransactionid: '',
              transactionId: reqDataObj.ma_transfers_id,
              message: response.meta.description || bankErrorMsg.responseCode[10001],
              rrn: rrn,
              benename: beneName
            }]
          }
        }
      }
      /* Default Case */
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        bulkData: {
          finalStatus: 1,
          transferBulkData: [{
            status: -1,
            message: response.meta.description || bankErrorMsg.responseCode[10013],
            request_number: request_number
          }]
        }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async bulkTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'request', fields: reqDataObj })
    const resultTransactionArr = []
    // process.exit()
    try {
      if (!reqDataObj && !('request_data' in reqDataObj) && Object.keys(reqDataObj.request_data).length == 0) {
        return { status: 400, respcode: 10034, message: bankErrorMsg.responseCode[10034], transaction_status: 0 }
      }

      /** ** Get/Set Agent session **/
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      /** ** Get/Set Agent session **/

      const requestData = reqDataObj.request_data
      const customPayloadList = []
      const transferResponsePromiseObject = {}

      for (const transferDataKey in requestData) {
        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'requestData', fields: requestData })
        const { ifsc_code, bank_name, transfer_amount, request_number } = reqDataObj.request_data[transferDataKey]
        const customPayload = {
          amount: transfer_amount,
          customerMobile: reqDataObj.senderid,
          name: reqDataObj.remitter_name,
          custPincode: reqDataObj.pincode,
          custAddress: reqDataObj.address,
          bankName: bank_name,
          ifsc: ifsc_code,
          beneAccNo: reqDataObj.beneAccNo,
          beneMobNo: reqDataObj.beneMobNo,
          beneficiaryId: reqDataObj.receiverid,
          txnReqId: request_number
        }

        const validationRes = await this.validateAPIInput('BULK_TRANSFER', customPayload)
        if (validationRes.status !== 200) {
          log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'validateAPIInput', fields: validationRes })
          return validationRes
        }
        customPayloadList.push(customPayload)
      }

      customPayloadList.forEach(customPayload => {
        const { firstName: custFirstName, lastName: custLastName } = commonFunction.splitRemitterNameWithSpace(customPayload.name)
        const payLoadJson = {
          ver: '2.0',
          feSessionId: this.getUnique('TRF'),
          channel: 'EXTP',
          apiMode: 'P',
          partnerId: currentSessionId.partnerId,
          customerId: customPayload.customerMobile,
          amount: customPayload.amount,
          bankName: customPayload.bankName,
          ifsc: customPayload.ifsc,
          beneAccNo: customPayload.beneAccNo,
          beneMobNo: customPayload.beneMobNo,
          externalRefNo: customPayload.txnReqId,
          reference1: reqDataObj.ma_user_id,
          reference2: reqDataObj.senderid,
          reference3: reqDataObj.remarks,
          reference4: '',
          reference5: '',
          custFirstName: custFirstName,
          custLastName: custLastName,
          custPincode: customPayload.custPincode,
          custAddress: customPayload.custPincode,
          custDob: customPayload.custDob || '',
          stateCode: customPayload.stateCode || ''
        }

        const hashDataString = [payLoadJson.channel, payLoadJson.partnerId, payLoadJson.customerId, payLoadJson.amount, payLoadJson.ifsc, payLoadJson.beneAccNo, currentSessionId.salt].map((e) => e.toString()).join('#')

        payLoadJson.hash = getHash512(hashDataString)

        const apiResponse = AirtelRequest.post(
          'BULK_TRANSFER',
          payLoadJson,
          {},
          {})

        transferResponsePromiseObject[customPayload.txnReqId] = apiResponse

        try {
          console.log('API_[' + sessionRQ + ']_AIRTEL_BULK_TRANSFER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']')
        } catch (error) {
          console.log(error)
        }
      })

      const requestStart = process.hrtime()
      const _responsePromiseList = await Promise.all(Object.values(transferResponsePromiseObject))
      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_BULK_TRANSFER_REQUEST_[' + JSON.stringify(customPayloadList) + ']_RESPONSE_[' + JSON.stringify(_responsePromiseList) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'bankResponse', fields: _responsePromiseList })

      const transactionRequestIds = Object.keys(transferResponsePromiseObject)

      const transactionResultList = []
      _responsePromiseList.forEach((apiResponse, index) => {
        let transactionResult = {}

        if (apiResponse.status != 200) {
          transactionResult = {
            status: -1,
            message: bankErrorMsg.responseCode[10013],
            finalStatus: 0,
            request_number: transactionRequestIds[index]
          }
        }

        const { code, messageText, amount, externalRefNo, rrn, charges, tranId, beneName, feSessionId } = apiResponse.response
        /* status : unknow  */
        if (!code) {
          transactionResult = {
            status: -1,
            message: bankErrorMsg.responseCode[10013],
            finalStatus: 0,
            request_number: transactionRequestIds[index]
          }
        }
        /* status : Success  */
        if (code == 0) {
          transactionResult = {
            status: 1,
            request_number: externalRefNo,
            amount: amount,
            bank_service_charges: charges,
            bank_gst: 0,
            bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
            bankrefno: tranId,
            bankTransactionid: feSessionId,
            transactionId: reqDataObj.ma_transfers_id,
            message: messageText,
            rrn: rrn,
            benename: beneName,
            finalStatus: 1
          }
        }
        /* status : Failure  */
        if (code == 1) {
          transactionResult = {
            status: 0,
            request_number: externalRefNo || transactionRequestIds[index],
            amount: amount,
            bank_service_charges: charges,
            bank_gst: 0,
            bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
            bankrefno: tranId,
            bankTransactionid: feSessionId,
            transactionId: reqDataObj.ma_transfers_id,
            message: messageText || bankErrorMsg.responseCode[10001],
            rrn: rrn,
            benename: beneName,
            finalStatus: 1
          }
        }
        /* status : Timeout  */
        if (code == 2) {
          transactionResult = {
            status: -1,
            request_number: externalRefNo,
            amount: amount,
            bank_service_charges: charges,
            bank_gst: 0,
            bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
            bankrefno: tranId,
            bankTransactionid: feSessionId,
            transactionId: reqDataObj.ma_transfers_id,
            message: messageText || bankErrorMsg.responseCode[10001],
            rrn: rrn,
            benename: beneName,
            finalStatus: 1
          }
        }

        transactionResultList.push(transactionResult)
      })

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        bulkData: {
          finalStatus: 1,
          transferBulkData: transactionResultList
        }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async doubleVerify (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: reqDataObj })

    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const customerController = require('../../../customer/customerController')

      const customPayload = {
        externalRefNo: reqDataObj.bcpartnerrefno
      }

      const validationRes = await this.validateAPIInput('DOUBLE_VERIFICATION', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const sessioTokenRes = await customerController.airtelGenerateSessionTokenApi(reqDataObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'sessioTokenRes', fields: sessioTokenRes })
      if (sessioTokenRes.status != 200) return { status: 400, respcode: 1001, message: sessioTokenRes.message, action_code: 1001 }
      const headersData = {
        token: sessioTokenRes.data.token
      }
 
      const requestData = {
        externalRefNo: customPayload.externalRefNo
      }
 
      const apiResult = await AirtelRequest.proxyRequest('imps-enquiry', requestData, headersData)
      const parsedData =  JSON.parse(apiResult)
      console.log('--apiResult --', parsedData )
      const response = parsedData.airtelResponse
 
      if (response.meta.status == 0) {
      const { responseTimestamp, externalRefNo, tranId, rrn, amount, charges } = response.data
        return {
          status: 1,
          request_number: externalRefNo,
          amount: amount,
          bank_service_charges: charges,
          bank_gst: 0,
          bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
          bankrefno: tranId,
          bankTransactionid: '',
          transactionId: reqDataObj.ma_transfers_id,
          message: response.meta.description,
          rrn: rrn,
          benename: ''
        }
      }
      /* status : Failure  */
      if (response.meta.status == 1) {
        return {
          status: 0,
          request_number: customPayload.externalRefNo,
          amount: '',
          bank_service_charges: '',
          bank_gst: 0,
          bank_gross_amount: 0,
          bankrefno: '',
          bankTransactionid: '',
          transactionId: reqDataObj.ma_transfers_id,
          message: response.meta.description || bankErrorMsg.responseCode[10012],
          rrn: '',
          benename: '',
          autorefundcredit: true
        }
      }
      /* status : Timeout  */
      if (response.meta.status == 2) {
        const { responseTimestamp, externalRefNo, tranId, rrn, amount, charges } = response.data
        return {
          status: -1,
          request_number: externalRefNo,
          amount: amount,
          bank_service_charges: charges,
          bank_gst: 0,
          bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
          bankrefno: tranId,
          bankTransactionid: '',
          transactionId: reqDataObj.ma_transfers_id,
          message: response.meta.description || bankErrorMsg.responseCode[10012],
          rrn: rrn,
          benename: ''
        }
      }
      /* Default Case */
      return {
        status: -1,
        message: response.meta.description || bankErrorMsg.responseCode[10013],
        request_number: reqDataObj.bcpartnerrefno
      }

      // const payLoadJson = {
      //   ver: '1.0', // new changes
      //   feSessionId: this.getUnique('DVF'),
      //   channel: 'EXTP',
      //   partnerId: currentSessionId.partnerId,
      //   externalRefNo: customPayload.externalRefNo
      // }

      // const hashDataString = [payLoadJson.channel, currentSessionId.partnerId, payLoadJson.externalRefNo, currentSessionId.salt].map((e) => e.toString()).join('#')

      // payLoadJson.hash = getHash512(hashDataString)

      // // OP_NAME, PARAMS, { TOKENDATA }
      // const requestStart = process.hrtime()
      // const apiResponse = await AirtelRequest.post(
      //   'DOUBLE_VERIFICATION',
      //   payLoadJson,
      //   {},
      //   {})

      // try {
      //   const requestEnd = process.hrtime(requestStart)
      //   const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      //   console.log('API_[' + sessionRQ + ']_AIRTEL_DOUBLE_VERIFICATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      // } catch (error) {
      //   console.log(error)
      // }

      // log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'bankResponse', fields: apiResponse })
      // // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'bankResponse', fields: apiResponse })

      // if (apiResponse.status != 200) {
      //   apiResponse.status = 400
      //   return apiResponse
      // }

      // const { code, messageText, amount, externalRefNo, rrn, charges, tranId, beneName, feSessionId } = apiResponse.response

      // /* status : Success  */
      // if (code == 0) {
      //   return {
      //     status: 1,
      //     request_number: externalRefNo,
      //     amount: amount,
      //     bank_service_charges: charges,
      //     bank_gst: 0,
      //     bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
      //     bankrefno: tranId,
      //     bankTransactionid: feSessionId,
      //     transactionId: reqDataObj.ma_transfers_id,
      //     message: messageText,
      //     rrn: rrn,
      //     benename: beneName
      //   }
      // }
      // /* status : Failure  */
      // if (code == 1) {
      //   return {
      //     status: 0,
      //     request_number: externalRefNo || customPayload.externalRefNo,
      //     amount: amount,
      //     bank_service_charges: charges,
      //     bank_gst: 0,
      //     bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
      //     bankrefno: tranId,
      //     bankTransactionid: feSessionId,
      //     transactionId: reqDataObj.ma_transfers_id,
      //     message: bankErrorMsg.responseCode[10012],
      //     rrn: rrn,
      //     benename: beneName,
      //     autorefundcredit: true
      //   }
      // }
      // /* status : Timeout  */
      // if (code == 2) {
      //   return {
      //     status: -1,
      //     request_number: externalRefNo,
      //     amount: amount,
      //     bank_service_charges: charges,
      //     bank_gst: 0,
      //     bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
      //     bankrefno: tranId,
      //     bankTransactionid: feSessionId,
      //     transactionId: reqDataObj.ma_transfers_id,
      //     message: messageText,
      //     rrn: rrn,
      //     benename: beneName
      //   }
      // }
      // /* Default Case */
      // return {
      //   status: -1,
      //   message: messageText || bankErrorMsg.responseCode[10013],
      //   request_number: reqDataObj.bcpartnerrefno
      // }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }
  async refund () {
    log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'request', fields: arguments })
    try {
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'error', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async verifyOTPRefund () {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'request', fields: arguments })
    try {
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'error', fields: JSON.stringify(error) })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async preValidateRemitter (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber, remitter_name: reqDataObj.remitter_name }

      const validationRes = await this.validateAPIInput('PRE_VALIDATE', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // const hashDataString = [BANK_API_ENDPOINTS.PRE_VALIDATE.replace('{CustomerMobileNumber}', customPayload.customerMobile), currentSessionId.salt].map((e) => e.toString()).join('#')
      // production changes
      const hashDataString = [`/partners/remittance-services/api/v1/customers/${customPayload.customerMobile}`, currentSessionId.salt].map((e) => e.toString()).join('#')

      const hashedStr = getHash512(hashDataString)

      const urlParams = {
        CustomerMobileNumber: customPayload.customerMobile
      }

      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.get(
        'PRE_VALIDATE',
        {},
        {
          channel: 'EXTP',
          fesessionid: this.getUnique('PVR'),
          hash: hashedStr,
          partnerid: currentSessionId.partnerId
        },
        urlParams
      )

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: 'API_[' + sessionRQ + ']_NSDL_AGENT_DETAIL_REQUEST_[' + JSON.stringify(urlParams) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms' })
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, code, description } = apiResponse.response.meta

      if (status != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      /* Customer Already Registered */
      if (status == 0 &&
        code == '000' &&
        description == 'This is Registered Customer'
      ) {
        return {
          status: 200,
          respcode: 1000,
          message: description,
          senderid: customPayload.customerMobile,
          remitter_name: customPayload.remitter_name
        }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async syncRemitterId (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'request', fields: arguments })
    try {
      //
      const remitterDetails = await this.getRemitterDetails(reqDataObj)
      return remitterDetails
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'error', fields: error })
    }
  }

  async syncBeneficaryId (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const validationRes = await this.validateAPIInput('VIEW_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      // final payload format
      const urlParams = {
        CustomerMobileNumber: customPayload.customerMobile
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      const hashDataString = [BANK_API_ENDPOINTS.VIEW_BENEFICIARY.replace('{CustomerMobileNumber}', customPayload.customerMobile), currentSessionId.salt].map((e) => e.toString()).join('#')

      const hashedStr = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.get(
        'VIEW_BENEFICIARY',
        {},
        {
          channel: 'EXTP',
          fesessionid: this.getUnique('VBF'),
          hash: hashedStr,
          partnerid: currentSessionId.partnerId
        },
        urlParams)

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_VIEW_BENEFICIARY_REQUEST_[' + JSON.stringify(urlParams) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { status, description } = apiResponse.response.meta

      if (status != 0 || typeof apiResponse.response.data != 'object') {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + description || '' }
      }

      const { beneList } = apiResponse.response.data

      if (beneList.constructor.name != 'Array') {
        return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
      }

      if (beneList.length == 0) {
        return { status: 400, respcode: 10007, message: bankErrorMsg.responseCode[10007] + '::: ' + 'Bene not found !' }
      }

      const beneficiary = beneList.filter((bene) => bene.beneAccount == reqDataObj.accountnumber)

      if (beneficiary.length == 0) {
        return { status: 400, respcode: 10007, message: bankErrorMsg.responseCode[10007] + '::: ' + 'Bene not found !' }
      }

      return {
        senderid: customPayload.customerMobile,
        beneficiaryid: beneficiary[0].beneId,
        maBeneficiaryId: reqDataObj.beneficiaryId,
        status: 200,
        respcode: 1000,
        message: bankErrorMsg.responseCode[10000]
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getSessionId (maUserId, maBankOnBoardingId, prevConn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'request', fields: { maUserId, maBankOnBoardingId } })
    try {
      const sessionData = await bankOnBoardDetails.getSessionDetails(null, { ma_user_id: maUserId, ma_bank_on_boarding_id: maBankOnBoardingId }, prevConn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'response', fields: sessionData })
      if (sessionData.status !== 200 && Object.keys(sessionData.data).length == 0) {
        return { status: 400, respcode: 10003, message: bankErrorMsg.responseCode[10003] }
      }
      const tmpSessionData = sessionData.data[0]
      const locationidStr = tmpSessionData.locationid
      if (!de(locationidStr) || locationidStr.indexOf('::') === -1) {
        return { status: 400, respcode: 10027, message: bankErrorMsg.responseCode[10027] }
      }

      const secretLoginArr = locationidStr.split('::')
      // const partnerId = secretLoginArr[0]
      const partnerId = '**********' // hardcoded partnerId
      const partnerSalt = secretLoginArr[1]
      const agentId = secretLoginArr[2]

      const hashData = { status: 200, respcode: 1000, partnerId, salt: partnerSalt, agentId }
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'hashData', fields: { accessTokenData: 'accessTokenData', hashData } })
      return hashData
    } catch (error) {
      console.log(error)
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async validateBeneficiaryBeforeAdd (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'request', fields: reqDataObj })
    try {
      // set penny drop amount
      reqDataObj.amount = 1
      // initTransaction
      const requestNumber = reqDataObj.request_number
      reqDataObj.request_data = {}
      reqDataObj.request_data[requestNumber] = {
        ifsc_code: reqDataObj.ifsc_code,
        bank_name: reqDataObj.bank_name,
        request_number: requestNumber
      }
      const transactionResponse = await this.initTransaction(reqDataObj, sessionRQ, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'initTransaction-response', fields: transactionResponse })

      if (transactionResponse.status != 200) {
        return transactionResponse
      }
      const { bulkData } = transactionResponse

      if (bulkData.transferBulkData.length == 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      return bulkData.transferBulkData[0]
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async validateAPIInput (actionType, reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateAPIInput', type: 'request', fields: { validateAPIInput: 'validateAPIInput', actionType, reqDataObj } })
    const defaultResponse = {
      status: 200,
      respcode: 1001,
      message: 'API valid input'
    }
    if (typeof (actionType) === 'undefined' && typeof (reqDataObj) === 'undefined') {
      return defaultResponse
    }
    if (paytmrules == null && Object.keys(paytmrules).length == 0 && !(actionType in paytmrules)) {
      return defaultResponse
    }
    const currentRules = paytmrules[actionType]
    /**
     * Loop through fields
     */
    for (const [field, fieldRules] of Object.entries(currentRules)) {
      // console.log('currentRules fields', field, fieldRules)
      /**
           * Check Rule field exist in reqDataObj if exist then validate
           */
      if (field in reqDataObj) {
        /**
           * Loop throgh field Rule
           */
        // console.log('fieldRules fields', Object.entries(fieldRules))
        for (const rulename in fieldRules) {
          const rulevalue = fieldRules[rulename]
          // for (var (rulename, rulevalue) in fieldRules ) {
          //  console.log('currentRules fields', rulename, rulevalue)
          const isCurrentFieldValid = apiValidator.validInput(rulename, reqDataObj[field], rulevalue)
          // log.logger(field, rulename, reqDataObj[field], rulevalue, isCurrentFieldValid)
          if (isCurrentFieldValid === false) {
            const invValidMsg = `::: ${field} invalid value ${reqDataObj[field]} for rule ${rulename} `
            defaultResponse.status = 400
            defaultResponse.respcode = 10011
            defaultResponse.message = bankErrorMsg.responseCode[10011] + invValidMsg
            break
          }
        }
      }
    }

    return defaultResponse
  }

  // START -- New api IMPLEMET for Client - Cred and Get customer API
  async GenerateSessionToken(data) {
    try {
      const payloadData = {
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
        customerId: data.sendermobilenumber
      }
      const getTokenResp = await AirtelRequest.proxyRequest('client-credentials', payloadData, {})
      log.logger({ pagename: require('path').basename(__filename), action: 'GenerateSessionToken', type: 'getTokenResp', fields: getTokenResp })
      const getToken = JSON.parse(getTokenResp)
      console.log('getToken parsedData--->', getToken)
      const response = getToken.airtelResponse
      console.log('GenerateSessionToken airtelResponse--->', response)
      const fields = { ma_user_id: data.ma_user_id || 0, userid: data.userid || 0, bank_name: "AIRTEL", mobile_number: data.sendermobilenumber || 0, sessionRQ: data.sessionRQ || 0, request: JSON.stringify({ ...payloadData }), response: JSON.stringify(response), api_status: response.meta.status == 0 ? 200 : 400, request_type: 'client-credentials', url: PROXY_URL }
      const result = await TransferController.saveApiLogDmt(fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'GenerateSessionToken', type: 'result', fields: result })
      if (getToken.status != 200) return { status: 400, message: 'Fail:Airtel API Error', respcode: 1001 }
      if (response.meta.status != 0 && response.meta.description != 'Success') return { status: 400, message: response.meta.description, action_code: 1001 }
      return { status: 200, message: 'success', action_code: 1000, data: response }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'GenerateSessionToken', type: 'error', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async GetCustomerDetails(reqDataObj) {
    try {
      const headersData = {
        token: reqDataObj.token,
      }
      const getCustomerDetailsResp = await AirtelRequest.proxyRequest('validate-customer', '', headersData)
      log.logger({ pagename: require('path').basename(__filename), action: 'GetCustomerDetails', type: 'getCustomerDetailsResp', fields: getCustomerDetailsResp })
      const getCustomerDetails = JSON.parse(getCustomerDetailsResp)
      log.logger({ pagename: require('path').basename(__filename), action: 'GetCustomerDetails', type: 'getCustomerDetails', fields: getCustomerDetails })
      const response = getCustomerDetails.airtelResponse
      log.logger({ pagename: require('path').basename(__filename), action: 'GetCustomerDetails', type: 'response', fields: response })
      const fields = { ma_user_id: reqDataObj.ma_user_id || 0, userid: reqDataObj.userid || 0, bank_name: "AIRTEL", mobile_number: reqDataObj.sendermobilenumber || 0, sessionRQ: reqDataObj.sessionRQ || 0, request: JSON.stringify(headersData), response: JSON.stringify(response), api_status: response.meta.status == 0 ? 200 : 400, request_type: 'validate-customer', url: PROXY_URL }
      const result = await TransferController.saveApiLogDmt(fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'GetCustomerDetails', type: 'result', fields: result })
      if (getCustomerDetails.status != 200) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      return { status: 200, message: 'success', action_code: 1000, data: response }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'GetCustomerDetails', type: 'error', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
  // END ---New api IMPLEMET for Client - Cred and Get customer API

  async getRemitterAvailableBalance (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'request', fields: reqDataObj })
    try {
      /* Commenting Old Code and URL getRemitterAvailableBalance for Airtel
      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber
      }

      const validationRes = await this.validateAPIInput('GET_REMITTER_BALANCE', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const feSessionId = this.getUnique('PVR')

      const hashDataString = [currentSessionId.partnerId, customPayload.customerMobile, feSessionId, currentSessionId.salt].map((e) => e.toString()).join('#')

      const hashedStr = getHash512(hashDataString)

      const payLoadJson = {
        appVersion: '2.0',
        caf: 'C2A',
        channel: 'EXTP',
        customerId: customPayload.customerMobile,
        feSessionId: feSessionId,
        hash: hashedStr,
        partnerId: currentSessionId.partnerId
      }

      const apiResponse = await AirtelRequest.post(
        'GET_REMITTER_BALANCE',
        payLoadJson,
        {},
        {}
      )

      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }
      */
      /*
        mcmv - Monthly consumed maximum value
        mcv  - Monthly consumed value
      */
     /*
      const { code, messageText, mcmv, mcv } = apiResponse.response

      if (code != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + messageText || '' }
      }
      */
      reqDataObj.sessionRQ = sessionRQ
      let tokenResp = await this.GenerateSessionToken(reqDataObj);
      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'tokenResp', fields: tokenResp })
      let token = await tokenResp.data.data.token || ''
      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'token', fields: token })

      reqDataObj.token = token
      let getCustomerRes = await this.GetCustomerDetails(reqDataObj);
      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'getCustomerRes', fields: getCustomerRes })
      //   dcmv: '25000',Daily consumed maximum value. Daily limit. For future use
      //   mcmv: '25000',Monthly consumed maximum value. This is the monthly limit. Currently Rs 25000
      //   mpt: '5000',Maximum limit per transaction. Currently Rs 5000 
      //   dcv: '0',Daily consumed value. For future use
      //   mcv: '50.00',Monthly consumed value. This is the amount already remitted by the sender
      //   ycmv: '300000',Yearly consumed maximum value. Daily limit. For future use
      //   ycv: '50.00',Yearly consumed maximum value. 
      let mcv = 0
      let mcmv = 0
      let messageText = getCustomerRes.data.meta.description || ""
      if(getCustomerRes.data.meta.status == 0){
        mcv = getCustomerRes.data.data.mcv
        mcmv = getCustomerRes.data.data.mcmv
      }
      return {
        status: 200,
        respcode: 1000,
        message: messageText,
        remitterDetail: true,
        consumedLimit: mcv,
        remainingLimit: ((getCustomerRes?.data?.meta?.status == 1) ? 25000 : (mcmv - mcv)) || 25000,
        senderid: reqDataObj.sendermobilenumber
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async doubleVerifyBene (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const customPayload = {
        externalRefNo: reqDataObj.bcpartnerrefno
      }
      const validationRes = await this.validateAPIInput('DOUBLE_VERIFICATION', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const payLoadJson = {
        ver: '2.0',
        feSessionId: this.getUnique('DVF'),
        channel: 'EXTP',
        partnerId: currentSessionId.partnerId,
        externalRefNo: customPayload.externalRefNo
      }

      const hashDataString = [payLoadJson.channel, currentSessionId.partnerId, payLoadJson.externalRefNo, currentSessionId.salt].map((e) => e.toString()).join('#')

      payLoadJson.hash = getHash512(hashDataString)

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'DOUBLE_VERIFICATION',
        payLoadJson,
        {},
        {})

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_DOUBLE_VERIFICATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { code, messageText, amount, externalRefNo, rrn, charges, tranId, beneName, feSessionId } = apiResponse.response

      /* status : Success  */
      if (code == 0) {
        return {
          status: 1,
          request_number: externalRefNo,
          amount: amount,
          bank_service_charges: charges,
          bank_gst: 0,
          bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
          bankrefno: tranId,
          bankTransactionid: feSessionId,
          transactionId: reqDataObj.ma_transfers_id,
          message: messageText,
          rrn: rrn,
          benename: beneName
        }
      }
      /* status : Failure  */
      if (code == 1) {
        return {
          status: 0,
          request_number: externalRefNo || customPayload.externalRefNo,
          amount: amount,
          bank_service_charges: charges,
          bank_gst: 0,
          bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
          bankrefno: tranId,
          bankTransactionid: feSessionId,
          transactionId: reqDataObj.ma_transfers_id,
          message: bankErrorMsg.responseCode[10012],
          rrn: rrn,
          benename: beneName,
          autorefundcredit: true
        }
      }
      /* status : Timeout  */
      if (code == 2) {
        return {
          status: -1,
          request_number: externalRefNo,
          amount: amount,
          bank_service_charges: charges,
          bank_gst: 0,
          bank_gross_amount: ((parseFloat(amount) || 0) + (parseFloat(charges) || 0)),
          bankrefno: tranId,
          bankTransactionid: feSessionId,
          transactionId: reqDataObj.ma_transfers_id,
          message: messageText,
          rrn: rrn,
          benename: beneName
        }
      }
      /* Default Case */
      return {
        status: -1,
        message: messageText || bankErrorMsg.responseCode[10013],
        request_number: reqDataObj.bcpartnerrefno
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async remitterNameValidation (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        firstName: reqDataObj.firstName,
        lastName: reqDataObj.lastName
      }

      const validationRes = await this.validateAPIInput('REMITTER_NAME_VALIDATION', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const feSessionId = this.getUnique('RMB')

      const hashDataString = [customPayload.firstName, customPayload.lastName, currentSessionId.salt].map((e) => e.toString()).join('#')

      const hashedStr = getHash512(hashDataString)

      const payLoadJson = {
        ...customPayload,
        hash: hashedStr,
        partnerId: currentSessionId.partnerId
      }
      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.post(
        'REMITTER_NAME_VALIDATION',
        payLoadJson,
        { fesessionid: feSessionId },
        {}
      )

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_REMITTER_NAME_VALIDATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { code, description } = apiResponse.response.meta

      if (description == 'Customer Name Is Valid' && code == '000') {
        return {
          status: 200,
          respcode: 1000,
          message: description,
          /* save remitter name verification request changes */
          bankRequest: payLoadJson,
          bankResponse: apiResponse.response,
          ma_bank_on_boarding_id: reqDataObj.ma_bank_on_boarding_id
        }
      }

      return {
        status: 400,
        respcode: 10005,
        message: bankErrorMsg.responseCode[10038] + '::: ' + description || ''
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  getUnique (OP) {
    const fesessionid = 'AAIRTEL' + OP + [...Array(10)].map(() => (~~(Math.random() * 36)).toString(36)).join('').toUpperCase()
    return fesessionid
  }

  async getBeneficiaryList (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'request', fields: reqDataObj || '' })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber
      }

      const validationRes = await this.validateAPIInput('VIEW_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }
      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      // URL#Salt
      const hashDataString = [`/partners/remittance-services/api/v1/customers/${customPayload.customerMobile}/beneficiaries`, currentSessionId.salt].map((e) => e.toString()).join('#')

      const urlParams = {
        CustomerMobileNumber: customPayload.customerMobile
      }
      const headers = {
        channel: 'EXTP',
        fesessionid: this.getUnique('VBEN'),
        hash: getHash512(hashDataString),
        partnerid: currentSessionId.partnerId
      }

      const requestStart = process.hrtime()
      const apiResponse = await AirtelRequest.get(
        'VIEW_BENEFICIARY',
        {},
        headers,
        urlParams)

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_AIRTEL_VIEW_BENEFICIARY_REQUEST_[' + JSON.stringify(headers) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'bankResponse', fields: apiResponse })
      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { meta, data } = apiResponse.response

      if (meta.status != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + apiResponse.meta.description || '' }
      }

      const beneArr = []

      const { beneList } = data
      const beneListLength = beneList.length

      for (let index = 0; index < beneListLength; index++) {
        /* bene name blank case handle while sync bene */
        if (beneList[index].beneName != '' &&
          beneList[index].beneAccount != '' &&
          beneList[index].beneIfscCode != '') {
          beneArr.push({
            accountNumber: beneList[index].beneAccount,
            ifscCode: beneList[index].beneIfscCode,
            beneficiaryMobileNumber: beneList[index].beneMobileNumber,
            beneficiaryName: beneList[index].beneName,
            bankName: beneList[index].beneBankName,
            beneficiaryId: beneList[index].beneId
          })
        }
      }

      return {
        status: 200,
        respcode: 1000,
        beneList: beneArr
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  /**
    * validateAadhaar - This function validates an Aadhaar number by making a request to the Airtel DMT API.
    *
    * @param {Object} reqDataObj - The request data object containing the Aadhaar number to be validated.
    * @param {string} reqDataObj.aadhaar_number - The 12-digit Aadhaar number that needs to be validated.
    *
    * @returns {Promise<Object>} .
    *   - Possible keys in the response: `status`, `respcode`, `message`.
  */
  async validateRemitterAadhaar (reqDataObj, sessionRQ) {
    try {
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'validateAirtelRemitterAadhaar',
        type: 'request',
        fields: reqDataObj
      })
      const { remitter_aadhaar_number, token, ma_bank_on_boarding_id, ma_bank_name } = reqDataObj
      const header = { token }
      const payload = { uid: remitter_aadhaar_number }
      const aadhaarValidatedData = await AirtelRequest.proxyRequest('validate-uid', payload, header)
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'validateRemitterAadhaarProxyData',
        type: 'response',
        fields: aadhaarValidatedData
      })

      const { status, airtelResponse } = JSON.parse(aadhaarValidatedData)
      const saveLogsData = {
        ...reqDataObj,
        request: JSON.stringify({ payload, header }),
        response: JSON.stringify(aadhaarValidatedData),
        api_status: status,
        request_type: 'VALIDATE-UID',
        url: PROXY_URL,
        sessionRQ,
        bank_name: 'AIRTEL'
      }

      const savelogResponse = await TransferController.saveApiLogDmt(saveLogsData)
      log.logger({ pagename: require('path').basename(__filename), action: 'saveApiLog', type: 'savelogResponse', fields: savelogResponse })

      if (status == 200) {
        const { meta: { code, description, status: responseStatus } = {} } = airtelResponse || {}
        // Update status for invalid codes
        if (!((code == '000' || code == 'RS_OB_001') && responseStatus == 0)) {
          return this.buildResponse(400, 'Fail:' + description, 'EKYC registration failed', 'show_aadhar', sessionRQ, ma_bank_on_boarding_id, ma_bank_name, token)
        }
        return this.buildResponse(200, 'Aadhaar verified successfully.', 'Aadhaar verified successfully.', 'show_biometric', sessionRQ, ma_bank_on_boarding_id, ma_bank_name, token)
      } else {
        return this.buildResponse(400, 'Fail:' + aadhaarValidatedData.message, 'EKYC registration failed', 'show_aadhar', sessionRQ, ma_bank_on_boarding_id, ma_bank_name, token)
      }
    } catch (error) {
      // Log the error details
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'remitterAadhaarValidateError',
        type: 'error',
        fields: error
      })
      return this.buildErrorResponse(error)
    }
  }

  // Helper function to build response object
  buildResponse (status, message, displayMessage, navigation, sessionRQ, ma_bank_on_boarding_id, ma_bank_name, token) {
    return {
      status,
      respcode: status === 200 ? 1000 : 1001,
      action_code: status === 200 ? 1000 : 1001,
      requestID: '',
      message,
      displayMessage,
      navigation,
      sessionRQ: sessionRQ || '',
      bankID: ma_bank_on_boarding_id || '',
      bankName: ma_bank_name || '',
      acToken: token
    }
  }

  buildErrorResponse (error) {
    return {
      status: 400,
      respcode: 10001,
      message: bankErrorMsg.responseCode[10001],
      fields: error
    }
  }

  // Biometrics
  async processCustomerEKYC (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'request', fields: reqDataObj })
    try {
      // Get session id
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const { ma_user_id, userid, ma_bank_on_boarding_id, ma_bank_name, remitter_mobile_number, otpToken, ...rest } = reqDataObj
      // const { biometricType, consentTaken, consentRef, pidBlock } = rest

      // const payload = { biometricType, consentTaken, consentRef, ...pidBlock }
      const payload = rest

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'payload', fields: payload })

      const requestStart = process.hrtime()
      console.time('TIMER_PROCESS_CUSTOMER_EKYC')
      const response = await AirtelRequest.proxyRequest('customer-ekyc', payload, { token: otpToken })
      const apiResponse = JSON.parse(response)
      console.log('API Result: ', apiResponse)
      console.timeEnd('TIMER_PROCESS_CUSTOMER_EKYC')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_PROCESS_CUSTOMER_EKYC_[' + JSON.stringify(payload) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'bankResponse', fields: apiResponse })

      // store logs
      const logObj = {
        ma_user_id: ma_user_id,
        userid: userid,
        bank_name: ma_bank_name,
        mobile_number: remitter_mobile_number,
        sessionRQ: sessionRQ,
        request_type: 'processCustomerEKYC',
        request: JSON.stringify(payload),
        response: JSON.stringify(apiResponse),
        url: PROXY_URL,
        api_status: apiResponse.airtelResponse.meta.status == 0 ? 200 : 400
      }
      const storeLogs = await TransferController.saveApiLogDmt(logObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'store logs', fields: storeLogs })

      if (apiResponse.airtelResponse.meta.status == 0) {
        return {
          status: apiResponse.status,
          respcode: apiResponse.respcode || 1000,
          action_code: 1000,
          requestID: '',
          message: 'Aadhaar verified successfully.',
          displayMessage: 'Aadhaar verified successfully.',
          navigation: 'show_biometric',
          sessionRQ: sessionRQ || '',
          bankID: reqDataObj.ma_bank_on_boarding_id || '',
          bankName: reqDataObj.ma_bank_name || ''
        }
      } else if (apiResponse.airtelResponse.meta.status == 1 && apiResponse.airtelResponse.meta.code == '1791') {
        return {
          status: 400,
          respcode: 1001,
          action_code: 1001,
          requestID: '',
          message: 'Fail: Airtel - ' + apiResponse.airtelResponse.meta.description,
          displayMessage: 'Biometric mismatch',
          navigation: 'show_aadhar',
          sessionRQ: sessionRQ,
          bankID: reqDataObj.ma_bank_on_boarding_id || '',
          bankName: reqDataObj.ma_bank_name || ''
        }
      } else {
        return {
          status: 400,
          respcode: 1001,
          action_code: 1001,
          requestID: '',
          message: 'Fail: Airtel - ' + apiResponse.airtelResponse.meta.description,
          displayMessage: 'EKYC registration failed',
          navigation: 'show_aadhar',
          sessionRQ: sessionRQ,
          bankID: reqDataObj.ma_bank_on_boarding_id || '',
          bankName: reqDataObj.ma_bank_name || ''
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'catcherror', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async customerGenerateOTP (reqDataObj, sessionRQ, connection) {
    try {
      let action_type
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'request', fields: reqDataObj })
      if (!reqDataObj.otp_type || (reqDataObj.otp_type != 1 && reqDataObj.otp_type != 5)) {
        return { status: 400, message: 'OTP Type is not valid', respcode: 1001 }
      }
      // if (reqDataObj.otp_type == 5 && !reqDataObj.amount) {
      //   return { status: 400, message: 'Amount is required for Transaction OTP', respcode: 1001 }
      // }

      const data = {
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
        userId: reqDataObj.mobile_number
      }

      if (reqDataObj.otp_type == 1) action_type = 'otp'
      if (reqDataObj.otp_type == 5) {
        if (reqDataObj.amount == '' || reqDataObj.amount == null || reqDataObj.amount == undefined) {
          reqDataObj.amount = '1'
        }
        action_type = 'auth-otp'
        data.amount = reqDataObj.amount
        data.externalRefNo = reqDataObj.requestNumber
      }

      const result = await AirtelRequest.proxyRequest(action_type, data)
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'proxyResponse', fields: result })
      const resp = JSON.parse(result)

      // Code for saving logs
      reqDataObj.bank_name = 'AIRTEL'
      reqDataObj.request_type = reqDataObj.otp_type == 1 ? 'GENERATE_CUST_OTP' : 'GENERATE_TRANSACTION_OTP'
      reqDataObj.request = JSON.stringify(data)
      reqDataObj.response = JSON.stringify(resp)
      reqDataObj.url = PROXY_URL
      reqDataObj.api_status = resp.status

      const savelogRes = await TransferController.saveApiLogDmt(reqDataObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'saveApiLog', fields: savelogRes })
      if (resp.status != 200) return resp

      if (resp.airtelResponse.data.token != '') {
        return { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000], otp_id: resp.airtelResponse.data.token }
      } else {
        return { status: 400, respcode: 10001, message: 'Airtel: Bank Side Token Not Found' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'error', fields: err })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async customerVerifyOTP (reqDataObj, sessionRQ, connection) {
    try {
      let action_type
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'request', fields: reqDataObj })
      if (!reqDataObj.otp_type || (reqDataObj.otp_type != 1 && reqDataObj.otp_type != 5)) {
        return { status: 400, message: 'OTP Type is not valid', respcode: 1001 }
      }
      if (reqDataObj.otp_type == 5 && !reqDataObj.amount) {
        return { status: 400, message: 'Amount is required for Transaction OTP', respcode: 1001 }
      }

      const data = {
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
        userId: reqDataObj.mobile_number,
        otp: reqDataObj.otp_pin
      }
      if (reqDataObj.otp_type == 1) action_type = 'otp-verify'
      if (reqDataObj.otp_type == 5) {
        action_type = 'auth-otp-verify'
        data.amount = reqDataObj.amount
        data.externalRefNo = reqDataObj.requestNumber
      }

      const result = await AirtelRequest.proxyRequest(action_type, data, { token: reqDataObj.otp_id })
      // console.log('resp', resp)
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'proxyResponse', fields: result })
      const resp = JSON.parse(result)

      // Code for saving logs
      reqDataObj.bank_name = 'AIRTEL'
      reqDataObj.request_type = reqDataObj.otp_type == 1 ? 'VERIFY_CUST_OTP' : 'VERIFY_TRANSACTION_OTP'
      reqDataObj.request = JSON.stringify(data)
      reqDataObj.reponse = JSON.stringify(resp)
      reqDataObj.url = PROXY_URL
      reqDataObj.api_status = resp.status

      const savelogRes = await TransferController.saveApiLogDmt(reqDataObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'customerGenerateOTP', type: 'saveApiLog', fields: savelogRes })
      if (resp.status != 200) return resp

      if (resp.airtelResponse.meta.status != 1) {
        return { status: 200, respcode: 10000, message: resp.airtelResponse.meta.description, token: resp.airtelResponse.data.token }
      } else {
        return { status: 400, respcode: 10001, message: resp.airtelResponse.meta.description }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'customerVerifyOTP', type: 'error', fields: err })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }
}

module.exports = {
  BANK: AIRTELBank
}
