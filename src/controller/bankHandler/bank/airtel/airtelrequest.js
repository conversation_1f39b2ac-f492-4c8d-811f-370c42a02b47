'use strict'

const { BANK_API_URL, BANK_PAYMENT_API_URL, TRANSFER_MODES, BANK_API_ENDPOINTS, BANK_API_ENDPOINTS_TIMEOUT, HIDE_REMARKS, PROXY_URL, PROXY_KEY, PROXY_IV } = require('./config').BANK_CONSTANTS
const { apiJsonRequest } = require('../../../../util/jsonrequest')
const log = require('../../../../util/log')
const bankErrorMsg = require('./error')
const { isJson } = require('../../bankcommon')
const errorMsg = require('../../../../util/error')
const matchAll = require('match-all')
const crypto = require('crypto')
class AirtelRequest {
  static async post (OP_NAME, PARAMS, HEADERDATA, urlParams) {
    const requestStart = process.hrtime()
    try {
      const airtelPostRequest = await apiJsonRequest.post(
        this.getBaseURL(OP_NAME) + this.getEndPoint(OP_NAME, urlParams),
        PARAMS,
        {
          headers: { 'content-type': 'application/json', ...HEADERDATA },
          timeout: BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] ? BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] : 0
        }
      )
      console.log('AIRTEL' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify(airtelPostRequest.data || {}))

      if (airtelPostRequest.status != 200) {
        console.log('airtelPostResponse>>', airtelPostRequest)
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_NOTSUCESS_' + OP_NAME + '_' + timeInMs + 'ms')
        return {
          status: 400,
          respcode: 10001,
          message: bankErrorMsg.responseCode[10001]
        }
      }

      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        response: airtelPostRequest.data
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'airtelcatcherror', fields: error })

      /* LOGIC ERROR */
      if (!error.isAxiosError) {
        return {
          status: 400,
          respcode: 1001,
          message: errorMsg.responseCode[1001]
        }
      }

      console.log('AIRTEL_CATCH' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify({ res: error.response.data }))
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_CATCH_' + OP_NAME + '' + timeInMs + 'ms')

      /* CLIENT SIDE  */
      if (!error.response) {
        return {
          status: 400,
          respcode: 10037,
          message: bankErrorMsg.responseCode[10037]
        }
      }
      /* SERVER DOWN */
      if (
        error.response &&
        (error.response.status.toString().match(/5[0-9]{2}/) || error.response.status.toString().match(/4[0-9]{2}/))
      ) {
        let errorMessage = ''
        if (!isJson(error.response.data)) {
          if (error.response.data && error.response.data.meta && error.response.data.meta.description) {
            errorMessage = error.response.data.meta.description
          }
        }
        if (errorMessage == '') {
          return {
            status: 400,
            respcode: 10035,
            message: bankErrorMsg.responseCode[10035]
          }
        }

        return {
          status: 400,
          respcode: 10001,
          message: `${bankErrorMsg.responseCode[10001]} :: ${errorMessage}`
        }
      }

      /* TIMEOUT */
      if (
        error.code == 'ECONNABORTED'
      ) {
        return {
          status: 400,
          respcode: 10031,
          message: bankErrorMsg.responseCode[10031]
        }
      }
    } finally {
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_' + OP_NAME + '' + timeInMs + 'ms')
    }
  }

  static async get (OP_NAME, PARAMS, HEADERDATA, urlParams) {
    const requestStart = process.hrtime()
    try {
      const airtelGettRequest = await apiJsonRequest.get(
        this.getBaseURL(OP_NAME) + this.getEndPoint(OP_NAME, urlParams),
        {
          params: PARAMS,
          headers: { 'content-type': 'application/json', ...HEADERDATA },
          timeout: BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] ? BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] : 0
        }
      )
      console.log('AIRTEL' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify(airtelGettRequest.data || {}))

      if (airtelGettRequest.status != 200) {
        console.log('airtelPostResponse>>', airtelGettRequest)
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_NOTSUCESS_' + OP_NAME + '_' + timeInMs + 'ms')
        return {
          status: 400,
          respcode: 10001,
          message: bankErrorMsg.responseCode[10001]
        }
      }

      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        response: airtelGettRequest.data
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'airtelcatcherror', fields: error })

      /* LOGIC ERROR */
      if (!error.isAxiosError) {
        return {
          status: 400,
          respcode: 10001,
          message: errorMsg.responseCode[10001]
        }
      }

      console.log('AIRTEL_CATCH' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify({ res: error.response.data }))
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_CATCH_' + OP_NAME + '' + timeInMs + 'ms')

      /* CLIENT SIDE  */
      if (!error.response) {
        return {
          status: 400,
          respcode: 10037,
          message: bankErrorMsg.responseCode[10037]
        }
      }
      /* SERVER DOWN */
      if (
        error.response &&
        (error.response.status.toString().match(/5[0-9]{2}/) || error.response.status.toString().match(/4[0-9]{2}/))
      ) {
        let errorMessage = ''
        if (!isJson(error.response.data)) {
          if (error.response.data && error.response.data.meta && error.response.data.meta.description) {
            errorMessage = error.response.data.meta.description
          }
        }
        if (errorMessage == '') {
          return {
            status: 400,
            respcode: 10035,
            message: bankErrorMsg.responseCode[10035]
          }
        }
        return {
          status: 400,
          respcode: 10001,
          message: `${bankErrorMsg.responseCode[10001]} :: ${errorMessage}`
        }
      }

      /* TIMEOUT */
      if (
        error.code == 'ECONNABORTED'
      ) {
        return {
          status: 400,
          respcode: 10031,
          message: bankErrorMsg.responseCode[10031]
        }
      }
    } finally {
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_FINAALY_' + OP_NAME + '_' + timeInMs + 'ms')
    }
  }

  static getEndPoint (OP_NAME, PARAMS) {
    log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'getEndPoint', fields: JSON.stringify({ OP_NAME, PARAMS }) })
    if (!BANK_API_ENDPOINTS[OP_NAME].match(/{\w*}/)) {
      return BANK_API_ENDPOINTS[OP_NAME]
    }

    let replacedURL = BANK_API_ENDPOINTS[OP_NAME]
    /* find all the match and replace with dynamic value */
    // const allMatch = [...BANK_API_ENDPOINTS[OP_NAME].matchAll(/{(\w*)}/g)].forEach(matchFind => {
    matchAll(BANK_API_ENDPOINTS[OP_NAME], /{(\w*)}/g).toArray().forEach(matchFind => {
      const key = matchFind
      if (!PARAMS[key]) {
        throw new Error(`${key} params missing`)
      }
      const reqex = new RegExp(`{${key}}`)
      replacedURL = replacedURL.replace(reqex, PARAMS[key])
    })

    log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'replacedURL', fields: replacedURL })

    return replacedURL
  }

  static getBaseURL (OP_NAME) {
    if (['INIT_TRANSACTION', 'BULK_TRANSFER', 'GET_REMITTER_BALANCE', 'DOUBLE_VERIFICATION'].includes(OP_NAME)) {
      return BANK_PAYMENT_API_URL
    }
    return BANK_API_URL
  }

  static async proxyRequest (action_type, request, header = {}) {
    const requestStart = process.hrtime()
    try {
      // console.log('tooekn', token)
      // const header = { Authorization: `Bearer ${token.data}`, client_id: 'AIRPAY', action_type: action_type }
      // console.log('req,', request)
      console.log('action', action_type, 'request', request, 'header', header)
      header.action_type = action_type
      const encData = this.proxyEnc(JSON.stringify(request), PROXY_KEY, PROXY_IV)
      console.log('encData', encData)
      const payload = { Request: encData }
      const response = await apiJsonRequest.post(PROXY_URL, payload, { headers: header })
      console.log('res', response)
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('Proxy_Request_time_' + timeInMs + 'ms')

      const decData = this.proxyDecrypt(response.data, PROXY_KEY, PROXY_IV)
      console.log('decData>>>', decData)
      return decData

      // if (res.status != 200) {
      //   return {
      //     status: 400,
      //     respcode: 10001,
      //     message: 'Wrong Request for proxy api'
      //   }
      // }

      // if (!res.data.ResponseCode) {
      //   return {
      //     status: 400,
      //     respcode: 1001,
      //     message: 'No data in proxy api'
      //   }
      // }

      // if (res.data.ResponseCode == 1) {
      //   return {
      //     status: 400,
      //     respcode: 1001,
      //     message: res.data.DisplayMessage
      //   }
      // }
      // return {
      //   status: 200,
      //   respcode: 10000,
      //   message: bankErrorMsg.responseCode[10000],
      //   response: decData
      // }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: action_type, type: 'airtelError', fields: err })
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_CATCH_' + timeInMs + 'ms')
      return {
        status: 400,
        respcode: 1001,
        message: 'Proxy Api Error'
      }
    }
  }

  static proxyEnc (data, key, iv) {
    try {
      const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'hex'), Buffer.from(iv, 'hex'))
      let encrypted = cipher.update(data, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      return encrypted
    } catch (error) {
      console.log('Error in Encryption --->', error)
      return {
        status: 400,
        message: 'Error in Encryption'
      }
    }
  }

  static proxyDecrypt (data, key, iv) {
    try {
      const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'hex'), Buffer.from(iv, 'hex'))
      let decrypted = decipher.update(data, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      return decrypted
    } catch (error) {
      console.log('Error in Decryption --->', error)
      return {
        status: 400,
        message: 'Error in Decryption'
      }
    }
  }
}

module.exports = AirtelRequest
