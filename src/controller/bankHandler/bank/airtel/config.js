const util = require('../../../../util/util')

const DEVELOPMENT_ENDPOINTS = {
  ADD_REMITTER: '/partners/remittance-services/api/v1/wi-auth/customers/{CustomerMobileNumber}',
  SEND_OTP_ADD_REMITTER: '/partners/remittance-services/api/v1/wi-register/customers',
  ADD_BENEFICIARY: '/partners/remittance-services/api/v1/bene-auth/customers/{CustomerMobileNumber}/beneficiaries/{BeneficiaryId}',
  INIT_TRANSACTION: '/payments/impsTransaction',
  BULK_TRANSFER: '/payments/impsTransaction',
  DOUBLE_VERIFICATION: '/payments/transactionEnquiry',
  SEND_OTP_ADD_BENEFICIARY: '/partners/remittance-services/api/v1/bene-register/customers/{CustomerMobileNumber}/beneficiaries',
  RESEND_OTP_ADD_BENEFICIARY: '/partners/remittance-services/api/v1/bene-register/customers/{CustomerMobileNumber}/beneficiaries',
  GET_REMITTER_BALANCE: 'api/v1/sender/limit',
  PRE_VALIDATE: '/partners/remittance-services/api/v1/customers/{CustomerMobileNumber}',
  VIEW_BENEFICIARY: '/partners/remittance-services/api/v1/customers/{CustomerMobileNumber}/beneficiaries',
  REMITTER_NAME_VALIDATION: '/partners/remittance-services/api/v1/wi-register/validate-name'
  // VALIDATE_BENEFICIARY_BEFORE_ADD: '/api/tops/remittance/v2/penny-drop',
  // SEND_OTP_DELETE_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
  // RESEND_OTP_DELETE_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
  // DELETE_BENEFICIARY: '/api/tops/remittance/v1/user/delete-beneficiary',
}

const PRODUCTION_ENDPOINTS = {
  ADD_REMITTER: '/kongpartners/PAYU/api/v1/wi-auth/customers/{CustomerMobileNumber}',
  SEND_OTP_ADD_REMITTER: '/kongpartners/PAYU/api/v1/wi-register/customers',
  ADD_BENEFICIARY: '/kongpartners/PAYU/api/v1/bene-auth/customers/{CustomerMobileNumber}/beneficiaries/{BeneficiaryId}',
  INIT_TRANSACTION: '/RetailerPortal/AIRPAYMENTimps',
  BULK_TRANSFER: '/RetailerPortal/AIRPAYMENTimps',
  DOUBLE_VERIFICATION: '/RetailerPortal/AIRPAYMENTEnquiry',
  SEND_OTP_ADD_BENEFICIARY: '/kongpartners/PAYU/api/v1/bene-register/customers/{CustomerMobileNumber}/beneficiaries',
  RESEND_OTP_ADD_BENEFICIARY: '/kongpartners/PAYU/api/v1/bene-register/customers/{CustomerMobileNumber}/beneficiaries',
  GET_REMITTER_BALANCE: '/remittance/partner/api/v1/sender/limit',
  PRE_VALIDATE: '/kongpartners/AIRPAYMENT/api/v1/customers/{CustomerMobileNumber}',
  VIEW_BENEFICIARY: '/kongpartners/AIRPAYMENT/api/v1/customers/{CustomerMobileNumber}/beneficiaries',
  REMITTER_NAME_VALIDATION: '/kongpartners/AIRPAYMENT/api/v1/wi-register/validate-name'
  // VALIDATE_BENEFICIARY_BEFORE_ADD: '/api/tops/remittance/v2/penny-drop',
  // SEND_OTP_DELETE_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
  // RESEND_OTP_DELETE_BENEFICIARY: '/api/tops/remittance/v1/send-otp',
  // DELETE_BENEFICIARY: '/api/tops/remittance/v1/user/delete-beneficiary',
}

const BANK_CONSTANTS = {

  BANK_API_URL: util.isProduction() ? 'https://portal.airtelbank.com' : 'https://apbuat.airtelbank.com/',
  BANK_PAYMENT_API_URL: util.isProduction() ? 'https://portal.airtelbank.com/' : 'https://apbuat.airtelbank.com:5055/',
  PROXY_URL: util.isProduction() ? 'https://flerken.airpay.co.in/airtel-dmt-api-prod' : 'https://jtgouoxjj4.execute-api.ap-south-1.amazonaws.com/dev/airtelint',
  PROXY_KEY: util.isProduction() ? 'f5cfe332abf810ffad232a5412653542a3cbd5f05662efd4326e642fc6286cb4' : 'e7434f26d6b7e5ca13f2e6a8078ea9202e58c181718c1d4927b5dbb7416ce1cd',
  PROXY_IV: util.isProduction() ? 'df09bd76fc329581c49310b5f91a52dd' : '25fafe704a0cc98b3dd440f7ce2b1941',
  // CLIENT_ID: util.isProduction() ? '**********' : '**********',
  CLIENT_ID: util.isProduction() ? '**********' : '**********',
  // CLIENT_SECRET: util.isProduction() ? 'OTgxNTAwQzEtQjg1Qi00QUY3LUEzQUQtRkJBOTlBOEM0MkM4' : 'VzFrBFXJW0IMWJ1Y',
  CLIENT_SECRET: util.isProduction() ? 'sxKObT4isHCLAtWB' : 'VzFrBFXJW0IMWJ1Y',
  BANK_API_ENDPOINTS: util.isProduction() ? PRODUCTION_ENDPOINTS : DEVELOPMENT_ENDPOINTS,
  BANK_API_ENDPOINTS_TIMEOUT: {
    ADD_REMITTER: 60000,
    SEND_OTP_ADD_REMITTER: 60000,
    // VALIDATE_BENEFICIARY_BEFORE_ADD: 60000,
    SEND_OTP_ADD_BENEFICIARY: 60000,
    ADD_BENEFICIARY: 60000,
    INIT_TRANSACTION: 60000,
    BULK_TRANSFER: 60000,
    DOUBLE_VERIFICATION: 60000,
    // SEND_OTP_DELETE_BENEFICIARY: 60000,
    // RESEND_OTP_DELETE_BENEFICIARY: 60000,
    GET_REMITTER_BALANCE: 60000,
    PRE_VALIDATE: 60000,
    // DELETE_BENEFICIARY: 60000,
    VIEW_BENEFICIARY: 60000
  },
  PAYMENT_MODES: {
    0: 'neft',
    2: 'imps'
  },
  TRANSFER_MODES: {
    NEFT: 'neft',
    IMPS: 'imps'
  },
  TRANSFER_DETAILS_MODE: {
    0: 'all',
    1: 'neft',
    2: 'imps'
  },
  TRANSACTION_TYPE: {
    B: 'Success',
    R: 'Rejection',
    C: 'Refund',
    F: 'Failure',
    BV: 'Bene Validation'
  },
  STATUS: {
    0: 'FAILURE',
    1: 'SUCCESS',
    '-1': 'TIMEOUT',
    91: 'UNKNOWN'
  },
  REMITTERSTATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  BENEFICIARY_STATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  IMPS_STATUS: {
    0: 'pending',
    1: 'active'
  },
  TRANSSTATUS: {
    0: 'Pending',
    1: 'Process',
    2: 'Credited',
    3: 'Rejected or Refund Processing',
    4: 'Refund Success or refund Processed',
    5: 'remitter Registration',
    6: 'failure',
    '-1': 'Unknown'
  },
  PAYMENTSTATUS: {
    0: 'pending',
    1: 'processing',
    2: 'credited',
    3: 'rejected',
    4: 'refund process',
    6: 'failure'
  },
  BANK_LIMIT: 25000,
  transfer_status_code: {
    status_200: {
      0: 'success',
      1023: 'unknown',
      1030: 'failure',
      1032: 'failure',
      1033: 'failure',
      1043: 'duplicate',
      1061: 'failure',
      1062: 'failure',
      1064: 'failure',
      1065: 'failure',
      1068: 'failure',
      1105: 'failure',
      1106: 'failure',
      1107: 'pending',
      1108: 'failure',
      1207: 'failure',
      1304: 'pending',
      2003: 'unknown',
      8003: 'pending'
    },
    status_400: {
      0: 'failure',
      1039: 'failure',
      1070: 'failure',
      1208: 'failure'
    },
    status_401: {
      2001: 'failure',
      2004: 'failure'
    },
    status_500: {
      2002: 'unknown'
    }
  },
  verify_bene_status_code: {
    status_200: {
      0: 'success',
      1023: 'unknown',
      1030: 'failure',
      1032: 'failure',
      1033: 'failure',
      1043: 'duplicate',
      1064: 'failure',
      1065: 'failure',
      1103: 'failure',
      1104: 'failure',
      1107: 'pending',
      1108: 'failure',
      2003: 'unknown',
      8003: 'pending'
    },
    status_400: {
      0: 'failure'
    },
    status_401: {
      2001: 'failure'
    },
    status_500: {
      2002: 'unknown'
    }
  },
  HIDE_REMARKS: { 1067: 'Transaction Failed~[INSB]' } // response_code
}

module.exports = {
  BANK_CONSTANTS: BANK_CONSTANTS
}
