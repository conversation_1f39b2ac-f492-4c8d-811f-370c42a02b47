module.exports = {
  ADD_REMITTER: {
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    otp: {
      required: true
    }
  },
  SEND_OTP_ADD_REMITTER: {
    custMsisdn: {
      required: true,
      mobilecheck: true
    },
    firstName: {
      required: true,
      paytmname: true
    },
    lastName: {
      required: true,
      paytmname: true
    },
    address: {
      required: true,
      minlength: 2,
      maxlength: 500
    },
    pinCode: {
      required: true,
      indianpincode: true
    }
  },
  VALIDATE_BENEFICIARY_BEFORE_ADD: {
    accountNumber: {
      required: true,
      alphanumeric: true
    },
    bankName: {
      required: true
    },
    benIfsc: {
      required: true,
      indiabankifsc: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    txnReqId: {
      required: true,
      alphanumeric: true,
      maxlength: 20
    }
  },
  SEND_OTP_ADD_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    beneMobileNumber: {
      required: true,
      mobilecheck: true
    },
    accountNumber: {
      required: true,
      alphanumeric: true
    },
    bankName: {
      required: true
    },
    benIfsc: {
      required: true,
      indiabankifsc: true
    },
    name: {
      required: true,
      paytmname: true
    }
  },
  RESEND_OTP_ADD_BENEFICIARY: {
    mobile_number: {
      required: true,
      mobilecheck: true
    },
    beneMobileNumber: {
      required: true,
      mobilecheck: true
    },
    beneAccount: {
      required: true,
      alphanumeric: true
    },
    beneBankName: {
      required: true
    },
    beneIfscCode: {
      required: true,
      indiabankifsc: true
    },
    beneName: {
      required: true,
      paytmname: true
    }
  },
  ADD_BENEFICIARY: {
    otp: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    transactionId: {
      required: true
    }
  },
  INIT_TRANSACTION: {
    amount: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    name: {
      required: true
    },
    custPincode: {
      required: true
    },
    custAddress: {
      required: true
    },
    stateCode: {
      required: true
    },
    bankName: {
      required: true
    },
    ifsc: {
      required: true
    },
    beneAccNo: {
      required: true
    },
    beneMobNo: {
      required: true,
      mobilecheck: true
    }
  },
  BULK_TRANSFER: {
    amount: {
      required: true
    },
    beneficiaryId: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    name: {
      required: true
    },
    custPincode: {
      required: true
    },
    custAddress: {
      required: true
    },
    stateCode: {
      required: true
    },
    bankName: {
      required: true
    },
    ifsc: {
      required: true
    },
    beneAccNo: {
      required: true
    },
    beneMobNo: {
      required: true,
      mobilecheck: true
    }
  },
  DOUBLE_VERIFICATION: {
    externalRefNo: {
      required: true
    }
  },
  SEND_OTP_DELETE_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  RESEND_OTP_DELETE_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  GET_REMITTER_BALANCE: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  PRE_VALIDATE: {
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    remitter_name: {
      required: true
    }
  },
  VIEW_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  REMITTER_NAME_VALIDATION: {
    firstName: {
      required: true,
      paytmname: true
    },
    lastName: {
      required: true,
      paytmname: true
    }
  }
}
