'use strict'

const { BANK_API_URL, BANK_API_ENDPOINTS, BANK_API_ENDPOINTS_TIMEOUT, PASS_PHRASE_HEADER, PASS_PHRASE_BODY, PROXY_URL } = require('./config').BANK_CONSTANTS
const { apiJsonRequest } = require('../../../../util/jsonrequest')
const log = require('../../../../util/log')
const bankErrorMsg = require('./error')
const errorMsg = require('../../../../util/error')
const crypto = require('crypto')
class FinoRequest {
  /**
   *
   * @param {{endPoint:String, payLoad:Object, keys:{ClientId:String, AuthKey:String},mode?:'IMPS'|'IMPS_FINO'|'NEFT'|'NEFT_FINO'}}} param0
   * @returns
   */
  static async post ({ endPoint, payLoad, keys, mode }) {
    const requestStart = process.hrtime()
    try {
      const headers = { ...this.getDefaultHeader(keys) }

      log.logger({ pagename: require('path').basename(__filename), action: endPoint, type: 'headers', fields: headers })

      const finoPostRequest = await apiJsonRequest.post(
        this.getEndPoint({ endPoint, mode }),
        JSON.stringify(this.encryptBody(payLoad)),
        {
          headers,
          timeout: BANK_API_ENDPOINTS_TIMEOUT[endPoint] ? BANK_API_ENDPOINTS_TIMEOUT[endPoint] : 0
        })

      console.log('FINO' + '_$$$_' + endPoint + '_$$$_' + JSON.stringify(payLoad) + '_$$$_' + JSON.stringify(finoPostRequest.data || {}))

      if (finoPostRequest.status != 200) {
        console.log('finoPostResponse>>', finoPostRequest)
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_NOTSUCESS_' + endPoint + '_' + timeInMs + 'ms')
        return {
          status: 400,
          respcode: 10001,
          message: bankErrorMsg.responseCode[10001]
        }
      }

      if (!finoPostRequest.data) {
        return {
          status: 400,
          respcode: 1001,
          message: bankErrorMsg.responseCode[10035]
        }
      }

      /* Response Data 'null' */
      if (!finoPostRequest.data.ResponseData) {
        return {
          status: 200,
          respcode: 10000,
          message: bankErrorMsg.responseCode[10000],
          response: {
            ...finoPostRequest.data,
            ResponseData: {}
          }
        }
      }

      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        response: {
          ...finoPostRequest.data,
          ResponseData: JSON.parse(this.decryptBody(finoPostRequest.data.ResponseData))
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: endPoint, type: 'finocatcherror', fields: error })

      /* LOGIC ERROR */
      if (!error.isAxiosError) {
        return {
          status: 400,
          respcode: 1001,
          message: errorMsg.responseCode[1001]
        }
      }

      console.log('FINO_CATCH' + '_$$$_' + endPoint + '_$$$_' + JSON.stringify(payLoad) + '_$$$_' + JSON.stringify({ res: error.response.data }))
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_CATCH_' + endPoint + '' + timeInMs + 'ms')

      /* CLIENT SIDE  */
      if (!error.response) {
        return {
          status: 400,
          respcode: 10037,
          message: bankErrorMsg.responseCode[10037]
        }
      }
      /* TIMEOUT */
      if (
        error.code == 'ECONNABORTED'
      ) {
        return {
          status: 400,
          respcode: 10031,
          message: bankErrorMsg.responseCode[10031]
        }
      }

      if (error.code == 'ERR_INVALID_ARG_TYPE') {
        return {
          status: 400,
          respcode: 1001,
          message: bankErrorMsg.responseCode[10035]
        }
      }

      return {
        status: 400,
        respcode: 1001,
        message: bankErrorMsg.responseCode[10001]
      }
    } finally {
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_' + endPoint + '' + timeInMs + 'ms')
    }
  }

  /**
   *
   * @param {{ClientId:String, AuthKey:String}} param0
   * @returns
   */
  static getDefaultHeader ({ ClientId, AuthKey }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getHeader', type: 'request', fields: { ClientId, AuthKey } })
    const authenticationHeader = JSON.stringify({ ClientId: ClientId, AuthKey: AuthKey })
    log.logger({ pagename: require('path').basename(__filename), action: 'getHeader', type: 'authenticationHeader', fields: authenticationHeader })

    return {
      Authentication: this.encrypt({ response: authenticationHeader, passphrase: PASS_PHRASE_HEADER }),
      'Content-Type': 'application/json'
    }
  }

  /**
   *
   * @param {Object} body
   * @returns
   */
  static encryptBody (body) {
    return this.encrypt({ response: JSON.stringify(body), passphrase: PASS_PHRASE_BODY })
  }

  /**
   *
   * @param {string} body
   * @returns
   */
  static decryptBody (body) {
    return this.decrypt({ response: body, passphrase: PASS_PHRASE_BODY })
  }

  /**
   *
   * @param {string} buffer
   * @returns
   */
  static md5 (buffer) {
    return crypto.createHash('md5').update(buffer).digest()
  }

  /**
   *
   * @param {String} passphrase
   * @param {Buffer} salt
   * @returns
   */
  static evpkdf (passphrase, salt) {
    const passphraseBuffer = Buffer.from(passphrase)
    const hash1 = this.md5(Buffer.concat([passphraseBuffer, salt]))
    const hash2 = this.md5(Buffer.concat([hash1, passphraseBuffer, salt]))
    const hash3 = this.md5(Buffer.concat([hash2, passphraseBuffer, salt]))
    const salted = Buffer.concat([hash1, hash2, hash3])
    const key = salted.subarray(0, 32)
    const iv = salted.subarray(32, 48)
    return { key, iv }
  }

  /**
   *
   * @param {String} encryptTxt
   * @returns
   */
  static decode (encryptTxt) {
    const data = Buffer.from(encryptTxt, 'base64')

    if (data.subarray(0, 8).toString() !== 'Salted__') throw new Error('Invalid String')

    const salt = data.subarray(8, 16)
    const decryptTxt = data.subarray(16)
    return { decryptTxt, salt }
  }

  /**
   *
   * @param {{ response:string, passphrase:string }} param0
   * @returns
   */
  static encrypt ({ response, passphrase }) {
    /* calculated key and iv */
    const buffer = Buffer.from(response)
    const salt = crypto.randomBytes(8)
    const { key, iv } = this.evpkdf(passphrase, salt)
    /* AES Ciphering */
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
    const chunks = [Buffer.from('Salted__'), salt]
    chunks.push(cipher.update(buffer))
    chunks.push(cipher.final())
    /* base64 encode */
    return Buffer.concat(chunks).toString('base64')
  }

  /**
   *
   * @param {{ response:string, passphrase:string }} param0
   * @returns
   */
  static decrypt ({ response, passphrase }) {
    const { decryptTxt, salt } = this.decode(response)
    const { key, iv } = this.evpkdf(passphrase, salt)
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
    let decrypted = decipher.update(decryptTxt)
    decrypted += decipher.final()
    return decrypted
  }

  /**
   *
   * @param {{{ endPoint:String, mode?:'IMPS'|'IMPS_FINO'|'NEFT'|'NEFT_FINO' }}} param0
   * @returns
   */
  static getEndPoint ({ endPoint, mode }) {
    log.logger({ pagename: require('path').basename(__filename), action: endPoint, type: 'getEndPoint', fields: endPoint })
    let URL = ''
    URL = `${BANK_API_URL}/${BANK_API_ENDPOINTS[endPoint]}`
    console.log('==URL==', URL)
    if (mode) {
      URL = `${BANK_API_URL}/${BANK_API_ENDPOINTS[`${endPoint}_${mode}`]}`
      console.log('==mode URL==', URL)
    }
    log.logger({ pagename: require('path').basename(__filename), action: URL, type: 'getEndPoint', fields: URL })
    return URL
  }

  static async proxyRequest (action_type, request) {
    // const requestStart = process.hrtime()
    try {
      const token = await this.getProxyHeader('token')
      console.log('tooekn', token)
      const header = { Authorization: `Bearer ${token.data}`, client_id: 'AIRPAY', action_type: action_type }
      // console.log('req,', request)
      const res = await apiJsonRequest.post(PROXY_URL, request, { headers: header })
      console.log('res', res.data, res.status)

      // if (res.status != 200) {
      //   return {
      //     status: 400,
      //     respcode: 10001,
      //     message: 'Wrong Request for proxy api'
      //   }
      // }

      // if (!res.data.ResponseCode) {
      //   return {
      //     status: 400,
      //     respcode: 1001,
      //     message: 'No data in proxy api'
      //   }
      // }

      // if (res.data.ResponseCode == 1) {
      //   return {
      //     status: 400,
      //     respcode: 1001,
      //     message: res.data.DisplayMessage
      //   }
      // }
      return {
        status: 200,
        respcode: 10000,
        message: bankErrorMsg.responseCode[10000],
        response: res.data
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: action_type, type: 'finocatcherror', fields: err })
      return {
        status: 400,
        respcode: 1001,
        message: 'Proxy Api Error'
      }
    }
  }

  static async getProxyHeader (action_type) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'Get token', type: 'request', fields: action_type })
      const header = { action_type: action_type }
      const token = await apiJsonRequest.post(PROXY_URL, '', { headers: header })
      console.log('token', token.status, token.data)
      // log.logger({ pagename: require('path').basename(__filename), action: 'Get token', type: 'request', fields: token })
      if (token.status != 200 || token.data.access_token == '') return { status: 400, respcode: 1001, message: 'No token is found' }
      return { status: 200, rescode: 1000, message: 'Token Generate successfully', data: token.data.access_token }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'Get token', type: 'error', fields: err })
      return {
        status: 400,
        respcode: 1001,
        message: 'Proxy Api Error'
      }
    }
  }
}

module.exports = FinoRequest
