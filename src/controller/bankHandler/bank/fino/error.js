const responseCode = {
  10000: 'Success',
  10001: 'Fail: FINO API Error ',
  10002: 'Fail: FINO Session Error ',
  10003: 'Fail: FINO Session Details record not found ',
  10004: 'Fail: FINO Undefined Response ',
  10005: 'Fail: FINO API Failure ',
  10006: 'Fail: FINO Add Remitter ',
  10007: 'Fail: FINO Add Beneficiary ',
  10008: 'Fail: FINO Refund send otp Failure ',
  10009: 'Fail: FINO Refund verify send otp Failure ',
  10010: 'Fail: FINO Sender details not found ',
  10011: 'Fail: FINO Invalid input provided ',
  10012: 'Fail: FINO Transfer failed ',
  10013: 'Awaiting response from FINO bank',
  10014: 'Fail: FINO Transfer Requery failed ',
  10025: 'Fail: FINO Transfer Requery Unknow status ',
  10026: 'Fail: FINO Account Already Exists With Diffrent Ifsc code ',
  10027: 'Fail: FINO Agent Login Details Missing ',
  10028: 'Fail: FINO Delete Beneficary ',
  10029: 'Fail: FINO Beneficiary validation Failed ',
  10030: 'Fail: Transaction not found!',
  10031: 'Fail: Internal timeout!',
  10032: 'Fail: FINO Authentication Failed ',
  10033: 'Fail: FINO Token Exception!',
  10034: 'Fail: FINO Bulk data empty!',
  10035: 'Fail: FINO Unknown Response',
  10036: 'FINO Method Not Supported',
  10037: 'FINO API Not Found',
  10038: 'Fail: Remitter Name Validation Error',
  10039: 'Fail: FINO API Failure :: Timeout/Pending'

}

module.exports = { responseCode }
