module.exports = {
  ADD_REMITTER: {
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    otp: {
      required: true
    }
  },
  SEND_OTP_ADD_REMITTER: {
    custMsisdn: {
      required: true,
      mobilecheck: true
    }
  },
  VALIDATE_BENEFICIARY: {
    // accountNumber
    beneAccNo: {
      required: true,
      alphanumeric: true
    },
    bankName: {
      required: true
    },
    // benIfsc
    ifsc: {
      required: true,
      indiabankifsc: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    }
    // txnReqId: {
    //   required: true,
    //   alphanumeric: true,
    //   maxlength: 20
    // }
  },
  INIT_TRANSACTION: {
    amount: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    name: {
      required: true
    },
    bankName: {
      required: true
    },
    ifsc: {
      required: true
    },
    beneAccNo: {
      required: true
    },
    beneName: {
      required: true
    },
    mode: {
      required: true
    }
  },
  BULK_TRANSFER: {
    amount: {
      required: true
    },
    beneficiaryId: {
      required: true
    },
    customerMobile: {
      required: true,
      mobilecheck: true
    },
    name: {
      required: true
    },
    bankName: {
      required: true
    },
    ifsc: {
      required: true
    },
    beneAccNo: {
      required: true
    },
    beneName: {
      required: true
    },
    mode: {
      required: true
    }
  },
  DOUBLE_VERIFICATION: {
    externalRefNo: {
      required: true
    },
    mode: {
      required: true
    }
  },
  GET_REMITTER_BALANCE: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  REMITTER_NAME_VALIDATION: {
    firstName: {
      required: true,
      paytmname: true
    },
    lastName: {
      required: true,
      paytmname: true
    }
  },
  SEND_OTP_DELETE_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  }
}
