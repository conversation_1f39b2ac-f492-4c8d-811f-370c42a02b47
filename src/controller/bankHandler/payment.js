const { BANK_DIR } = require('./bankcommon')
const log = require('../../util/log')
const errorMsg = require('../../util/error')

class Payment {
  constructor (bank = null, type = null, prevConn, onBoardId = null) {
    // /**
    //  * Tempory For Testing
    //  */
    // if (bank === null) {
    //   bank = 'nsdl'
    // }
    bank = bank.toLowerCase()
    this._CURRENT_BANK_PATH = BANK_DIR + bank + '/' + bank
    this.type = type
    this.prevConn = prevConn
    this.onBoardId = onBoardId
    this.CURRENT_BANK_OBJECT = null
  }

  async requestToBank (actionType, reqDataObj, requestNo, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'requestToBank', type: 'request', fields: { actionType } })

    try {
      if (this.CURRENT_BANK_OBJECT == null) {
        this.CURRENT_BANK_CLASS = require(this._CURRENT_BANK_PATH).BANK

        this.CURRENT_BANK_OBJECT = new this.CURRENT_BANK_CLASS()
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'requestToBank', type: 'error', fields: JSON.stringify(error) })
      return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
    }

    switch (actionType) {
      case 'LOGIN':
        return this.CURRENT_BANK_OBJECT.agentLogin(reqDataObj, requestNo, conn)
      case 'AGENT_DETAILS':
        return this.CURRENT_BANK_OBJECT.getRemitterDetails(reqDataObj, requestNo, conn)
      case 'PREVALIDATE':
        return this.CURRENT_BANK_OBJECT.preValidateRemitter(reqDataObj, requestNo, conn)
      case 'SEND_OTP_ADD_REMITTER':
        return this.CURRENT_BANK_OBJECT.addRemitterSendOtp(reqDataObj, requestNo, conn)
      case 'RESEND_OTP_ADD_REMITTER':
        return this.CURRENT_BANK_OBJECT.addRemitterReSendOtp(reqDataObj, requestNo, conn)
      case 'ADD_REMITTER':
        return this.CURRENT_BANK_OBJECT.addRemitter(reqDataObj, requestNo, conn)
      case 'VALIDATE_BENEFICIARY_BEFORE_ADD':
        return this.CURRENT_BANK_OBJECT.validateBeneficiaryBeforeAdd(reqDataObj, requestNo, conn)
      case 'IS_BENEFICIARY_EXISTS':
        return this.CURRENT_BANK_OBJECT.getRemitterDetails(reqDataObj, requestNo, conn)
      case 'ADD_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.addBeneficiary(reqDataObj, requestNo, conn)
      case 'SEND_OTP_ADD_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.addBeneficiarySendOtp(reqDataObj, requestNo, conn)
      case 'RESEND_OTP_ADD_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.addBeneficiaryReSendOtp(reqDataObj, requestNo, conn)
      case 'SEND_OTP_DELETE_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.deleteBeneficiarySendOtp(reqDataObj, requestNo, conn)
      case 'RESEND_OTP_DELETE_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.deleteBeneficiaryReSendOtp(reqDataObj, requestNo, conn)
      case 'DELETE_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.deleteBeneficiary(reqDataObj, requestNo, conn)
      case 'INIT_TRANSACTION':
        return this.CURRENT_BANK_OBJECT.initTransaction(reqDataObj, requestNo, conn)
      case 'BULK_TRANSACTION':
        // eslint-disable-next-line no-case-declarations
        const res = await this.CURRENT_BANK_OBJECT.bulkTransaction(reqDataObj, requestNo, conn)
        console.log('PAYMENTLIB_BULK_TRANSACTION_', { request: reqDataObj, response: res })
        return res
      case 'DOUBLE_VERIFICATION':
        return this.CURRENT_BANK_OBJECT.doubleVerify(reqDataObj, requestNo, conn)
      case 'DOUBLE_VERIFICATION_BENE':
        return this.CURRENT_BANK_OBJECT.doubleVerifyBene(reqDataObj, requestNo, conn)
      case 'REVALIDATE':
        return this.CURRENT_BANK_OBJECT.revalidate(reqDataObj, requestNo, conn)
      case 'REFUND':
        return this.CURRENT_BANK_OBJECT.refund(reqDataObj, requestNo, conn)
      case 'VERIFY_REFUND':
        return this.CURRENT_BANK_OBJECT.verifyOTPRefund(reqDataObj, requestNo, conn)
      case 'REFUND_VERIFY':
        return this.CURRENT_BANK_OBJECT.refundVerify(reqDataObj, requestNo, conn) // NEW CHANGES : NSDL REFUND WITHOUT OTP
      case 'GET_REMITTER_BALANCE':
        return this.CURRENT_BANK_OBJECT.getRemitterAvailableBalance(reqDataObj, requestNo, conn)
      case 'VIEW_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.getBeneExist(reqDataObj, requestNo, conn)
      case 'SYNC_BENEFICIARY':
        return this.CURRENT_BANK_OBJECT.getBeneficiaryList(reqDataObj, requestNo, conn)
      case 'REMITTER_NAME_VALIDATION':
        return this.CURRENT_BANK_OBJECT.remitterNameValidation(reqDataObj, requestNo, conn)
      case 'INITIATE_CUST_ONBOARDING':
        return this.CURRENT_BANK_OBJECT.requestCustomerOnboardingDmt(reqDataObj, requestNo, conn)
      case 'REGISTER_NEW_REMITTER_KYC':
        return this.CURRENT_BANK_OBJECT.registerNewRemitterKyc(reqDataObj, requestNo, conn)
      case 'REGISTER_REMITTER_KYC':
        return this.CURRENT_BANK_OBJECT.registerRemitterKyc(reqDataObj, requestNo, conn)
      case 'REMITTER_KYC_STATUS':
        return this.CURRENT_BANK_OBJECT.getRemitterKycStatus(reqDataObj, requestNo, conn)
      case 'GENERATE_OTP':
        return this.CURRENT_BANK_OBJECT.customerGenerateOTP(reqDataObj, requestNo, conn)
      case 'PROCESS_CUSTOMER_REGISTRATION':
        return this.CURRENT_BANK_OBJECT.processCustomerRegistration(reqDataObj, requestNo, conn)
      case 'PROCESS_CUSTOMER_EKYC':
        return this.CURRENT_BANK_OBJECT.processCustomerEKYC(reqDataObj, requestNo, conn)
      case 'VALIDATE_REMITTER_AADHAAR':
        return this.CURRENT_BANK_OBJECT.validateRemitterAadhaar(reqDataObj, requestNo, conn)
      case 'CONFIRM_REMITTER_ONBOARDING':
        return this.CURRENT_BANK_OBJECT.confirmOnboardingDetails(reqDataObj, requestNo, conn)
      case 'GET_CUST_KYC_STATUS':
        return this.CURRENT_BANK_OBJECT.getCustomerOnboardingDetails(reqDataObj, requestNo, conn)
      case 'CUST_KYC_ONBOARDING':
        return this.CURRENT_BANK_OBJECT.nsdlcustKycOnboarding(reqDataObj, requestNo, conn)
      case 'PROCESS_CUSTOMER_BIOMETRIC':
        return this.CURRENT_BANK_OBJECT.processCustomerBiometric(reqDataObj, requestNo, conn)
      case 'VERIFY_OTP':
        return this.CURRENT_BANK_OBJECT.customerVerifyOTP(reqDataObj, requestNo, conn)
      default:
        return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
    }
  }

  async responseToBank () {

  }
}

module.exports = Payment
