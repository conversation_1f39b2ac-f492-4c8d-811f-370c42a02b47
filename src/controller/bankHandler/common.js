const crypto = require('crypto')
// aysc propsExist (reqProps,srcProps) {
//   try{
//     let allPropExist = true ;
//     if(Object.keys(reqProps).length > 0 && Object.keys(srcProps).length >0){
//       for (const key in object) {
//         if (object.hasOwnProperty(key)) {
//           const element = object[key];

//         }
//       }
//     }else{
//       return false
//     }
//   }catch(error){
//     return false
//   }
// }

/**
 * To do Require Validation
 */
const propsExist = async (reqProps, srcProps) => {
  return true
}

const propExist = (prop, obj) => {
  try {
    if (prop in obj) {
      return true
    }
    return false
  } catch (error) {
    return false
  }
}

const getSha1 = (text) => {
  const shasum = crypto.createHash('sha1')
  shasum.update(text)

  const hashPassword = shasum.digest('hex')
  return hashPassword
}

module.exports = {
  BANK_DIR: './bank/',
  propsExist: propsExist,
  getSha1: getSha1,
  propExist: propExist
}
