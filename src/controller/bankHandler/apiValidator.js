/**
 * <AUTHOR>
 * @param {*} type
 * @param {*} validData
 * @param {*} extraData
 */
const validInput = (type, validData, extraData) => {
  // console.log('validInput', type, validData, extraData)
  var inValidIp = (typeof (validData) === 'undefined' || validData === null || validData === '')
  switch (type) {
    case 'required':
      return !inValidIp
    case 'numberonly':
      var numberonly = /^\d+$/
      return !inValidIp && numberonly.test(validData)
    case 'stringlength':
      var isEmpty = inValidIp
      if (!isEmpty) {
        return validData.length === extraData
      } else {
        return false
      }
    case 'indianmobile':
      var indianmobile = /^\d{10}$/
      return !inValidIp && indianmobile.test(validData)
    case 'minlength':
      var minlengthStr = !inValidIp ? validData.toString() : ''
      return minlengthStr.length >= extraData
    case 'maxlength':
      var maxlengthStr = !inValidIp ? validData.toString() : ''
      return maxlengthStr.length <= extraData
    case 'alphasinglespace':
      var alphasinglespace = /^[a-z]+([\s]{1}[a-z]+)*$/i
      return !inValidIp && alphasinglespace.test(validData)
    case 'alphanumeric':
      var alphanumericregex = /^[a-z0-9]+$/i
      return !inValidIp && alphanumericregex.test(validData)
    case 'alphanumericspacecomma':
      var alphanumericspacecomma = /^[a-z\d,\s]+$/i
      return !inValidIp && alphanumericspacecomma.test(validData)
    case 'alphanumericspace':
      var alphanumericspace = /^[a-z\d\s]+$/i
      return !inValidIp && alphanumericspace.test(validData)
    case 'alphanumericsinglespace':
      var alphanumericsinglespace = /^[a-z0-9]+([\s]{1}[a-z0-9]+)*$/i
      return !inValidIp && alphanumericsinglespace.test(validData)
    case 'alphanumericsinglespacedashunderscore':
      var alphanumericsinglespacedashunderscore = /^[a-z0-9]+([-_\s]{1}[a-z0-9]+)*$/i
      return !inValidIp && alphanumericsinglespacedashunderscore.test(validData)
    case 'email':
      var emailregex = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
      return !inValidIp && emailregex.test(validData)
    case 'indianpincode':
      var indianpincode = /^[1-9][0-9]{5}$/
      return !inValidIp && indianpincode.test(validData)
    case 'name':
      var name = /^[a-zA-Z ]{2,50}$/
      return !inValidIp && name.test(validData)
    case 'passwordmin':
      var passwordmin = /[a-zA-Z0-9]{7,}$/
      return !inValidIp && passwordmin.test(validData)
    case 'passwordmax':
      var passwordmax = /[a-zA-Z0-9]{7,32}$/
      return !inValidIp && passwordmax.test(validData)
    case 'nospecialchar':
      var nospecialchar = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{7,32}$/
      return !inValidIp && nospecialchar.test(validData)
    case 'mobilecheck':
      var mobilecheck = /^[6-9][0-9]{9}$/
      return !inValidIp && mobilecheck.test(validData)
    case 'paytmname':
      var paytmname = /^[a-zA-Z. ']{1,100}$/
      return !inValidIp && paytmname.test(validData)
    case 'paytmaddress':
      var paytmaddress = /^[a-zA-Z0-9_ ,/.=-]{2,500}$/
      return !inValidIp && paytmaddress.test(validData)
    case 'indiabankifsc':
      var indiabankifsc = /^[A-Za-z]{4}0[A-Z0-9a-z]{6}$/ // https://en.wikipedia.org/wiki/Indian_Financial_System_Code
      return !inValidIp && indiabankifsc.test(validData) // https://stackoverflow.com/questions/********/regular-expression-for-ifsc-codefirst-four-alphabet-and-then-7-digit
    default:
      return true
  }
}

module.exports = { validInput }
