const DAO = require('../../lib/dao')
const log = require('../../util/log')

class DMTStorage extends DAO {
  static get TABLE_NAME () {
    return 'ma_agent_session'
  }

  static get PRIMARY_KEY () {
    return 'ma_agent_id'
  }

  async storeAgentSession () {
    log.logger({ pagename: require('path').basename(__filename), action: 'storeAgentSession', type: 'request', fields: arguments })
  }

  async getAgentSessionId () {

  }
}

module.exports = {
  DMTStorage: DMTStorage
}
