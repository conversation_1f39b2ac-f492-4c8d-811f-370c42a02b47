/* eslint-disable padded-blocks */
/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
// const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
// const sms = require('../../util/sms')

class tickets extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  static get TABLE_NAME () {
    return 'ma_tickets'
  }

  static get PRIMARY_KEY () {
    return 'ma_tickets_id'
  }

  /**
   * Returns TABLE_NAME details by its PRIMARY_KEY
   */
  static async getByID (_, { id }) {
    // eslint-disable-next-line no-return-await
    return await this.find(id)
  }

  /**
   * Returns a list of neft details matching the passed fields
   * @param {*} fields - Fields to be matched
   */
  static async findMatching (_, fields) {
    log.logger({ pagename: 'ticketsController.js', action: 'findMatching', type: 'request', fields: fields })
    // Returns early with all cars if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching cars
    return this.findByFields({
      fields
    })
  }

  /**
   * Creates a new entry in ma_tickets table
   */
  // eslint-disable-next-line camelcase
  static async createEntry (_, { ticket_type, ma_user_id, uic, ticket_message, ticket_status, connection = null }) {
    log.logger({ pagename: 'ticketsController.js', action: 'createEntry', type: 'request', fields: { ticket_type, ma_user_id, uic, ticket_message, ticket_status } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        // console.log('createConnection')
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      const _result = await this.insert(connection, {
        data: {
          ticket_type,
          ma_user_id,
          uic,
          ticket_message,
          ticket_status
        }
      })
      log.logger({ pagename: 'ticketsController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) {
        connection.release()
      }
    }
  }

  /**
   * Validate customer ticket already exists
   * @param {*} args
   * @param {*} connection
   */
  static async validateTicketAlreadyExists (args, connection) {
    log.logger({ pagename: 'ticketsController.js', action: 'validateTicketAlreadyExists', type: 'request', fields: args })
    try {
      const sql = `SELECT ma_tickets_id FROM ma_tickets WHERE uic = '${args.uic}' AND ticket_type = ${args.ticket_type} AND ticket_status IN (1,2)`
      console.log(sql)
      const queryResponse = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'ticketsController.js', action: 'validateTicketAlreadyExists', type: 'response', fields: queryResponse })
      if (queryResponse.length > 0) {
        return { status: 400, respcode: 1074, message: errorMsg.responseCode[1074] }
      } else {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateTicketAlreadyExists', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  // ----------------------------------- API FUNCTIONS --------------------------------------

  /**
   * Add customer tickets
   * @param {*} _
   * @param {*} args
   */
  static async addTicket (_, args) {
    log.logger({ pagename: 'ticketsController.js', action: 'addTicket', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let validationResponse = {}
      // Transaction Begins
      // await mySQLWrapper.beginTransaction(connection)

      if (typeof args.uic !== 'undefined' && args.uic !== '') {
        validationResponse = await this.validateTicketAlreadyExists(args, connection)
        if (validationResponse.status !== 200) {
          return validationResponse
        }
      }

      // const ticketMessage = util.ticketsTemplate[args.ticket_type]
      const executionResponse = await this.createEntry(_, {
        ticket_type: args.ticket_type,
        ma_user_id: args.ma_user_id,
        uic: args.uic,
        ticket_message: args.ticket_message,
        ticket_status: 1,
        connection
      })
      if (executionResponse.status !== 200) {
        // Rollback
        // await mySQLWrapper.rollback(connection)
        return executionResponse
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addTicket', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * Update tickets
   * @param {*} _
   * @param {*} args
   */
  static async updateTickets (_, args) {
    log.logger({ pagename: 'ticketsController.js', action: 'updateTickets', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Transaction Begins
      // await mySQLWrapper.beginTransaction(connection)

      const sql = `UPDATE ma_tickets SET action_by = ${args.action_by}, ticket_status = ${args.ticket_status}, remarks = '${args.remarks}' WHERE ma_tickets_id = ${args.ma_tickets_id}`
      const queryResponse = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'ticketsController.js', action: 'updateTickets', type: 'request', fields: queryResponse })
      if (queryResponse.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: queryResponse }
      } else {
        return { status: 400, respcode: 1072, message: errorMsg.responseCode[1072] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateTickets', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * Get tickets listing
   * @param {*} _
   * @param {*} args
   */
  static async getTicketsListing (_, args) {
    log.logger({ pagename: 'ticketsController.js', action: 'getTicketsListing', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Transaction Begins
      // await mySQLWrapper.beginTransaction(connection)
      let condition = ''
      if (typeof args.uic !== 'undefined') {
        condition = `AND uic = ${args.uic}`
      }

      const sql = `SELECT * FROM ma_tickets WHERE ma_user_id = ${args.ma_user_id} ${condition}`
      const queryResponse = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'ticketsController.js', action: 'getTicketsListing', type: 'response', fields: queryResponse })
      if (queryResponse.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], tickets: queryResponse }
      } else {
        return { status: 400, respcode: 1041, message: errorMsg.responseCode[1041] }
      }

      // Transaction Commit
      // await mySQLWrapper.commit(connection)
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTicketsListing', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async getTicketTypeList (_, { type_id = null, connection = null }) {
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      let condition = ''
      if (type_id !== null && type_id !== undefined) {
        condition = ' AND type_id = ' + type_id
      }
      const sql = 'SELECT * FROM ma_ticketstype_master WHERE type_status = 1' + condition
      const queryResponse = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'ticketsController.js', action: 'getTicketTypeList', type: 'response', fields: queryResponse })
      if (queryResponse.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], type_details: queryResponse }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTicketTypeList', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (isSet) {
        connection.release()
      }
    }
  }
}

module.exports = tickets
