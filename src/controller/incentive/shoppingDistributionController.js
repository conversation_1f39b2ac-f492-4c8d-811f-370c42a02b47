const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const validator = require('../../util/validator')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const common_fns = require('../../util/common_fns')
const getTransactionController = require('../transaction/transactionController')
const commonFunction = require('../common/commonFunctionController')
class shoppingDistribution extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_slabwise_distribution_shopping'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_shopping_id'
  }

  /**
   * getDistribution description - Distribution Configuration for Shopping Incentive
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, product_id: number, state_master_id: number, amount: number }} fields
   * @param {any} con
   * @returns {{ status: number, respcode: number, message: string, result?: any}}
   */
  static async getDistribution (_, fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'request', fields: fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'product_id', 'state_master_id'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      let state_master_id = fields.state_master_id
      let ma_user_id = fields.ma_user_id
      const sql = `SELECT
          *
        FROM
          ma_slabwise_distribution_shopping
        WHERE
          ${fields.amount} BETWEEN min_amount AND max_amount AND\
          record_status='Y' AND
          product_id = '${fields.product_id}'`

      // Check retailer specific configuration
      const retailerSpecificQuery = sql + ` and ma_user_id=${ma_user_id}`
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'sql - Retailer Specific Query', fields: retailerSpecificQuery })
      const retailerSpecificResult = await this.rawQuery(retailerSpecificQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Retailer Specific', fields: retailerSpecificResult })

      if (retailerSpecificResult.length > 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], result: retailerSpecificResult }

      // If retailer specific details not found
      // Check other configuration (For now this is for distributor specific)
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields, connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionEntry', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], result: getDistributionAdditionalCondition.configurationData }

      // Reinitializing variables
      ma_user_id = 0

      // If other specific details not found
      // Check state specific configuration
      // state_master_id = 2 // FOR TESTING ONLY
      const stateSpecificQuery = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'sql - State Specific', fields: stateSpecificQuery })
      const stateSpecificResult = await this.rawQuery(stateSpecificQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - State Specific', fields: stateSpecificResult })

      if (stateSpecificResult.length > 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], result: stateSpecificResult }

      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0

      // If retailer and state specific configuration not found
      // Check default/global configuration
      const defaultQuery = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'sql - Default', fields: defaultQuery })
      const defaultResult = await this.rawQuery(defaultQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Default', fields: defaultResult })
      if (defaultResult.length > 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], result: defaultResult }

      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) connection.release()
    }
  }

  /**
   * distributionEntries description - To distribute the incentives
   * @param {{ ma_user_id: number, userid: number, orderid: string, distributeObj: any }} fields
   */
  static async distributionEntries (_, fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'product_id', 'state_master_id'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.orderid, connection)
      const now = new Date()
      const thisMonth = util.monthsconfig[now.getMonth()]
      let appliedCommission = ''
      const distribution = fields.distributeObj

      let itter = 1
      for (const value of distribution) {
        let subAmount = value.amount
        let tds = 0
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: 35,
            amount: subAmount,
            ma_deduction_type: value.TDStype,
            connection
          })

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          appliedCommission = commissionDetails.status === 400 ? '' : commissionDetails.appliedCommission
          log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'TDS', fields: { tds } })

          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: (util.IncentiveCommissionDescriptionNew).replace('#type#', 'BAAZAAR'),
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection
            })

            if (tdsInsert.status === 400) return tdsInsert

            // Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, connection)
            if (gstSqlRes.length > 0) gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))

            const insertOrderwiseTaxes = require('./orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: '35',
              orderid: fields.orderid,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: connection
            })

            if (orderwiseEntry.status === 400) return orderwiseEntry
            // Make entry in ma_orderwise_taxes table
          }
        }
        if (subAmount > 0) {
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection })
          const common = require('../../util/common')
          const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '35' }, connection)
          const descType = (descObj.code_desc ? descObj.code_desc : '').replace(/shopping cart/ig, 'BAAZAAR')
          const descPart = tds !== 0 ? ' - (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 35,
              description: 'Credit - ' + descType + descPart,
              ma_status: 'S',
              orderid: fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection
            })

            if (pointsLedgerCredits.status === 400) return pointsLedgerCredits
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 35,
              description: 'Credit - ' + descType + descPart,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + itter + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection
            })

            if (pointsLedgerCredits.status === 400) return pointsLedgerCredits

            itter++

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: '35',
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id
            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, connection)
            if (monthlyIncCreate.status === 400) return monthlyIncCreate
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) connection.release()
    }
  }

  /**
   * calculateIncentiveAmount description - Calculate amount based on applied_type and operative_amount
   * @param {{
   *  ma_user_id: number,
   *  userid: number,
   *  customer_charges_applied_type: number,
   *  customer_charges: number,
   *  amount: number,
   *  data: {
   *    applied_type: number,
   *    operative_amount: number,
   *    amount: number
   *  }
   * }} fields
   */
  static async calculateIncentiveAmount (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateIncentiveAmount', type: 'request', fields })
    let customerCharges = fields.customer_charges
    if (fields.customer_charges_applied_type === '2') {
      customerCharges = fields.customer_charges * fields.amount / 100
    }
    let amount = 0
    if (fields.data.applied_type === '1') {
      amount = fields.data.amount
    } else if (fields.data.applied_type === '2') {
      if (fields.data.operative_amount === '1') { // % of transaction amount
        amount = fields.amount * fields.data.amount / 100
      } else if (fields.data.operative_amount === '2') { // % of customer charges
        amount = customerCharges * fields.data.amount / 100
      }
    }
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateIncentiveAmount', type: 'responce', fields: amount })
    return amount
  }

  /**
   * incentiveShareDistribution description - distributes share and reduces reduandant code
   * @param {null} _
   * @param {{ ma_user_id:number, totalIncentiveAmount: number, pan: string, data: any }} fields
   * @param {any} con connection
   * @returns {{ status: number, message: string, respcode: number, totalIncentiveAmount: number, distributionObj: any }}
   */
  static async incentiveShareDistribution (_, fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveShareDistribution', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'totalIncentiveAmount', 'data'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      let TDStypeval = '1'
      const isValidPAN = validator.validatePAN(fields.pan)

      if (isValidPAN) TDStypeval = '3'

      const share = await this.calculateIncentiveAmount(fields)
      fields.totalIncentiveAmount += share
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveShareDistribution', type: 'totalIncentiveAmount', fields: { share, totalIncentiveAmount: fields.totalIncentiveAmount, excessInsentives: share - fields.totalIncentiveAmount, TDStypeval, ma_user_id: fields.ma_user_id } })

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        totalIncentiveAmount: fields.totalIncentiveAmount,
        distributionObj: {
          ma_user_id: fields.ma_user_id,
          amount: share,
          TDStype: TDStypeval // With PAN or without
        }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveShareDistribution', type: 'err', fields })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * Incentive entries for Shopping transaction
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, orderid: string }} fields
   * @param {any} con
   */
  static async incentiveEntries (_, fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid'])
    if (validatorResponse.status != 200) return validatorResponse

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    const decryptionKey = util[env].secondaryEncrptionKey

    try {
      const distributeObj = []
      let totalIncentiveAmount = 0
      // PAN DECRYPTION
      const userDetailsQuery = `SELECT
      profileid,
      user_type,
      distributer_user_master_id,
      state,
      CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan
    FROM
      ma_user_master
    WHERE
      profileid = `
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'incentiveEntries',
        type: 'response - RT',
        fields: {
          userDetailsQuery: `SELECT
            profileid,
            user_type,
            distributer_user_master_id,
            state,
            CAST(AES_DECRYPT(pan,'${decryptionKey.replace(/./g, '*')}') AS CHAR) as pan
          FROM
            ma_user_master
          WHERE profileid = ` + fields.ma_user_id
        }
      })
      // const userDetailsQuery = `SELECT
      //     profileid,
      //     user_type,
      //     distributer_user_master_id,
      //     state,
      //     pan
      //   FROM
      //     ma_user_master
      //   WHERE
      //     profileid = `
      // log.logger({
      //   pagename: require('path').basename(__filename),
      //   action: 'incentiveEntries',
      //   type: 'response - RT',
      //   fields: {
      //     userDetailsQuery: `SELECT
      //       profileid,
      //       user_type,
      //       distributer_user_master_id,
      //       state,
      //       pan
      //     FROM
      //       ma_user_master
      //     WHERE profileid = ` + fields.ma_user_id
      //   }
      // })

      const userDetails = await this.rawQuery(userDetailsQuery + fields.ma_user_id, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })

      if (userDetails.length <= 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }

      const orderDetailsQuery = `SELECT ma_shopping_order_master_id, product_id, amount from ma_shopping_order_master where orderid = '${fields.orderid}'`
      const orderDetails = await this.rawQuery(orderDetailsQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - Shopping', fields: orderDetails })

      if (orderDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid Orderid' }
      } else {
        fields.product_id = orderDetails[0].product_id
      }

      fields.state_master_id = userDetails[0].state
      // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id
      const configuration = await this.getDistribution(null, fields, connection)
      // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
      delete fields.distributerIdForGetDistribution
      if (configuration.status === 400) return configuration

      // Applied Mode
      fields.inclusive_flag = configuration.result[0].inclusive_flag

      // Customer Charges
      fields.customer_charges = configuration.result[0].customer_charges
      fields.customer_charges_applied_type = configuration.result[0].customer_charges_applied_type

      // share configuration
      const rtIncentiveShareData = {
        amount: configuration.result[0].rt_share,
        applied_type: configuration.result[0].rt_share_applied_type,
        operative_amount: configuration.result[0].rt_operative_amount,
        user_type: 'RT'
      }
      const dtIncentiveShareData = {
        amount: configuration.result[0].dt_share,
        applied_type: configuration.result[0].dt_share_applied_type,
        operative_amount: configuration.result[0].dt_operative_amount,
        user_type: 'DT'
      }
      const sdtIncentiveShareData = {
        amount: configuration.result[0].sd_share,
        applied_type: configuration.result[0].sd_share_applied_type,
        operative_amount: configuration.result[0].sd_operative_amount,
        user_type: 'SDT'
      }

      // Push RT details
      if (configuration.result[0].rt_share > 0) {
        const incentiveShareDistributionResponse = await this.incentiveShareDistribution(null, { ...fields, totalIncentiveAmount, pan: userDetails[0].pan, data: rtIncentiveShareData })
        if (incentiveShareDistributionResponse.status !== 200) return incentiveShareDistributionResponse

        totalIncentiveAmount = incentiveShareDistributionResponse.totalIncentiveAmount
        distributeObj.push(incentiveShareDistributionResponse.distributionObj)
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'totalIncentiveAmount - RT', fields: { customer_charges: fields.customer_charges, totalIncentiveAmount, excessInsentives: fields.customer_charges - totalIncentiveAmount, distributeObj } })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userDetailsQuery + userDetails[0].distributer_user_master_id, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer

          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details

            if (configuration.result[0].dt_share > 0) {
              const incentiveShareDistributionResponse = await this.incentiveShareDistribution(null, { ...fields, totalIncentiveAmount, ma_user_id: distribuetrDetails[0].profileid, pan: distribuetrDetails[0].pan, data: dtIncentiveShareData })
              if (incentiveShareDistributionResponse.status !== 200) return incentiveShareDistributionResponse

              totalIncentiveAmount = incentiveShareDistributionResponse.totalIncentiveAmount
              distributeObj.push(incentiveShareDistributionResponse.distributionObj)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'totalIncentiveAmount - DT', fields: { customer_charges: fields.customer_charges, totalIncentiveAmount, excessInsentives: fields.customer_charges - totalIncentiveAmount, distributeObj } })
            }

            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userDetailsQuery + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (configuration.result[0].sd_share > 0) {
                  const incentiveShareDistributionResponse = await this.incentiveShareDistribution(null, { ...fields, totalIncentiveAmount, ma_user_id: superdistribuetrDetails[0].profileid, pan: superdistribuetrDetails[0].pan, data: sdtIncentiveShareData })
                  if (incentiveShareDistributionResponse.status !== 200) return incentiveShareDistributionResponse

                  totalIncentiveAmount = incentiveShareDistributionResponse.totalIncentiveAmount
                  distributeObj.push(incentiveShareDistributionResponse.distributionObj)
                  log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'totalIncentiveAmount - SDT - 1', fields: { customer_charges: fields.customer_charges, totalIncentiveAmount, excessInsentives: fields.customer_charges - totalIncentiveAmount, distributeObj } })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') { // if dt not found check whether merchant is a sdt
            if (configuration.result[0].sd_share > 0) {
              const incentiveShareDistributionResponse = await this.incentiveShareDistribution(null, { ...fields, totalIncentiveAmount, ma_user_id: distribuetrDetails[0].profileid, pan: distribuetrDetails[0].pan, data: sdtIncentiveShareData })
              if (incentiveShareDistributionResponse.status !== 200) return incentiveShareDistributionResponse

              totalIncentiveAmount = incentiveShareDistributionResponse.totalIncentiveAmount
              distributeObj.push(incentiveShareDistributionResponse.distributionObj)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'totalIncentiveAmount - SDT', fields: { customer_charges: fields.customer_charges, totalIncentiveAmount, excessInsentives: fields.customer_charges - totalIncentiveAmount, distributeObj } })
            }
          }
        }
      }

      // Customer charge calculate
      if (fields.customer_charges_applied_type == 2) fields.customer_charges = fields.amount * fields.customer_charges / 100

      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'totalIncentiveAmount', fields: { customer_charges: fields.customer_charges, totalIncentiveAmount, excessInsentives: fields.customer_charges - totalIncentiveAmount } })
      if (fields.customer_charges - totalIncentiveAmount > 0) {
        distributeObj.push({
          ma_user_id: util.airpayCommissionId,
          amount: fields.customer_charges - totalIncentiveAmount
        })
      }

      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '35' }, connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      if (fields.inclusive_flag == 'Y') {
        // For Cash Account credit to Airpay
        const cashEntry = await cashAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.customer_charges,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: connection
        })

        if (cashEntry.status === undefined || cashEntry.status !== 200) return cashEntry

        // For Points Account Credit from Airpay Account
        const pointsEntry = await pointsAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.customer_charges,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: connection
        })

        if (pointsEntry.status === undefined || pointsEntry.status !== 200) return pointsEntry

        // Airpay Debit points ledger enntry
        const pointsLedgerAP = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.customer_charges,
          mode: 'dr',
          transaction_type: 35, // Shopping Incentive type
          description: 'Debit - ' + descType,
          orderid: fields.orderid,
          userid: fields.userid,
          ma_status: 'S',
          corresponding_id: fields.ma_user_id,
          connection: connection,
          cms_merchant: true
        })
        if (pointsLedgerAP.status === 400) {
          return pointsLedgerAP
        }
      }

      // Debit Entries for all shares
      fields.distributeObj = distributeObj
      const allShares = await this.distributionEntries(null, fields, connection)
      if (allShares.status === 400) return allShares

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) connection.release()
    }
  }
}

module.exports = shoppingDistribution
