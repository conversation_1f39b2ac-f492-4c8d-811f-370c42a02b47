/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const incentiveDetailsController = require('./incentiveDetailsController')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class incentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_incentive_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_incentive_master_id'
  }


  /**
     * Creates a new entry in ma_neft_details table
     */
  // eslint-disable-next-line camelcase
  static async createEntry (_, { ma_user_id, transaction_type, user_type, connection = null }) {
    log.logger({ pagename: 'incentiveController.js', action: 'createEntry', type: 'request', fields: { ma_user_id, transaction_type, user_type } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      const _result = await this.insert(connection, {
        data: {
          ma_user_id,
          transaction_type,
          user_type
        }
      })
      log.logger({ pagename: 'incentiveController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
    * Add entry in Incentive Master table
    */
  static async addIncentive (_, args) {
    log.logger({ pagename: 'incentiveController.js', action: 'addIncentive', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)
      const insertedData = await this.createEntry(_, {
        ma_user_id: args.ma_user_id,
        transaction_type: args.transaction_type,
        user_type: args.user_type,
        connection
      })
      if (insertedData.status === 200) {
        for (const details of args.details) {
          const insertedDetails = await incentiveDetailsController.createEntry(_, {
            ma_im_id: insertedData.id,
            ma_user_id: details.ma_user_id,
            percentage: details.percentage,
            fixed_cost: details.fixed_cost,
            connection
          })
          if (insertedDetails.status !== 200) {
            return insertedDetails
          }
        }
        await mySQLWrapper.commit(connection)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else {
        await mySQLWrapper.rollback(connection)
        return insertedData
      }
    } catch (error) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'addIncentive', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
    * Get Incentive Master By Id
    */
  // eslint-disable-next-line camelcase
  static async getIncentiveById (_, args) {
    log.logger({ pagename: 'incentiveController.js', action: 'getIncentiveById', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      args.query.splice(args.query.indexOf('status'), 1)
      args.query.splice(args.query.indexOf('message'), 1)
      let queryParams = '1'
      if (args.query.length > 0) {
        queryParams = args.query.join()
      }
      const sql = `SELECT ${queryParams} FROM ma_incentive_master WHERE ma_incentive_master_id = ${args.ma_incentive_master_id}`

      let queryData = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'incentiveController.js', action: 'getIncentiveById', type: 'response', fields: queryData })
      if (queryData.length > 0) {
        queryData = queryData[0]
        queryData.status = 200
        queryData.message = errorMsg.responseCode[1000]
        queryData.respcode = 1000
        return queryData
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveById', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
    * Get Incentive Master
    */
  // eslint-disable-next-line camelcase
  static async getIncentiveMaster (_, { ma_user_id, transaction_type, connection = null }) {
    log.logger({ pagename: 'incentiveController.js', action: 'getIncentiveMaster', type: 'request', fields: { ma_user_id, transaction_type } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // eslint-disable-next-line camelcase
      const sql = `SELECT ma_incentive_master_id,ma_user_id,transaction_type,user_type FROM ma_incentive_master WHERE ma_user_id=${ma_user_id} AND transaction_type = '${transaction_type}'`
      let insentiveMaster = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'incentiveController.js', action: 'getIncentiveMaster', type: 'response', fields: insentiveMaster })
      if (insentiveMaster.length > 0) {
        insentiveMaster = insentiveMaster[0]
        insentiveMaster.status = 200
        insentiveMaster.message = errorMsg.responseCode[1000]
        insentiveMaster.respcode = 1000
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }

      return insentiveMaster
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveMaster', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  // eslint-disable-next-line camelcase
  static async getIncentiveWithDetails (_, { ma_user_id, transaction_type, connection = null }) {
    log.logger({ pagename: 'incentiveController.js', action: 'getIncentiveWithDetails', type: 'request', fields: { ma_user_id, transaction_type } })
    var isSet = false
    try {
      const incentiveData = {}
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = `SELECT mim.transaction_type,mid.ma_im_id,mid.ma_user_id,mid.percentage
                  FROM ma_incentive_master mim
                  JOIN ma_incentive_details mid ON mim.ma_incentive_master_id = mid.ma_im_id
                  WHERE mim.ma_user_id=${ma_user_id} 
                  AND mim.transaction_type = '${transaction_type}'`
      const insentiveMaster = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'incentiveController.js', action: 'getIncentiveWithDetails', type: 'response', fields: insentiveMaster })
      if (insentiveMaster.length > 0) {
        incentiveData.status = 200
        incentiveData.message = errorMsg.responseCode[1000]
        incentiveData.respcode = 1000
        incentiveData.data = insentiveMaster
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      return incentiveData
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getIncentiveWithDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }
}

module.exports = incentive
