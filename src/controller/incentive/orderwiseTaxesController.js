const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const util = require('../../util/util')

class orderwiseTaxes extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_orderwise_taxes'
  }

  static get PRIMARY_KEY () {
    return 'ma_orderwise_taxes_id'
  }

  /**
     * Creates a new cash ledger entry
     *
     */
  static async createEntry (_, fields) {
    log.logger({ pagename: 'orderwiseTaxesController.js', action: 'createEntry', type: 'request', fields: fields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      if (fields.transaction_status == 'R') {
        fields.amount = -fields.amount
      }
      let ma_transfers_id = null
      if (fields.ma_transfers_id != null && ma_transfers_id != undefined) {
        ma_transfers_id = fields.ma_transfers_id
      }

      // Condition for parentid
      let parent_id = ''
      if ((fields.orderid).includes('-')) {
        parent_id = (fields.orderid).substr((fields.orderid).lastIndexOf('-') + 1)
      } else {
        parent_id = (fields.orderid)
      }
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(parent_id, fields.connection)

      var sales_id = 0
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'	
      var decryptionKey = util[env].secondaryEncrptionKey	
      const salesIdUser = await this.rawQuery(`SELECT ma_user_master_id, profileid, userid, user_password, roleid, distributer_user_master_id, reseller_user_master_id, email_id, mobile_id, loginkey, firstname, lastname, address, city, state, country, pincode, user_type, user_status, username, password, security_pin, security_pin_attempts, security_lock_expiry, security_pin_expiry, apikey, last_login, imei, login_ip, gst_number, CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan, aadhar_number, company, sales_id, zone, addedon, updatedon, jwtkey, logo, mer_user, esign_link from ma_user_master where profileid= ${fields.ma_user_id}`, fields.connection)	

      // const salesIdUser = await this.rawQuery('SELECT * from ma_user_master where profileid='+fields.ma_user_id,fields.connection)
      if(salesIdUser.length > 0) {
        if(salesIdUser[0].sales_id > 0){
          sales_id = salesIdUser[0].sales_id
        }
      }

      const _result = await this.insert(fields.connection, {
        data: {
          transaction_type: fields.transaction_type,
          ma_transfers_id,
          orderid: fields.orderid,
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          gst_amount: fields.gst_amount,
          tds_amount: fields.tds_amount,
          ma_status: fields.ma_status,
          sales_id,
          parent_id,
          parent_transaction_master_id
        }
      })
      log.logger({ pagename: 'orderwiseTaxesController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) fields.connection.release()
    }
  }
}

module.exports = orderwiseTaxes
