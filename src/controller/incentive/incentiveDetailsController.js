const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class incentiveDetails extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_incentive_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_incentive_details_id'
  }

  /**
     * Creates a new entry in ma_neft_details table
     */
  // eslint-disable-next-line camelcase
  static async createEntry (_, { ma_im_id, ma_user_id, percentage, fixed_cost, connection = null }) {
    log.logger({ pagename: 'incentiveDetailsController.js', action: 'createEntry', type: 'request', fields: { ma_im_id, ma_user_id, percentage, fixed_cost } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const _result = await this.insert(connection, {
        data: {
          ma_im_id,
          ma_user_id,
          percentage,
          fixed_cost
        }
      })
      log.logger({ pagename: 'incentiveDetailsController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) {
        connection.release()
      }
    }
  }
}

module.exports = incentiveDetails
