/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class MonthlyIncentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_monthly_incentives_process'
  }

  static get PRIMARY_KEY () {
    return 'ma_monthly_incentives_process_id'
  }


  static async createEntry (_, fields,connection) {
    log.logger({ pagename: 'monthlyIncentiveDistributionController.js', action: 'createEntry', type: 'request', fields })
    try {
      const _result = await this.insert(connection, {
        data: fields
      })
      if (_result.affectedRows > 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, insertId: _result.insertId }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028], respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {

    }
  }
  /**
     * Creates a new entry in ma_neft_details table
     */
  // eslint-disable-next-line camelcase


  static async getGlobalIncentiveSetting (fields) {
    log.logger({ pagename: 'monthlyIncentiveDistributionController.js', action: 'getGlobalIncentiveSetting', type: 'request', fields: fields })
    try {
      const sql = `SELECT state,user_type FROM ma_user_master WHERE profileid = '${fields.ma_user_id}'`
      const userData = await this.rawQuery(sql, fields.connection)
      var incentiveType = ''
      const sqlincmer = `SELECT incentive_type FROM ma_incentives_global_settings WHERE ma_user_id = ${fields.ma_user_id} AND user_type = '${userData[0].user_type}'  AND CURRENT_DATE >= from_date AND CURRENT_DATE <= to_date `
      const dataincmer = await this.rawQuery(sqlincmer, fields.connection)
      if (dataincmer.length > 0) {
        incentiveType = dataincmer[0].incentive_type
      } else {
        const sqlincstate = `SELECT incentive_type FROM ma_incentives_global_settings WHERE ma_user_id = 0 AND state_id = ${userData[0].state} AND user_type = '${userData[0].user_type}'  AND CURRENT_DATE >= from_date AND CURRENT_DATE <= to_date`
        const dataincstate = await this.rawQuery(sqlincstate, fields.connection)
        if (dataincstate.length > 0) {
          incentiveType = dataincstate[0].incentive_type
        } else {
          const sqlincglobal = `SELECT incentive_type FROM ma_incentives_global_settings WHERE ma_user_id = 0 AND state_id = 0 AND user_type = '${userData[0].user_type}' AND CURRENT_DATE >= from_date AND CURRENT_DATE <= to_date`
          const dataincglobal = await this.rawQuery(sqlincglobal, fields.connection)
          if (dataincglobal.length > 0) {
            incentiveType = dataincglobal[0].incentive_type
          }
        }
      }
      return { incentiveType: incentiveType}
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getGlobalIncentiveSetting', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}

module.exports = MonthlyIncentive
