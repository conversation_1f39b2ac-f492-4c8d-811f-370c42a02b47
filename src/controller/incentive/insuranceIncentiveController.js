/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const util = require('../../util/util')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const balanceController = require('../balance/balanceController')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const common = require('../../util/common')
const commonFunction = require('../common/commonFunctionController')
class insuranceIncentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_slab_distribution_insurance'
  }

  static get PRIMARY_KEY () {
    return 'ma_slab_distribution_insurance_id'
  }


  static async incentiveDistribution (fields) {
    log.logger({ pagename: 'insuranceIncentive.js', action: 'incentiveDistribution', type: 'request', fields: fields })
    const userSqlForDistributerIdForGetDistribution = 'SELECT distributer_user_master_id FROM ma_user_master where profileid ='
    const userSqlForDistributerIdForGetDistributionResp = await this.rawQuery(userSqlForDistributerIdForGetDistribution + fields.ma_user_id, fields.connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'userSqlForDistributerIdForGetDistributionResp', fields: userSqlForDistributerIdForGetDistributionResp })
    fields.distributerIdForGetDistribution = 0
    if (userSqlForDistributerIdForGetDistributionResp.length > 0) fields.distributerIdForGetDistribution = userSqlForDistributerIdForGetDistributionResp[0].distributer_user_master_id
    const config = await this.getDistributionConfig(fields)
    delete fields.distributerIdForGetDistribution
    if ( config.status === 400) {
        return config
    }
    var finalUserObj = []
    //console.log(config.configdata.RT)
    finalUserObj.push({
        ma_user_id : fields.ma_user_id,
        amount: config.configdata.RT.deduction_share,
        tdstype : (fields.pan !== null && fields.pan !== undefined) ? 3 : 1
    })  
    // PAN DECRYPTION
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    var decryptionKey = util[env].secondaryEncrptionKey
    if (fields.distributer_user_master_id != 0) {
        const sqldt = `Select profileid,userid,distributer_user_master_id,user_type,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan from ma_user_master where profileid=${fields.distributer_user_master_id} AND user_status = "Y"`
        //const sqldt = 'Select profileid,userid,distributer_user_master_id,user_type,pan from ma_user_master where profileid=' + fields.distributer_user_master_id + ' AND user_status = "Y"'
        
        const respdt =   await this.rawQuery(sqldt, fields.connection)
        if (respdt.length > 0 ) {
            if ( respdt[0].user_type == 'DT' && respdt[0].distributer_user_master_id != 0){
                finalUserObj.push({
                    ma_user_id : respdt[0].profileid,
                    amount: config.configdata.DT.deduction_share,
                    tdstype : (respdt[0].pan !== null && respdt[0].pan !== undefined) ? 3 : 1
                })
                  // PAN DECRYPTION
                  const sqlsd = `Select profileid,userid,distributer_user_master_id,user_type,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan from ma_user_master where profileid=${respdt[0].distributer_user_master_id} AND user_status = "Y"`

                // const sqlsd = 'Select profileid,userid,distributer_user_master_id,user_type,pan from ma_user_master where profileid=' + respdt[0].distributer_user_master_id + ' AND user_status = "Y"'
                const respsd =   await this.rawQuery(sqlsd, fields.connection)
                if ( respsd.length > 0) {
                    finalUserObj.push({
                        ma_user_id : respsd[0].profileid,
                        amount: config.configdata.SD.deduction_share,
                        tdstype : (respsd[0].pan !== null && respsd[0].pan !== undefined) ? 3 : 1
                    })
                }
            } else if (respdt[0].user_type == 'DT' && respdt[0].distributer_user_master_id == 0) {
                // distribute dt
                finalUserObj.push({
                    ma_user_id : respdt[0].profileid,
                    amount: config.configdata.DT.deduction_share,
                    tdstype : (respdt[0].pan !== null && respdt[0].pan !== undefined) ? 3 : 1
                })
            } 
            else if ( respdt[0].user_type == 'SDT') {
                // distribute sd
                finalUserObj.push({
                    ma_user_id : respdt[0].profileid,
                    amount: config.configdata.SD.deduction_share,
                    tdstype : (respdt[0].pan !== null && respdt[0].pan !== undefined) ? 3 : 1
                })
            }
        }
    }
    console.log ('fields user obj', finalUserObj)
    var utilisedamount = 0
    finalUserObj.forEach(element => {
        utilisedamount = utilisedamount + element.amount
      })
    fields.distributeAmount = utilisedamount 
    /*finalUserObj.push({
        ma_user_id : util.airpayCommissionId,
        amount:  utilisedamount,
        tdstype : ''
    })*/  
    console.log('after ap obj', finalUserObj)

      // Debit Entries for all shares
      fields.distributeObj = finalUserObj
      const allShares = await this.distributionEntries(fields)
      if (allShares.status === 400) {
        return allShares
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    //console.log('final user data', finalUserObj)
  }

  static async getDistributionConfig (fields) {
    log.logger({ pagename: 'insuranceIncentive.js', action: 'getDistributionConfig', type: 'request', fields: fields })
    var isSet = false
    let connection = null
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
      }
      var shareObj = []
      const sqlmer = `select * FROM ma_slab_distribution_insurance WHERE ma_insurance_category_master_id = ${fields.catId} AND ma_insurance_subcategory_master_id=${fields.subCatId} AND record_status = 'Y'`

      console.log(sqlmer)
      const _commssionmer = await this.rawQuery(sqlmer, connection)
      if (_commssionmer.length > 0) {
        shareObj.RT = {
            share:  _commssionmer[0].rt_share,
            applied_type: _commssionmer[0].rt_applied_type
        }
        shareObj.DT = {
            share : _commssionmer[0].dt_share,
            applied_type : _commssionmer[0].dt_applied_type
        }
        shareObj.SD = {
            share: _commssionmer[0].sd_share,
            applied_type: _commssionmer[0].sd_applied_type
        }
      } else {
        // Check other configuration (For now this is for distributor specific)
        const sqlDist = `select * FROM ma_slab_distribution_insurance WHERE ma_insurance_category_master_id = ${fields.catId} AND ma_insurance_subcategory_master_id=${fields.subCatId} AND record_status = 'Y'`
        const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql: sqlDist, fields, connection })
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
        if (getDistributionAdditionalCondition.status == 200) {
          shareObj.RT = {
            share: getDistributionAdditionalCondition.configurationData[0].rt_share,
            applied_type: getDistributionAdditionalCondition.configurationData[0].rt_applied_type
          }
          shareObj.DT = {
            share : getDistributionAdditionalCondition.configurationData[0].dt_share,
            applied_type : getDistributionAdditionalCondition.configurationData[0].dt_applied_type
          }
          shareObj.SD = {
            share: getDistributionAdditionalCondition.configurationData[0].sd_share,
            applied_type: getDistributionAdditionalCondition.configurationData[0].sd_applied_type
          }
        } else {
          const sqlstate = `select * FROM ma_slabwise_distribution_bbps WHERE state_master_id = '${fields.stateid}' AND ma_user_id = '${fields.ma_user_id}' AND record_status = 'Y'`
          const _commssionstate = await this.rawQuery(sqlstate, connection)
          if (_commssionstate.length > 0) {
            shareObj.RT = {
              share:  _commssionstate[0].rt_share,
              applied_type: _commssionstate[0].rt_applied_type
            }
            shareObj.DT = {
              share : _commssionstate[0].dt_share,
              applied_type : _commssionstate[0].dt_applied_type
            }
            shareObj.SD = {
              share: _commssionstate[0].sd_share,
              applied_type: _commssionstate[0].sd_applied_type
            }
          }
        }
      }

      console.log('share obj', shareObj)

      var finalObj = {}
      
      Object.keys(shareObj).forEach((key, index) => {
              var calcamt = ''
          if (shareObj[key].applied_type == '2' && shareObj[key].share != 0) {
              //var data = key
              calcamt = fields.amount
              finalObj[key] = { deduction_share : shareObj[key].share * calcamt / 100 }
          } else {
            finalObj[key] = { deduction_share  : shareObj[key].share }
          }
      })

      console.log('final obj', finalObj)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], configdata : finalObj }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionConfig', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

 static async distributionEntries (fields) {
    log.logger({ pagename: 'insuranceIncentive.js', action: 'distributionEntries', type: 'request', fields: fields })
      //return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    try {
      let executionResponse = {}
      const distribution = fields.distributeObj
      console.log('distribution', distribution)
      const incentiveOrdeid = common.incentiveOrderid(fields.ma_user_id)    
      console.log(incentiveOrdeid)

        await mySQLWrapper.beginTransaction(fields.connection)

      // For Cash Account credit to Airpay
        executionResponse = await cashAccount.createEntry('_', {
            ma_user_id: util.airpayUserId,
            amount: fields.distributeAmount,
            transaction_type: 'IN',
            ma_status: 'S',
            orderid: incentiveOrdeid,
            userid: fields.userid,
            corresponding_id: fields.ma_user_id,
            connection: fields.connection
          })

          if (executionResponse.status === undefined || executionResponse.status !== 200) {
            await mySQLWrapper.rollback(fields.connection)
            return executionResponse
          }
          // For Points Account Credit from Airpay Account
          executionResponse = await pointsAccount.createEntry('_', {
            ma_user_id: util.airpayUserId,
            amount: fields.distributeAmount,
            transaction_type: 'IN',
            ma_status: 'S',
            orderid: incentiveOrdeid,
            userid: fields.userid,
            corresponding_id: fields.ma_user_id,
            connection: fields.connection
          })
          if (executionResponse.status === undefined || executionResponse.status !== 200) {
            await mySQLWrapper.rollback(fields.connection)
            return executionResponse
          }
        

      // Points Ledger Debit from Airpay User
        executionResponse = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.distributeAmount,
          mode: 'dr',
          transaction_type: 19, // 19 :insurance incentive
          description: util.incentivePointsDebitDescription + 'Insurance',
          orderid: incentiveOrdeid,
          userid: fields.userid,
          ma_status: 'S',
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })
        if (executionResponse.status === 400) {
           await mySQLWrapper.rollback(fields.connection)
          return executionResponse
        }
        // ma_monthly_incentives table entry 

        var monthlyIncentives = await this.createInsMonth('_', {
          ma_user_id: fields.ma_user_id,
          ma_transaction_count: fields.transaction_count,
          ma_transaction_amount: fields.amount,
          ma_incentive_amount: fields.distributeAmount,
          ma_from_date: fields.from_date,
          ma_to_date: fields.to_date,
          table_name: 'ma_monthly_incentives',
          connection: fields.connection
        })
        if (monthlyIncentives.status === 400) {
           await mySQLWrapper.rollback(fields.connection)
          return monthlyIncentives
        }


      for (const value of distribution) {
          console.log('distribution key', value)
          var subAmount = value.amount
          if (subAmount <= 0) { 
              continue
          }    
          if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
            // TDS will be be applied
            var tds = 0
            const commissionDetails = await commissionController.getCommission('_', {
              ma_user_id: value.ma_user_id,
              ma_commission_type: 19,
              amount: subAmount,
              ma_deduction_type: value.tdstype,
              connection: fields.connection
            }
            )

            tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
            // TDS entry
            if (tds > 0) {
              subAmount = subAmount - tds
              const tdsInsert = await pointsLedger.createEntry('_', {
                ma_user_id: util.airpayCommissionId,
                amount: tds,
                mode: 'cr',
                transaction_type: 11, // 11 tds use
                description: util.IncentiveCommissionDescription + 'Insurance',
                ma_status: 'S',
                orderid: value.ma_user_id + '-' + incentiveOrdeid,
                userid: fields.userid,
                corresponding_id: fields.ma_user_id,
                connection: fields.connection
              }
              )
              if (tdsInsert.status === 400) {
                await mySQLWrapper.rollback(fields.connection)
                return tdsInsert
              }

              // ma_monthly_incentives_details table entry 

              const monthlyIncentivesdet = await this.createInsMonth('_', {
                    ma_monthly_incentives_id: monthlyIncentives.insertId,
                    ma_user_id: util.airpayCommissionId,
                    ma_insurance_category_master_id: fields.catId,
                    ma_insurance_subcategory_master_id: fields.subCatId,
                    ma_orderid: value.ma_user_id + '-' + incentiveOrdeid,
                    ma_amount: tds,
                    ma_description: util.IncentiveCommissionDescription + 'Insurance',
                    ma_from_date: fields.from_date,
                    ma_to_date: fields.to_date,
                    table_name: 'ma_monthly_incentives_details',
                    connection: fields.connection
                  })
                  if (monthlyIncentivesdet.status === 400) {
                     await mySQLWrapper.rollback(fields.connection)
                    return monthlyIncentivesdet
                  }
            }
          }

          const pointsLedgerCredits = await pointsLedger.createEntry('_', {
            ma_user_id: value.ma_user_id,
            amount: subAmount,
            mode: 'cr',
            transaction_type: 19,
            description: util.IncentiveEarnedMerchant + 'Insurance',
            ma_status: 'S',
            orderid: incentiveOrdeid,
            userid: fields.userid,
            corresponding_id: fields.ma_user_id,
            connection: fields.connection
          })
          if (pointsLedgerCredits.status === 400) {
            await mySQLWrapper.rollback(fields.connection)
            return pointsLedgerCredits
          }

          // ma_monthly_incentives_details table entry 

            const monthlyIncentivesdetnew = await this.createInsMonth('_', {
                  ma_monthly_incentives_id: monthlyIncentives.insertId,
                  ma_user_id: value.ma_user_id,
                  ma_insurance_category_master_id: fields.catId,
                  ma_insurance_subcategory_master_id: fields.subCatId,
                  ma_orderid: incentiveOrdeid,
                  ma_amount: subAmount,
                  ma_description: util.IncentiveEarnedMerchant + 'Insurance',
                  ma_from_date: fields.from_date,
                  ma_to_date: fields.to_date,
                  table_name: 'ma_monthly_incentives_details',
                  connection: fields.connection
                })
                if (monthlyIncentivesdetnew.status === 400) {
                   await mySQLWrapper.rollback(fields.connection)
                  return monthlyIncentivesdetnew
                }

        }
        
      await mySQLWrapper.commit(fields.connection)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  } 

  static async createInsMonth(_, fields) {
    log.logger({ pagename: 'insuranceIncentive.js', action: 'createInsMonth', type: 'request', fields: fields })
    var isSet = false
    let connection = null
    try {
      // Create seperate connection if not passed as an argument

      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
      }
      /*// Check if User exists or not
      var sql = `select ma_user_master_id from ma_user_master where profileid=${fields.ma_user_id} AND userid=${fields.userid}`
      const userDetails = await this.rawQuery(sql, connection)
      console.log('length = ',userDetails);
      if (userDetails.length <= 0) {
        return { status: 400, message: 'Invalid user' }
      }*/

      this.TABLE_NAME = fields.table_name
      delete fields.table_name
      delete fields.connection
      const _result = await this.insert(connection, {
        data: fields
      })
      log.logger({ pagename: 'insuranceIncentive.js', action: 'createTransaction', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, insertId: _result.insertId }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028], respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }
}
module.exports = insuranceIncentive
