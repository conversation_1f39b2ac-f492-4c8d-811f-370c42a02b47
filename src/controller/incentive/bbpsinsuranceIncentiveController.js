const DAO = require('../../lib/dao')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const validator = require('../../util/validator')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const common_fns = require('../../util/common_fns')
const path = require('path')
const commonFunction = require('../common/commonFunctionController')
class BbpsInsuranceIncentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_slabwise_distribution_bbps_insurance'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_bbps_insurance_id'
  }

  static async getDistributionEntry (fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'request', fields: fields })
    try {
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var condition = ''

      if (fields.provider_id != undefined && fields.provider_id != null && fields.provider_id > 0) {
        condition = ' AND provider_id=' + fields.provider_id
      } else if (fields.provider_name != undefined && fields.provider_name != null && fields.provider_name != '') {
        condition = ` AND provider_name= '${fields.provider_name.toUpperCase()}' `
      } else {
        return { status: 400, message: 'failed operator missing', respcode: 1001 }
      }
      var sql = `SELECT * FROM ma_slabwise_distribution_bbps_insurance where record_status='Y' and ${fields.amount} BETWEEN min_amount AND max_amount ${condition} `
      console.log('Here query ', sql)
      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerSql, fields.connection)
        log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Check condition for integrated users
      const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
      const resultint = await this.rawQuery(userintegrated, fields.connection)
      log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        ma_user_id = util.integratedIncentive[resultint[0].integration_code]
        result = {}
        const retailerintegratedSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerintegratedSql, fields.connection)
        log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'response - Integrated Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        } else {
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
        }
      }

      // Check other configuration (For now this is for distributor specific)
      result = {}
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }

      // Reinitializing variables
      ma_user_id = 0
      result = {}

      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(stateSql, fields.connection)
        log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'response - State Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}

      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      result = await this.rawQuery(defaultSql, fields.connection)
      log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getDistributionEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * To distribute the incentives
   * Array with  ma_user_id,amount,applied_type,user_type
   */
  static async distributionEntries (fields) {
    log.logger({ pagename: path.basename(__filename), action: 'distributionEntries', type: 'request', fields: fields })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.orderid, fields.connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = fields.distributeObj
      for (const value of distribution) {
        var subAmount = value.amount
        var descPart = ''
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          var tds = 0
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: '18',
            amount: subAmount,
            ma_deduction_type: value.TDStype,
            connection: fields.connection
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          console.log('TDS', tds)
          var appliedCommission = ''
          // TDS entry
          if (tds > 0) {
            // const common = require('../../util/common')
            // const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '11' }, fields.connection)
            // const descType = descObj.code_desc ? descObj.code_desc : ''
            subAmount = subAmount - tds
            appliedCommission = commissionDetails.appliedCommission
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: util.IncentiveCommissionDescription + 'Insurance',
              // description: 'Credit - ' + descType,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: fields.connection
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, fields.connection)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: '18',
              orderid: fields.orderid,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: fields.connection
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
            // [END] Make entry in ma_orderwise_taxes table
          }
        }
        if (subAmount > 0) {
          const common = require('../../util/common')
          const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '18' }, fields.connection)
          const descType = (descObj.code_desc ? descObj.code_desc : '').replace(/Recharge/i, 'Recharge Insurance')
          descPart = tds !== 0 ? ' (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: fields.connection })
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: '18',
              // description: util.IncentiveEarnedMerchant + 'Recharge ' + descPart,
              description: 'Credit - ' + descType + descPart,
              ma_status: 'S',
              orderid: fields.orderid,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: '18',
              // description: util.IncentiveEarnedMerchant + 'Recharge ' + descPart,
              description: 'Credit - ' + descType + descPart,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: '18',
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id

            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, fields.connection)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * Calculate amount based on applied_type and operative_amount
   */
  static async calculateIncentiveAmount (fields) {
    log.logger({ pagename: path.basename(__filename), action: 'calculateIncentiveAmount', type: 'request', fields: fields })
    var customerCharges = fields.data.customer_charges
    if (fields.data.customer_charges_applied_type === '2') {
      customerCharges = fields.data.customer_charges * parseFloat(fields.amount) / 100
    }
    var amount = 0
    if (fields.data.applied_type === '1') {
      amount = fields.data.amount
    } else if (fields.data.applied_type === '2') {
      if (fields.data.operative_amount === '1') { // % of transaction amount
        amount = parseFloat(fields.amount) * fields.data.amount / 100
      } else if (fields.data.operative_amount === '2') { // % of customer charges
        amount = customerCharges * fields.data.amount / 100
      }
    }
    log.logger({ pagename: path.basename(__filename), action: 'calculateIncentiveAmount', type: 'responce', fields: amount })
    return amount
  }

  static async incentiveDistribution (fields) {
    log.logger({ pagename: path.basename(__filename), action: 'incentiveDistribution', type: 'request', fields: fields })
    try {
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey

      /**
     * Get Transaction Details
     */
      // fields.orderid - Order id field used. Need to change if key is changed
      const txnSql = `SELECT * from ma_billpay_transaction_master bpt where bpt.order_id = '${fields.orderid}'`
      const txnDetails = await this.rawQuery(txnSql, fields.connection)
      if (txnDetails.length <= 0) {
        return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
      }

      fields.provider_id = txnDetails[0].provider_id
      fields.provider_name = txnDetails[0].provider_name

      /** Amount varification */
      // if (fields.amount != txnDetails[0].amount) {
      //   return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Operator id or operator name required' }
      // }
      /* End  of txn details */
      if (fields.provider_name == undefined || fields.provider_name == null) {
        if (fields.provider_id == undefined || fields.provider_id == null) {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Provider id or provider name required' }
        }
      }
      // PAN DECRYPTION
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master where profileid =`
      // const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan FROM ma_user_master where profileid ='
      const userDetails = await this.rawQuery(userSql + fields.ma_user_id, fields.connection)
      log.logger({ pagename: path.basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })
      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }
      fields.state_master_id = fields.state || fields.stateid || userDetails[0].state
      // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id

      const distributionShare = await this.getDistributionEntry(fields)
      console.log('Distribution Details', distributionShare)
      // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
      delete fields.distributerIdForGetDistribution
      if (distributionShare.status == 400) {
        return distributionShare
      }
      var totalIncentiveAmount = 0
      var distributeObj = []
      if (distributionShare[0].rt_share > 0) {
        fields.data = {
          amount: distributionShare[0].rt_share,
          applied_type: distributionShare[0].rt_share_applied_type,
          operative_amount: distributionShare[0].rt_operative_amount,
          customer_charges: distributionShare[0].customer_charges,
          customer_charges_applied_type: distributionShare[0].customer_charges_applied_type,
          user_type: 'RT'
        }
        const rt_share = await this.calculateIncentiveAmount(fields)
        totalIncentiveAmount += rt_share
        const UserTDStype = this.getTDStype(userDetails[0].pan)
        distributeObj.push({
          ma_user_id: fields.ma_user_id,
          amount: rt_share,
          TDStype: UserTDStype // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, fields.connection)
        log.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
        // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
          // Push DT details
            if (distributionShare[0].dt_share > 0) {
              fields.data = {
                amount: distributionShare[0].dt_share,
                applied_type: distributionShare[0].dt_share_applied_type,
                operative_amount: distributionShare[0].dt_operative_amount,
                customer_charges: distributionShare[0].customer_charges,
                customer_charges_applied_type: distributionShare[0].customer_charges_applied_type,
                user_type: 'DT'
              }
              const dt_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += dt_share

              const DistTDStype = this.getTDStype(distribuetrDetails[0].pan)
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                TDStype: DistTDStype // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', fields.connection)
              log.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
              // Push SDT details
                if (distributionShare[0].sd_share > 0) {
                  fields.data = {
                    amount: distributionShare[0].sd_share,
                    applied_type: distributionShare[0].sd_share_applied_type,
                    operative_amount: distributionShare[0].sd_operative_amount,
                    customer_charges: distributionShare[0].customer_charges,
                    customer_charges_applied_type: distributionShare[0].customer_charges_applied_type,
                    user_type: 'SDT'
                  }
                  const sd_share = await this.calculateIncentiveAmount(fields)
                  totalIncentiveAmount += sd_share
                  const SuperTDSType = this.getTDStype(superdistribuetrDetails[0].pan)
                  distributeObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    TDStype: SuperTDSType // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (distributionShare[0].sd_share > 0) {
              fields.data = {
                amount: distributionShare[0].sd_share,
                applied_type: distributionShare[0].sd_share_applied_type,
                operative_amount: distributionShare[0].sd_operative_amount,
                customer_charges: distributionShare[0].customer_charges,
                customer_charges_applied_type: distributionShare[0].customer_charges_applied_type,
                user_type: 'SDT'
              }
              const sd_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += sd_share
              const DistTDStype = this.getTDStype(distribuetrDetails[0].pan)
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                TDStype: DistTDStype // With PAN or without
              })
            }
          }
        }
      }

      if (totalIncentiveAmount > 0) {
      // For Cash Account credit to Airpay
        const cashEntry = await cashAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: totalIncentiveAmount,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })

        if (cashEntry.status === undefined || cashEntry.status !== 200) {
          return cashEntry
        }

        // For Points Account Credit from Airpay Account
        const pointsEntry = await pointsAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: totalIncentiveAmount,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })
        if (pointsEntry.status === undefined || pointsEntry.status !== 200) {
          return pointsEntry
        }

        // Airpay Debit points ledger enntry
        const common = require('../../util/common')
        const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '18' }, fields.connection)
        const descType = (descObj.code_desc ? descObj.code_desc : '').replace(/Recharge/i, 'Recharge Insurance')
        const pointsLedgerAP = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: totalIncentiveAmount,
          mode: 'dr',
          transaction_type: '18',
          // description: util.incentivePointsDebitDescription + 'Recharge',
          description: 'Debit - ' + descType,
          orderid: fields.orderid,
          userid: fields.userid,
          ma_status: 'S',
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })
        if (pointsLedgerAP.status === 400) {
          return pointsLedgerAP
        }

        // Debit Entries for all shares
        fields.distributeObj = distributeObj
        const allShares = await this.distributionEntries(fields)
        if (allShares.status === 400) {
          return allShares
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
    // console.log('Details here', distributeObj)
    // return { status: 200, message: 'hjgdf', respcode: '1001' }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'incentiveDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static getTDStype (pan = null) {
    let TDStypevalRT = '1' // // Without PAN
    if (typeof (pan) === 'string') {
      const isValidPANRT = validator.validatePAN(pan)
      if (isValidPANRT) {
        TDStypevalRT = '3' // With PAN
      }
    }
    return TDStypevalRT
  }
}

module.exports = BbpsInsuranceIncentive
