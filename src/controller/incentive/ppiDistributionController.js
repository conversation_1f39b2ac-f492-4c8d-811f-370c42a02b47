const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const balanceController = require('../balance/balanceController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const validator = require('../../util/validator')
const orderwiseTaxes = require('./orderwiseTaxesController')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const common_fns = require('../../util/common_fns')
const commonFunction = require('../common/commonFunctionController')
class ppiDistribution extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_slabwise_distribution_dmt_ppi'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_dmt_ppi_id'
  }

  /**
   * Distribution Configuration for AEPS Incentive
   * fields : amount, ma_user_id,state_master_id,connection
   */
  static async getDistribution (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'request', fields: fields })
    try {
      var ma_user_id = 0
      var result = {}
      var customerCharges = 0
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey

      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =${fields.ma_user_id} and userid = ${fields.userid} limit 1 `
      const userDetails = await this.rawQuery(userSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })
      // log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - RT', fields: userDetails })
      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }
      fields.state_master_id = userDetails[0].state
      // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id
      /* SLABWISE CHANGES : KYC AND NON-KYC CUSTOMER CHANGES  */
      var sql = `SELECT *
      FROM ma_slabwise_distribution_dmt_ppi WHERE ${fields.amount} 
      BETWEEN min_amount AND max_amount AND record_status='Y' `

      if (fields.transfer_mode !== null && fields.transfer_mode !== undefined && fields.transfer_mode != '') {
        sql = sql + ` AND mode='${fields.transfer_mode}' AND mode_status='Y'`
      }
      console.log('sql--->', sql)
      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'retailerSql', fields: retailerSql })
        result = await this.rawQuery(retailerSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          if (result[0].customer_charges_applied_type == 1) {
            customerCharges = result[0].customer_charges
          } else {
            customerCharges = result[0].customer_charges * fields.amount / 100
          }
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          result.customer_charges = customerCharges
          return result
        }
      }

      // Check condition for integrated users
      const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} and usr.id = ${fields.userid} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
      const resultint = await this.rawQuery(userintegrated, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        ma_user_id = util.integratedIncentive[resultint[0].integration_code]
        result = {}
        const retailerintegratedSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerintegratedSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Integrated Retailer Specific', fields: result })
        if (result.length > 0) {
          if (result[0].customer_charges_applied_type == 1) {
            customerCharges = result[0].customer_charges
          } else {
            customerCharges = result[0].customer_charges * fields.amount / 100
          }
        }
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        result.customer_charges = customerCharges
        return result
      }

      // Check other configuration (For now this is for distributor specific)
      result = {}
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields, connection: connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        if (result[0].customer_charges_applied_type == 1) customerCharges = result[0].customer_charges
        else customerCharges = result[0].customer_charges * fields.amount / 100

        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        result.customer_charges = customerCharges
        return result
      }
      // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
      delete fields.distributerIdForGetDistribution

      // Reinitializing variables
      ma_user_id = 0
      result = {}

      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - State Specific', fields: result })
        result = await this.rawQuery(stateSql, connection)
        if (result.length > 0) {
          if (result[0].customer_charges_applied_type == 1) {
            customerCharges = result[0].customer_charges
          } else {
            customerCharges = result[0].customer_charges * fields.amount / 100
          }
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          result.customer_charges = customerCharges
          return result
        }
      }

      // Reinitializing variables
      ma_user_id = 0
      fields.state_master_id = 0
      result = {}

      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - defaultSql', fields: defaultSql })
      result = await this.rawQuery(defaultSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        if (result[0].customer_charges_applied_type == 1) {
          customerCharges = result[0].customer_charges
        } else {
          customerCharges = result[0].customer_charges * fields.amount / 100
        }
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        result.customer_charges = customerCharges
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getDistributionAllHeads (fields, connection) {
    var $final_data = {}

    var distributionObj = []
    var rt_share = 0
    var dt_share = 0
    var sd_share = 0
    var customer_charges = 0

    const bunch = util.transferBunch
    const expectedExactBunch = Math.floor(fields.amount / bunch)
    const smallBunchAmount = fields.amount - expectedExactBunch * bunch
    var transactionChargesAll = 0
    var smallChunck

    // Transaction charges for less than 5k amount
    const requstFields = {
      ma_user_id: fields.ma_user_id,
      userid: fields.userid,
      amount: fields.amount,
      connection
    }
    smallChunck = await this.getDistribution(requstFields)
    if (smallChunck.status !== undefined && smallChunck.status === 200) {
      fields.smallBunch = smallChunck
      transactionChargesAll = transactionChargesAll + smallChunck.customer_charges
    }

    if (transactionChargesAll > 0) {
      distributionObj.push({ transaction_charges: transactionChargesAll })
      // Get the state of retailer
      // // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      let userSql = ''
      if ('userid' in fields && fields.userid != '') {
        userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid = ${fields.ma_user_id} and userid = ${fields.userid} limit 1 `
      } else {
        userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid = ${fields.ma_user_id} limit 1 `
      }

      const userDetails = await this.rawQuery(userSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })
      // log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - RT', fields: userDetails })

      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }

      // Small chunck
      if ((smallChunck != null || smallChunck != undefined) && smallChunck.length > 0) {
        var customer_charges_amount = await this.findValue(fields.amount, 0, smallChunck[0].customer_charges, smallChunck[0].customer_charges_applied_type)
        //  customer charge
        customer_charges += customer_charges_amount
        // RT share
        rt_share += await this.findValue(fields.amount, customer_charges_amount, smallChunck[0].rt_share, smallChunck[0].rt_share_applied_type, smallChunck[0].rt_operative_amount)
        // DT share
        dt_share += await this.findValue(fields.amount, customer_charges_amount, smallChunck[0].dt_share, smallChunck[0].dt_applied_type, smallChunck[0].dt_operative_amount)
        // SD share
        sd_share += await this.findValue(fields.amount, customer_charges_amount, smallChunck[0].sd_share, smallChunck[0].sd_applied_type, smallChunck[0].sd_operative_amount)
      }
      console.log('Calculated shares data', rt_share, dt_share, sd_share, customer_charges)

      var afterDeductionAmt = customer_charges
      const sql = `SELECT percentage from ma_gst_master where ma_user_id=${util.airpayCommissionId} limit 1 `
      const gstAP = await this.rawQuery(sql, connection)
      console.log('Airpay GST', gstAP)
      if (gstAP.length > 0) {
        const AirpayGST = parseFloat((customer_charges - (customer_charges * (100 / (100 + gstAP[0].percentage)))).toFixed(4))
        console.log('Airpay GST', AirpayGST)
        afterDeductionAmt = afterDeductionAmt - AirpayGST
      }

      console.log('after Deduction Amount before share starts', afterDeductionAmt)

      if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        afterDeductionAmt = afterDeductionAmt - rt_share
        distributionObj.push({
          ma_user_id: fields.ma_user_id,
          amount: parseFloat(rt_share).toFixed(4),
          user_type: 'RT',
          TDS: await this.getTDS(fields.ma_user_id, TDStypeval, rt_share, connection) // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid = ${userDetails[0].distributer_user_master_id} limit 1 `
        const distribuetrDetails = await this.rawQuery(userSql, connection)
        distribuetrDetails.share = dt_share
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - dt_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: parseFloat(dt_share).toFixed(4),
                user_type: 'DT',
                TDS: await this.getTDS(distribuetrDetails[0].profileid, TDStypeval, dt_share, connection) // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid = ${distribuetrDetails[0].distributer_user_master_id} and user_type='SDT' limit 1 `
              const superdistribuetrDetails = await this.rawQuery(userSql, connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  afterDeductionAmt = afterDeductionAmt - sd_share
                  distributionObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: parseFloat(sd_share).toFixed(4),
                    gst_number: superdistribuetrDetails[0].gst_number,
                    user_type: 'SDT',
                    TDS: await this.getTDS(superdistribuetrDetails[0].profileid, TDStypeval, sd_share, connection) // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - sd_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: parseFloat(sd_share).toFixed(4),
                user_type: 'SDT',
                gst_number: distribuetrDetails[0].gst_number,
                TDS: await this.getTDS(distribuetrDetails[0].profileid, TDStypeval, sd_share, connection) // With PAN or without
              })
            }
          }
        }
      }
      // Airpay Share array
      if (afterDeductionAmt > 0) {
        distributionObj.push({
          ma_user_id: util.airpayCommissionId,
          user_type: 'SD',
          amount: parseFloat(afterDeductionAmt).toFixed(4),
          TDS: 0
        })
      }

      console.log('Final Object', distributionObj)
    }

    return distributionObj
  }

  static async getTDS (ma_user_id, type, amount, connection) {
    const commissionDetails = await commissionController.getCommission('_', {
      ma_user_id: ma_user_id,
      ma_commission_type: '2',
      amount: amount,
      ma_deduction_type: type,
      connection
    })

    const tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
    return tds
  }

  static async globalValues (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'request', fields })
    var txnCharge = {}
    var state_master_id = 0
    var ma_user_id = 0
    var result = {}
    // Customer Charges  calculate
    if (fields.transaction_charges <= 0) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }

    var customerCharges = fields.transaction_charges

    /* NSDL EKYC CHANGES : DT AND SDT INCENTIVE changes */
    if (fields.amount > 25000 && fields.ma_bank_on_boarding_id == 5) {
      log.logger({ pagename: require('path').basename(__filename), action: 'NSDL EKYC CHANGES : DT AND SDT INCENTIVE changes', type: 'request', fields })
      const kycSqlSlabSQL = 'SELECT dt_share_amt,sdt_share_amt FROM ma_kyc_dt_sdt_shares ORDER BY addedon DESC LIMIT 1'
      const kycSqlSlabSQLResult = await this.rawQuery(kycSqlSlabSQL, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'kycSqlSlabSQLResult', type: 'request', kycSqlSlabSQLResult })
      if (kycSqlSlabSQLResult.length > 0) {
        /* CHANGE THE OBJECT TYPE */
        const result = await this.buildShareObject([{
          dt_share: kycSqlSlabSQLResult[0].dt_share_amt,
          dt_applied_type: 1,
          dt_operative_amount: 2,
          sd_share: kycSqlSlabSQLResult[0].sdt_share_amt,
          sd_applied_type: 1,
          sd_operative_amount: 2
        }])
        if (result) {
          return result
        }
      }
    }
    /* END NSDL EKYC CHANGES */

    // Get Global settings
    var sql = `SELECT state_master_id,ma_user_id,dt_share,
    dt_applied_type,dt_operative_amount,
    sd_share,sd_applied_type,sd_operative_amount,
    ma_bank_on_boarding_id,record_status,
    settings_flag 
    FROM ma_transfers_shares 
    where record_status='Y' and settings_flag='2'`
    if (('ma_bank_on_boarding_id' in fields) && validator.definedVal(fields.ma_bank_on_boarding_id) && fields.ma_bank_on_boarding_id > 0) {
      sql += ` and ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id} `
    }
    // Check retailer specific configuration
    if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
      ma_user_id = fields.ma_user_id

      /* NEW CHANGES :  CHECK DT SPECIFIC CONFIG IN `ma_transfers_shares`  */
      /* INGORE FOR RT : 34221 [user_type = 'integrated'] */
      if (ma_user_id != 34221) {
        const checkDtQuery = `SELECT distributer_user_master_id FROM ma_user_master where profileid = ${ma_user_id} LIMIT 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'sql - checkDtId', fields: checkDtQuery })
        const checkDtResult = await this.rawQuery(checkDtQuery, fields.connection)
        let dtID = 0
        if (checkDtResult.length > 0) dtID = checkDtResult[0].distributer_user_master_id
        log.logger({ pagename: require('path').basename(__filename), action: 'distributer_user_master_id', type: 'dtID', fields: dtID })
        if (dtID !== null && dtID !== undefined && dtID > 0) ma_user_id = dtID
      }

      /* END NEW CHANGES :  CHECK DT SPECIFIC CONFIG IN `ma_transfers_shares`  */

      const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'retailerSql', fields: retailerSql })
      result = await this.rawQuery(retailerSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - DT Retailer Specific', fields: result })
      const globalContri = await this.buildShareObject(result)
      if (globalContri) {
        return globalContri
      }
    }

    // Check condition for integrated users
    const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
    const resultint = await this.rawQuery(userintegrated, fields.connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Integrated user check', fields: resultint })
    if (resultint.length > 0) {
      result = {}
      ma_user_id = util.integratedIncentive[resultint[0].integration_code]
      const retailerintegratedSql = `SELECT state_master_id,ma_user_id,dt_share,dt_applied_type,dt_operative_amount,sd_share,sd_applied_type,sd_operative_amount,ma_bank_on_boarding_id,record_status,settings_flag FROM ma_transfers_shares where record_status='Y' and ma_user_id=${ma_user_id}`
      if (('ma_bank_on_boarding_id' in fields) && validator.definedVal(fields.ma_bank_on_boarding_id) && fields.ma_bank_on_boarding_id > 0) {
        sql += ` and ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id} `
      }
      result = await this.rawQuery(retailerintegratedSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Integrated Retailer Specific', fields: result })
      const globalContri = await this.buildShareObject(result)
      // if (globalContri) {
      return globalContri
      // }
    }

    // Check other configuration (For now this is for distributor specific)
    /* Remove DT condition FOR ma_transfers_shares  AS this table contain entry only for dt and sdt */
    /* result = {}
    const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields, connection: fields.connection })
    log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
    if (getDistributionAdditionalCondition.status == 200) {
      result = getDistributionAdditionalCondition.configurationData
      const globalContri = await this.buildShareObject(result)
      if (globalContri) return globalContri
    } */

    // Reinitializing variables
    ma_user_id = 0
    result = {}
    if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
      const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
      result = await this.rawQuery(stateSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - State Specific', fields: result })
      const globalContri = await this.buildShareObject(result)
      if (globalContri) {
        return globalContri
      }
    }

    // Reinitializing variables
    ma_user_id = 0
    state_master_id = 0
    result = {}

    // Check default configuration
    const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
    result = await this.rawQuery(defaultSql, fields.connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Default', fields: result })
    return await this.buildShareObject(result)
  }

  static async buildShareObject (globalContri) {
    if (globalContri.length > 0) {
      var globalShare = []
      if (globalContri[0].dt_share > 0) {
        globalShare.push({
          dt_share: globalContri[0].dt_share,
          dt_share_applied_type: globalContri[0].dt_applied_type,
          dt_operative_amount: globalContri[0].dt_operative_amount
        })
      }

      if (globalContri[0].sd_share > 0) {
        globalShare.push({
          sd_share: globalContri[0].sd_share,
          sd_share_applied_type: globalContri[0].sd_applied_type,
          sd_operative_amount: globalContri[0].sd_operative_amount
        })
      }
      return globalShare
    }
    return false
  }

  /**
   * To distribute the incentives
   * Array with  ma_user_id,amount,applied_type,user_type
   */
  static async distributionEntries (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'request', fields })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.aggregator_order_id, connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = fields.distributeObj
      for (const value of distribution) {
        var subAmount = value.amount
        var descPart = ''
        var tds = 0
        var subIdGST = 0
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // GST applicable
          if (value.gst_number !== null && value.gst_number !== undefined) {
            const subSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const subGst = await this.rawQuery(subSql, connection)
            if (subGst.length > 0) {
              subIdGST = parseFloat((value.amount - (value.amount * (100 / (100 + subGst[0].percentage)))).toFixed(4))
            }
          }

          // TDS will be be applied
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: '2',
            amount: value.amount,
            ma_deduction_type: value.TDStype,
            connection: connection
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          console.log('TDS', tds)
          var appliedCommission = ''
          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            appliedCommission = commissionDetails.appliedCommission
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: util.TDSonCommission + value.user_type,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.aggregator_order_id,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: connection
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
          }

          if (subIdGST > 0 || tds > 0) {
            const orderwiseEntry = await orderwiseTaxes.createEntry('_', {
              transaction_type: '4',
              orderid: fields.aggregator_order_id,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: subIdGST,
              tds_amount: tds,
              ma_status: 'S',
              connection: connection
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
          }
        }
        if (subAmount > 0) {
          descPart = tds !== 0 ? ' (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: fields.connection })
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: '4',
              description: 'Credit - DIGI KHATA DMT PPI - ' + util.CommissionEarnedDMT + descPart,
              ma_status: 'S',
              orderid: fields.aggregator_order_id,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: '4',
              description: 'Credit - DIGI KHATA DMT PPI - ' + util.CommissionEarnedDMT + descPart,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.aggregator_order_id,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: '4',
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id

            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, connection)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * Calculate amount based on applied_type and operative_amount
   */
  static async findValue (txnAmount, chargeAmount, share, applied_type, operative_amount = null) {
    console.log('FINVALUE :::::', txnAmount, chargeAmount, share, applied_type, operative_amount)
    if (applied_type == 1) {
      return share
    }
    if (operative_amount == null) {
      return share * txnAmount / 100
    }
    if (operative_amount == 1) {
      return share * txnAmount / 100
    }
    return share * chargeAmount / 100
  }

  /**
   * Incentive entries for AEPS transaction
   */
  static async incentiveDistribution (fields, connection) {
    // fields.userid = userid
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'request', fields: fields })
    try {
      // fields.connection = fields.connection || connection
      fields.connection = (connection == null || connection == undefined || connection == '') ? fields.connection : connection || fields.connection
      const balSql = `SELECT balance,actual_balance,wallet_type FROM ma_allow_withdrawal WHERE ma_user_id = '${fields.ma_user_id}'  and  FIND_IN_SET('4', transaction_type)`
      const balDetails = await this.rawQuery(balSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - walletdetails', fields: balDetails })
      if (balDetails.length <= 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'Wallet Type [4] Missing', fields: balDetails })
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Wallet Type [4] Missing' }
      }

      var distributionObj = []
      var rt_share = 0
      var dt_share = 0
      var sd_share = 0
      var customer_charges = 0

      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      // const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`
      // const userDetails = await this.rawQuery(userSql + fields.ma_user_id, fields.connection)
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid = ${fields.ma_user_id} and userid = ${fields.userid} limit 1 `
      const userDetails = await this.rawQuery(userSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })
      // log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails })
      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }
      fields.state_master_id = userDetails[0].state
      // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id

      const configuration = await this.getDistribution(fields, fields.connection)
      console.log('configuration--->', configuration)

      if ((fields.amount != null || fields.amount != undefined) && configuration.length > 0) {
        var customer_charges_amount = await this.findValue(fields.amount, 0, configuration[0].customer_charges, configuration[0].customer_charges_applied_type)
        //  customer charge
        customer_charges += customer_charges_amount
        // RT share
        rt_share += await this.findValue(fields.amount, customer_charges_amount, configuration[0].rt_share, configuration[0].rt_share_applied_type, configuration[0].rt_operative_amount)
        // DT share
        dt_share += await this.findValue(fields.amount, customer_charges_amount, configuration[0].dt_share, configuration[0].dt_applied_type, configuration[0].dt_operative_amount)
        // SD share
        sd_share += await this.findValue(fields.amount, customer_charges_amount, configuration[0].sd_share, configuration[0].sd_applied_type, configuration[0].sd_operative_amount)
      }
      console.log('Calculated shares data', rt_share, dt_share, sd_share, customer_charges)
      const ma_status = 'S'
      // Points Details Entries
      /* const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.transaction_charges,
        transactionType: '1',
        connection: fields.connection
      })
      if (pointsDetailsEntries.status === 400) {
        return pointsDetailsEntries
      }
      console.log('PD :::', pointsDetailsEntries)
      // Retailer Debit for transactional charges
      const retailerDr = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.transaction_charges,
        mode: 'dr',
        transaction_type: '13',
        description: util.TransferCharges,
        ma_status,
        orderid: fields.aggregator_order_id,
        userid: fields.userid,
        corresponding_id: util.airpayCommissionId,
        connection: fields.connection
      })
      if (retailerDr.status === 400) {
        return retailerDr
      }
      console.log('1. RT debit txnCharge', retailerDr)
      // Respective Debit Entry in Point details as per wallet balances
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: retailerDr.id,
          orderid: fields.aggregator_order_id,
          ma_status,
          transaction_status: ma_status,
          connection: fields.connection
        })
        if (entry.status === 400) {
          return entry
        }
        console.log('2. Wallet PD debit', entry)
      } */
      // GST AP
      /* BEGIN TRANSACTION */
      await mySQLWrapper.beginTransaction(fields.connection)
      var afterDeductionAmt = customer_charges
      const sql = `SELECT percentage from ma_gst_master where ma_user_id=${util.airpayCommissionId}`
      const gstAP = await this.rawQuery(sql, fields.connection)
      if (gstAP.length > 0) {
        const AirpayGST = parseFloat((customer_charges - (customer_charges * (100 / (100 + gstAP[0].percentage)))).toFixed(4))
        afterDeductionAmt = afterDeductionAmt - AirpayGST
        console.log('After AP GST ', afterDeductionAmt)
        if (AirpayGST > 0) {
          const airpayGSTcr = await pointsLedger.createEntry('_', {
            ma_user_id: util.airpayCommissionId,
            amount: AirpayGST,
            mode: 'cr',
            transaction_type: '12',
            // description: util.GSTAP,
            description: (util.GSTAP).replace('DMT', 'DIGI KHATA DMT PPI'),
            ma_status,
            orderid: util.airpayCommissionId + '-' + fields.aggregator_order_id,
            userid: fields.userid,
            corresponding_id: fields.ma_user_id,
            connection: fields.connection
          })
          if (airpayGSTcr.status === 400) {
            /* ROLLBACK ON FAILURE */
            await mySQLWrapper.rollback(fields.connection)
            return airpayGSTcr
          }
          console.log('3. AP GST Credit', airpayGSTcr)
        }
      }
      console.log('after Deduction Amount before share starts', afterDeductionAmt)

      // Push RT details
      if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        afterDeductionAmt = afterDeductionAmt - rt_share
        distributionObj.push({
          ma_user_id: fields.ma_user_id,
          amount: rt_share,
          gst_number: userDetails[0].gst_number,
          user_type: 'RT',
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, fields.connection)
        distribuetrDetails.share = dt_share
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - dt_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                gst_number: distribuetrDetails[0].gst_number,
                user_type: 'DT',
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', fields.connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  afterDeductionAmt = afterDeductionAmt - sd_share
                  distributionObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    gst_number: superdistribuetrDetails[0].gst_number,
                    user_type: 'SDT',
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - sd_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                user_type: 'SDT',
                gst_number: distribuetrDetails[0].gst_number,
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }
      // Airpay Share array
      if (afterDeductionAmt > 0) {
        distributionObj.push({
          ma_user_id: util.airpayCommissionId,
          user_type: 'SD',
          amount: afterDeductionAmt
        })
      }
      // Debit Entries for all shares
      fields.distributeObj = distributionObj
      const allShares = await this.distributionEntries(fields, fields.connection)
      if (allShares.status === 400) {
        /* ROLLBACK ON FAILURE */
        await mySQLWrapper.rollback(fields.connection)
        return allShares
      }
      /* FINALLY COMMIT TRANSACTION */
      await mySQLWrapper.commit(fields.connection)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}

module.exports = ppiDistribution
