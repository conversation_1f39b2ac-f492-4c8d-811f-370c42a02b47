const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const util = require('../../util/util')
const commonFunction = require('../common/commonFunctionController')
const validator = require('../../util/validator')
const commissionController = require('../commission/commissionController')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const integrated = require('../integrated/integratedController')
const balanceController = require('../balance/balanceController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')

class CustomerKycIncentiveController extends DAO {
  static get TABLE_NAME () {
    return 'ma_dmt_kyc_shares'
  }

  static get PRIMARY_KEY () {
    return 'ma_dmt_kyc_shares_id'
  }

  static async getDistributionAllHeads ({ ma_user_id, ma_bank_on_boarding_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      const decryptionKey = util[env].secondaryEncrptionKey
      const distributionObj = []
      let rt_share = 0
      let dt_share = 0
      let sd_share = 0
      let customer_charges = 0

      // PAN DECRYPTION
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,gst_number,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master where profileid =`
      // const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan,gst_number FROM ma_user_master where profileid ='
      const userDetails = await this.rawQuery(userSql + ma_user_id, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - RT', fields: userDetails })

      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }
      const state_master_id = userDetails[0].state
      const globalValues = await this.globalValues({
        state_master_id,
        ma_user_id,
        ma_bank_on_boarding_id,
        conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - globalValues', fields: globalValues })
      if (Object.keys(globalValues).length > 0) {
        customer_charges = globalValues.customer_charges
        rt_share = await this.findValue(customer_charges, globalValues.globalShare[0].rt_share, globalValues.globalShare[0].rt_share_applied_type)
        dt_share = await this.findValue(customer_charges, globalValues.globalShare[1].dt_share, globalValues.globalShare[1].dt_share_applied_type)
        sd_share = await this.findValue(customer_charges, globalValues.globalShare[2].sd_share, globalValues.globalShare[2].sd_share_applied_type)
      }

      distributionObj.push({ transaction_charges: customer_charges })

      console.log('Calculated shares data', rt_share, dt_share, sd_share, customer_charges, globalValues)
      // const ma_status = 'S'
      let afterDeductionAmt = customer_charges

      // Push RT details
      if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        afterDeductionAmt = afterDeductionAmt - rt_share
        distributionObj.push({
          ma_user_id: ma_user_id,
          amount: rt_share,
          gst_number: userDetails[0].gst_number,
          user_type: 'RT',
          TDS: await this.getTDS(ma_user_id, TDStypeval, rt_share, conn) // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, conn)
        distribuetrDetails.share = dt_share
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - dt_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                gst_number: distribuetrDetails[0].gst_number,
                user_type: 'DT',
                TDS: await this.getTDS(ma_user_id, TDStypeval, rt_share, conn) // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', conn)
              log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  afterDeductionAmt = afterDeductionAmt - sd_share
                  distributionObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    gst_number: superdistribuetrDetails[0].gst_number,
                    user_type: 'SDT',
                    TDS: await this.getTDS(ma_user_id, TDStypeval, rt_share, conn) // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - sd_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                user_type: 'SDT',
                gst_number: distribuetrDetails[0].gst_number,
                TDS: await this.getTDS(ma_user_id, TDStypeval, rt_share, conn) // With PAN or without
              })
            }
          }
        }
      }
      // Airpay Share array
      if (afterDeductionAmt > 0) {
        distributionObj.push({
          ma_user_id: util.airpayCommissionId,
          user_type: 'SD',
          amount: afterDeductionAmt,
          TDS: 0
        })
      }
      return distributionObj
    } catch (error) {
      log.logger({ pagename: 'customerKycIncentiveController.js', action: 'getDistributionAllHeads', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async getTDS (ma_user_id, type, amount, connection) {
    const commissionDetails = await commissionController.getCommission('_', {
      ma_user_id: ma_user_id,
      ma_commission_type: '2',
      amount: amount,
      ma_deduction_type: type,
      connection
    })

    const tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
    return tds
  }

  static async globalValues ({ ma_bank_on_boarding_id, ma_user_id, state_master_id = 0, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const baseSql = `SELECT state_master_id,ma_user_id,
      customer_charges,bank_charges,ma_bank_on_boarding_id,rt_share,
      rt_applied_type,dt_share,dt_applied_type,
      sd_share,sd_applied_type,
      record_status,settings_flag
      FROM ma_dmt_kyc_shares 
      where record_status='Y' 
      and settings_flag='2'
      and ma_bank_on_boarding_id = ${ma_bank_on_boarding_id}
      `
      /* retailer specific configuration  */
      const retailerResult = await this.retailerValues({ baseSql, ma_user_id, conn })
      if (retailerResult) {
        return retailerResult
      }
      /* Check condition for integrated users  */
      const integratedMerchantResult = await this.integratedMerchantValues({ baseSql, ma_user_id, conn })
      if (integratedMerchantResult) {
        return integratedMerchantResult
      }
      /* Check other configuration (For now this is for distributor specific) */
      const dTResult = await this.DTValues({ baseSql, ma_user_id, conn })
      if (dTResult) {
        return dTResult
      }
      /* state specific configuration  */
      const stateResult = await this.stateValues({ baseSql, ma_user_id, state_master_id, conn })
      if (stateResult) {
        return stateResult
      }
      /* Check default configuration  */
      const globalSql = `${baseSql} and state_master_id=0 and ma_user_id=0 and ma_dt_sdt_id=0`
      log.logger({ pagename: 'customerKycChargeController.js', action: 'globalSql', type: 'global specific configuration Query', fields: globalSql })
      const globalResult = await this.rawQuery(globalSql, conn)
      const result = this.buildShareObject(globalResult)
      if (result) return result

      return { status: 400, respcode: 1112, message: errorMsg.responseCode[1112] }
    } catch (error) {
      log.logger({ pagename: 'customerKycChargeController.js', action: 'globalValues', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async retailerValues ({ baseSql, ma_user_id, conn }) {
    if (ma_user_id == null && ma_user_id == undefined && ma_user_id == 0) return false
    const retailerSql = `${baseSql} and ma_user_id=${ma_user_id}`
    log.logger({ pagename: 'customerKycChargeController.js', action: 'retailerSql', type: 'retailer specific configuration Query', fields: retailerSql })
    const retailerResult = await this.rawQuery(retailerSql, conn)
    return this.buildShareObject(retailerResult)
  }

  static async integratedMerchantValues ({ baseSql, ma_user_id, conn }) {
    const userintegratedSql = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${ma_user_id} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
    const userintegratedResult = await this.rawQuery(userintegratedSql, conn)
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Integrated user check', fields: userintegratedResult })
    if (userintegratedResult.length == 0) return false
    const maUserId = util.integratedIncentive[userintegratedResult[0].integration_code]

    const integratedRetailerSql = `${baseSql} and ma_user_id=${maUserId}`
    log.logger({ pagename: 'customerKycChargeController.js', action: 'retailerSql', type: 'integrated retailer specific configuration Query', fields: integratedRetailerSql })
    const integratedRetailer = await this.rawQuery(integratedRetailerSql, conn)
    return this.buildShareObject(integratedRetailer)
  }

  static async DTValues ({ baseSql, ma_user_id, conn }) {
    const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({
      sql: baseSql,
      fields: {
        ma_user_id
      },
      connection: conn
    })
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
    if (getDistributionAdditionalCondition.status != 200) return false
    return this.buildShareObject(getDistributionAdditionalCondition.configurationData)
  }

  static async stateValues ({ baseSql, ma_user_id, state_master_id, conn }) {
    let stateMasterId = parseInt(state_master_id)
    // PAN DECRYPTION
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    const decryptionKey = util[env].secondaryEncrptionKey

    if (state_master_id == 0 || state_master_id == undefined || state_master_id == null) {
      // PAN DECRYPTION
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,gst_number,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master where profileid = ${ma_user_id}`
      // const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,pan,gst_number FROM ma_user_master where profileid = ${ma_user_id}`
      const userResult = await this.rawQuery(userSql, conn)
      if (userResult.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      stateMasterId = userResult[0].state
    }
    if (stateMasterId == null && stateMasterId == undefined && stateMasterId == 0) return false
    const stateSql = `${baseSql} and state_master_id=${stateMasterId}`
    log.logger({ pagename: 'customerKycChargeController.js', action: 'stateSql', type: 'state specific configuration Query', fields: stateSql })
    const stateResult = await this.rawQuery(stateSql, conn)
    return this.buildShareObject(stateResult)
  }

  static buildShareObject (globalContri) {
    if (globalContri.length == 0) return false
    const globalShare = []
    const customer_charges = globalContri[0].customer_charges
    const bank_charges = globalContri[0].bank_charges
    globalShare.push({
      rt_share: globalContri[0].rt_share,
      rt_share_applied_type: globalContri[0].rt_applied_type
    })

    globalShare.push({
      dt_share: globalContri[0].dt_share,
      dt_share_applied_type: globalContri[0].dt_applied_type
    })

    globalShare.push({
      sd_share: globalContri[0].sd_share,
      sd_share_applied_type: globalContri[0].sd_applied_type
    })

    return { customer_charges, bank_charges, globalShare }
  }

  static findValue (chargeAmount, share, applied_type) {
    console.log('FINVALUE :::::', chargeAmount, share, applied_type)
    if (share == null || share == undefined) return 0
    if (applied_type == 2) return share * chargeAmount / 100
    return share
  }

  static async distributionEntries ({ userid, distributionObj, aggregator_order_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'request', fields: { distributionObj, aggregator_order_id, userid } })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(aggregator_order_id, conn)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = distributionObj
      for (const value of distribution) {
        var subAmount = value.amount
        var descPart = ''
        var tds = 0
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: this.getKycIncentiveTransactionType(),
            amount: value.amount,
            ma_deduction_type: value.TDStype,
            connection: conn
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          console.log('TDS', tds)
          var appliedCommission = ''
          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            appliedCommission = commissionDetails.appliedCommission
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: util.TDSonCommission + value.user_type,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + aggregator_order_id,
              userid: userid,
              corresponding_id: util.airpayUserId,
              connection: conn
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, conn)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: this.getKycIncentiveTransactionType(),
              orderid: aggregator_order_id,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: conn
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
            // [END] Make entry in ma_orderwise_taxes table
          }
        }
        if (subAmount > 0) {
          descPart = tds !== 0 ? ' (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: conn })
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: this.getKycIncentiveTransactionType(),
              description: 'Credit - ' + util.CommissionEarnedKYCVerification + descPart,
              ma_status: 'S',
              orderid: aggregator_order_id,
              userid: userid,
              corresponding_id: util.airpayUserId,
              connection: conn
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: this.getKycIncentiveTransactionType(),
              description: 'Credit - ' + util.CommissionEarnedKYCVerification + descPart,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + aggregator_order_id,
              userid: userid,
              corresponding_id: util.airpayUserId,
              connection: conn
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: this.getKycIncentiveTransactionType(),
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id

            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, conn)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: 'customerKycChargeController.js', action: 'distributionEntries', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async incentiveDistribution ({ ma_user_id, userid, state_master_id = 0, aggregator_order_id, ma_bank_on_boarding_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      const decryptionKey = util[env].secondaryEncrptionKey

      const distributionObj = []
      let rt_share = 0
      let dt_share = 0
      let sd_share = 0
      let customer_charges = 0

      const balSql = `SELECT balance,actual_balance,wallet_type FROM ma_allow_withdrawal WHERE ma_user_id = '${ma_user_id}'  and  FIND_IN_SET('38', transaction_type)`
      const balDetails = await this.rawQuery(balSql, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - walletdetails', fields: balDetails })
      if (balDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Wallet [22] Type Missing' }
      }
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,gst_number,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master where profileid =`
      // const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan,gst_number FROM ma_user_master where profileid ='
      const userDetails = await this.rawQuery(userSql + ma_user_id, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails })

      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }
      const state_master_id = userDetails[0].state

      const globalValues = await this.globalValues({
        state_master_id,
        ma_user_id,
        ma_bank_on_boarding_id,
        conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })
      if (Object.keys(globalValues).length > 0) {
        customer_charges = globalValues.customer_charges
        rt_share = this.findValue(customer_charges, globalValues.globalShare[0].rt_share, globalValues.globalShare[0].rt_share_applied_type)
        dt_share = this.findValue(customer_charges, globalValues.globalShare[1].dt_share, globalValues.globalShare[1].dt_share_applied_type)
        sd_share = this.findValue(customer_charges, globalValues.globalShare[2].sd_share, globalValues.globalShare[2].sd_share_applied_type)
      }

      console.log('Calculated shares data', rt_share, dt_share, sd_share, customer_charges, globalValues)
      const ma_status = 'S'
      var afterDeductionAmt = customer_charges

      // Push RT details
      if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        afterDeductionAmt = afterDeductionAmt - rt_share
        distributionObj.push({
          ma_user_id: ma_user_id,
          amount: rt_share,
          gst_number: userDetails[0].gst_number,
          user_type: 'RT',
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, conn)
        distribuetrDetails.share = dt_share
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - dt_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                gst_number: distribuetrDetails[0].gst_number,
                user_type: 'DT',
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', conn)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  afterDeductionAmt = afterDeductionAmt - sd_share
                  distributionObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    gst_number: superdistribuetrDetails[0].gst_number,
                    user_type: 'SDT',
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - sd_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                user_type: 'SDT',
                gst_number: distribuetrDetails[0].gst_number,
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }
      // Airpay Share array
      if (afterDeductionAmt > 0) {
        distributionObj.push({
          ma_user_id: util.airpayCommissionId,
          user_type: 'SD',
          amount: afterDeductionAmt
        })
      }
      // Debit Entries for all shares
      const allShares = await this.distributionEntries({
        distributionObj,
        aggregator_order_id,
        userid,
        connection: conn
      })
      if (allShares.status === 400) {
        return allShares
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: 'customerKycChargeController.js', action: 'incentiveDistribution', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async reverseIncentive ({ ma_user_id, userid, aggregator_order_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      // Cloning request and removing connection
      log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentive', type: 'request', fields: { ma_user_id, userid, aggregator_order_id } })
      const incentiveDetailSql = `SELECT * from ma_points_ledger_master where parent_id = '${aggregator_order_id}' and transaction_type in 
      (${this.getKycTransactionType()},${this.getKycIncentiveTransactionType()},11) order by mode,ma_points_ledger_master_id desc`
      const incentiveDetailResult = await pointsLedger.rawQuery(incentiveDetailSql, conn)
      const transaction_status = 'REV'
      const transaction_type = this.getKycTransactionType()
      if (incentiveDetailResult.length == 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }

      /* REMOVE : EMITRA CASE */
      /* const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${incentiveDetailResult[0].userid}`
      const integratedMerchantResult = await this.rawQuery(userSQL, conn)
      console.log(userSQL)
      if (integratedMerchantResult.length > 0) {
        const res = await integrated.index({
          action: 'REFUND',
          aggregator_user_id: integratedMerchantResult[0].aggregator_user_id,
          aggregator_order_id: aggregator_order_id,
          ma_user_id: ma_user_id
        }, conn)
        console.log('integrated returned this', res)
        if (res.status != 200) {
          return res
        }
      } */
      /* REMOVE : EMITRA CASE */

      // const incentiveDetails = fields.incentiveDetails
      var resp
      var newFields
      var pointsDetails = false
      // Reversing points ledger entries
      for (var i = 0; i < incentiveDetailResult.length; i++) {
        var pointsDetailsEntries = {}
        pointsDetails = false
        if (incentiveDetailResult[i].mode === 'cr') {
          newFields = {
            ma_user_id: incentiveDetailResult[i].ma_user_id,
            corresponding_id: incentiveDetailResult[i].corresponding_id,
            amount: incentiveDetailResult[i].amount,
            userid: incentiveDetailResult[i].userid,
            orderid: transaction_status + '-' + incentiveDetailResult[i].orderid,
            mode: 'dr',
            transaction_type: incentiveDetailResult[i].transaction_type,
            ma_status: transaction_status,
            // description: (fields.transaction_status === 'R' ? 'Refunded - ' : 'Reversed - ') + incentiveDetailResult[i].description,
            description: incentiveDetailResult[i].description + (transaction_status === 'R' ? ' - Refunded' : ' - Reversed'),
            connection: connection
          }

          // Point Details Entries
          if (util.airpaymerchantconfig.includes(incentiveDetailResult[i].ma_user_id) == false) {
            pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
              ma_user_id: incentiveDetailResult[i].ma_user_id,
              amount: incentiveDetailResult[i].amount,
              transactionType: '1',
              connection: connection
            })
            if (pointsDetailsEntries.status === 400) {
              return pointsDetailsEntries
            }
            pointsDetails = true
          }
        } else {
          newFields = {
            ma_user_id: incentiveDetailResult[i].ma_user_id,
            corresponding_id: incentiveDetailResult[i].corresponding_id,
            amount: -incentiveDetailResult[i].amount,
            userid: incentiveDetailResult[i].userid,
            orderid: transaction_status + '-' + incentiveDetailResult[i].orderid,
            mode: 'cr',
            transaction_type: transaction_type,
            ma_status: transaction_status,
            // description: (fields.transaction_status === 'R' ? 'Refunded - ' : 'Reversed - ') + incentiveDetailResult[i].description.replace('debited', 'credited'),
            description: incentiveDetailResult[i].description.replace('Debit', 'Credit') + (transaction_status === 'R' ? ' - Refunded' : ' - Reversed'),
            connection: connection
          }
        }
        resp = await pointsLedger.createEntry('_', newFields)
        if (resp.status === 400) {
          return resp
        }
        if (pointsDetails && util.airpaymerchantconfig.includes(incentiveDetailResult[i].ma_user_id) == false) {
          if (pointsDetailsEntries.details.length > 0) {
            for (var j = 0; j < pointsDetailsEntries.details.length; j++) {
              const entry = await pointsDetailsController.createEntry('_', {
                ma_user_id: incentiveDetailResult[i].ma_user_id,
                amount: pointsDetailsEntries.details[j].deductionAmount,
                wallet_type: pointsDetailsEntries.details[j].wallet_type,
                ma_points_ledger_master_id: resp.id,
                orderid: transaction_status + '-' + incentiveDetailResult[i].orderid,
                ma_status: 'R',
                transaction_status: 'S',
                connection: connection
              })
              if (entry.status === 400) {
                return entry
              }
            }
          }
        }
      }

      /* REMOVE : EMITRA CASE */
      // call integrated send money based on emitra condition
      // if (integratedMerchantResult.length > 0) {
      //   const resrev = await integrated.sendMoneyReverse({
      //     ma_user_id,
      //     aggregator_order_id
      //   }, connection)
      //   if (resrev.status != 200) {
      //     return resrev
      //   }
      // }
      /* REMOVE : EMITRA CASE */

      // [START] Make refund/reverse entry in ma_orderwise_taxes table
      // if (fields.transaction_status === 'R') {
      // Get order wise taxes success entry
      const sqlOrderWiseSql = `SELECT * FROM ma_orderwise_taxes WHERE parent_id = '${aggregator_order_id}' AND ma_status='S' AND transaction_type='${this.getKycIncentiveTransactionType()}'`
      const sqlOrderWiseResult = await this.rawQuery(sqlOrderWiseSql, connection)
      // console.log('sqlOrderWiseRes', sqlOrderWiseRes)
      log.logger({ pagename: 'beneVerifyIncentiveController.js', action: 'reverseIncentive', type: 'response', fields: sqlOrderWiseResult })
      if (sqlOrderWiseResult.length > 0) {
        const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
        for (const valueOrderWise of sqlOrderWiseResult) {
          const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
            transaction_type: valueOrderWise.transaction_type,
            orderid: transaction_status + '-' + valueOrderWise.orderid,
            ma_user_id: valueOrderWise.ma_user_id,
            amount: (-1 * valueOrderWise.amount),
            gst_amount: (-1 * valueOrderWise.gst_amount),
            tds_amount: (-1 * valueOrderWise.tds_amount),
            ma_status: 'R',
            connection: connection
          })
          if (orderwiseEntry.status === 400) {
            return orderwiseEntry
          }
        }
      }
      // }
      // [END] Make refund/reverse entry in ma_orderwise_taxes table

      // check for entry in monthly incentives
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(aggregator_order_id, connection)
      const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
      const sqlincmonth = `SELECT * FROM ma_monthly_incentives_process WHERE transaction_master_id = '${parent_transaction_master_id}' AND isRefund = 'FALSE'`
      const sqlincmonthRes = await this.rawQuery(sqlincmonth, connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      for (let i = 0; i < sqlincmonthRes.length; i++) {
        const monthlyIncCreateRev = await monthlyIncentiveController.createEntry('_', {
          ma_user_id: sqlincmonthRes[i].ma_user_id,
          amount: -(sqlincmonthRes[i].amount),
          transaction_type: sqlincmonthRes[i].transaction_type,
          monthyear: thisMonth + ' ' + now.getFullYear(),
          summary_id: 0,
          transaction_master_id: parent_transaction_master_id,
          isRefund: 'TRUE'
        },
        connection)
        if (monthlyIncCreateRev.status === 400) {
          return monthlyIncCreateRev
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.logger({ pagename: 'customerKycChargeController.js', action: 'reverseIncentive', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static getKycTransactionType () {
    return '38'
  }

  static getKycIncentiveTransactionType () {
    return '39'
  }
}

module.exports = CustomerKycIncentiveController
