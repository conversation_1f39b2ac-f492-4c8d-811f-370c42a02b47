const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const validator = require('../../util/validator')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const common_fns = require('../../util/common_fns')
const commonFunction = require('../common/commonFunctionController')

class ondcDistribution extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_slabwise_distribution_ondc'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_ondc_id'
  }

  /**
   * Distribution Configuration for CMS Incentive
   * fields : amount, ma_user_id,state_master_id,connection
   */
  static async getDistribution (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'request', fields: fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var sql = `SELECT * FROM ma_slabwise_distribution_ondc where ${fields.amount} between min_amount and max_amount and record_status='Y' `

      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'sql - Retailer Specific', fields: retailerSql })
        result = await this.rawQuery(retailerSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Check other configuration (For now this is for distributor specific)
      result = {}
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields, connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }

      // Reinitializing variables
      ma_user_id = 0
      result = {}

      // If retailer specific details not found
      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        // state_master_id = 2 // FOR TESTING ONLY
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'sql - State Specific', fields: stateSql })
        result = await this.rawQuery(stateSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - State Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}

      // If retailer and state specific configuration not found
      // Check default/global configuration
      const defaultSql = sql + ` and state_master_id = ${state_master_id} and ma_user_id = ${ma_user_id} and ma_dt_sdt_id = 0`
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'sql - Default', fields: defaultSql })
      result = await this.rawQuery(defaultSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * To distribute the incentives
   * Array with  ma_user_id,amount,applied_type,user_type
   */
  static async distributionEntries (fields) {
    var requestFields = {}
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'request', fields: requestFields })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.orderid, fields.connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      let appliedCommission = ''
      const distribution = fields.distributeObj
      for (const value of distribution) {
        const userSql = `SELECT profileid, user_type FROM ma_user_master where profileid = '${value.ma_user_id}'`
        const userDetails = await this.rawQuery(userSql, fields.connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - RT', fields: userDetails })
        let userDescription = ` - For ${value.ma_user_id}`
        if (userDetails.length > 0) userDescription = ` - For ${value.ma_user_id} (${userDetails[0].user_type})`

        var subAmount = value.amount
        var tds = 0
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: 45,
            amount: subAmount,
            ma_deduction_type: value.TDStype,
            connection: fields.connection
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          appliedCommission = commissionDetails.status === 400 ? '' : commissionDetails.appliedCommission
          console.log('TDS', tds)
          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: `${(util.IncentiveCommissionDescriptionNew).replace('#type#', 'CMS')}${userDescription}`,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: fields.connection
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, fields.connection)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('./orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: '45',
              orderid: fields.orderid,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: fields.connection
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
            // [END] Make entry in ma_orderwise_taxes table
          }
        }
        if (subAmount > 0) {
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: fields.connection })
          const common = require('../../util/common')
          const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '45' }, fields.connection)
          const descType = descObj.code_desc ? descObj.code_desc : ''
          const descPart = tds !== 0 ? ' - (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 45,
              description: 'Credit - ' + descType + descPart + userDescription,
              ma_status: 'S',
              orderid: fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 45,
              description: 'Credit - ' + descType + descPart + userDescription,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: '45',
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id
            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, fields.connection)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * Calculate amount based on applied_type and operative_amount
   */
  static async calculateIncentiveAmount (fields) {
    var requestFields = {}
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateIncentiveAmount', type: 'request', fields: requestFields })
    var customerCharges = fields.customer_charges
    if (fields.customer_charges_applied_type === '2') {
      customerCharges = fields.customer_charges * fields.amount / 100
    }
    var amount = 0
    if (fields.data.applied_type === '1') {
      amount = fields.data.amount
    } else if (fields.data.applied_type === '2') {
      if (fields.data.operative_amount === '1') { // % of transaction amount
        amount = fields.amount * fields.data.amount / 100
      } else if (fields.data.operative_amount === '2') { // % of customer charges
        amount = customerCharges * fields.data.amount / 100
      }
    }
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateIncentiveAmount', type: 'responce', fields: amount })
    return amount
  }

  /**
   * Incentive entries for CMS transaction
   * @param {Object} fields
   * @param {number} fields.userid
   * @param {number} fields.ma_user_id
   * @param {number} fields.cms_ma_user_id
   * @param {string} fields.orderid
   * @param {Object} connection
   */
  static async incentiveEntries (fields, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    let requestFields = {}
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    requestFields = { ...fields }
    requestFields.connectionThreadId = connection.threadId
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'request', fields: requestFields })
    try {
      const distributeObj = []
      let totalIncentiveAmount = 0
      const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state, pan  FROM ma_user_master where profileid ='
      const userDetails = await this.rawQuery(userSql + fields.ma_user_id, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - RT', fields: userDetails })
      if (userDetails.length <= 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }

      fields.state_master_id = userDetails[0].state
      // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id
      const configuration = await this.getDistribution(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - CMS getDistribution', fields: configuration })
      fields.connection = connection
      // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
      delete fields.distributerIdForGetDistribution
      if (configuration.status === 400) return configuration

      // Applied Mode
      fields.inclusive_flag = configuration[0].inclusive_flag

      // Customer Charges
      fields.customer_charges = configuration[0].customer_charges
      fields.customer_charges_applied_type = configuration[0].customer_charges_applied_type

      let TDStypeval = ''
      // Push RT details
      if (configuration[0].rt_share > 0) {
        TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        fields.data = {
          amount: configuration[0].rt_share,
          applied_type: configuration[0].rt_share_applied_type,
          operative_amount: configuration[0].rt_operative_amount,
          user_type: 'RT'
        }
        const rt_share = await this.calculateIncentiveAmount(fields)
        totalIncentiveAmount += rt_share
        distributeObj.push({
          ma_user_id: fields.ma_user_id,
          amount: rt_share,
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (configuration[0].dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              fields.data = {
                amount: configuration[0].dt_share,
                applied_type: configuration[0].dt_share_applied_type,
                operative_amount: configuration[0].dt_operative_amount,
                user_type: 'DT'
              }
              const dt_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += dt_share
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (configuration[0].sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  fields.data = {
                    amount: configuration[0].sd_share,
                    applied_type: configuration[0].sd_share_applied_type,
                    operative_amount: configuration[0].sd_operative_amount,
                    user_type: 'SDT'
                  }
                  const sd_share = await this.calculateIncentiveAmount(fields)
                  totalIncentiveAmount += sd_share
                  distributeObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (configuration[0].sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              fields.data = {
                amount: configuration[0].sd_share,
                applied_type: configuration[0].sd_share_applied_type,
                operative_amount: configuration[0].sd_operative_amount,
                user_type: 'SDT'
              }
              const sd_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += sd_share
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }

      // Customer charge calculate
      if (fields.customer_charges_applied_type == 2) {
        fields.customer_charges = fields.amount * fields.customer_charges / 100
      }

      if (fields.customer_charges - totalIncentiveAmount > 0) {
        distributeObj.push({
          ma_user_id: util.airpayCommissionId,
          amount: fields.customer_charges - totalIncentiveAmount
        })
      }

      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '45' }, connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      if (fields.inclusive_flag == 'Y') {
      // For Cash Account credit to Airpay
        const cashEntry = await cashAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.customer_charges,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: connection
        })

        if (cashEntry.status === undefined || cashEntry.status !== 200) {
          return cashEntry
        }
        // For Points Account Credit from Airpay Account
        const pointsEntry = await pointsAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.customer_charges,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: connection
        })
        if (pointsEntry.status === undefined || pointsEntry.status !== 200) {
          return pointsEntry
        }

        // Airpay Debit points ledger enntry
        const pointsLedgerAP = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: fields.customer_charges,
          mode: 'dr',
          transaction_type: 45, // CMS Incentive type
          description: 'Debit - ' + descType,
          orderid: `${util.airpayUserId}-${fields.ma_user_id}-${fields.orderid}`,
          userid: fields.userid,
          ma_status: 'S',
          corresponding_id: fields.ma_user_id,
          connection: connection,
          cms_merchant: true
        })
        if (pointsLedgerAP.status === 400) {
          return pointsLedgerAP
        }
      }

      // Debit Entries for all shares
      fields.distributeObj = distributeObj
      const allShares = await this.distributionEntries(fields)
      if (allShares.status === 400) {
        return allShares
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }
}

module.exports = ondcDistribution
