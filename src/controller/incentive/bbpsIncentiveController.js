/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const util = require('../../util/util')
const validator = require('../../util/validator')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const commonFunction = require('../common/commonFunctionController')

class BbpsIncentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_slabwise_distribution_bbps'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_bbps_id'
  }

  static async calculateSurcharge (_, { ma_user_id, transaction_type, amount, userid }) {
    log.logger({ pagename: 'bbpsIncentiveController.js', action: 'calculateSurcharge', type: 'request', fields: { ma_user_id, transaction_type, amount, userid } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_user_master_id,state FROM ma_user_master WHERE profileid = '${ma_user_id}' AND userid = '${userid}'`
      const userData = await this.rawQuery(sql, connection)
      var surcharge = 0
      if (transaction_type == '6') {
        if (userData.length > 0) {
          const bbpssurcharge = await this.getSurcharge(_, {
            ma_user_id: ma_user_id,
            amount: amount,
            stateid: userData[0].state,
            connection: connection
          })

          if (bbpssurcharge.status !== undefined && bbpssurcharge.status === 200) {
            surcharge = bbpssurcharge.commissionVal
          }
        } else {
          return { status: 400, message: 'fail : User does not exists.' }
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, surcharge: parseFloat(surcharge), amount: parseFloat(amount) + parseFloat(surcharge) }
      } else {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, surcharge: parseFloat(surcharge), amount: parseFloat(amount) + parseFloat(surcharge) }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculateSurcharge', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getSurcharge (_, { ma_user_id, stateid, amount, connection = null }) {
    log.logger({ pagename: 'bbpsIncentiveController.js', action: 'getSurcharge', type: 'request', fields: { ma_user_id, stateid, amount } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      var charges = 0
      var applied_type = ''
      var commissionVal = 0
      const sqlmer = `select customer_charges,customer_charges_applied_type FROM ma_slabwise_distribution_bbps WHERE ma_user_id = ${ma_user_id} AND min_amount<=${amount} AND max_amount>=${amount} AND record_status = 'Y'`
      const _commssionmer = await this.rawQuery(sqlmer, connection)
      if (_commssionmer.length > 0) {
        charges = _commssionmer[0].customer_charges
        applied_type = _commssionmer[0].customer_charges_applied_type
      } else {
        // Check condition for integrated users
        const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${ma_user_id} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
        var resultint = await this.rawQuery(userintegrated, connection)
        log.logger({ pagename: 'bbpsIncentiveController.js', action: 'getSurcharge', type: 'response - Integrated user check', fields: resultint })
        if (resultint.length > 0) {
          const ma_user_id_default = util.integratedIncentive[resultint[0].integration_code]
          const sqlmerdefault = `select customer_charges,customer_charges_applied_type FROM ma_slabwise_distribution_bbps WHERE ma_user_id = ${ma_user_id_default} AND min_amount<=${amount} AND max_amount>=${amount} AND record_status = 'Y'`
          const _commssionmerdef = await this.rawQuery(sqlmerdefault, connection)
          if (_commssionmerdef.length > 0) {
            charges = _commssionmerdef[0].customer_charges
            applied_type = _commssionmerdef[0].customer_charges_applied_type
          } else {
            const sqlgeneral = `select customer_charges,customer_charges_applied_type FROM ma_slabwise_distribution_bbps WHERE state_master_id = 0 AND ma_user_id = 0 AND min_amount<=${amount} AND max_amount>=${amount} AND record_status = 'Y'`
            const _commssiongeneral = await this.rawQuery(sqlgeneral, connection)
            if (_commssiongeneral.length > 0) {
              charges = _commssiongeneral[0].customer_charges
              applied_type = _commssiongeneral[0].customer_charges_applied_type
            } else {
              charges = 0
              applied_type = 1
            }
          }
        } else {
          // Check other configuration (For now this is for distributor specific) ***********
          const sql = `SELECT distributer_user_master_id FROM ma_user_master WHERE profileid = '${ma_user_id}' LIMIT 1`
          const userData = await this.rawQuery(sql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'userData response', fields: userData })

          // create checkOtherConfigurationFields for checkOtherConfiguration function
          const checkOtherConfigurationFields = { distributerIdForGetDistribution: 0, ma_user_id }
          if (userData.length > 0) checkOtherConfigurationFields.distributerIdForGetDistribution = userData[0].distributer_user_master_id
          log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'checkOtherConfigurationFields', fields: checkOtherConfigurationFields })

          const sqlDtSdt = `select * FROM ma_slabwise_distribution_bbps WHERE min_amount <= ${amount} AND max_amount >= ${amount} AND record_status = 'Y'`
          const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql: sqlDtSdt, fields: checkOtherConfigurationFields, connection })
          log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
          if (getDistributionAdditionalCondition.status == 200) {
            charges = getDistributionAdditionalCondition.configurationData[0].customer_charges || 0
            applied_type = getDistributionAdditionalCondition.configurationData[0].customer_charges_applied_type || 1
          } else {
            // State specific configuration ***********
            const sqlstate = `select customer_charges,customer_charges_applied_type FROM ma_slabwise_distribution_bbps WHERE state_master_id = '${stateid}' AND ma_user_id = 0 AND min_amount<=${amount} AND max_amount>=${amount} AND record_status = 'Y'`
            const _commssionstate = await this.rawQuery(sqlstate, connection)
            if (_commssionstate.length > 0) {
              charges = _commssionstate[0].customer_charges
              applied_type = _commssionstate[0].customer_charges_applied_type
            } else {
              // Global specific configuration ************
              const sqlgeneral = `select customer_charges,customer_charges_applied_type FROM ma_slabwise_distribution_bbps WHERE state_master_id = 0 AND ma_user_id = 0 AND min_amount<=${amount} AND max_amount>=${amount} AND record_status = 'Y'  and ma_dt_sdt_id = 0`
              const _commssiongeneral = await this.rawQuery(sqlgeneral, connection)
              if (_commssiongeneral.length > 0) {
                charges = _commssiongeneral[0].customer_charges
                applied_type = _commssiongeneral[0].customer_charges_applied_type
              } else {
                charges = 0
                applied_type = 1
              }
            }
          }
        }
      }
      if (applied_type == '2' && charges != 0) {
        commissionVal = charges * amount / 100
      } else {
        commissionVal = charges
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], commissionVal: commissionVal }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSurcharge', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async incentiveDistribution (fields) {
    log.logger({ pagename: 'bbpsIncentiveController.js', action: 'incentiveDistribution', type: 'request', fields })
    const userSql = `SELECT distributer_user_master_id FROM ma_user_master where profileid = ${fields.ma_user_id}`
    const userDetails = await this.rawQuery(userSql, fields.connection)
    log.logger({ pagename: 'bbpsIncentiveController.js', action: 'incentiveDistribution', type: 'userDetails', fields: userDetails })
    fields.distributerIdForGetDistribution = 0
    if (userDetails.length > 0) fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id

    const config = await this.getDistributionConfig(fields)
    // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
    delete fields.distributerIdForGetDistribution
    // PAN DECRYPTION
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    var decryptionKey = util[env].secondaryEncrptionKey
    console.log('getDistributionConfig >>', config)
    if (config.status === 400) {
      return config
    }
    var finalUserObj = []
    // console.log(config.configdata.RT)
    var TDStypevalRT = '1'
    const isValidPANRT = validator.validatePAN(fields.pan)
    if (isValidPANRT) {
      TDStypevalRT = '3'
    }
    finalUserObj.push({
      ma_user_id: fields.ma_user_id,
      amount: config.configdata.RT.deduction_share,
      tdstype: TDStypevalRT
    })
    if (fields.distributer_user_master_id != 0) {
       // PAN DECRYPTION
      const sqldt = `Select profileid,userid,distributer_user_master_id,user_type,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan 	
      from ma_user_master where profileid='${fields.distributer_user_master_id}' AND user_status = "Y"`
      // const sqldt = 'Select profileid,userid,distributer_user_master_id,user_type,pan from ma_user_master where profileid=' + fields.distributer_user_master_id + ' AND user_status = "Y"'
      const respdt = await this.rawQuery(sqldt, fields.connection)
      if (respdt.length > 0) {
        if (respdt[0].user_type == 'DT' && respdt[0].distributer_user_master_id != 0) {
          var TDStypevalDT = '1'
          const isValidPANDT = validator.validatePAN(respdt[0].pan)
          if (isValidPANDT) {
            TDStypevalDT = '3'
          }
          finalUserObj.push({
            ma_user_id: respdt[0].profileid,
            amount: config.configdata.DT.deduction_share,
            tdstype: TDStypevalDT
          })

          // PAN DECRYPTION
          const sqlsd = `Select profileid,userid,distributer_user_master_id,user_type,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan from ma_user_master where profileid= ${respdt[0].distributer_user_master_id} AND user_status = "Y"`
          // const sqlsd = 'Select profileid,userid,distributer_user_master_id,user_type,pan from ma_user_master where profileid=' + respdt[0].distributer_user_master_id + ' AND user_status = "Y"'
          const respsd = await this.rawQuery(sqlsd, fields.connection)
          var TDStypevalSD = '1'
          const isValidPANSD = validator.validatePAN(respsd[0].pan)
          if (isValidPANSD) {
            TDStypevalSD = '3'
          }
          if (respsd.length > 0) {
            finalUserObj.push({
              ma_user_id: respsd[0].profileid,
              amount: config.configdata.SD.deduction_share,
              tdstype: TDStypevalSD
            })
          }
        } else if (respdt[0].user_type == 'DT' && respdt[0].distributer_user_master_id == 0) {
          var TDStypevalDT = '1'
          const isValidPANDT = validator.validatePAN(respdt[0].pan)
          if (isValidPANDT) {
            TDStypevalDT = '3'
          }
          // distribute dt
          finalUserObj.push({
            ma_user_id: respdt[0].profileid,
            amount: config.configdata.DT.deduction_share,
            tdstype: TDStypevalDT
          })
        } else if (respdt[0].user_type == 'SDT') {
          var TDStypevalSD = '1'
          const isValidPANSD = validator.validatePAN(respdt[0].pan)
          if (isValidPANSD) {
            TDStypevalSD = '3'
          }
          // distribute sd
          finalUserObj.push({
            ma_user_id: respdt[0].profileid,
            amount: config.configdata.SD.deduction_share,
            tdstype: TDStypevalSD
          })
        }
      }
    }
    console.log('fields user obj', finalUserObj)
    var utilisedamount = 0
    finalUserObj.forEach(element => {
      utilisedamount = utilisedamount + element.amount
    })

    finalUserObj.push({
      ma_user_id: util.airpayCommissionId,
      amount: fields.commission_amount - utilisedamount,
      tdstype: ''
    })
    console.log('after ap obj', finalUserObj)

    /* var pointsDetailsEntries = {}
    // getWalletBalance replaced here
    pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
      ma_user_id: fields.ma_user_id,
      amount: fields.commission_amount,
      transactionType: '1',
      connection: fields.connection
    })
    console.log('2. wallet points balance', pointsDetailsEntries)
    if (pointsDetailsEntries.status === 400) {
      // if (isSet) await mySQLWrapper.rollback(connection)
      return pointsDetailsEntries
    } */

    const sqlpointcmm = 'select * from ma_points_ledger_master where transaction_type = 14 AND ma_status = "S" AND orderid = "' + fields.orderid + '" limit 1'
    const resppointcmmt = await this.rawQuery(sqlpointcmm, fields.connection)
    console.log('resppointcmmt', resppointcmmt)
    if (resppointcmmt.length <= 0) {
      return { status: 400, message: errorMsg.responseCode[1002] + ' for commission', respcode: 1001 }
    }

    const ma_points_ledger_master_id = resppointcmmt[0].ma_points_ledger_master_id
    /* const common = require('../../util/common')
    const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '14' }, fields.connection)
    const descType = descObj.code_desc ? descObj.code_desc : ''
    const pointsLedgerAP = await pointsLedger.createEntry('_', {
      ma_user_id: fields.ma_user_id,
      amount: fields.commission_amount,
      mode: 'dr',
      transaction_type: 14, // BBPS surcharge type
      // description: util.surchargePointsDebitDescription + 'BBPS',
      description: 'Debit - ' + descType,
      orderid: fields.orderid,
      userid: fields.userid,
      ma_status: 'S',
      corresponding_id: util.airpayCommissionId, // for noe apcomissionid
      connection: fields.connection
    })
    if (pointsLedgerAP.status === 400) {
      return pointsLedgerAP
    } */
    /*
    for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
      const entry = await pointsDetailsController.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: pointsDetailsEntries.details[i].deductionAmount,
        wallet_type: pointsDetailsEntries.details[i].wallet_type,
        ma_points_ledger_master_id: ma_points_ledger_master_id,
        orderid: fields.orderid,
        ma_status: 'S',
        connection: fields.connection
      })
      if (entry.status === 400) {
        // if (isSet) await mySQLWrapper.rollback(connection)
        return entry
      }
    } */
    // Debit Entries for all shares
    fields.distributeObj = finalUserObj
    const allShares = await this.distributionEntries(fields)
    if (allShares.status === 400) {
      return allShares
    }
    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    // console.log('final user data', finalUserObj)
  }

  static async getDistributionConfig (fields) {
    log.logger({ pagename: 'bbpsIncentiveController.js', action: 'getDistributionConfig', type: 'request', fields: fields })
    var isSet = false
    let connection = null
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
      }
      var shareObj = []
      const sqlmer = `select * FROM ma_slabwise_distribution_bbps WHERE ma_user_id = ${fields.ma_user_id} AND min_amount<=${fields.amount} AND max_amount>=${fields.amount} AND record_status = 'Y'`
      const _commssionmer = await this.rawQuery(sqlmer, connection)
      if (_commssionmer.length > 0) {
        shareObj.RT = {
          share: _commssionmer[0].rt_share,
          applied_type: _commssionmer[0].rt_share_applied_type,
          operative_amount: _commssionmer[0].rt_operative_amount
        }
        shareObj.DT = {
          share: _commssionmer[0].dt_share,
          applied_type: _commssionmer[0].dt_share_applied_type,
          operative_amount: _commssionmer[0].dt_operative_amount
        }
        shareObj.SD = {
          share: _commssionmer[0].sd_share,
          applied_type: _commssionmer[0].sd_share_applied_type,
          operative_amount: _commssionmer[0].sd_operative_amount
        }
      } else {
        // Check condition for integrated users
        const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
        var resultint = await this.rawQuery(userintegrated, connection)
        log.logger({ pagename: 'bbpsIncentiveController.js', action: 'getDistributionConfig', type: 'response - Integrated user check', fields: resultint })
        if (resultint.length > 0) {
          const ma_user_id_default = util.integratedIncentive[resultint[0].integration_code]
          const sqlmerdefault = `select customer_charges,customer_charges_applied_type FROM ma_slabwise_distribution_bbps WHERE ma_user_id = ${ma_user_id_default} AND min_amount<=${fields.amount} AND max_amount>=${fields.amount} AND record_status = 'Y'`
          const _commssionmerdef = await this.rawQuery(sqlmerdefault, connection)
          if (_commssionmerdef.length > 0) {
            shareObj.RT = {
              share: _commssionmerdef[0].rt_share,
              applied_type: _commssionmerdef[0].rt_share_applied_type,
              operative_amount: _commssionmerdef[0].rt_operative_amount
            }
            shareObj.DT = {
              share: _commssionmerdef[0].dt_share,
              applied_type: _commssionmerdef[0].dt_share_applied_type,
              operative_amount: _commssionmerdef[0].dt_operative_amount
            }
            shareObj.SD = {
              share: _commssionmerdef[0].sd_share,
              applied_type: _commssionmerdef[0].sd_share_applied_type,
              operative_amount: _commssionmerdef[0].sd_operative_amount
            }
          }
        } else {
          // Check other configuration (For now this is for distributor specific)
          const sqlDtSdt = `select * FROM ma_slabwise_distribution_bbps WHERE min_amount <= ${fields.amount} AND max_amount >= ${fields.amount} AND record_status = 'Y'`
          const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql: sqlDtSdt, fields, connection })
          log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
          if (getDistributionAdditionalCondition.status == 200) {
            shareObj.RT = {
              share: getDistributionAdditionalCondition.configurationData[0].rt_share,
              applied_type: getDistributionAdditionalCondition.configurationData[0].rt_share_applied_type,
              operative_amount: getDistributionAdditionalCondition.configurationData[0].rt_operative_amount
            }
            shareObj.DT = {
              share: getDistributionAdditionalCondition.configurationData[0].dt_share,
              applied_type: getDistributionAdditionalCondition.configurationData[0].dt_share_applied_type,
              operative_amount: getDistributionAdditionalCondition.configurationData[0].dt_operative_amount
            }
            shareObj.SD = {
              share: getDistributionAdditionalCondition.configurationData[0].sd_share,
              applied_type: getDistributionAdditionalCondition.configurationData[0].sd_share_applied_type,
              operative_amount: getDistributionAdditionalCondition.configurationData[0].sd_operative_amount
            }
          } else {
            // state specific configuration
            const sqlstate = `select * FROM ma_slabwise_distribution_bbps WHERE state_master_id = '${fields.stateid}' AND ma_user_id = 0 AND min_amount<=${fields.amount} AND max_amount>=${fields.amount} AND record_status = 'Y'`
            const _commssionstate = await this.rawQuery(sqlstate, connection)
            if (_commssionstate.length > 0) {
              shareObj.RT = {
                share: _commssionstate[0].rt_share,
                applied_type: _commssionstate[0].rt_share_applied_type,
                operative_amount: _commssionstate[0].rt_operative_amount
              }
              shareObj.DT = {
                share: _commssionstate[0].dt_share,
                applied_type: _commssionstate[0].dt_share_applied_type,
                operative_amount: _commssionstate[0].dt_operative_amount
              }
              shareObj.SD = {
                share: _commssionstate[0].sd_share,
                applied_type: _commssionstate[0].sd_share_applied_type,
                operative_amount: _commssionstate[0].sd_operative_amount
              }
            } else {
              const sqlgeneral = `select * FROM ma_slabwise_distribution_bbps WHERE state_master_id = 0 AND ma_user_id = 0 AND min_amount<=${fields.amount} AND max_amount>=${fields.amount} AND record_status = 'Y' AND ma_dt_sdt_id = 0`
              const _commssiongeneral = await this.rawQuery(sqlgeneral, connection)
              if (_commssiongeneral.length > 0) {
                shareObj.RT = {
                  share: _commssiongeneral[0].rt_share,
                  applied_type: _commssiongeneral[0].rt_share_applied_type,
                  operative_amount: _commssiongeneral[0].rt_operative_amount
                }
                shareObj.DT = {
                  share: _commssiongeneral[0].dt_share,
                  applied_type: _commssiongeneral[0].dt_share_applied_type,
                  operative_amount: _commssiongeneral[0].dt_operative_amount
                }
                shareObj.SD = {
                  share: _commssiongeneral[0].sd_share,
                  applied_type: _commssiongeneral[0].sd_share_applied_type,
                  operative_amount: _commssiongeneral[0].sd_operative_amount
                }
              }
            }
          }
        }
      }
      var finalObj = {}

      Object.keys(shareObj).forEach((key, index) => {
        if (shareObj[key].applied_type == '2' && shareObj[key].share != 0) {
          // var data = key
          var calcamt = ''
          if (shareObj[key].operative_amount == 1) {
            calcamt = fields.amount
          } else if (shareObj[key].operative_amount == 2) {
            calcamt = fields.commission_amount
          }
          finalObj[key] = { deduction_share: shareObj[key].share * calcamt / 100 }
        } else {
          finalObj[key] = { deduction_share: shareObj[key].share }
        }
      })

      console.log('final obj', finalObj)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], configdata: finalObj }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionConfig', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async distributionEntries (fields) {
    log.logger({ pagename: 'bbpsIncentiveController.js', action: 'distributionEntries', type: 'request', fields: fields })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.orderid, fields.connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = fields.distributeObj
      console.log('distribution', distribution)
      for (const value of distribution) {
        console.log('distribution key', value)
        var subAmount = value.amount
        if (subAmount <= 0) {
          continue
        }
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          var tds = 0
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: 9,
            amount: subAmount,
            ma_deduction_type: value.tdstype,
            connection: fields.connection
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: util.IncentiveCommissionDescription + 'BBPS',
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: fields.connection
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, fields.connection)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: '9',
              orderid: fields.orderid,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: fields.connection
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
            // [END] Make entry in ma_orderwise_taxes table
          }
        }

        if (subAmount > 0) {
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: fields.connection })
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 9,
              // description: util.IncentiveEarnedMerchant + 'BBPS',
              description: (util.IncentiveEarnedMerchantNew).replace('#type#', 'BBPS'),
              ma_status: 'S',
              orderid: fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 9,
              // description: util.IncentiveEarnedMerchant + 'BBPS',
              description: (util.IncentiveEarnedMerchantNew).replace('#type#', 'BBPS'),
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: fields.ma_user_id,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: '9',
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id

            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, fields.connection)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}
module.exports = BbpsIncentive
