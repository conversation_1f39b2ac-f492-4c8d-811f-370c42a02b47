/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const util = require('../../util/util')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const commonFunction = require('../common/commonFunctionController')
class TopupIncentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_slabwise_incentive_topup'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_incentive_topup_id'
  }

  static async calculateSurcharge (_, { ma_user_id, amount, userid}) {
    log.logger({ pagename: 'TopupIncentive.js', action: 'calculateSurcharge', type: 'request', fields: { ma_user_id, amount, userid } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_user_master_id,state,user_type FROM ma_user_master WHERE profileid = '${ma_user_id}' AND userid = '${userid}'`
      const userData = await this.rawQuery(sql, connection)
      var surcharge = 0
      if (userData.length > 0) {
        const topupsurcharge = await this.getSurcharge(_,{
          ma_user_id: ma_user_id,
          amount: amount,
          stateid: userData[0].state,
          user_type: userData[0].user_type,
          connection: connection
        })
                
        if (topupsurcharge.status !== undefined && topupsurcharge.status === 200) {
          surcharge = topupsurcharge.commissionVal
        }  
      } else {
        return { status: 400, message: 'fail : User does not exists.' }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, surcharge: surcharge, amount: amount + surcharge}   
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculateSurcharge', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getSurcharge (_, { ma_user_id, stateid, amount, user_type, connection = null }) {
    log.logger({ pagename: 'TopupIncentive.js', action: 'getSurcharge', type: 'request', fields: { ma_user_id, stateid, amount } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      var charges = ''
      var applied_type = ''
      var commissionVal = 0
      const sqlmer = `select share_amount,share_amount_applied_type FROM ma_slabwise_incentive_topup WHERE ma_user_id = ${ma_user_id} AND min_amount<=${amount} AND max_amount>=${amount} AND user_type = '${user_type}' AND record_status = 'Y' AND charge_type = 'C'`
      const _commssionmer = await this.rawQuery(sqlmer, connection)
      if (_commssionmer.length > 0) {
        charges = _commssionmer[0].share_amount
        applied_type = _commssionmer[0].share_amount_applied_type
      } else {
        // Check other configuration (For now this is for distributor specific) ***********
        const sql = `SELECT distributer_user_master_id FROM ma_user_master WHERE profileid = '${ma_user_id}' LIMIT 1`
        const userData = await this.rawQuery(sql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'userData response', fields: userData })

        // create checkOtherConfigurationFields for checkOtherConfiguration function
        const checkOtherConfigurationFields = { distributerIdForGetDistribution: 0, ma_user_id }
        // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
        if (userData.length > 0) checkOtherConfigurationFields.distributerIdForGetDistribution = userData[0].distributer_user_master_id
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'checkOtherConfigurationFields', fields: checkOtherConfigurationFields })

        const sqlForOtherConfig = `select share_amount,share_amount_applied_type FROM ma_slabwise_incentive_topup WHERE min_amount<=${amount} AND max_amount>=${amount} AND user_type = '${user_type}' AND record_status = 'Y' AND charge_type = 'C'`
        // Check other configuration (For now this is for distributor specific)
        const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql: sqlForOtherConfig, fields: checkOtherConfigurationFields, connection })
        log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionEntry', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
        if (getDistributionAdditionalCondition.status == 200) {
          charges = getDistributionAdditionalCondition.configurationData[0].share_amount
          applied_type = getDistributionAdditionalCondition.configurationData[0].share_amount_applied_type
        } else {
          const sqlstate = `select share_amount,share_amount_applied_type FROM ma_slabwise_incentive_topup WHERE state_master_id = '${stateid}' AND ma_user_id = 0 AND min_amount<=${amount} AND max_amount>=${amount} AND user_type = '${user_type}' AND record_status = 'Y' AND charge_type = 'C'`
          const _commssionstate = await this.rawQuery(sqlstate, connection)
          if (_commssionstate.length > 0) {
            charges = _commssionstate[0].share_amount
            applied_type = _commssionstate[0].share_amount_applied_type
          } else {
            const sqlgeneral = `select share_amount,share_amount_applied_type FROM ma_slabwise_incentive_topup WHERE state_master_id = 0 AND ma_user_id = 0 AND min_amount<=${amount} AND max_amount>=${amount} AND user_type = '${user_type}' AND  record_status = 'Y' AND ma_dt_sdt_id = 0 AND charge_type = 'C'`
            const _commssiongeneral = await this.rawQuery(sqlgeneral, connection)
            if (_commssiongeneral.length > 0) {
              charges = _commssiongeneral[0].share_amount
              applied_type = _commssiongeneral[0].share_amount_applied_type
            } else {
              charges = 0
              applied_type = 1
            }
          }
        }
      }
      if (applied_type == '2' && charges != 0) {
        commissionVal = charges * amount / 100
      } else {
        commissionVal = charges
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], commissionVal : commissionVal }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSurcharge', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async incentiveDistribution (fields) {
    log.logger({ pagename: 'TopupIncentive.js', action: 'incentiveDistribution', type: 'request', fields: fields })
    try {  
      const surcharge_calc = await this.calculateSurcharge('',{ma_user_id: fields.ma_user_id, amount: fields.amount, userid: fields.userid})
      if (surcharge_calc.status === 400) {
        return surcharge_calc
      }

      if ( surcharge_calc.surcharge == 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
      // check global config for daily or monthly
      const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting(fields)
      if (globalIncentiveType.incentiveType == '') {
        log.logger({ pagename: 'TopupIncentive.js', action: 'incentiveDistribution', type: 'response', fields: { message : 'Global Incentive Type setting not found' } })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
      // if daily execute below code else credit amount for 99999 and add record in another table

      // For Cash Account credit to Airpay
     
        const cashEntry = await cashAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: surcharge_calc.surcharge,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid:  fields.aggregator_order_id,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })

        if (cashEntry.status === undefined || cashEntry.status !== 200) {
          return cashEntry
        }
        // For Points Account Credit from Airpay Account
        const pointsEntry = await pointsAccount.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: surcharge_calc.surcharge,
          transaction_type: 'IN',
          ma_status: 'S',
          orderid:  fields.aggregator_order_id,
          userid: fields.userid,
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })
        if (pointsEntry.status === undefined || pointsEntry.status !== 200) {
          return pointsEntry
        }

        const common = require('../../util/common')
        const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '16' }, fields.connection)
        const descType = descObj.code_desc ? descObj.code_desc : ''

        const pointsLedgerDebits = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: surcharge_calc.surcharge,
          mode: 'dr',
          transaction_type: 16,
          // description: util.incentivePointsDebitDescription + 'Credits',
          description: 'Debit - ' + descType,
          orderid: fields.aggregator_order_id,
          userid: fields.userid,
          ma_status: 'S',
          corresponding_id: fields.ma_user_id,
          connection: fields.connection
        })
        if (pointsLedgerDebits.status === 400) {
          return pointsLedgerDebits
        }

        if ( globalIncentiveType.incentiveType == 'D') {
        const pointsLedgerCredits = await pointsLedger.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: surcharge_calc.surcharge,
          mode: 'cr',
          transaction_type: 16,
          // description: util.IncentiveEarnedMerchant + 'Credits',
          description: 'Credits' + descType,
          ma_status: 'S',
          orderid: fields.aggregator_order_id,
          userid: fields.userid,
          corresponding_id: util.airpayUserId,
          connection: fields.connection
        })
        if (pointsLedgerCredits.status === 400) {
          return pointsLedgerCredits
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      } else {
        fields.incamount =  surcharge_calc.surcharge
        fields.transactiontype = '16'

        const pointsLedgerCredits = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayHoldingId,
          amount: fields.incamount,
          mode: 'cr',
          transaction_type: fields.transactiontype,
          // description: util.IncentiveEarnedMerchant + 'Credits',
          description: 'Credits' + descType,
          ma_status: 'S',
          orderid: fields.ma_user_id + '-' + fields.aggregator_order_id,
          userid: fields.userid,
          corresponding_id: util.airpayUserId,
          connection: fields.connection
        })
        if (pointsLedgerCredits.status === 400) {
          return pointsLedgerCredits
        }
  
        const getTransactionController = require('../transaction/transactionController')
        const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.aggregator_order_id, fields.connection)
        var now       = new Date()
        var thisMonth = util.monthsconfig[now.getMonth()]
        const data = {
          ma_user_id : fields.ma_user_id,
          amount: fields.incamount,
          transaction_type : fields.transactiontype,
          monthyear:  thisMonth + ' ' + now.getFullYear(),
          summary_id : 0,
          transaction_master_id : parent_transaction_master_id
  
        }
        const monthlyIncCreate = await monthlyIncentiveController.createEntry('_',data,fields.connection)
        if (monthlyIncCreate.status === 400) {
          return monthlyIncCreate
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }  
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }    

  }


}
module.exports = TopupIncentive
