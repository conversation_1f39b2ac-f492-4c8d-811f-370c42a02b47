const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const balanceController = require('../balance/balanceController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const integrated = require('../integrated/integratedController')
const common_fns = require('../../util/common_fns')
const commonFunction = require('../common/commonFunctionController')
class BeneVerifyIncentive extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_bene_shares'
  }

  static get PRIMARY_KEY () {
    return 'ma_bene_shares_id'
  }

  static async getDistributionAllHeads (fields, connection) {
    var distributionObj = []
    var rt_share = 0
    var dt_share = 0
    var sd_share = 0
    var customer_charges = 0
    fields.connection = connection
    // PAN DECRYPTION
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'	
    var decryptionKey = util[env].secondaryEncrptionKey	
    const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`

    // const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan,gst_number FROM ma_user_master where profileid ='
    const userDetails = await this.rawQuery(userSql + fields.ma_user_id, fields.connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })
    // log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - RT', fields: userDetails })

    if (userDetails.length <= 0) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
    }
    fields.state_master_id = userDetails[0].state
    fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id

    var globalValues = await this.globalValues(fields, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - globalValues', fields: globalValues })
    const globalSettings = Object.keys(globalValues).length > 0
    if (globalSettings) {
      customer_charges = globalValues.customer_charges
      rt_share = await this.findValue(customer_charges, globalValues.globalShare[0].rt_share, globalValues.globalShare[0].rt_share_applied_type)
      dt_share = await this.findValue(customer_charges, globalValues.globalShare[1].dt_share, globalValues.globalShare[1].dt_share_applied_type)
      sd_share = await this.findValue(customer_charges, globalValues.globalShare[2].sd_share, globalValues.globalShare[2].sd_share_applied_type)
    }

    distributionObj.push({ transaction_charges: customer_charges })

    console.log('Calculated shares data', rt_share, dt_share, sd_share, customer_charges, globalValues)
    // const ma_status = 'S'
    var afterDeductionAmt = customer_charges

    // Push RT details
    if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
      var TDStypeval = '1'
      const isValidPAN = validator.validatePAN(userDetails[0].pan)
      if (isValidPAN) {
        TDStypeval = '3'
      }
      afterDeductionAmt = afterDeductionAmt - rt_share
      distributionObj.push({
        ma_user_id: fields.ma_user_id,
        amount: rt_share,
        gst_number: userDetails[0].gst_number,
        user_type: 'RT',
        TDS: await this.getTDS(fields.ma_user_id, TDStypeval, rt_share, connection) // With PAN or without
      })
    }

    // Find DT
    if (userDetails[0].distributer_user_master_id > 0) {
      const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, fields.connection)
      distribuetrDetails.share = dt_share
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - DT', fields: distribuetrDetails })
      if (distribuetrDetails.length > 0) {
        // IF Distributer
        if (distribuetrDetails[0].user_type === 'DT') {
          // Push DT details
          if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
            TDStypeval = '1'
            const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
            if (isValidPAN) {
              TDStypeval = '3'
            }
            afterDeductionAmt = afterDeductionAmt - dt_share
            distributionObj.push({
              ma_user_id: distribuetrDetails[0].profileid,
              amount: dt_share,
              gst_number: distribuetrDetails[0].gst_number,
              user_type: 'DT',
              TDS: await this.getTDS(fields.ma_user_id, TDStypeval, rt_share, connection) // With PAN or without
            })
          }
          // Find Super DT
          if (distribuetrDetails[0].distributer_user_master_id > 0) {
            const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', fields.connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'getDistributionAllHeads', type: 'response - SDT', fields: superdistribuetrDetails })
            if (superdistribuetrDetails.length > 0) {
              // Push SDT details
              if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                TDStypeval = '1'
                const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                if (isValidPAN) {
                  TDStypeval = '3'
                }
                afterDeductionAmt = afterDeductionAmt - sd_share
                distributionObj.push({
                  ma_user_id: superdistribuetrDetails[0].profileid,
                  amount: sd_share,
                  gst_number: superdistribuetrDetails[0].gst_number,
                  user_type: 'SDT',
                  TDS: await this.getTDS(fields.ma_user_id, TDStypeval, rt_share, connection) // With PAN or without
                })
              }
            }
          }
        } else if (distribuetrDetails[0].user_type === 'SDT') {
          if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
            TDStypeval = '1'
            const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
            if (isValidPAN) {
              TDStypeval = '3'
            }
            afterDeductionAmt = afterDeductionAmt - sd_share
            distributionObj.push({
              ma_user_id: distribuetrDetails[0].profileid,
              amount: sd_share,
              user_type: 'SDT',
              gst_number: distribuetrDetails[0].gst_number,
              TDS: await this.getTDS(fields.ma_user_id, TDStypeval, rt_share, connection) // With PAN or without
            })
          }
        }
      }
    }
    // Airpay Share array
    if (afterDeductionAmt > 0) {
      distributionObj.push({
        ma_user_id: util.airpayCommissionId,
        user_type: 'SD',
        amount: afterDeductionAmt,
        TDS: 0
      })
    }
    return distributionObj
  }

  static async getTDS (ma_user_id, type, amount, connection) {
    const commissionDetails = await commissionController.getCommission('_', {
      ma_user_id: ma_user_id,
      ma_commission_type: '2',
      amount: amount,
      ma_deduction_type: type,
      connection
    })

    const tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
    return tds
  }

  static async globalValues (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'request', fields })
    var txnCharge = {}
    var state_master_id = 0
    var ma_user_id = 0
    var result = {}

    // Get Global settings
    const sql = `SELECT state_master_id,ma_user_id,
    customer_charges,ma_bank_on_boarding_id,rt_share,
    rt_applied_type,dt_share,dt_applied_type,
    sd_share,sd_applied_type,
    record_status,settings_flag
    FROM ma_bene_shares 
    where record_status='Y' 
    and settings_flag='2'
    and ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}
    `
    // Check retailer specific configuration
    if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
      ma_user_id = fields.ma_user_id
      const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
      result = await this.rawQuery(retailerSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Retailer Specific', fields: result })
      const globalContri = await this.buildShareObject(result)
      if (globalContri) {
        return globalContri
      }
    }

    // Check condition for integrated users
    const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id`
    const resultint = await this.rawQuery(userintegrated, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Integrated user check', fields: resultint })
    if (resultint.length > 0) {
      result = {}
      ma_user_id = util.integratedIncentive[resultint[0].integration_code]
      const retailerintegratedSql = `SELECT state_master_id,ma_user_id,
      customer_charges,ma_bank_on_boarding_id,rt_share,
      rt_applied_type,dt_share,dt_applied_type,
      sd_share,sd_applied_type,
      record_status,settings_flag FROM ma_bene_shares where record_status='Y' and ma_user_id=${ma_user_id} and ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}`
      result = await this.rawQuery(retailerintegratedSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Integrated Retailer Specific', fields: result })
      const globalContri = await this.buildShareObject(result)
      // if (globalContri) {
      return globalContri
      // }
    }

    // Check other configuration (For now this is for distributor specific)
    result = {}
    const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields, connection })
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
    if (getDistributionAdditionalCondition.status == 200) {
      result = getDistributionAdditionalCondition.configurationData
      const globalContri = await this.buildShareObject(result)
      if (globalContri) return globalContri
    }
    // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
    delete fields.distributerIdForGetDistribution

    // Reinitializing variables
    ma_user_id = 0
    result = {}
    if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
      const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
      result = await this.rawQuery(stateSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - State Specific', fields: result })
      const globalContri = await this.buildShareObject(result)
      if (globalContri) {
        return globalContri
      }
    }

    // Reinitializing variables
    ma_user_id = 0
    state_master_id = 0
    result = {}

    // Check default configuration
    const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
    result = await this.rawQuery(defaultSql, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'globalValues', type: 'response - Default', fields: result })
    return await this.buildShareObject(result)
  }

  static async buildShareObject (globalContri) {
    if (globalContri.length > 0) {
      var globalShare = []
      const customer_charges = globalContri[0].customer_charges
      globalShare.push({
        rt_share: globalContri[0].rt_share,
        rt_share_applied_type: globalContri[0].rt_applied_type
      })

      globalShare.push({
        dt_share: globalContri[0].dt_share,
        dt_share_applied_type: globalContri[0].dt_applied_type
      })

      globalShare.push({
        sd_share: globalContri[0].sd_share,
        sd_share_applied_type: globalContri[0].sd_applied_type
      })

      return { customer_charges: customer_charges, globalShare: globalShare }
    }
    return false
  }

  /**
   * Calculate amount based on applied_type and operative_amount
   */
  static async findValue (chargeAmount, share, applied_type) {
    console.log('FINVALUE :::::', chargeAmount, share, applied_type)
    if (share == null || share == undefined) {
      return 0
    }
    if (applied_type == 2) {
      return share * chargeAmount / 100
    }
    return share
  }

  /**
   * To distribute the incentives
   * Array with  ma_user_id,amount,applied_type,user_type
   */
  static async distributionEntries (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'request', fields })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.aggregator_order_id, connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = fields.distributeObj
      for (const value of distribution) {
        var subAmount = value.amount
        var descPart = ''
        var tds = 0
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: '22',
            amount: value.amount,
            ma_deduction_type: value.TDStype,
            connection: connection
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          console.log('TDS', tds)
          var appliedCommission = ''
          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            appliedCommission = commissionDetails.appliedCommission
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: util.TDSonCommission + value.user_type,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.aggregator_order_id,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: connection
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, connection)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: '22',
              orderid: fields.aggregator_order_id,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: connection
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
            // [END] Make entry in ma_orderwise_taxes table
          }
        }
        if (subAmount > 0) {
          descPart = tds !== 0 ? ' (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: connection })
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: '22',
              description: 'Credit - ' + util.CommissionEarnedBeneValidation + descPart,
              ma_status: 'S',
              orderid: fields.aggregator_order_id,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: '22',
              description: 'Credit - ' + util.CommissionEarnedBeneValidation + descPart,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.aggregator_order_id,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: '22',
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id

            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, connection)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * Incentive entries for AEPS transaction
   */
  static async incentiveDistribution (fields, connection) {
    // fields.userid = userid
    // PAN DECRYPTION
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'	
    var decryptionKey = util[env].secondaryEncrptionKey
    log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'request', fields: fields })
    try {
      var distributionObj = []
      var rt_share = 0
      var dt_share = 0
      var sd_share = 0
      var customer_charges = 0

      const balSql = `SELECT balance,actual_balance,wallet_type FROM ma_allow_withdrawal WHERE ma_user_id = '${fields.ma_user_id}'  and  FIND_IN_SET('22', transaction_type)`
      const balDetails = await this.rawQuery(balSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - walletdetails', fields: balDetails })
      if (balDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Wallet [22] Type Missing' }
      }
      // PAN DECRYPTION
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`
      // const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan,gst_number FROM ma_user_master where profileid ='
      const userDetails = await this.rawQuery(userSql + fields.ma_user_id, connection)
      // log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails })
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - RT', fields: userDetails.map(e => common_fns.maskValue(e, 'pan')) })

      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }
      fields.state_master_id = userDetails[0].state
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id

      var globalValues = await this.globalValues(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })
      const globalSettings = Object.keys(globalValues).length > 0
      if (globalSettings) {
        customer_charges = globalValues.customer_charges
        rt_share = await this.findValue(customer_charges, globalValues.globalShare[0].rt_share, globalValues.globalShare[0].rt_share_applied_type)
        dt_share = await this.findValue(customer_charges, globalValues.globalShare[1].dt_share, globalValues.globalShare[1].dt_share_applied_type)
        sd_share = await this.findValue(customer_charges, globalValues.globalShare[2].sd_share, globalValues.globalShare[2].sd_share_applied_type)
      }

      console.log('Calculated shares data', rt_share, dt_share, sd_share, customer_charges, globalValues)
      const ma_status = 'S'
      var afterDeductionAmt = customer_charges

      // Push RT details
      if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        afterDeductionAmt = afterDeductionAmt - rt_share
        distributionObj.push({
          ma_user_id: fields.ma_user_id,
          amount: rt_share,
          gst_number: userDetails[0].gst_number,
          user_type: 'RT',
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, connection)
        distribuetrDetails.share = dt_share
        log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - dt_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                gst_number: distribuetrDetails[0].gst_number,
                user_type: 'DT',
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', connection)
              log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  afterDeductionAmt = afterDeductionAmt - sd_share
                  distributionObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    gst_number: superdistribuetrDetails[0].gst_number,
                    user_type: 'SDT',
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - sd_share
              distributionObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                user_type: 'SDT',
                gst_number: distribuetrDetails[0].gst_number,
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }
      // Airpay Share array
      if (afterDeductionAmt > 0) {
        distributionObj.push({
          ma_user_id: util.airpayCommissionId,
          user_type: 'SD',
          amount: afterDeductionAmt
        })
      }
      // Debit Entries for all shares
      fields.distributeObj = distributionObj
      const allShares = await this.distributionEntries(fields, connection)
      if (allShares.status === 400) {
        return allShares
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async reverseIncentive (_, fields, connection) {
    // Cloning request and removing connection
    log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentive', type: 'request', fields: fields })
    var sql = `SELECT * from ma_points_ledger_master where parent_id = '${fields.orderid}' and transaction_type in (21,22,11) order by mode,ma_points_ledger_master_id desc`
    const incentiveDetails = await pointsLedger.rawQuery(sql, connection)
    if (incentiveDetails.length > 0) {
      fields.incentiveDetails = incentiveDetails
      fields.transaction_status = 'REV'
      fields.transaction_type = '21'
    }

    // Check if integrated merchant
    const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${incentiveDetails[0].userid}`
    const integratedMer = await this.rawQuery(userSQL, connection)
    console.log(userSQL)
    if (integratedMer.length > 0) {
      fields.action = 'REFUND'
      fields.aggregator_user_id = integratedMer[0].aggregator_user_id
      fields.aggregator_order_id = fields.orderid
      const res = await integrated.index(fields, connection)
      console.log('integrated returned this', res)
      if (res.status != 200) {
        return res
      }
    }

    // const incentiveDetails = fields.incentiveDetails
    var resp
    var newFields
    var pointsDetails = false
    // Reversing points ledger entries
    for (var i = 0; i < incentiveDetails.length; i++) {
      var pointsDetailsEntries = {}
      pointsDetails = false
      if (incentiveDetails[i].mode === 'cr') {
        newFields = {
          ma_user_id: incentiveDetails[i].ma_user_id,
          corresponding_id: incentiveDetails[i].corresponding_id,
          amount: incentiveDetails[i].amount,
          userid: incentiveDetails[i].userid,
          orderid: fields.transaction_status + '-' + incentiveDetails[i].orderid,
          mode: 'dr',
          transaction_type: incentiveDetails[i].transaction_type,
          ma_status: fields.transaction_status,
          // description: (fields.transaction_status === 'R' ? 'Refunded - ' : 'Reversed - ') + incentiveDetails[i].description,
          description: incentiveDetails[i].description + (fields.transaction_status === 'R' ? ' - Refunded' : ' - Reversed'),
          connection: connection
        }

        // Point Details Entries
        if (util.airpaymerchantconfig.includes(incentiveDetails[i].ma_user_id) == false) {
          pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
            ma_user_id: incentiveDetails[i].ma_user_id,
            amount: incentiveDetails[i].amount,
            transactionType: '1',
            connection: connection
          })
          if (pointsDetailsEntries.status === 400) {
            return pointsDetailsEntries
          }
          pointsDetails = true
        }
      } else {
        newFields = {
          ma_user_id: incentiveDetails[i].ma_user_id,
          corresponding_id: incentiveDetails[i].corresponding_id,
          amount: -incentiveDetails[i].amount,
          userid: incentiveDetails[i].userid,
          orderid: fields.transaction_status + '-' + incentiveDetails[i].orderid,
          mode: 'cr',
          transaction_type: fields.transaction_type,
          ma_status: fields.transaction_status,
          // description: (fields.transaction_status === 'R' ? 'Refunded - ' : 'Reversed - ') + incentiveDetails[i].description.replace('debited', 'credited'),
          description: incentiveDetails[i].description.replace('Debit', 'Credit') + (fields.transaction_status === 'R' ? ' - Refunded' : ' - Reversed'),
          connection: connection
        }
      }
      resp = await pointsLedger.createEntry(_, newFields)
      if (resp.status === 400) {
        return resp
      }
      if (pointsDetails && util.airpaymerchantconfig.includes(incentiveDetails[i].ma_user_id) == false) {
        if (pointsDetailsEntries.details.length > 0) {
          for (var j = 0; j < pointsDetailsEntries.details.length; j++) {
            const entry = await pointsDetailsController.createEntry(_, {
              ma_user_id: incentiveDetails[i].ma_user_id,
              amount: pointsDetailsEntries.details[j].deductionAmount,
              wallet_type: pointsDetailsEntries.details[j].wallet_type,
              ma_points_ledger_master_id: resp.id,
              orderid: fields.transaction_status + '-' + incentiveDetails[i].orderid,
              ma_status: 'R',
              transaction_status: 'S',
              connection: connection
            })
            if (entry.status === 400) {
              return entry
            }
          }
        }
      }
    }

    // call integrated send money based on emitra condition
    if (integratedMer.length > 0) {
      const params = {
        ma_user_id: fields.ma_user_id,
        aggregator_order_id: fields.orderid
      }
      const resrev = await integrated.sendMoneyReverse(params, connection)
      if (resrev.status != 200) {
        return resrev
      }
    }

    // [START] Make refund/reverse entry in ma_orderwise_taxes table
    // if (fields.transaction_status === 'R') {
    // Get order wise taxes success entry
    const sqlOrderWise = `SELECT * FROM ma_orderwise_taxes WHERE parent_id = '${fields.orderid}' AND ma_status='S' AND transaction_type='22'`
    const sqlOrderWiseRes = await this.rawQuery(sqlOrderWise, connection)
    // console.log('sqlOrderWiseRes', sqlOrderWiseRes)
    log.logger({ pagename: 'beneVerifyIncentiveController.js', action: 'reverseIncentive', type: 'response', fields: sqlOrderWiseRes })
    if (sqlOrderWiseRes.length > 0) {
      const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
      for (const valueOrderWise of sqlOrderWiseRes) {
        const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
          transaction_type: valueOrderWise.transaction_type,
          orderid: fields.transaction_status + '-' + valueOrderWise.orderid,
          ma_user_id: valueOrderWise.ma_user_id,
          amount: (-1 * valueOrderWise.amount),
          gst_amount: (-1 * valueOrderWise.gst_amount),
          tds_amount: (-1 * valueOrderWise.tds_amount),
          ma_status: 'R',
          connection: connection
        })
        if (orderwiseEntry.status === 400) {
          return orderwiseEntry
        }
      }
    }
    // }
    // [END] Make refund/reverse entry in ma_orderwise_taxes table

    // check for entry in monthly incentives
    const getTransactionController = require('../transaction/transactionController')
    const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.orderid, connection)
    const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
    const sqlincmonth = `SELECT * FROM ma_monthly_incentives_process WHERE transaction_master_id = '${parent_transaction_master_id}' AND isRefund = 'FALSE'`
    const sqlincmonthRes = await this.rawQuery(sqlincmonth, connection)
    var now = new Date()
    var thisMonth = util.monthsconfig[now.getMonth()]
    for (let i = 0; i < sqlincmonthRes.length; i++) {
      const monthlyIncCreateRev = await monthlyIncentiveController.createEntry('_', {
        ma_user_id: sqlincmonthRes[i].ma_user_id,
        amount: -(sqlincmonthRes[i].amount),
        transaction_type: sqlincmonthRes[i].transaction_type,
        monthyear: thisMonth + ' ' + now.getFullYear(),
        summary_id: 0,
        transaction_master_id: parent_transaction_master_id,
        isRefund: 'TRUE'
      },
      connection)
      if (monthlyIncCreateRev.status === 400) {
        return monthlyIncCreateRev
      }
    }
    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
  }
}

module.exports = BeneVerifyIncentive
