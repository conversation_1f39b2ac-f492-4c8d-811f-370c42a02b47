const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const validator = require('../../util/validator')
const monthlyIncentiveController = require('./monthlyIncentiveDistributionController')
const common_fns = require('../../util/common_fns')
const commonFunction = require('../common/commonFunctionController')

class aepsDistribution extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_slabwise_distribution_aeps'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_aeps_id'
  }

  /**
   * Distribution Configuration for AEPS Incentive
   * fields : amount, ma_user_id,state_master_id,connection
   */
  static async getDistribution (fields) {
    log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'request', fields: common_fns.maskValue(fields, 'customer_aadhaar') })
    try {
      const checkOtherConfigurationFields = { ...fields }
      delete fields.distributerIdForGetDistribution
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var sql = `SELECT * FROM ma_slabwise_distribution_aeps where ${fields.amount} between min_amount and max_amount and record_status='Y' and transaction_mode = '${fields.transaction_mode}'`
      log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'request - Integrated user check', fields: sql })
      // Check condition for integrated users
      // add user id condition, to add check for whether user is web or emitra
      const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.id = ${fields.userid} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id limit 1 `
      const resultint = await this.rawQuery(userintegrated, fields.connection)
      log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        ma_user_id = util.integratedIncentive[resultint[0].integration_code]
        result = {}
        const retailerintegratedSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerintegratedSql, fields.connection)
        log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - Integrated Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        } else {
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
        }
      }

      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerSql, fields.connection)
        log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Check other configuration (For now this is for distributor specific)
      result = {}
      console.log('sql>>>>>>>>>', sql)
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields: checkOtherConfigurationFields, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      // Reinitializing variables
      ma_user_id = 0
      result = {}

      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        // state_master_id = 2 // FOR TESTING ONLY
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - State Specific', fields: result })
        result = await this.rawQuery(stateSql, fields.connection)
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}

      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      result = await this.rawQuery(defaultSql, fields.connection)
      log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * To distribute the incentives
   * Array with  ma_user_id,amount,applied_type,user_type
   */
  static async distributionEntries (fields) {
    var requestFields = {}
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: 'aepsDistributionController.js', action: 'distributionEntries', type: 'request', fields: requestFields })
    try {
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(fields.orderid, fields.connection)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = fields.distributeObj
      for (const value of distribution) {
        var subAmount = value.amount
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          var tds = 0
          const commissionDetails = await commissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: fields.incentive_type,
            amount: subAmount,
            ma_deduction_type: value.TDStype,
            connection: fields.connection
          }
          )

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          console.log('TDS', tds)
          // TDS entry
          const common = require('../../util/common')
          const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.incentive_type }, fields.connection)
          const descType = descObj.code_desc ? descObj.code_desc : ''
          if (tds > 0) {
            subAmount = subAmount - tds
            const tdsInsert = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              // description: util.IncentiveCommissionDescription + 'AEPS',
              description: (util.IncentiveCommissionDescriptionNew).replace('#type#', descType),
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: fields.connection
            }
            )
            if (tdsInsert.status === 400) {
              return tdsInsert
            }
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, fields.connection)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: String(fields.incentive_type),
              orderid: fields.orderid,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: fields.connection
            })
            if (orderwiseEntry.status === 400) {
              return orderwiseEntry
            }
            // [END] Make entry in ma_orderwise_taxes table
          }
        }
        if (subAmount > 0) {
          const globalIncentiveType = await monthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: fields.connection })
          const common = require('../../util/common')
          const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.incentive_type }, fields.connection)
          const descType = descObj.code_desc ? descObj.code_desc : ''
          if (globalIncentiveType.incentiveType == 'D') {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: fields.incentive_type,
              // description: util.IncentiveEarnedMerchant + 'AEPS',
              description: 'Credit - ' + descType,
              ma_status: 'S',
              orderid: fields.orderid,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }
          } else {
            const pointsLedgerCredits = await pointsLedger.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: fields.incentive_type,
              // description: util.IncentiveEarnedMerchant + 'AEPS',
              description: 'Credit - ' + descType,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + fields.orderid,
              userid: fields.userid,
              corresponding_id: util.airpayUserId,
              connection: fields.connection
            })
            if (pointsLedgerCredits.status === 400) {
              return pointsLedgerCredits
            }

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: String(fields.incentive_type),
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id

            }
            const monthlyIncCreate = await monthlyIncentiveController.createEntry('_', data, fields.connection)
            if (monthlyIncCreate.status === 400) {
              return monthlyIncCreate
            }
          }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * Calculate amount based on applied_type and operative_amount
   */
  static async calculateIncentiveAmount (fields) {
    var requestFields = {}
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: 'aepsDistributionController.js', action: 'calculateIncentiveAmount', type: 'request', fields: requestFields })
    var customerCharges = fields.customer_charges
    if (fields.customer_charges_applied_type === '2') {
      customerCharges = fields.customer_charges * fields.amount / 100
    }
    var amount = 0
    if (fields.data.applied_type === '1') {
      amount = fields.data.amount
    } else if (fields.data.applied_type === '2') {
      if (fields.data.operative_amount === '1') { // % of transaction amount
        amount = fields.amount * fields.data.amount / 100
      } else if (fields.data.operative_amount === '2') { // % of customer charges
        amount = customerCharges * fields.data.amount / 100
      }
    }
    log.logger({ pagename: 'aepsDistributionController.js', action: 'calculateIncentiveAmount', type: 'responce', fields: amount })
    return amount
  }

  /**
   * Incentive entries for AEPS CW transaction
   */
  static async incentiveEntries (fields, userid) {
    fields.userid = userid
    var requestFields = {}
    // PAN DECRYPTION
    const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
    var decryptionKey = util[env].secondaryEncrptionKey 
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: 'aepsDistributionController.js', action: 'incentiveEntries', type: 'request', fields: requestFields })
    try {
      const distributeObj = []
      var totalIncentiveAmount = 0
      // PAN DECRYPTION
      // const userSql = `SELECT profileid,user_type,distributer_user_master_id,state, CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master where profileid =`
      // const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan FROM ma_user_master where profileid ='
      // const userDetails = await this.rawQuery(userSql + fields.ma_user_id, fields.connection)
      const userSql = `SELECT profileid,user_type,distributer_user_master_id,state, CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master where profileid =${fields.ma_user_id} and userid = ${userid} limit 1 `
      const userDetails = await this.rawQuery(userSql, fields.connection)
      log.logger({ pagename: 'aepsDistributionController.js', action: 'incentiveEntries', type: 'response - RT', fields: userDetails })
      console.log('sql++++++++++++++++++**********', `SELECT profileid,user_type,distributer_user_master_id,state, CAST(AES_DECRYPT(pan,'${decryptionKey.replace(/./g, '*')}') AS CHAR) as pan FROM ma_user_master where profileid = ${fields.ma_user_id}`)	
      const userDetailsLog = userDetails.map(e => common_fns.maskValue(e, 'pan'))	
      console.log('response++++++++++++*****************', userDetailsLog)
      if (userDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' Invalid user' }
      }

      fields.state_master_id = userDetails[0].state
      // distributerIdForGetDistribution for commonFunction.checkOtherCommonConfigurationV2() function
      fields.distributerIdForGetDistribution = userDetails[0].distributer_user_master_id
      const configuration = await this.getDistribution(fields)
      // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
      delete fields.distributerIdForGetDistribution
      if (configuration.status === 400) {
        return configuration
      }
      // Customer Charges
      fields.customer_charges = configuration[0].customer_charges
      fields.customer_charges_applied_type = configuration[0].customer_charges_applied_type

      // Push RT details
      if (configuration[0].rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        fields.data = {
          amount: configuration[0].rt_share,
          applied_type: configuration[0].rt_share_applied_type,
          operative_amount: configuration[0].rt_operative_amount,
          user_type: 'RT'
        }
        const rt_share = await this.calculateIncentiveAmount(fields)
        totalIncentiveAmount += rt_share
        distributeObj.push({
          ma_user_id: fields.ma_user_id,
          amount: rt_share,
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, fields.connection)
        log.logger({ pagename: 'aepsDistributionController.js', action: 'incentiveEntries', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (configuration[0].dt_share > 0) {
              var TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              fields.data = {
                amount: configuration[0].dt_share,
                applied_type: configuration[0].dt_share_applied_type,
                operative_amount: configuration[0].dt_operative_amount,
                user_type: 'DT'
              }
              const dt_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += dt_share
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const userSql = `SELECT profileid,user_type,distributer_user_master_id,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,gst_number FROM ma_user_master where profileid =`
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', fields.connection)
              log.logger({ pagename: 'aepsDistributionController.js', action: 'incentiveEntries', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (configuration[0].sd_share > 0) {
                  var TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  fields.data = {
                    amount: configuration[0].sd_share,
                    applied_type: configuration[0].sd_share_applied_type,
                    operative_amount: configuration[0].sd_operative_amount,
                    user_type: 'SDT'
                  }
                  const sd_share = await this.calculateIncentiveAmount(fields)
                  totalIncentiveAmount += sd_share
                  distributeObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (configuration[0].sd_share > 0) {
              var TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              fields.data = {
                amount: configuration[0].sd_share,
                applied_type: configuration[0].sd_share_applied_type,
                operative_amount: configuration[0].sd_operative_amount,
                user_type: 'SDT'
              }
              const sd_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += sd_share
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }

      // Customer charge calculate
      if (fields.customer_charges_applied_type == 2) {
        fields.customer_charges = fields.amount * fields.customer_charges / 100
      }

      // Incentive type
      fields.incentive_type = 8
      if (fields.transaction_type == 40) {
        fields.incentive_type = 41
      }
      if (fields.transaction_type == 42) {
        fields.incentive_type = 43
      }

      // Airpay Share array
      /* fields.data = {
        amount: configuration[0].ap_share,
        applied_type: configuration[0].ap_applied_type,
        operative_amount: configuration[0].ap_operative_amount,
        user_type: 'AP'
      }
      const ap_share = await this.calculateIncentiveAmount(fields)
      totalIncentiveAmount += ap_share */
      if (fields.customer_charges - totalIncentiveAmount > 0) {
        distributeObj.push({
          ma_user_id: util.airpayCommissionId,
          amount: fields.customer_charges - totalIncentiveAmount
        })
      }

      // For Cash Account credit to Airpay
      const cashEntry = await cashAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.customer_charges,
        transaction_type: 'IN',
        ma_status: 'S',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })

      if (cashEntry.status === undefined || cashEntry.status !== 200) {
        return cashEntry
      }
      // For Points Account Credit from Airpay Account
      const pointsEntry = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.customer_charges,
        transaction_type: 'IN',
        ma_status: 'S',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsEntry.status === undefined || pointsEntry.status !== 200) {
        return pointsEntry
      }

      // Airpay Debit points ledger enntry
      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.incentive_type }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      const pointsLedgerAP = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.customer_charges,
        mode: 'dr',
        transaction_type: fields.incentive_type, // AEPS Incentive type
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        ma_status: 'S',
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsLedgerAP.status === 400) {
        return pointsLedgerAP
      }

      // Debit Entries for all shares
      fields.distributeObj = distributeObj
      const allShares = await this.distributionEntries(fields)
      if (allShares.status === 400) {
        return allShares
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'incentiveEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getDistributionAllHeads (fields, connection) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const distributeObj = []
      let totalIncentiveAmount = 0

      const userSql = 'SELECT profileid,user_type,distributer_user_master_id,state,pan FROM ma_user_master where profileid ='
      const userDetails = await this.rawQuery(userSql + fields.ma_user_id, conn)

      /* set connection for next request */
      fields.connection = conn
      const configuration = await this.getDistribution(fields)
      delete fields.distributerIdForGetDistribution
      if (configuration.status != 200) {
        return configuration
      }

      // Customer Charges
      fields.customer_charges = configuration[0].customer_charges
      fields.customer_charges_applied_type = configuration[0].customer_charges_applied_type

      distributeObj.push({ transaction_charges: fields.customer_charges })

      // Push RT details
      if (configuration[0].rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(userDetails[0].pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        fields.data = {
          amount: configuration[0].rt_share,
          applied_type: configuration[0].rt_share_applied_type,
          operative_amount: configuration[0].rt_operative_amount,
          user_type: 'RT'
        }
        const rt_share = await this.calculateIncentiveAmount(fields)
        totalIncentiveAmount += rt_share
        distributeObj.push({
          ma_user_id: fields.ma_user_id,
          amount: rt_share,
          user_type: 'RT',
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (userDetails[0].distributer_user_master_id > 0) {
        const distribuetrDetails = await this.rawQuery(userSql + userDetails[0].distributer_user_master_id, fields.connection)
        log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistributionAllHeads', type: 'response - DT', fields: distribuetrDetails })
        if (distribuetrDetails.length > 0) {
          // IF Distributer
          if (distribuetrDetails[0].user_type === 'DT') {
            // Push DT details
            if (configuration[0].dt_share > 0) {
              let TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              fields.data = {
                amount: configuration[0].dt_share,
                applied_type: configuration[0].dt_share_applied_type,
                operative_amount: configuration[0].dt_operative_amount,
                user_type: 'DT'
              }
              const dt_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += dt_share
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: dt_share,
                user_type: 'DT',
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distribuetrDetails[0].distributer_user_master_id > 0) {
              const superdistribuetrDetails = await this.rawQuery(userSql + distribuetrDetails[0].distributer_user_master_id + ' and user_type=\'SDT\'', fields.connection)
              log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistributionAllHeads', type: 'response - SDT', fields: superdistribuetrDetails })
              if (superdistribuetrDetails.length > 0) {
                // Push SDT details
                if (configuration[0].sd_share > 0) {
                  let TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superdistribuetrDetails[0].pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  fields.data = {
                    amount: configuration[0].sd_share,
                    applied_type: configuration[0].sd_share_applied_type,
                    operative_amount: configuration[0].sd_operative_amount,
                    user_type: 'SDT'
                  }
                  const sd_share = await this.calculateIncentiveAmount(fields)
                  totalIncentiveAmount += sd_share
                  distributeObj.push({
                    ma_user_id: superdistribuetrDetails[0].profileid,
                    amount: sd_share,
                    user_type: 'SDT',
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distribuetrDetails[0].user_type === 'SDT') {
            if (configuration[0].sd_share > 0) {
              let TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distribuetrDetails[0].pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              fields.data = {
                amount: configuration[0].sd_share,
                applied_type: configuration[0].sd_share_applied_type,
                operative_amount: configuration[0].sd_operative_amount,
                user_type: 'SDT'
              }
              const sd_share = await this.calculateIncentiveAmount(fields)
              totalIncentiveAmount += sd_share
              distributeObj.push({
                ma_user_id: distribuetrDetails[0].profileid,
                amount: sd_share,
                user_type: 'SDT',
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }

      // Customer charge calculate
      if (fields.customer_charges_applied_type == 2) {
        fields.customer_charges = fields.amount * fields.customer_charges / 100
      }

      if (fields.customer_charges - totalIncentiveAmount > 0) {
        distributeObj.push({
          ma_user_id: util.airpayCommissionId,
          amount: fields.customer_charges - totalIncentiveAmount,
          user_type: 'SD'
        })
      }
      log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistributionAllHeads - final', type: 'distributeObj', fields: distributeObj })

      return distributeObj
    } catch (error) {
      log.logger({ pagename: 'aepsDistributionController.js', action: 'getDistributionAllHeads', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = aepsDistribution
