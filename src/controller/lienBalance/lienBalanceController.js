const DAO = require('../../lib/dao')
const log = require('../../util/log')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const BalanceController = require('../balance/balanceController')

class LienBalanceController extends DAO {
  /**
   *
   * @param {{ma_user_id:Number,userid:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async isLienBalanceApplicable ({ ma_user_id, userid, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isLienBalanceApplicable', type: 'request', fields: { ma_user_id, userid } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isLienBalanceApplicableQuery = `SELECT SUM(amount) as amount FROM ma_lien_risk_reserve WHERE ma_user_id = ${ma_user_id} AND activity = 'A'`
      const isLienBalanceApplicableResult = await this.rawQuery(isLienBalanceApplicableQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'isLienBalanceApplicableResult', type: 'result', fields: isLienBalanceApplicableResult })

      if (isLienBalanceApplicableResult.length > 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, isLienBalanceApplicable: true, lienAmount: isLienBalanceApplicableResult[0].amount }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, isLienBalanceApplicable: false, lienAmount: 0 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isLienBalanceApplicable', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:Number,userid:Number,lienAmount:Number,deductAmount:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async isTransactionBalanceAboveLienBalance ({ ma_user_id, userid, lienAmount, deductAmount, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionBalanceAboveLienBalance', type: 'request', fields: { ma_user_id, userid, deductAmount, lienAmount } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* GET WALLET BALANCE */
      const availableBalanceResp = await BalanceController.getWalletBalancesDirect('_', {
        ma_user_id: ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection: connRead
      })
      if (availableBalanceResp.status != 200) return availableBalanceResp

      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalancesDirect', type: 'result', fields: availableBalanceResp })

      const availableBalanceAfterLienAmount = parseFloat(availableBalanceResp.amount) - parseFloat(lienAmount)

      log.logger({ pagename: require('path').basename(__filename), action: 'availableBalanceAfterLienAmount', type: 'result', fields: availableBalanceAfterLienAmount })
      /* CHECK THE AMOUNT IS GREATER THAN LIEN BALANCE */
      if (deductAmount > availableBalanceAfterLienAmount) return { status: 400, message: `${errorMsg.responseCode[1028]}: You are not allowed to do the transactions for this amount, kindly contact your ASM or customer support immediately`, respcode: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionBalanceAboveLienBalance', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:Number,userid:Number,lienAmount:Number,deductAmount:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async isWithdrawalBalanceAboveLienBalance ({ ma_user_id, userid, lienAmount, deductAmount, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isWithdrawalBalanceAboveLienBalance', type: 'request', fields: { ma_user_id, userid, deductAmount, lienAmount } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* GET WALLET BALANCE */
      const availableBalanceResp = await BalanceController.getWalletBalances('_', {
        ma_user_id: ma_user_id,
        userid: userid,
        balance_flag: 'SUMMARY'
      })
      if (availableBalanceResp.status != 200) return availableBalanceResp

      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalancesDirect', type: 'result', fields: availableBalanceResp })

      const availableBalanceAfterLienAmount = parseFloat(availableBalanceResp.amount) - parseFloat(lienAmount)

      log.logger({ pagename: require('path').basename(__filename), action: 'availableBalanceAfterLienAmount', type: 'result', fields: availableBalanceAfterLienAmount })
      /* CHECK THE AMOUNT IS GREATER THAN LIEN BALANCE */
      if (deductAmount > availableBalanceAfterLienAmount) return { status: 400, message: `${errorMsg.responseCode[1028]}: You are not allowed to do the transactions for this amount, kindly contact your ASM or customer support immediately`, respcode: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isWithdrawalBalanceAboveLienBalance', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:Number,userid:Number,transactionType:Number,transactionAmount:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async isTransactionAmountAboveLienBalance ({ ma_user_id, userid, transactionType, transactionAmount, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionAmountAboveLienBalance', type: 'request', fields: { ma_user_id, userid, transactionAmount, transactionType } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* SKIP TRANSACTION TYPE */
      transactionType = transactionType.toString()
      /* INGORE HATM,POS,ADHOC,COLLECT MONEY & AEPS (CW, BE & MS) */
      if (['20', '23', '27', '10', '5', '1', '40', '42', '67', '68', '69'].includes(transactionType)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
      /* IS LEIN BALANCE APPLICABLE */
      const isLienBalanceApplicableResp = await this.isLienBalanceApplicable({ ma_user_id, userid, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'isLienBalanceApplicable', type: 'result', fields: isLienBalanceApplicableResp })
      if (isLienBalanceApplicableResp.status != 200) return isLienBalanceApplicableResp
      if (isLienBalanceApplicableResp.status == 200 && !isLienBalanceApplicableResp.isLienBalanceApplicable) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      /* BALANCE CHECK */
      const isBalanceAboveLienBalanceResp = await this.isTransactionBalanceAboveLienBalance({
        ma_user_id,
        userid,
        lienAmount: isLienBalanceApplicableResp.lienAmount,
        deductAmount: transactionAmount,
        connectionRead: connRead
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'isBalanceAboveLienBalance', type: 'result', fields: isBalanceAboveLienBalanceResp })
      if (isBalanceAboveLienBalanceResp.status != 200) return isBalanceAboveLienBalanceResp

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionAmountAboveLienBalance', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:Number,userid:Number,withdrawalAmount:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async isWithdrawalAmountAboveLienBalance ({ ma_user_id, userid, withdrawalAmount, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isWithdrawalAmountAboveLienBalance', type: 'request', fields: { ma_user_id, userid, withdrawalAmount } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* IS LEIN BALANCE APPLICABLE */
      const isLienBalanceApplicableResp = await this.isLienBalanceApplicable({ ma_user_id, userid, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'isLienBalanceApplicable', type: 'result', fields: isLienBalanceApplicableResp })
      if (isLienBalanceApplicableResp.status != 200) return isLienBalanceApplicableResp
      if (isLienBalanceApplicableResp.status == 200 && !isLienBalanceApplicableResp.isLienBalanceApplicable) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      /* BALANCE CHECK */
      const isBalanceAboveLienBalanceResp = await this.isWithdrawalBalanceAboveLienBalance({
        ma_user_id,
        userid,
        lienAmount: isLienBalanceApplicableResp.lienAmount,
        deductAmount: withdrawalAmount || 0,
        connectionRead: connRead
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'isBalanceAboveLienBalance', type: 'result', fields: isBalanceAboveLienBalanceResp })

      if (isBalanceAboveLienBalanceResp.status != 200) return isBalanceAboveLienBalanceResp

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isWithdrawalAmountAboveLienBalance', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:Number,userid:Number,transactionAmount:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async reverseCreditLienBalanceCheck ({ ma_user_id, userid, transactionAmount, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'reverseCreditLienBalanceCheck', type: 'request', fields: { ma_user_id, userid, transactionAmount } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* IS LEIN BALANCE APPLICABLE */
      const isLienBalanceApplicableResp = await this.isLienBalanceApplicable({ ma_user_id, userid, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'isLienBalanceApplicable', type: 'result', fields: isLienBalanceApplicableResp })
      if (isLienBalanceApplicableResp.status != 200) return isLienBalanceApplicableResp
      if (isLienBalanceApplicableResp.status == 200 && !isLienBalanceApplicableResp.isLienBalanceApplicable) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      /* BALANCE CHECK */
      const isBalanceAboveLienBalanceResp = await this.isTransactionBalanceAboveLienBalance({
        ma_user_id,
        userid,
        lienAmount: isLienBalanceApplicableResp.lienAmount,
        deductAmount: transactionAmount,
        connectionRead: connRead
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'reverseCreditLienBalanceCheck', type: 'result', fields: isBalanceAboveLienBalanceResp })
      if (isBalanceAboveLienBalanceResp.status != 200) return isBalanceAboveLienBalanceResp

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'reverseCreditLienBalanceCheck', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }
}

module.exports = LienBalanceController
