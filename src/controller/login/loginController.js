/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const path = require('path')
/* NEW CHANGES : MERCHANT ONBOARDING CHANGES */
class Login extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_user_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_master_id'
  }

  static async tokenValidation (token, userid) {
    //   this.TABLE_NAME = 'ma_user_master'
    // console.trace()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('Beforeusertoken>>', token, userid)
      //    const usertoken = await this.findMatching('', { userid: userid, jwtkey: token })
      const sql = `Select * from ma_user_authentication where user_id = ${userid} AND jwt_token = '${token}'`
      console.log('query', sql)
      // console.log('connection', connection)
      const usertoken = await this.rawQuery(sql, connection)
      console.log('usertoken>>', usertoken)
      if (usertoken.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else {
        return { status: 400, respcode: 1009, message: errorMsg.responseCode[1009] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'tokenValidation', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async logout (token, userid) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = 'Update ma_user_authentication SET jwt_token = "" WHERE user_id = ? AND BINARY jwt_token = ?'
      const result = await this.secureRawQuery(sql, { connection, params: [userid, token] })
      log.logger({ pagename: path.basename(__filename), action: 'logout', type: 'result', fields: result })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], affectedRows: result.affectedRows }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'logout', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, affectedRows: 0 }
    } finally {
      connection.release()
    }
  }

}

module.exports = Login
