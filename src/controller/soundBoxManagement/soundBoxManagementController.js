const { default: Axios } = require('axios')

const DAO = require('../../lib/dao')
const log = require('../../util/log')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const { soundBoxEncrypt, soundBoxDecrypt, soundBoxChecksum, queryObject } = require('../../util/common')
const util = require('../../util/util')
const xml2js = require('fast-xml-parser')
const j2xml = require('js2xmlparser')
const FormData = require('form-data')
var moment = require('moment-timezone')
const moment1 = require('moment')
const TransactionController = require('../transaction/transactionController')
// const { select } = require('async')
const sms = require('../../util/sms')

class SoundBoxManagementController extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  static get TABLE_NAME () {
    return 'ma_soundbox_details_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_soundbox_details_master_id'
  }

  /**
   * Register SOUNDBOX DEVICE
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async registerDevice (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'registerDevice', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* GENERATE VPA */
      const generateQR = await TransactionController.getQrCodeForUpi(_, fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'getQrCodeForUpi', fields: generateQR })
      if (generateQR.status != 200) return generateQR
      const { barcode_string } = generateQR
      const queryObjectResp = queryObject(barcode_string)
      if (!queryObjectResp['upi://pay?pa']) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      fields.vpa_id = queryObjectResp['upi://pay?pa']
      /* VERFIY REQUEST */
      const validateFieldResp = await this.validateRegisteredFields({ fields, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'validateFields', type: 'response', fields: validateFieldResp })
      if (validateFieldResp.status != 200) return { ...validateFieldResp, action_code: 1001 }
      /* VERIFY VPA  */
      /* const isVPAVerifiedResp = await this.verifyVPA({ request: fields, connection, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'response', fields: isVPAVerifiedResp })
      if (isVPAVerifiedResp.status != 200) return { ...isVPAVerifiedResp, action_code: 1001 } */
      /* REGISTERED SOUNDBOX */
      const request = {
        ACTION: 'LINK',
        PROFILEID: fields.ma_user_id.toString(),
        DEVICEID: fields.device_id
      }
      const registerDeviceResp = await this.callSoundBoxAPI({ request, action: 'DEVICE' })
      log.logger({ pagename: require('path').basename(__filename), action: 'registerDeviceResp', type: 'response', fields: registerDeviceResp })
      if (registerDeviceResp.status != 200) return { ...registerDeviceResp, action_code: 1001 }
      /* INSERT NEW DB ENTRIES */
      if (Object.keys(validateFieldResp.data).length > 0) {
        const id = validateFieldResp.data.ma_soundbox_details_master_id
        const data = {
          device_status: 'Y',
          vpa_id: fields.vpa_id,
          api_request: JSON.stringify(request),
          api_response: JSON.stringify(registerDeviceResp.resp),
          transaction_types: 10
        }
        const updateResp = await this.updateWhere(connection, { data, id, where: 'ma_soundbox_details_master_id' })
        if (updateResp.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        log.logger({ pagename: require('path').basename(__filename), action: 'update record', type: 'response', fields: updateResp })
      }

      if (Object.keys(validateFieldResp.data).length == 0) {
        const data = {
          ma_user_id: fields.ma_user_id,
          vpa_id: fields.vpa_id,
          device_id: fields.device_id,
          device_status: 'Y',
          api_request: JSON.stringify(request),
          api_response: JSON.stringify(registerDeviceResp.resp),
          transaction_types: 10
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'insert new record', type: 'data', fields: data })
        const insertResp = await this.insert(connection, { data })
        log.logger({ pagename: require('path').basename(__filename), action: 'insert new record', type: 'response', fields: insertResp })
        if (!insertResp.insertId) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
      return { status: 200, message: 'Successfully registered soundbox device', respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'registerDevice', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async validateRegisteredFields ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateRegisteredFields', type: 'request', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* MERCHANT ID VALIDATION */
      const isMerchantExistsResp = await this.isMerchantExists({ ma_user_id: fields.ma_user_id, userid: fields.userid, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'isMerchantExistsResp', type: 'result', fields: isMerchantExistsResp })
      if (isMerchantExistsResp.status != 200) return isMerchantExistsResp

      /* VPA ID VALIDATION */
      const VPAResult = await this.findByFields({
        fields: { vpa_id: fields.vpa_id },
        limit: 1
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'VPAResult', type: 'error', fields: VPAResult })

      if (VPAResult.length > 0 && VPAResult[0].device_status == 'Y') return { status: 400, message: 'This VPA ID is not registered on your MID.', respcode: 1028 }

      /* DEVICE VALIDATION */
      const deviceResult = await this.findByFields({
        fields: { ma_user_id: fields.ma_user_id, device_id: fields.device_id },
        limit: 1
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'deviceResult', type: 'error', fields: deviceResult })

      if (deviceResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, data: {} }
      const deviceData = deviceResult[0]

      if (deviceData.device_status == 'Y') return { status: 400, message: 'This device is already registered on your MID', respcode: 1028 }

      if (deviceData.device_status == 'N') return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: deviceData }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateRegisteredFields', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   * CALL MS API AND VERIFY VPA ID
   * @param {{request:Object,payLoad:Object}} param0
   * @returns
   */
  static async verifyVPA ({ request }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'request', fields: request })
    try {
      if (!('vpa_id' in request)) return { status: 400, message: `${errorMsg.responseCode[1028]}: vpa_id field missing`, respcode: 1001 }
      /* KEYS */
      const env = util[process.env.NODE_ENV || 'development']
      const url = `${env.soundBoxVerifyVPAURL}/${env.soundBoxendpoint.VERIFY_VPA}`
      const payLoad = {
        MerchantId: request.ma_user_id,
        MerchantVpa: request.vpa_id
      }
      const data = this.verifyVPAJSONXMLRequest({ wrapWith: 'XML', payLoad })
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'data', fields: data })
      if (!data) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      const config = {
        method: 'post',
        url,
        headers: { 'Content-Type': 'application/xml' },
        data
      }
      const requestStart = process.hrtime()
      console.time('TIMER_VERIFY_VPA')
      const resp = await Axios.request(config)
      console.timeEnd('TIMER_VERIFY_VPA')
      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000
        console.log('API_TIMER_VERIFY_VPA_REQUEST_[' + JSON.stringify(payLoad) + ']_RESPONSE_[' + JSON.stringify(resp.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }
      const respJson = this.verifyVPAXMLJSONResponse({ data: resp.data.trim('\n') })
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'respJson', fields: respJson })
      if (!respJson) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (!('XML' in respJson)) return { status: 400, message: `${errorMsg.responseCode[1028]}: API Error`, respcode: 1001 }
      const ActCode = Number(respJson.XML.ActCode)
      if (ActCode == 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      if (ActCode == 1) return { status: 400, message: 'This VPA ID is not registered on your MID.', respcode: 1001 }
      if (ActCode == 2) return { status: 400, message: 'You have entered an invalid VPA ID. Please try again.', respcode: 1001 }
      if ([1, 2].includes(ActCode)) return { status: 400, message: `${respJson.XML.Message || 'API Error'}`, respcode: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   *
   * @param {{wrapWith:string,payLoad:Object}} param0
   * @returns
   */
  static verifyVPAJSONXMLRequest ({ wrapWith, payLoad }) {
    try {
      return j2xml.parse(wrapWith, payLoad)
    } catch (error) {
      return false
    }
  }

  /**
   *
   * @param {{data:string}} param0
   * @returns
   */
  static verifyVPAXMLJSONResponse ({ data }) {
    try {
      return xml2js.parse(data, { parseTrueNumberOnly: true }, true)
    } catch (error) {
      return false
    }
  }

  /**
   * CALL MA API AND REGISTER SOUNDBOX
   * @param {{request:Object,action:DEVICE|NOTIFY}} param0
   * @returns
   */
  static async callSoundBoxAPI ({ request, action }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'callSoundBoxAPI', type: 'request', fields: { request, action } })
    try {
      /* KEYS */
      const env = util[process.env.NODE_ENV || 'development']
      /* ENCRYPT REQUEST */
      const encryptRequest = soundBoxEncrypt({ plainText: JSON.stringify(request), key: env.soundBoxAirpaykey })
      log.logger({ pagename: require('path').basename(__filename), action: 'encryptRequest', type: 'string', fields: encryptRequest })
      if (!encryptRequest) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      /* GENERATE CHECKSUM */
      const checkSum = soundBoxChecksum({ paramArr: Object.values(request), key: env.soundBoxAirpaykey })
      log.logger({ pagename: require('path').basename(__filename), action: 'checkSum', type: 'string', fields: checkSum })
      if (!checkSum) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      /* API CALL */
      const config = {
        method: 'post',
        url: `${env.soundBoxBaseURL}/${env.soundBoxendpoint[action]}`,
        headers: {
          'Content-Type': 'application/json',
          AFFILIATE: env.soundBoxAffiliate,
          AIRPAYKEY: env.soundBoxAirpaykey,
          CHECKSUM: checkSum
        },
        data: JSON.stringify({ query: encryptRequest })
      }
      const requestStart = process.hrtime()
      console.time('TIMER_SOUNDBOX')
      const response = await Axios.request(config)
      console.timeEnd('TIMER_SOUNDBOX')
      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000
        console.log('API_TIMER_SOUNDBOX[' + JSON.stringify(config) + ']_RESPONSE_[' + JSON.stringify(response.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'SoundBox Response', type: 'response', fields: response })
      /* DECRYPT RESPONSE */
      const decryptResponse = soundBoxDecrypt({ encryptedText: response.data.result, key: env.soundBoxAirpaykey })
      log.logger({ pagename: require('path').basename(__filename), action: 'decryptResponse', type: 'string', fields: decryptResponse })
      if (!decryptResponse) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      const apiResponse = JSON.parse(decryptResponse)
      if (apiResponse.status != 200) return { status: 400, message: apiResponse.message.replace('PROFILEID', 'MID') || errorMsg.responseCode[1001], respcode: 1001 }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, resp: apiResponse }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'callSoundBoxAPI', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * UnRegister SOUNDBOX DEVICE
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async unregisterDevice (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'unregisterDevice', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* VERFIY REQUEST */
      const validateFieldResp = await this.validateUnRegisteredFields({ fields, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'validateFields', type: 'response', fields: validateFieldResp })
      if (validateFieldResp.status != 200) return { ...validateFieldResp, action_code: 1001 }
      /* UNREGISTERED SOUNDBOX */
      const request = {
        ACTION: 'UNLINK',
        PROFILEID: fields.ma_user_id.toString(),
        DEVICEID: fields.device_id
      }
      const registerDeviceResp = await this.callSoundBoxAPI({ request, action: 'DEVICE' })
      log.logger({ pagename: require('path').basename(__filename), action: 'registerDeviceResp', type: 'response', fields: registerDeviceResp })
      if (registerDeviceResp.status != 200) return { ...registerDeviceResp, action_code: 1001 }
      /* UPDATED DB ENTRIES */
      const id = validateFieldResp.data.ma_soundbox_details_master_id
      const data = {
        device_status: 'N',
        api_request: JSON.stringify(request),
        api_response: JSON.stringify(registerDeviceResp.resp)
      }
      const updateResp = await this.updateWhere(connection, { data, id, where: 'ma_soundbox_details_master_id' })
      log.logger({ pagename: require('path').basename(__filename), action: 'update record', type: 'response', fields: updateResp })
      if (updateResp.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      return { status: 200, message: 'Successfully deregistered soundbox device', respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'unregisterDevice', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async validateUnRegisteredFields ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateRegisteredFields', type: 'request', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isMerchantExistsResp = await this.isMerchantExists({ ma_user_id: fields.ma_user_id, userid: fields.userid, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'isMerchantExistsResp', type: 'result', fields: isMerchantExistsResp })
      if (isMerchantExistsResp.status != 200) return isMerchantExistsResp

      const deviceResult = await this.findByFields({
        fields: { ma_user_id: fields.ma_user_id, device_id: fields.device_id },
        limit: 1,
        order: {
          by: 'updatedon',
          direction: 'desc'
        }
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'deviceResult', type: 'result', fields: deviceResult })

      if (deviceResult.length == 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }

      const deviceData = deviceResult[0]

      if (deviceData.device_status == 'N') return { status: 400, message: 'This device is already unregistered', respcode: 1028 }

      if (deviceData.device_status == 'Y') return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: deviceData }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateRegisteredFields', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   * Get the list Registered SOUNDBOX DEVICE
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async getDeviceList (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getDeviceList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const deviceDetailQuery = `SELECT vpa_id,device_id,device_status FROM ma_soundbox_details_master WHERE ma_user_id=${fields.ma_user_id} AND device_status='Y' LIMIT 1`
      const deviceDetailResult = await this.rawQuery(deviceDetailQuery, connectionRead)

      if (deviceDetailResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1000 }

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        action_code: 1000,
        device_detail: { ...deviceDetailResult[0] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDeviceList', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connectionRead.release()
    }
  }

  /**
   * SOUNDBOX Notification
   * @param {{ma_user_id:Number, txnType:String, orderid:string,amount:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async sendSoundBoxNotification ({ ma_user_id, txnType, orderid, amount, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'request', fields: { ma_user_id, txnType, amount, orderid } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const deviceDetailQuery = `SELECT vpa_id,device_id,device_status FROM ma_soundbox_details_master WHERE ma_user_id=${ma_user_id} AND device_status='Y' LIMIT 1`
      const deviceDetailResult = await this.rawQuery(deviceDetailQuery, connRead)

      if (deviceDetailResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }
      const fetchTransactionIDQuery = `SELECT ma_transaction_master_id FROM ma_transaction_master WHERE aggregator_order_id='${orderid}' LIMIT 1`
      const fetchTransactionIDResult = await this.rawQuery(fetchTransactionIDQuery, connRead)

      if (fetchTransactionIDResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      const request = {
        PROFILEID: ma_user_id.toString(),
        AMOUNT: amount,
        TXNID: fetchTransactionIDResult[0].ma_transaction_master_id
      }

      /* NEW SOUNDBOX CHANGES */
      if (txnType == 'UPI') {
        request.TXNTYPE = 1
        request.TXNMODE = 1
      }
      if (txnType == 'POS_HATM') {
        request.TXNTYPE = 2
        request.TXNMODE = 2
      }
      if (txnType == 'AEPS') {
        request.TXNTYPE = 2
        request.TXNMODE = 2
      }
      if (txnType == 'CMS_PEN') {
        request.TXNTYPE = '5'
        request.TXNMODE = '5'
      }
      if (txnType == 'CMS_SUC') {
        request.TXNTYPE = '3'
        request.TXNMODE = '3'
      }
      if (txnType == 'DMT_PEN') {
        request.TXNTYPE = '5'
        request.TXNMODE = '5'
      }
      if (txnType == 'DMT_SUCC') {
        request.TXNTYPE = 3
        request.TXNMODE = 3
      }
      if (txnType == 'RECHARGE_BBPS_PEN') {
        request.TXNTYPE = '5'
        request.TXNMODE = '5'
      }
      if (txnType == 'RECHARGE_BBPS_SUC') {
        request.TXNTYPE = '4'
        request.TXNMODE = '4'
      }
      if (txnType == 'BBPS_PEN') {
        request.TXNTYPE = '5'
        request.TXNMODE = '5'
      }
      if (txnType == 'BBPS_SUC') {
        request.TXNTYPE = '4'
        request.TXNMODE = '4'
      }
      if (txnType == 'ADPAY') {
        request.TXNTYPE = '1'
        request.TXNMODE = '1'
      }
      if (txnType == 'NMT_PEN') {
        request.TXNTYPE = '5'
        request.TXNMODE = '5'
      }
      if (txnType == 'NMT_SUC') {
        request.TXNTYPE = '3'
        request.TXNMODE = '3'
      }

      const registerDeviceResp = await this.callSoundBoxAPI({ request, action: 'NOTIFY' })
      log.logger({ pagename: require('path').basename(__filename), action: 'registerDeviceResp', type: 'response', fields: registerDeviceResp })
      if (registerDeviceResp.status != 200) return registerDeviceResp
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyVPA', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async isMerchantExists ({ ma_user_id, userid, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateRegisteredFields', type: 'request', fields: { ma_user_id, userid } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const merchantExistQuery = `SELECT username FROM users WHERE profileid=${ma_user_id} AND id=${userid} AND status=2`
      const merchantExistResult = await this.rawQuery(merchantExistQuery, connRead)

      if (merchantExistResult.length == 0) return { status: 400, message: errorMsg.responseCode[1137], respcode: 1137 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateRegisteredFields', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   * Get the list SOUNDBOX QR Download Pdf link
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async getSoundBoxQrCodeForUpi (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSoundBoxQrCodeForUpi', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()
    try {
      const deviceDetailQuery = `SELECT vpa_id,device_id,device_status FROM ma_soundbox_details_master WHERE ma_user_id=${fields.ma_user_id} and device_status = 'Y' LIMIT 1`
      const deviceDetailResult = await this.rawQuery(deviceDetailQuery, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSoundBoxQrCodeForUpi', type: 'deviceDetailResult', fields: deviceDetailResult })
      if (deviceDetailResult.length == 0) return { status: 400, message: 'Soundbox device not registered or Activated Device not found for this merchant.', respcode: 1001, action_code: 1001 }

      /* if (deviceDetailResult[0].device_status == 'N') {
        return { status: 400, message: 'Activated Device not found for this merchant.', respcode: 1001, action_code: 1001 }
      } */

      // call admin API - to create CAF pdf, send the path in response
      let response = {}
      const data = new FormData()
      const crypto = require('crypto')
      const input = JSON.stringify({ vpa_id: deviceDetailResult[0].vpa_id })
      const encryptKey = !util.isProduction() ? 'OlorK9nZbeqBoG1oTJilbPl6S4sb5cyT' : 'N1C5dgRpifQ7Y2D6W0PjQW7y1R8ybtIh'
      const iv = Buffer.from('0123456789abcdef')
      /* AES Ciphering */
      const cipher = crypto.createCipheriv('aes-256-cbc', encryptKey, iv)
      let encrypted = cipher.update(input, 'utf-8', 'base64')
      encrypted += cipher.final('base64')
      const strData = iv + encrypted
      data.append('query', strData)
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: !util.isProduction() ? 'https://retaila.airpay.ninja/api/qr_generator' : 'https://retaila.airpay.co.in/api/qr_generator',
        headers: {
          source: 'd2Vi',
          ...data.getHeaders()
        },
        data: data
      }
      console.log('config data ===== ', data)
      response = await Axios(config)

      log.logger({ pagename: require('path').basename(__filename), action: 'getSoundBoxQrCodeForUpi', type: 'Soundbox QR API) - response', fields: response })

      if (response != '' && response.status === 200) {
        const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
        if (responseData.status == 200) {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], vpaQrDownloadLink: responseData.data.full_path, action_code: 1000 }
        } else {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'getSoundBoxQrCodeForUpi', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving QR pdf.', action_code: 1001 }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSoundBoxQrCodeForUpi', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connectionRead.release()
    }
  }

  static async checkTransactionTypeIsAllowed (ma_user_id, transactionType) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkTransactionTypeIsAllowed', type: 'request', fields: { ma_user_id, transactionType } })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()
    try {
      // check transaction type is allowed to a particular merchant or  not
      const getTransactionTypeDataSql = `Select transaction_types from ma_soundbox_details_master where ma_user_id =${ma_user_id} and device_status ='Y' limit 1 `
      log.logger({ pagename: require('path').basename(__filename), action: 'checkTransactionTypeIsAllowed', type: 'getTransactionTypeDataSql', fields: getTransactionTypeDataSql })
      const getTransactionTypeData = await this.rawQuery(getTransactionTypeDataSql, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkTransactionTypeIsAllowed', type: 'getTransactionTypeData', fields: getTransactionTypeData })
      const intTransactionType = typeof (transactionType) == 'string' ? parseInt(transactionType) : transactionType
      if (getTransactionTypeData.length > 0) {
        const transactionTypeValue = getTransactionTypeData[0].transaction_types
        if (transactionTypeValue) {
          const valuesArray = transactionTypeValue.split(',')
          console.log('valuesArray>>', valuesArray)
          const intvaluesArray = valuesArray.map(Number)
          console.log('intvaluesArray>>', intvaluesArray)
          const isPresent = intvaluesArray.includes(intTransactionType)
          console.log('isPresent>>', isPresent)
          return isPresent
        }
        return false
      }
      return false
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkTransactionTypeIsAllowed', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connectionRead.release()
    }
  }

  static async fetchSelectedTransactionType (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchSelectedTransactionType', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()
    try {
      // Retrieve selected txn type
      const selectedTransactionType = `SELECT transaction_types FROM ma_soundbox_details_master WHERE ma_user_id = ${fields.ma_user_id} AND device_status = 'Y' LIMIT 1`
      const selectedTransactionTypeResult = await this.rawQuery(selectedTransactionType, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchSelectedTransactionType', type: 'selected transaction type', fields: selectedTransactionTypeResult })
      // Retrieve txn type list
      const getTransactionTypeList = 'SELECT transaction_type, display_name FROM ma_soundbox_transaction_list WHERE status = "A"'
      const getTransactionTypeListResult = await this.rawQuery(getTransactionTypeList, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchSelectedTransactionType', type: 'transaction type list', fields: getTransactionTypeListResult })
      if (getTransactionTypeListResult.length > 0) {
        const selectedTypes = selectedTransactionTypeResult[0].transaction_types
        const selectedTypesArr = selectedTypes.split(',').map(Number)
        if (selectedTypesArr) {
          const data = getTransactionTypeListResult.map(item => {
            return {
              display_name: item.display_name,
              key: item.transaction_type.toString(),
              name: item.display_name,
              enabled: selectedTypesArr.includes(item.transaction_type) ? 'true' : 'false'
            }
          })
          data.sort((a, b) => a.display_name > b.display_name ? 1 : -1)
          return { status: 200, respcode: 1000, message: 'Data fetched successfully', selectedTransactionTypeData: data }
        }
      } else {
        return { status: 400, respcode: 1001, message: 'No records found', action_code: 1001 }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchSelectedTransactionType', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connectionRead.release()
    }
  }

  static async updateSelectedTransactionType (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateSelectedTransactionType', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()
    const data = fields.key
    console.log(data)
    try {
      if (data.length > 0) {
        const formattedData = data.split(',').map(item => item.trim()).join(',')
        const updateTransactionType = `UPDATE ma_soundbox_details_master SET transaction_types = '${formattedData}' , updated_by='${fields.userid}' WHERE ma_user_id='${fields.ma_user_id}' AND device_status = 'Y'`
        const processTransactionType = await this.rawQuery(updateTransactionType, connectionRead)
        log.logger({ pagename: require('path').basename(__filename), action: 'updateSelectedTransactionType', type: 'update transaction type', fields: processTransactionType })
        return { status: 200, respcode: 1000, message: 'Data updated successfully', action_code: 1000 }
      } else {
        return { status: 400, respcode: 1001, message: 'At least one Transaction type is required', action_code: 1001 }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateSelectedTransactionType', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connectionRead.release()
    }
  }

  static async simRenewalTransactionEntires (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalTransactionEntires', type: 'request', fields: fields })
    const validator = require('../../util/validator')
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'order_id', 'amount', 'userid'])

    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const transactionFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.order_id,
        transaction_id: fields.order_id,
        amount: fields.amount,
        recon_amount: 0,
        transaction_type: 81,
        transaction_status: 'S',
        remarks: fields.remark || 'Soundbox Sim Activation',
        commission_amount: fields.customer_charge || 0,
        mobile_number: fields.phone_number || '',
        customer_mobile: fields.phone_number || '',
        // cms_parent_partner_id: fields.category_id ||'',
        // cms_partner_id: fields.product_id || '',
        connection
      }

      const transactionResponse = await TransactionController.initiateTransaction('_', transactionFields)
      console.log('transactionResponse===', transactionResponse)
      if (transactionResponse.status != 200) {
        return transactionResponse
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalTransactionEntires', type: 'response', fields: transactionResponse })

      const ledgerEntries = require('../ledgerEntries/ledgerEntriesController')
      const poinsledgerResponse = await ledgerEntries.simcardActivationLedgerEntries(fields, connection)
      console.log('poinsledgerResponse>>>>', poinsledgerResponse)
      if (poinsledgerResponse.status != 200) {
        return poinsledgerResponse
      }

      return transactionResponse
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalTransactionEntires', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async checkSimActivationStatus (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkSimActivationStatus', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()
    try {
      const getSimActivationDetails = `select sim_number, status, expiration_date,renewal_date,operator from ma_soundbox_sim_activations  where ma_user_id = '${fields.ma_user_id}' AND user_id= '${fields.userid}' LIMIT 1`
      const processSimActivationStatus = await this.rawQuery(getSimActivationDetails, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkSimActivationStatus', type: 'select sim activation status', fields: processSimActivationStatus })
      let simRegister = 'Y'
      let formattedDate = ''
      let statusMessage
      let simRenewal = 'N'
      let dayRemaining = ''
      let extra_expiry_message = ''
      if (processSimActivationStatus.length > 0) {
        const expirationDate = moment(processSimActivationStatus[0].expiration_date)
        const endDate = moment(expirationDate).add(90, 'days').startOf('day')
        const today = moment().startOf('day')
        let daysLeft = endDate.diff(today, 'days')
        formattedDate = moment(processSimActivationStatus[0].expiration_date).format('DD-MM-YYYY')
        const sql = `select renewal_amount from ma_soundbox_sim_renewals where operator = '${processSimActivationStatus[0].operator}' limit 1`
       log.logger({ pagename: require('path').basename(__filename), action: 'checkSimActivationStatus', type: 'sql', fields: sql })
       const sqlResult =  await this.rawQuery(sql,connectionRead)
       log.logger({ pagename: require('path').basename(__filename), action: 'checkSimActivationStatus', type: 'sqlResult', fields: sqlResult })
      if (sqlResult.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      const amount = sqlResult[0].renewal_amount

        switch (processSimActivationStatus[0].status) {
          case 'A':
            statusMessage = 'Active'
            break
          case 'P':
            statusMessage = 'Pending'
            break
          case 'N':
            statusMessage = 'New'
            break
          case 'I':
            statusMessage = 'Inactive'
            break
          case 'R':
            statusMessage = 'Renew'
            break
          case 'S':
            statusMessage = 'Suspended'
            break
          default:
            statusMessage = 'Unknown Status'
            break
        }
        if (processSimActivationStatus[0].status == 'P') {
          return { status: 200, respcode: 1000, message: 'Your SIM Card will be activated within the next 24 hours. if your SIM is not activated after 24 hours,please contact Customer Support.', sim_number: processSimActivationStatus[0].sim_number, title: 'SIM Activation Request Sent Successfully! ', sim_status: statusMessage, amount }
        } else if (processSimActivationStatus[0].status == 'A') {
          const expirationDate = moment1(processSimActivationStatus[0].expiration_date).endOf('day')
          const today = moment1().startOf('day')
          const timeDifference = expirationDate - today
          const daysRemaining = Math.ceil(timeDifference / (1000 * 3600 * 24))
          log.logger({ pagename: require('path').basename(__filename), action: 'checkSimActivationStatus', type: 'select sim activation status', fields: { today, timeDifference, daysRemaining, expirationDate } })
          dayRemaining = ''
          if (daysRemaining >= 1 && daysRemaining <= 7) {
            simRenewal = 'Y'
            simRegister = 'N'
            if (daysRemaining === 1) {
              dayRemaining = 'Expires today.'
            } else {
              dayRemaining = `Expires in ${daysRemaining} days.`
            }
            extra_expiry_message = `₹${amount} will be deducted on renewal of SIM.`
          } else if (daysRemaining < 1) {
            simRenewal = 'Y'
            extra_expiry_message = `₹${amount} will be deducted on renewal of SIM.`
            dayRemaining = 'Your plan has already expired.'
          } else if (daysRemaining > 7) {
            simRenewal = 'N'
            return { status: 200, respcode: 1000, message: '', valid_until: formattedDate, sim_renewal: simRenewal, sim_number: processSimActivationStatus[0].sim_number, sim_register: simRegister, sim_status: statusMessage, amount }
          }
          return { status: 200, respcode: 1000, message: extra_expiry_message, valid_until: formattedDate, expiry_message: dayRemaining, sim_renewal: simRenewal, sim_number: processSimActivationStatus[0].sim_number, sim_register: simRegister, sim_status: statusMessage, amount }
        } else if (processSimActivationStatus[0].status == 'I') {
          if (daysLeft < 0) {
            daysLeft = 0
          }
          if (daysLeft > 0) {
            simRenewal = 'Y'
            simRegister = 'N'
            extra_expiry_message = `₹${amount} will be deducted on renewal of SIM.`
            return { status: 200, respcode: 1000, message: extra_expiry_message, sim_number: processSimActivationStatus[0].sim_number, sim_status: statusMessage, valid_until: formattedDate, sim_renewal: simRenewal, expiry_message: `Kindly renew Plan in the next ${daysLeft} days or SIM will be permanently deactivated.`, sim_register: simRegister }
          } else {
            simRenewal = 'N'
            simRegister = 'N'
            statusMessage = 'Suspended'
            return { status: 200, respcode: 1000, message: '', sim_number: processSimActivationStatus[0].sim_number, sim_status: statusMessage, valid_until: formattedDate, sim_renewal: simRenewal, expiry_message: 'SIM has been deactivated permanently. Please contact customer support for any further queries. ', sim_register: simRegister }
          }
        } else if (daysLeft < 0 && processSimActivationStatus[0].status == 'S') {
          simRenewal = 'N'
          simRenewal = 'N'
          return { status: 200, respcode: 1000, message: 'Renewal is in progress.', valid_until: formattedDate, sim_renewal: simRenewal, sim_number: processSimActivationStatus[0].sim_number, sim_register: simRegister, sim_status: statusMessage, amount }
        }
      } else {
        statusMessage = 'New'
        return { status: 200, respcode: 1000, message: 'success', sim_register: simRegister, sim_status: statusMessage }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkSimActivationStatus', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connectionRead.release()
    }
  }

  static async activateNewSim (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'activateNewSim', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const checkSimAlreadyExist = `select ma_user_id,sim_number, status, expiration_date from ma_soundbox_sim_activations  where sim_number = '${fields.sim_number}' LIMIT 1 `
      const processCheckSimExist = await this.rawQuery(checkSimAlreadyExist, connectionRead)
      const checkUserAlreadyExist = `select ma_user_id,sim_number, status, expiration_date from ma_soundbox_sim_activations  where ma_user_id = '${fields.ma_user_id}' LIMIT 1 `
      const processCheckUserExist = await this.rawQuery(checkUserAlreadyExist, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'activateNewSim', type: 'select sim number status ', fields: processCheckSimExist })
      if (processCheckSimExist == 0) {
        return { status: 400, message: 'This SIM number is not available. Please check with a different number.', respcode: 1001, action_code: 1001 }
      }
      if (processCheckSimExist.length > 0 && processCheckSimExist[0].status == 'S') {
        return { status: 400, respcode: 1001, action_code: 1001, message: 'This SIM number is Suspended Please check with a different number.', sim_number: processCheckSimExist[0].sim_number }
      }
      if (processCheckSimExist.length > 0 && processCheckSimExist[0].ma_user_id != null) {
        return { status: 400, message: 'This SIM number has already been assigned to another merchant.', respcode: 1001, action_code: 1001 }
      } else if (processCheckUserExist.length > 0) {
        return { status: 400, message: 'This merchant has already been assigned a different number.', respcode: 1001, action_code: 1001 }
      } else {
        const getDeviceDetails = `select ma_user_id,vpa_id,device_id from ma_soundbox_details_master where ma_user_id = '${fields.ma_user_id}' AND device_status = 'Y' LIMIT 1 `
        const processDeviceDetails = await this.rawQuery(getDeviceDetails, connectionRead)
        if (processDeviceDetails.length == 0) {
          return { status: 400, message: 'No device register', respcode: 1001, action_code: 1001 }
        }
        const addSimDetails = `
        update ma_soundbox_sim_activations set ma_user_id = '${fields.ma_user_id}',user_id='${fields.userid}',device_id='${processDeviceDetails[0].device_id}',vpa_id='${processDeviceDetails[0].vpa_id}',status='P'
        WHERE sim_number  = '${fields.sim_number}'`
        const processInsertSimNumber = await this.rawQuery(addSimDetails, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'activateNewSim', type: 'register sim number', fields: processInsertSimNumber })
        if (processInsertSimNumber) {
          return { status: 200, respcode: 1000, message: 'Your SIM Card will be activated within the next 24 hours. If your SIM is not activated after 24 hours,please contact Customer Support.', title: ' SIM Activation Request Sent Successfully!' }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'activateNewSim', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  static async simCardRenewalTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const isRenewableSql = `Select ma_soundbox_sim_activations_id,expiration_date,operator from ma_soundbox_sim_activations where ma_user_id =${fields.ma_user_id} and user_id = ${fields.userid} and sim_number = '${fields.sim_no}' and status in('A','I') limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'isRenewableSql', fields: isRenewableSql })
      const isRenewable = await this.rawQuery(isRenewableSql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'isRenewable', fields: isRenewable })
      if (isRenewable.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      const getMobileNumberSql = `Select mobile_id from ma_user_master where profileid = ${fields.ma_user_id} and userid  =  ${fields.userid} limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'getMobileNumberSql', fields: getMobileNumberSql })
      const getMobileNumber = await this.rawQuery(getMobileNumberSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'getMobileNumber', fields: getMobileNumber })
      fields.phone_number = getMobileNumber[0].mobile_id
      const currentDate = moment1.tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')
      const operatorName = isRenewable[0].operator
      const expirationDate = isRenewable[0].expiration_date

      const sql = `select renewal_amount from ma_soundbox_sim_renewals where operator = '${operatorName}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'sql', fields: sql })
      const sqlResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'sqlResult', fields: sqlResult })
      if (sqlResult.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      fields.amount = sqlResult[0].renewal_amount

      // check available balance of merchant
      const balanceController = require('../balance/balanceController')
      const availableBalance = await balanceController.getWalletBalancesDirect(_, {
        ma_user_id: fields.ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection
      })
      console.log('availableBalance===', availableBalance)
      if (availableBalance.amount < fields.amount) {
        return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], action_code: 1001 }
      }
      // verify pin
      const pinResponse = await this.verifyPin(fields)
      if (pinResponse.status == 400) {
        return pinResponse
      }
      // Initiate transaction
      const transactionResponse = await this.simRenewalTransactionEntires(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'transactionResponse', fields: transactionResponse })
      if (transactionResponse.status == 400) {
        return transactionResponse
      }
      console.log('transactionResponse ===', transactionResponse)

      const updateQuery = `Update ma_soundbox_sim_activations set renewal_date = '${currentDate}', status ='R' where ma_soundbox_sim_activations_id = ${isRenewable[0].ma_soundbox_sim_activations_id}`
      const updateData = await this.rawQuery(updateQuery, connection)
      const transaction_status = 'Success'
      const receiptData = {
        order_id: fields.order_id,
        operator: isRenewable[0].operator,
        sim_number: fields.sim_no,
        amount: sqlResult[0].renewal_amount,
        remarks: 'Soundbox SIM - Plan renewal',
        transaction_date_time: moment1.tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss')
      }
      // Send Success SMS *****
      const custSuccessSMS = util.communication.SIMACTIVATIONSUCCESS
      await sms.sentSmsAsync(custSuccessSMS, parseInt(fields.phone_number), util.templateid.SIMACTIVATIONSUCCESS)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, receiptData, transaction_status } // transaction type
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'simCardRenewTransaction', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async verifyPin (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyPin', type: 'request', fields: fields })
    const validator = require('../../util/validator')
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'userid', 'security_pin'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const securePinCtrl = require('../securityPin/securityPinController')
      const securePinData = await securePinCtrl.verifySecurePin(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        security_pin: fields.security_pin,
        connection
      })
      console.log('securePinData', securePinData)
      return securePinData
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyPin', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async simRenewalReceiptDetails (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let isRenewableSql = `
        SELECT
          mtm.ma_user_id,
          mtm.userid,
          mtm.aggregator_txn_id,
          msa.ma_soundbox_sim_activations_id,
          msa.expiration_date,
          mtm.amount,
          msa.operator,
          msa.sim_number,
          mtm.transaction_status,
          mtm.mobile_number,
          mtm.transaction_reason,
          mtm.aggregator_order_id,
          mtm.ma_transaction_master_id,
          mtm.addedon,
          mum.address
        FROM
          ma_soundbox_sim_activations msa
        LEFT JOIN
          ma_transaction_master mtm ON msa.ma_user_id = mtm.ma_user_id
          LEFT JOIN ma_user_master mum ON msa.ma_user_id = mum.profileid
        WHERE
 
          msa.ma_user_id = ${fields.ma_user_id}
          AND msa.user_id = ${fields.userid}
          AND transaction_type = '${fields.transaction_type}'`

      if (fields.aggregator_order_id) {
        isRenewableSql += ` AND mtm.aggregator_order_id = '${fields.aggregator_order_id}'`
      }

      isRenewableSql += ' ORDER BY mtm.ma_transaction_master_id DESC  Limit 1'
      log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalDetails', type: 'request', isRenewableSql })
      const updateData = await this.rawQuery(isRenewableSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalDetails', type: 'request', updateData })
      const transaction_receipt_data = updateData.map(item => ({
        order_id: item.aggregator_order_id,
        operator: item.operator,
        sim_number: item.sim_number,
        amount: item.amount,
        remarks: 'Soundbox SIM - Plan renewal',
        selectedBiller: 'SIM recharge',
        transaction_date_time: moment1(item.addedon).tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss'),
        transaction_status: item.transaction_status,
        aggregator_txn_id: item.aggregator_txn_id,
        mobile_number: item.mobile_number,
        transaction_reason: item.transaction_reason,
        userid: item.userid,
        address: item.address

      }))
      log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalDetails', type: 'request', transaction_receipt_data })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, transaction_receipt_data: transaction_receipt_data }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'simRenewalDetails', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (connection) {
        connection.release()
      }
    }
  }
}

module.exports = SoundBoxManagementController
