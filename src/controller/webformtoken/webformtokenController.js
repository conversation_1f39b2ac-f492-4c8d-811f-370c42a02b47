const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const path = require('path')
// const validator = require('../../util/validator')
// const util = require('../../util/util')
const e = require('express')
const moment = require('moment')
// const momentTimezone = require('moment-timezone')
class webformtokenContoller extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'webform_token_log'
  }

  static get PRIMARY_KEY () {
    return 'webform_token_log_id'
  }

  // Api to maintain session token for all the form type
  static async getCaptcha (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getCaptcha', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // console.log(fields)
      // Generate jwt token
      // await this.clearToken()

      // if (1) {
      //   return 200
      // }
      const { ma_user_id, userid, form_type } = fields
      let token
      if (form_type === 'loan_lead' || form_type == 'support_ticket' || form_type == 'khatabook_reminder') {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'

        const generateRandomString = (length) => {
          let result = ''
          const charactersLength = characters.length
          for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength))
          }

          return result
        }

        token = (generateRandomString(10))

        console.log('token:', token)
        const insertUserQuery = `INSERT INTO webform_token_log (ma_user_id,user_id,token,session_counter,form_type,expire_time) VALUES (${ma_user_id},${userid},'${token}',0,'${form_type}',15)`
        log.logger({ pagename: path.basename(__filename), action: 'getCaptcha', type: 'insertUserQuery', fields: insertUserQuery })
        const params = await this.rawQuery(insertUserQuery, connection)
        log.logger({ pagename: path.basename(__filename), action: 'getCaptcha', type: 'insertUserQuery', fields: params })
        // await new Promise(resolve => setTimeout(resolve, 3000))
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, sessionId: token }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getCaptcha', type: 'request', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }
  // verify token details

  static async verifyCaptchaApp (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'verifyCaptchaApp', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const { ma_user_id, userid, form_type, token } = fields
      console.log(token)
      const getUserDetails = `SELECT webform_token_log_id,ma_user_id,user_id,token,session_counter,form_type,expire_time,addedon FROM webform_token_log WHERE token='${token}' ORDER BY webform_token_log_id DESC limit 1`
      log.logger({ pagename: path.basename(__filename), action: 'verifyCaptchaApp', type: 'getUserDetails_query', fields: getUserDetails })
      const userDetails = await this.rawQuery(getUserDetails, connection)
      console.log(userDetails)
      log.logger({ pagename: path.basename(__filename), action: 'verifyCaptchaApp', type: 'getUserDetails', fields: userDetails })

      if (userDetails.length == 0) {
        return { status: 400, message: 'Invalid token', respcode: 1001 }
      } else {
        console.log('this is the verify function controller')
        const dateTimeString = moment((userDetails[0].addedon)).format('YYYY-MM-DD HH:mm:ss')
        console.log('DateTimeString:', dateTimeString)

        const savedTime = moment(dateTimeString)

        // const date = moment.tz('Asia/Kolkata')

        const addTime = savedTime.add(userDetails[0].expire_time, 'minutes')
        const formatAddTime = moment(addTime).format('YYYY-MM-DD HH:mm:ss')
        const currTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')

        console.log({ currTime, formatAddTime })
        // savedTime.setMinutes(savedTime.getMinutes() + userDetails[0].expire_time)

        const hasexpired = currTime > formatAddTime

        if (hasexpired) {
          return { status: 400, message: 'session expired', respcode: 1001 }
        } else {
          // Validate the token
          console.log('verify to check the condition')
          if (((userDetails[0].ma_user_id == ma_user_id) || (userDetails[0].user_id == userid)) && (userDetails[0].form_type == form_type)) {
            if (userDetails[0].session_counter == '1') {
              console.log('form submitted already')
              return { status: 400, message: 'form is already submitted', respcode: 1001 }
            } else {
              console.log('update the counter')
              const updateCounterDetails = `UPDATE webform_token_log SET session_counter = '1' WHERE webform_token_log_id =${userDetails[0].webform_token_log_id}`
              const counter = await this.rawQuery(updateCounterDetails, connection)
              log.logger({ pagename: path.basename(__filename), action: 'verifyCaptchaApp', type: 'counterDetails', fields: counter })
              return { status: 200, message: 'success', respcode: 1000 }
            }
          } else {
            return { status: 400, message: 'Invalid user details', respcode: 1001 }
          }
        }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'verifyCaptchaApp', type: 'request', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  // clear captcha cron

  static async clearToken () {
    log.logger({ pagename: path.basename(__filename), action: 'clearToken', type: 'request', fields: 'request' })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const deleteDetails = 'DELETE FROM webform_token_log WHERE addedon<= CONCAT(CURDATE() - INTERVAL 1 DAY, " 23:59:59") LIMIT 1000'
      log.logger({ pagename: path.basename(__filename), action: 'clearToken', type: 'getDetails', fields: deleteDetails })
      const deleteAllUserDetails = await this.rawQuery(deleteDetails, connection)
      // console.log('deleteallusers', deleteAllUserDetails)
      console.log('Previous record deleted')
      log.logger({ pagename: path.basename(__filename), action: 'clearToken', type: 'getAllUserDetails', fields: deleteAllUserDetails })
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'clearToken', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = webformtokenContoller
