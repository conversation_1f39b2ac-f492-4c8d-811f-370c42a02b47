/* eslint-disable no-mixed-spaces-and-tabs */
/* eslint-disable no-tabs */
/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const sms = require('../../util/sms')
const errorEmail = require('../../util/errorHandler')
const axios = require('axios')
const crypto = require('crypto')
const moment = require('moment')
const { ACTIVATE_POS_API_URL, POS_STATUS, HEADER_CONTENT, FROM_EMAIL, EMAIL_SIGNATURE, POS_ACTIVATION_SUCCESS_EMAIL_SUBJECT, POS_ACTIVATION_FAIL_EMAIL_SUBJECT } = require('./config').POS_CONSTANTS
const posErrorMsg = require('./error')
const mailer = require('../../util/sendEmails')

class posActivationController extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_pos_details_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_pos_details_master_id'
  }

  /**
   * Get activated POS Device list
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} { ma_user_id: Integer, userid: Integer, device_type: String}
   * @returns { status: status_val, message: message_txt, respcode: 1000, device_list: device_list }
   */
  static async getActivatedPOSDevices (_, fields) {
  	log.logger({ pagename: 'posAcitvationController.js', action: 'getActivatedPOSDevices', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let message_txt = ''
      let response_code = ''
      let device_list = []

      // Validation for user exists
      const sql = `SELECT ma_user_master_id,state,user_type FROM ma_user_master WHERE profileid = '${fields.ma_user_id}' AND userid = '${fields.userid}'`
      const userData = await this.rawQuery(sql, connection)
      if (userData.length <= 0) {
        // Transaction Rollback
      //  await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'fail : User does not exists.' }
      }

      const sqlSerial = `SELECT serial_no, terminal_id, device_type FROM ma_pos_details_master WHERE ma_user_id = '${fields.ma_user_id}' AND device_type = '${fields.device_type}' AND device_status = '2' AND enable_status = '1' order by ma_pos_details_master_id desc`
	    const posDetails = await this.rawQuery(sqlSerial, connection)
      console.log('>>>>>', posDetails)
      if (posDetails.length > 0) {
        message_txt = 'Success'
        response_code = 1000
        device_list = posDetails
      } else {
        // message_txt = 'No Devices found'
        message_txt = 'No POS / HATM devices mapped.'
        response_code = 1003
      }
      return {
        status: 200,
        action_code: 1000,
        message: message_txt,
        respcode: response_code,
        device_list: device_list
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getActivatedPOSDevices', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * To update the transaction details of POS Transaction
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} { ma_user_id: Integer, userid: Integer, receipt_data: Object }
   * @returns { status: Integer, message: String, respcode: Integer }
   */
  static async updatePOSTransactionDetails (_, fields) {
  	log.logger({ pagename: 'posAcitvationController.js', action: 'updatePOSTransactionDetails', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'receipt_data'])
      const pos_data = {}
      console.log('receipt data', fields.receipt_data)
      const sqlReceipt = `SELECT bank_logo FROM ma_pos_banklist WHERE bank_name = '${fields.receipt_data.bankName}' AND record_status = 'A' `
	    const receiptDetails = await this.rawQuery(sqlReceipt, connection)

      console.log('receiptDetails', receiptDetails)

      if (receiptDetails.length > 0) {
        fields.receipt_data.bankLogo = receiptDetails[0].bank_logo
        console.log('updated receipt data', fields.receipt_data)
      }

      pos_data.pos_type = fields.receipt_data.transType
      pos_data.airpay_id = fields.receipt_data.airpayId
      pos_data.amount = fields.receipt_data.amount
      pos_data.receipt_data = JSON.stringify(fields.receipt_data)
      pos_data.ma_user_id = fields.ma_user_id
      const order_id = fields.order_id ? fields.order_id : null
      console.log('order_id', order_id)
      if (order_id) {
        const sql = `UPDATE ma_pos_transaction_logs set order_id = '${order_id}' where airpay_id = ${pos_data.airpay_id}`
        const result = await this.rawQuery(sql, connection)
        console.log('Update', result)
      } else {
        const result = (await mySQLWrapper.createTransactionalQuery({
          query: `INSERT INTO ${'ma_pos_transaction_logs'}
                        SET ?;`,
          params: [pos_data],
          connection: connection
        }))
        console.log('Insert', result)
      }

      return {
        status: 200,
        message: 'Success',
        respcode: 1000
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updatePOSTransactionDetails', type: 'catcherror', fields: err })
      let mesg = ''
      if (err.code && err.code === 'ER_DUP_ENTRY') {
        mesg = 'Duplicate Entry!'
      } else {
        mesg = 'Fail: Something went wrong.'
      }
      return { status: 400, message: mesg, respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * Get Receipt for the POS Transaction
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} { ma_user_id: Integer, userid: Integer, device_type: String}
   * @returns { status: status_val, message: message_txt, respcode: 1000, device_list: device_list }
   */
  static async getPosReceipt (_, fields) {
    log.logger({ pagename: 'posAcitvationController.js', action: 'getPosReceipt', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let filterCondition = ''
      let action_code = ''
      let response_code = ''
      const receipt_data = {}

      if (fields.order_id) {
        filterCondition = ` WHERE mptl.order_id= '${fields.order_id}' `
      } else {
        filterCondition = ` WHERE mptl.airpay_id= '${fields.airpay_id}' `
      }

      const sqlReceipt = `SELECT mptl.pos_type,
                              mptl.amount,
                              mptl.airpay_id,
                              mptl.order_id,
                              mptl.receipt_data
                            FROM ma_pos_transaction_logs mptl
                            ${filterCondition}
                            ORDER BY mptl.ma_pos_transaction_logs_id DESC LIMIT 1`
	    const receiptDetails = await this.rawQuery(sqlReceipt, connection)

      console.log(receiptDetails)

      if (receiptDetails.length > 0) {
        response_code = 1000
        action_code = 1000
        const parsedReceiptData = JSON.parse(receiptDetails[0].receipt_data)
        // Extract the bankName from the parsed JSON object
        const bankName = parsedReceiptData.bankName
        receipt_data.transaction_type = receiptDetails[0].pos_type
        receipt_data.amount = receiptDetails[0].amount
        receipt_data.airpay_id = receiptDetails[0].airpay_id
        receipt_data.order_id = receiptDetails[0].order_id
        receipt_data.receipt_json = JSON.parse(receiptDetails[0].receipt_data)
        const sqlReceiptData = `SELECT bank_logo FROM ma_pos_banklist WHERE bank_name = '${bankName}' AND record_status = 'A' `
        console.log('sqlReceiptData', sqlReceiptData)
        const receiptDetailsData = await this.rawQuery(sqlReceiptData, connection)
        console.log('get receiptDetailsData', receiptDetailsData)
        if (receiptDetailsData.length > 0) {
          receipt_data.receipt_json.bankLogo = receiptDetailsData[0].bank_logo
          console.log('updated receipt dataa', receipt_data)
        }
        const tnxdata = moment(receipt_data.receipt_json.txnDateTime, 'YYYYMMDDHHmmss')
        receipt_data.receipt_json.txnDateTime = moment(tnxdata).format('DD-MM-YYYY HH:mm:ss')
      } else {
        response_code = 1002
        action_code = 1003
      }
      return {
        status: 200,
        respcode: response_code,
        action_code: action_code,
        message: errorMsg.responseCode[response_code],
        receipt_data: receipt_data
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getPosReceipt', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * Get Receipt for the POS Transaction
   * <AUTHOR> Hassan
   * @param {*} _
   * @param {*} { ma_user_id: Integer, userid: Integer, device_type: String}
   * @returns { status: status_val, message: message_txt, respcode: 1000, device_list: device_list }
   */
  static async getPosTransactionDetails (_, fields) {
    log.logger({ pagename: 'posAcitvationController.js', action: 'getPosReceipt', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let action_code = ''
      let response_code = ''
      let receipt_data = []

      const ma_user_id = fields.ma_user_id
      const transaction_type = fields.transaction_type
      const datefrom = fields.datefrom
      const dateto = fields.dateto
      const limit = fields.limit
      const offset = fields.offset
      const order_id = fields.order_id
      const airpay_id = fields.airpay_id
      delete fields.userid
      delete fields.ma_user_id
      delete fields.datefrom
      delete fields.dateto
      delete fields.limit
      delete fields.offset
      delete fields.order_id
      delete fields.airpay_id
      delete fields.transaction_type

      var sql = `SELECT SQL_CALC_FOUND_ROWS
                      mptl.pos_type as transaction_type,
                      mptl.amount,
                      mptl.airpay_id,
                      mptl.order_id,
                      mptl.receipt_data as receipt_json
                    FROM ma_pos_transaction_logs mptl
                    WHERE mptl.addedon BETWEEN '${datefrom} 00:00:00' AND '${dateto} 23:59:59'
                    AND mptl.ma_user_id = '${ma_user_id}'`

      sql += validator.definedVal(transaction_type) ? ` AND mptl.pos_type LIKE '%${transaction_type}%'` : ''
      sql += validator.definedVal(order_id) ? ` AND mptl.order_id LIKE '%${order_id}%'` : ''
      sql += validator.definedVal(airpay_id) ? ` AND mptl.airpay_id = '${airpay_id}'` : ''
      sql += ` ORDER BY  mptl.addedon DESC LIMIT ${offset},${limit}`

	    const txnDetails = await this.rawQuery(sql, connection)
      console.log('txnDetails', txnDetails)
      let nextFlag = false
      if (txnDetails.length > 0) {
        const countSql = 'SELECT FOUND_ROWS() AS total'
        const countResult = global.G_MYSQL_SINGLE ? await this.rawQueryV2(countSql, connection) : await this.rawQuery(countSql, connection)
        if (countResult.length > 0) {
          const total = countResult[0].total
          console.log('Total : ' + total)
          console.log('Current : ' + (limit + offset))
          if ((limit + offset) < total) {
            nextFlag = true
          }
        }
        response_code = 1000
        action_code = 1000
        receipt_data = txnDetails.map((item) => {
          const returnData = {
            ...item,
            txnDateTime: item.receipt_json ? JSON.parse(item.receipt_json).txnDateTime : ''
          }
          delete returnData.receipt_json
          return returnData
        })
      } else {
        response_code = 1002
        action_code = 1003
      }
      return {
        status: 200,
        respcode: response_code,
        action_code: action_code,
        message: errorMsg.responseCode[response_code],
        nextFlag: nextFlag,
        receipt_data: receipt_data
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getPosReceipt', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async checkSerialNo (_, fields) {
  	log.logger({ pagename: 'posAcitvationController.js', action: 'checkSerialNo', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.serial_no != '' && fields.serial_no != undefined) {
        console.log('ma_user_id : ' + fields.ma_user_id)
        let device_type = ''
        let device_status = ''
        let message_txt = ''
        let status_val = ''
	        const sqlSerial = `SELECT device_type, device_status FROM ma_pos_details_master WHERE serial_no = '${fields.serial_no}' order by ma_pos_details_master_id desc limit 1`
	        const posDetails = await this.rawQuery(sqlSerial, connection)
	        log.logger({ pagename: 'posAcitvationController.js', action: 'checkSerialNo', type: 'queryresponse', fields: { posDetails } })
	        if (posDetails.length > 0) {
	          device_type = posDetails[0].device_type
	          device_status = POS_STATUS[posDetails[0].device_status]
	          if (posDetails[0].device_status == '0') {
				  message_txt = 'Device Inactive'
				  status_val = 200
	          } else if (posDetails[0].device_status == '1') {
				  message_txt = 'Device Inactive'
				  status_val = 200
	          } else if (posDetails[0].device_status == '2') {
				  message_txt = 'The device is already activated.'
				  status_val = 400
	          }
	          return { status: status_val, respcode: 1000, message: message_txt, serial_no: fields.serial_no, device_type: device_type }
	        } else {
	        	return { status: 400, message: 'The serial number provided for the device is invalid. Please check the serial number and try again.', respcode: 1028 }
	        }
	    } else {
	    	return { status: 400, message: errorMsg.responseCode[1028] + ': Serial No. cannot be empty. ', respcode: 1028 }
	    }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkSerialNo', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async getPosDetailForMerchant (_, fields) {
  	log.logger({ pagename: 'posAcitvationController.js', action: 'getPosDetailForMerchant', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('inside get posdetails for user ' + fields.ma_user_id)
    	if (fields.ma_user_id != '' && fields.ma_user_id != undefined) {
        const sql = `SELECT pm.device_type, pm.serial_no as pos_serial_no, pm.ma_user_id as airpay_mid, pm.terminal_id as airpay_tid, FROM_UNIXTIME(UNIX_TIMESTAMP(pm.updatedon_dt),'%d-%m-%Y %h:%i:%s') AS activation_date, pm.enable_status as pos_enable, dt.activation_required FROM ma_pos_details_master pm LEFT JOIN ma_pos_device_type dt ON (pm.device_type = dt.device_type)         
        WHERE pm.ma_user_id = ${fields.ma_user_id} AND pm.device_status = '2' GROUP BY pm.serial_no ORDER BY pm.updatedon_dt DESC`
	      const posMDetails = await this.rawQuery(sql, connection)
	      log.logger({ pagename: 'posAcitvationController.js', action: 'getPosDetailForMerchant', type: 'queryresponse', fields: { posMDetails } })
	      if (posMDetails.length > 0) {
          let pos_enable_status = ''
          const newPosData = []
          for (const element of posMDetails) {
            if (element.device_type == 'VM30' && element.pos_enable != '1') {
              pos_enable_status = 'true'
            } else {
              pos_enable_status = 'false'
            }
            element.pos_enable = pos_enable_status
            newPosData.push(element)
          }
          return { status: 200, action_code: 1000, respcode: 1000, message: 'success', posMerchantDetails: newPosData }
	      } else {
	      return { status: 200, action_code: 1000, message: errorMsg.responseCode[1002], respcode: 1002 }
	      }
	    } else {
	    	return { status: 400, message: errorMsg.responseCode[1028] + ': Ma User Id cannot be empty. ', respcode: 1028 }
	    }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getPosDetailForMerchant', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /*
    * send sms to client on success or failure transaction
  */
  static async sendSmsToPosMerchant (pos_type, serial_no, transaction_status, mobile_number, date_time = '', reason = '') {
    try {
      // Send sms to customer after transaction is successful
      const smsParams = {
        transaction_amt: pos_type,
        serial_no: serial_no,
        date_time: date_time,
        mobile_number: mobile_number,
        transaction_status: transaction_status,
        reason: reason
      }
      log.logger({ pagename: 'posActivationController.js', action: 'sendSmsToPosMerchant', type: 'response', fields: smsParams })
      let commMessage = ''
      let template = ''
      if (transaction_status == 'S') {
        commMessage = util.communication.POSACTIVATIONSUCCESS
        template = util.templateid.POSACTIVATIONSUCCESS
      } else if (transaction_status == 'F') {
        commMessage = util.communication.POSACTIVATIONFAILURE
        template = util.templateid.POSACTIVATIONFAILURE
      }

      var dt = new Date(date_time)
      const datetimeformat = dt.toLocaleString('en-IN')
      commMessage = commMessage.replace('<POS_TYPE>', pos_type)
      commMessage = commMessage.replace('<POS_S#>', serial_no)
      commMessage = commMessage.replace('<date_time>', date_time)
      commMessage = commMessage.replace('<Reason>', reason)
      commMessage = commMessage.replace('<signature>', util.communication.Signature)
      await sms.sentSmsAsync(commMessage, mobile_number, template)
      return 0
    } catch (err) {
      log.logger({ pagename: 'posActivationController.js', action: 'posActivate', type: 'catchsmserror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getMerchantDetail (userid, conn) {
    const tempConnection = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
	  var userDetails = ''
	  let mobile_no = ''
	  let email_id = ''
      const sql = `SELECT email_id, mobile_id FROM ma_user_master WHERE profileid = ${userid} and mer_user = 'mer' limit 1 `
      userDetails = await this.rawQuery(sql, connection)
      if (userDetails.length > 0) {
        mobile_no = userDetails[0].mobile_id
        email_id = userDetails[0].email_id
        return { status: 200, mobile_no: mobile_no, email_id: email_id }
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetail', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async sendPosMail (user_emailid, from_email, mail_type, pos_type, serial_no, date_time, email_signature, airpay_mid = '', airpay_tid = '', file_path_value = '') {
    var mailerData = ''
    if (mail_type == 'S') {
	  mailerData = {
        template: 'sendPosSuccessActivation.ejs',
        content: {
          pos_type: pos_type,
          serial_no: serial_no,
          date_time: date_time,
          airpay_mid: airpay_mid,
          airpay_tid: airpay_tid,
          signature: email_signature,
          file_path: file_path_value
        },
        from: from_email,
        to: [user_emailid],
        subject: POS_ACTIVATION_SUCCESS_EMAIL_SUBJECT
	  }
    } else if (mail_type == 'F') {
	  mailerData = {
        template: 'sendPosFailActivation.ejs',
        content: {
          pos_type: pos_type,
          serial_no: serial_no,
          date_time: date_time,
          signature: email_signature
        },
        from: from_email,
        to: [user_emailid],
        subject: POS_ACTIVATION_FAIL_EMAIL_SUBJECT
	  }
    }

    const mailResponse = await mailer(mailerData)
    log.logger({ pagename: 'posAcitvationController.js', action: 'sendPosMail', type: 'Mail content', fields: JSON.stringify(mailerData) })
    log.logger({ pagename: 'posAcitvationController.js', action: 'sendPosMail', type: 'Mail response', fields: mailResponse })
  }

  static async posActivate (_, fields) {
  	log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'request', fields: { fields } })
  	const connection = await mySQLWrapper.getConnectionFromPool()
  	try {
      console.log('inside get pos activate for serial no ' + fields.serial_no)
      if (fields.serial_no != '' && fields.serial_no != undefined) {
	      // Send serial no. to activate the POS device
	      const sql = `select profileid,apikey,username,password from ma_user_master where profileid = ${fields.ma_user_id} AND mer_user = 'mer' limit 1`
	      const data = await this.rawQuery(sql, connection)
	      log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'queryresponse', fields: { data } })
	      let stringkey = ''
	      let privatekey = ''
	      if (data.length > 0) {
	      	stringkey = `${data[0].apikey}@${data[0].username}:|:${data[0].password}`
	      	privatekey = crypto.createHash('sha256').update(stringkey).digest('hex')
	  	  }

        const sqlPos = `SELECT pm.ma_pos_details_master_id, pm.device_type, pm.device_status, dt.steps_description, dt.file_path,pm.enable_status,dt.activation_required,dt.activation_source FROM ma_pos_details_master pm left join ma_pos_device_type dt on (pm.device_type = dt.device_type) WHERE dt.status = "1" and pm.serial_no = '${fields.serial_no}' and pm.device_status = '0' limit 1`
        const posMerDetails = await this.rawQuery(sqlPos, connection)
        console.log(sqlPos)
        log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'queryresponse', fields: { posMerDetails } })
        const device_status = ''
        let device_type = ''
        let file_path_value = ''
        let steps_description = ''
        const device_serial = fields.serial_no
        const mercid = fields.ma_user_id
        let ma_pos_details_master_id = ''
        let pos_enable = ''
        if (posMerDetails.length > 0) {
          device_type = posMerDetails[0].device_type
          ma_pos_details_master_id = posMerDetails[0].ma_pos_details_master_id
          file_path_value = posMerDetails[0].file_path
          steps_description = posMerDetails[0].steps_description
          if (posMerDetails[0].activation_required == 1) {
            pos_enable = posMerDetails[0].enable_status == 0 ? 'true' : 'false'
          }
          if(device_type == 'VM30'){
            console.log("inside");
            if (!fields.isMobile && posMerDetails[0].activation_source == 2) {
              return { status: 400, message: 'To use this device functionality you need to use vyaapaar app ', respcode: 1001}
            }
          }
          if (posMerDetails[0].device_status == 2) {
            return { status: 200, action_code: 1000, message: ' Device Already Activated. ', respcode: 1000, serial_no: fields.serial_no, device_type: device_type, pos_enable: pos_enable }
          }
          const sqlCurrentTime = 'SELECT DATE_FORMAT(CURRENT_TIMESTAMP,\'%Y-%m-%d %H:%i:%s\') as currentTime'
          const resultTime = await this.rawQuery(sqlCurrentTime, connection)
          log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'result_date_time', fields: { resultTime } })
          const date_time = resultTime[0].currentTime

          // get user details
          const responseMData = await this.getMerchantDetail(fields.ma_user_id, connection)
          if (responseMData.status == 400) {
            return responseMData
          }
          log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'result_user_detail', fields: { responseMData } })
          const mobile_no = responseMData.mobile_no
          const email_id = responseMData.email_id

          try {
            console.time('TIMER_POS_ACTIVATION')
            const postData = JSON.stringify({ mercid: mercid, device_serial: device_serial, device_type: device_type, privatekey: privatekey })
            const response = await axios({
              method: 'post',
              url: ACTIVATE_POS_API_URL,
              data: postData,
              headers: HEADER_CONTENT,
              timeout: 20000
            })
            console.timeEnd('TIMER_POS_ACTIVATION')
            console.log('----MA POS API RESPONSE ---posActivate----', response.data)
            if (response != '' && response.status === 200) {
              const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
              if (responseData.status == 200) {
                if (responseData.terminalid != '' && responseData.terminalid != undefined) {
                  await mySQLWrapper.beginTransaction(connection)
                  const _result = await this.updateWhere(connection, {
                    id: ma_pos_details_master_id,
                    where: 'ma_pos_details_master_id',
                    data: {
                      terminal_id: responseData.terminalid,
                      ma_user_id: fields.ma_user_id,
                      device_status: '2'
                    }
                  })
                  await mySQLWrapper.commit(connection)
                }
                // Trigger Sms
                const sendPosSmsSuccess = await this.sendSmsToPosMerchant(device_type, fields.serial_no, 'S', mobile_no, date_time, '')
                log.logger({ pagename: require('path').basename(__filename), action: 'posActivate', type: 'sendPosSmsSuccessResponse', fields: { sendPosSmsSuccess } })
                const sendPostMailSuccess = await this.sendPosMail(email_id, FROM_EMAIL, 'S', device_type, fields.serial_no, date_time, EMAIL_SIGNATURE, fields.ma_user_id, responseData.terminalid, file_path_value)
                if (steps_description != '' && steps_description != null) {
                  steps_description = steps_description.replace('#mid#', fields.ma_user_id)
                  steps_description = steps_description.replace('#tid#', responseData.terminalid)
                  steps_description = steps_description.replace('#serialno#', fields.serial_no)
                }

                return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, terminalid: responseData.terminalid, serial_no: fields.serial_no, device_type: device_type, file_path: file_path_value, merchant_id: fields.ma_user_id, steps_description: steps_description, pos_enable: pos_enable }
              } else if (responseData.status == 632) {
                // update the device as Active
                if (responseData.terminalid != '' && responseData.terminalid != undefined) {
                  await mySQLWrapper.beginTransaction(connection)
                  const _result = await this.updateWhere(connection, {
                    id: ma_pos_details_master_id,
                    where: 'ma_pos_details_master_id',
                    data: {
                      terminal_id: responseData.terminalid,
                      ma_user_id: fields.ma_user_id,
                      device_status: '2'
                    }
                  })
                  await mySQLWrapper.commit(connection)
                }

                const sendPosSmsSuccess = await this.sendSmsToPosMerchant(device_type, fields.serial_no, 'S', mobile_no, date_time, '')
                log.logger({ pagename: require('path').basename(__filename), action: 'posActivate', type: 'sendPosSmsSuccessResponse', fields: { sendPosSmsSuccess } })
                const sendPostMailSuccess = await this.sendPosMail(email_id, FROM_EMAIL, 'S', device_type, fields.serial_no, date_time, EMAIL_SIGNATURE, fields.ma_user_id, responseData.terminalid, file_path_value)
                return { status: 200, respcode: responseData.status, message: posErrorMsg.responseCode[responseData.status], action_code: 1000, terminalid: responseData.terminalid, serial_no: fields.serial_no, device_type: device_type, file_path: file_path_value, merchant_id: fields.ma_user_id, steps_description: steps_description, pos_enable: pos_enable }
              } else if (responseData.status == 631) {
                // update the device as Active
                if (responseData.terminalid != '' && responseData.terminalid != undefined && fields.ma_user_id == responseData.mercid) {
                  await mySQLWrapper.beginTransaction(connection)
                  const _result = await this.updateWhere(connection, {
                    id: ma_pos_details_master_id,
                    where: 'ma_pos_details_master_id',
                    data: {
                      terminal_id: responseData.terminalid,
                      ma_user_id: fields.ma_user_id,
                      device_status: '2'
                    }
                  })
                  await mySQLWrapper.commit(connection)
                }
                const sendPosSmsSuccess = await this.sendSmsToPosMerchant(device_type, fields.serial_no, 'S', mobile_no, date_time, '')
                log.logger({ pagename: require('path').basename(__filename), action: 'posActivate', type: 'sendPosSmsSuccessResponse', fields: { sendPosSmsSuccess } })
                const sendPostMailSuccess = await this.sendPosMail(email_id, FROM_EMAIL, 'S', device_type, fields.serial_no, date_time, EMAIL_SIGNATURE, fields.ma_user_id, responseData.terminalid, file_path_value)
                return { status: 200, respcode: responseData.status, message: posErrorMsg.responseCode[responseData.status], action_code: 1000, terminalid: responseData.terminalid, serial_no: fields.serial_no, device_type: device_type, file_path: file_path_value, merchant_id: fields.ma_user_id, steps_description: steps_description, pos_enable: pos_enable }
              } else {
                const sendPosSmsFailure = await this.sendSmsToPosMerchant(device_type, fields.serial_no, 'F', mobile_no, date_time, posErrorMsg.responseCode[responseData.status])
                log.logger({ pagename: require('path').basename(__filename), action: 'posActivate', type: 'sendPosSmsFailureResponse', fields: { sendPosSmsFailure } })
                const sendPostMailFail = await this.sendPosMail(email_id, FROM_EMAIL, 'F', device_type, fields.serial_no, date_time, EMAIL_SIGNATURE, '', '', '')
                return { status: 400, respcode: responseData.status, message: posErrorMsg.responseCode[responseData.status] }
              }
            } else {
              return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in activating POS device' }
            }
          } catch (err) {
            log.logger({ pagename: 'posActivationController.js', action: 'posActivate', type: 'catchaxioserror', fields: err })
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
          }
        } else {
          return { status: 400, message: errorMsg.responseCode[1028] + ' : POS Device not supported', respcode: 1028 }
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ' : Serial No. cannot be empty.', respcode: 1028 }
      }
    } catch (err) {
	  log.logger({ pagename: 'posActivationController.js', action: 'posActivate', type: 'catcherror', fields: err })
	  return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getDeviceTypeManual (_, fields) {
    log.logger({ pagename: 'posAcitvationController.js', action: 'getDeviceTypeManual', type: 'request', fields: { fields } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('inside get device type manual download ' + fields.ma_user_id)
      if (fields.ma_user_id != '' && fields.ma_user_id != undefined) {
        const sqlM = ' SELECT device_type, file_path FROM ma_pos_device_type WHERE status = "1" order by ma_pos_device_type_id desc'
        const deviceMDetails = await this.rawQuery(sqlM, connection)
        log.logger({ pagename: 'posAcitvationController.js', action: 'getDeviceTypeManual', type: 'queryresponse', fields: { deviceMDetails } })
        if (deviceMDetails.length > 0) {
          return { status: 200, respcode: 1000, message: 'success', deviceManualUrl: deviceMDetails }
        } else {
          return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': Ma User Id cannot be empty. ', respcode: 1028 }
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDeviceTypeManual', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async enablePos (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'enablePos', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sqlPos = `SELECT pm.ma_pos_details_master_id, pm.device_type, pm.device_status, dt.steps_description, dt.file_path,
      pm.enable_status, dt.activation_required, dt.activation_source FROM ma_pos_details_master pm left join ma_pos_device_type dt on (pm.device_type = dt.device_type) WHERE dt.status = '1' and pm.serial_no = '${fields.serial_no}' and pm.device_status = '2' and pm.ma_user_id = '${fields.ma_user_id}' limit 1`
      const posMerDetails = await this.rawQuery(sqlPos, connection)
      console.log(sqlPos)
      log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'queryresponse', fields: { posMerDetails } })
      if (posMerDetails.length > 0) {
        if ((!fields.isMobile && posMerDetails[0].activation_source == '2') || (fields.isMobile && posMerDetails[0].activation_source != '2')) {
          return { status: 400, respcode: 1001, message: 'Please enable via App' }
        } else if (posMerDetails[0].enable_status == '1') {
          return { status: 400, respcode: 1001, message: 'Device already enabled' }
        }
        const updateData = {
          enable_status: '1'
        }
        await mySQLWrapper.beginTransaction(connection)
        const _result = await this.updateWhere(connection, {
          id: posMerDetails[0].ma_pos_details_master_id,
          where: 'ma_pos_details_master_id',
          data: updateData
        })
        await mySQLWrapper.commit(connection)
        if (_result.affectedRows > 0) {
          return { status: 200, respcode: 1000, message: 'Device enabled successfully.', action_code: 1000 }
        } else {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      } else {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1002], action_code: 1000 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'enablePos', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getposSDKdeviceDetails (_, fields) {
    log.logger({ pagename: 'posAcitvationController.js', action: 'getposSDKdeviceDetails', type: 'request', fields: { fields } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('get pos sdk details id ' + fields.userid)
      if (fields.app_type == null || fields.app_type == '' || fields.app_type == undefined) {
        fields.app_type = 'VM30_SDK'
      }
      // console.log("app type",fields.app_type);
      if (fields.userid != '' && fields.userid != undefined) {
        if (fields.app_version != '' || fields.app_version != undefined) {
          // const sqlM = `SELECT MAX(app_version) AS greater_version,download_url FROM ma_app_version_logs ORDER BY app_version DESC`;
          const sqlM = 'SELECT app_version,app_type,download_url,description,update_type,file_name,file_ext FROM ma_app_version_logs where status = \'ACTIVE\'  ORDER BY added_on DESC;'
          const sdkDetails = await this.rawQuery(sqlM, connection)
          log.logger({ pagename: 'posAcitvationController.js', action: 'getposSDKdeviceDetails', type: 'queryresponse', fields: { sdkDetails } })
          if (sdkDetails.length > 0) {
            if (sdkDetails[0].app_version != fields.app_version) {
              return { status: 200, respcode: 1000, message: 'success', checkPosUpdate: { data: sdkDetails[0], updateReqiured: true, message: 'Update available!' }, action_code: 1000 }
            } else {
              log.logger({ pagename: 'posAcitvationController.js', action: 'getposSDKdeviceDetails', type: 'app version check', fields: { sdkDetails } })
              return { status: 200, respcode: 1000, message: 'success', checkPosUpdate: { updateReqiured: false, message: 'Your SDK version is upto date!' }, action_code: 1000 }
            }
          } else {
            return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 }
          }
        } else {
          return { status: 400, message: errorMsg.responseCode[1028] + ': app version required ', respcode: 1028, action_code: 1001 }
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': userId cannot be empty. ', respcode: 1028, action_code: 1001 }
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getposSDKdeviceDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }
}
module.exports = posActivationController
