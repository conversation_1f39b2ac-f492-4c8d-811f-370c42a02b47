const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')

class EmitraDmtTransferMapController extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'emitra_transaction_session_mapping'
  }

  static get PRIMARY_KEY () {
    return 'emitra_transaction_session_mapping_id'
  }

  /**
   *
   * @param {{session_id:string,connectionRead : Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async fetchTransactionIds ({ session_id, connectionRead }) {
    log.logger({ pagename: 'emitraController.js', action: 'fetchTransactionIds', type: 'request', fields: session_id })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const transactionIdQuery = `SELECT ma_transaction_master_id,response FROM emitra_transaction_session_mapping WHERE session_id = '${session_id}'`
      const transactionIdResult = await this.rawQuery(transactionIdQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransferDetails', type: 'transactionIdResult', fields: transactionIdResult })

      if (transactionIdResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transactionIdResult }
    } catch (error) {
      log.logger({ pagename: 'customerController.js', action: 'createKycChargeTransaction', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{insertData : Array<string>}} param0
   */
  static async massInsert ({ insertData, connection }) {
    log.logger({ pagename: 'emitraController.js', action: 'fetchTransactionIds', type: 'request', fields: insertData })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connection
    try {
      await this.insertBulk(conn, { fields: '(`session_id`,`ma_transaction_master_id`,`aggregator_order_id`,`transaction_status`,`response`)', data: insertData.join(',') })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: 'customerController.js', action: 'createKycChargeTransaction', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }
}

module.exports = EmitraDmtTransferMapController
