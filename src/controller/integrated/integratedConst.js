const emitra_URL = 'https://emitraapp.rajasthan.gov.in/webServicesRepository'
module.exports = {
  // Emitra integration constants
  emitraTopupOrder: 'MAEMITRA',
  emitraTOPUPAmt: 5000000,
  emitraMerchantCode: 'AIRPAY1120',
  emitraThresholdAmount: 500000,
  entitytype: {
    2: 2,
    21: 2,
    6: 4,
    15: 4,
    17: 4,
    /* AEPS Emitra : Entity Type Added  */
    5: 2
  },
  emitraServices: {
    2: { serviceId: '7110', serviceName: 'Airpay DMT Service', channelCode: 'DMT' },
    21: { serviceId: '7130', serviceName: 'Airpay - Beneficiary Account verification', channelCode: 'DMT' },
    5: { serviceId: '7533', serviceName: 'Airpay AEPS Service', channelCode: 'AEPS' },
    15: { serviceId: '5419', serviceName: 'Airpay Insurance Service', channelCode: 'Insurance' },
    6: { serviceId: '5417', serviceName: 'Airpay DMT Service', channelCode: 'DMT' },
    17: { serviceId: '5417', serviceName: 'Airpay DMT Service', channelCode: 'DMT' }
  },
  revenueheds: {
    2: {
      Charges: 3082,
      APComm: 3102,
      EmitraComm: 3085
    },
    21: {
      APComm: 3082,
      EmitraComm: 3085
    },
    /* AEPS Emitra : Entity Type Added  */
    5: {
      Charges: 3082,
      APComm: 3085
    }
  },
  paymode: {
    5: '1091'
  },
  emitraOfficeCode: 'AIRPAY20',
  BACK_TO_BACK_TXN_URL: emitra_URL + '/backtobackTransactionWithEncryptionA',
  TXN_VERIFY: emitra_URL + '/getTokenVerifyNewProcessByRequestIdWithEncryption',
  RECEIPT_UPDATE: emitra_URL + '/updateTransactionPostingWithEncryption',
  ENCRYPTION: emitra_URL + '/emitraAESEncryption',
  DECRYPTION: emitra_URL + '/emitraAESDecryption',
  REFUND: emitra_URL + '/backendTransCancelByDepartmentWithEncryption'
}
/*
Service Name: Airpay DMT Service

Service ID: 5417

Office Code: RISLTESTHQ

Merchant Code: RISLTEST

Revenue Head:"863-0.00|865-10.00"

(863= DMT Head and 865 Emitra Commission Head)

Comp Type: 1
*/
