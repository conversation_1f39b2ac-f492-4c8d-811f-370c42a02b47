// const request = require('request');
var axios = require('axios')
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const util = require('../../util/util')
const common = require('../../util/common')
const emitraConst = require('./integratedConst')
const crypto = require('crypto')
const integrated = require('./integratedController')
const balanceController = require('../balance/balanceController')

class emitra extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_emitra_transaction_request'
  }

  static get PRIMARY_KEY () {
    return 'ma_emitra_transaction_request_id'
  }

  static async index (data, connection) {
    log.logger({ pagename: 'emitraController.js', action: 'index', type: 'request', fields: data })
    console.log('emitra controller index', data)
    var functionCode = data.action
    switch (functionCode) {
      case 'CREATETXN' : {
        console.log('Here is creating transaction emitra *****************')
        const res = await this.createTransaction(data, connection)
        return res
      }
      case 'POSTRECEIPT' : return await this.postReceipt(data, connection)
      case 'REFUND' : return await this.refundAPI(data, connection)
      default : return { status: 400, message: 'Product not integrated', respcode: 1001 }
    }
  }

  static async createTransaction (data, connection) {
    log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'request', fields: data })
    try {
      var getRevenueHead = {}
      var revenueHead1 = 0.00
      var totalAmount = data.amount
      data.transaction_type = data.transaction_type.toString()
      var cust_charges = 0
      var incentiveAmtWithTDS = 0
      var incentiveAmt = 0
      var airpayIncentive = 0
      var commType = '1'
      switch (data.transaction_type) {
        case '2' : {
          // Get RevenueHead
          const transfersDistribution = require('../incentive/transfersDistributionController')
          getRevenueHead = await transfersDistribution.getDistributionAllHeads(data, connection)
          log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: getRevenueHead })

          cust_charges = getRevenueHead[0].transaction_charges
          totalAmount = totalAmount + getRevenueHead[0].transaction_charges
          // console.log(totalAmount,"_",data.amount,"_",getRevenueHead[0].transaction_charges)

          for (let i = 1; i < getRevenueHead.length; i++) {
            if (getRevenueHead[i].user_type != 'SD') {
              incentiveAmt += (parseFloat(getRevenueHead[i].amount) - getRevenueHead[i].TDS)
              incentiveAmtWithTDS += parseFloat(getRevenueHead[i].amount)
            }
          }
          revenueHead1 = data.amount + cust_charges - incentiveAmt
          revenueHead1 = (Math.round(revenueHead1 * 100) / 100).toFixed(2)

          incentiveAmt = (Math.round(incentiveAmt * 100) / 100).toFixed(2)
          incentiveAmtWithTDS = (Math.round(incentiveAmtWithTDS * 100) / 100).toFixed(2)
          airpayIncentive = (Math.round((cust_charges - incentiveAmtWithTDS) * 100) / 100).toFixed(2)
          const totalCommissionAmount = parseFloat(airpayIncentive + incentiveAmtWithTDS).toFixed(2)
          data.amount = (Math.round(data.amount * 100) / 100).toFixed(2)
          // comment below code - as pe new change - 26-08-2024 
          // revenueHead1 = emitraConst.revenueheds[data.transaction_type].Charges + '-' + (data.amount) + '|' + emitraConst.revenueheds[data.transaction_type].APComm + '-' + airpayIncentive + '|' + emitraConst.revenueheds[data.transaction_type].EmitraComm + '-' + incentiveAmtWithTDS 
          revenueHead1 = emitraConst.revenueheds[data.transaction_type].Charges + '-' + (data.amount) + '|' + emitraConst.revenueheds[data.transaction_type].APComm + '-' + totalCommissionAmount + '|' + emitraConst.revenueheds[data.transaction_type].EmitraComm + '-' + incentiveAmtWithTDS 
          // revenueHead1 = emitraConst.revenueheds[data.transaction_type].Charges+ "-" +(data.amount)+ "|" +emitraConst.revenueheds[data.transaction_type].APComm+ "-3.00|" +emitraConst.revenueheds[data.transaction_type].EmitraComm+ "-5.00"
          commType = '1'
          break
        }
        case '21' : {
          // Get RevenueHead
          const beneVeriifyIncentive = require('../incentive/beneVerifyIncentiveController')
          getRevenueHead = await beneVeriifyIncentive.getDistributionAllHeads(data, connection)
          log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: getRevenueHead })

          totalAmount = getRevenueHead[0].transaction_charges
          // console.log(totalAmount,"_",data.amount,"_",getRevenueHead[0].transaction_charges)
          cust_charges = getRevenueHead[0].transaction_charges
          for (let i = 1; i < getRevenueHead.length; i++) {
            if (getRevenueHead[i].user_type != 'SD') {
              incentiveAmt += (parseFloat(getRevenueHead[i].amount) - getRevenueHead[i].TDS)
              incentiveAmtWithTDS += parseFloat(getRevenueHead[i].amount)
            }
          }
          revenueHead1 = data.amount + cust_charges - incentiveAmt
          revenueHead1 = (Math.round(revenueHead1 * 100) / 100).toFixed(2)
          data.amount = (Math.round(data.amount * 100) / 100).toFixed(2)
          incentiveAmt = (Math.round(incentiveAmt * 100) / 100).toFixed(2)
          incentiveAmtWithTDS = (Math.round(incentiveAmtWithTDS * 100) / 100).toFixed(2)
          airpayIncentive = (Math.round((cust_charges - incentiveAmtWithTDS) * 100) / 100).toFixed(2)
          revenueHead1 = emitraConst.revenueheds[data.transaction_type].APComm + '-' + airpayIncentive + '|' + emitraConst.revenueheds[data.transaction_type].EmitraComm + '-' + incentiveAmtWithTDS
          commType = '1'
          // emitraConst.revenueheds[data.transaction_type].Charges+ "-" +(data.amount)+ "|" +
          break
        }
        /* AEPS EMITRA CHANGES */
        case '5' : {
          const aepsDistribution = require('../incentive/aepsDistributionController')
          /* NEW APES CHANGES RELATED AEPS TRANSACTION MODE */
          data.transaction_mode = '1'
          getRevenueHead = await aepsDistribution.getDistributionAllHeads(data)

          cust_charges = getRevenueHead[0].transaction_charges
          totalAmount = totalAmount + getRevenueHead[0].transaction_charges
          // console.log(totalAmount,"_",data.amount,"_",getRevenueHead[0].transaction_charges)

          for (var i = 1; i < getRevenueHead.length; i++) {
            if (getRevenueHead[i].user_type != 'SD') {
              incentiveAmt += (parseFloat(getRevenueHead[i].amount) - getRevenueHead[i].TDS)
              incentiveAmtWithTDS += parseFloat(getRevenueHead[i].amount)
            }
          }
          revenueHead1 = data.amount + cust_charges - incentiveAmt
          revenueHead1 = (Math.round(revenueHead1 * 100) / 100).toFixed(2)

          incentiveAmt = (Math.round(incentiveAmt * 100) / 100).toFixed(2)
          incentiveAmtWithTDS = (Math.round(incentiveAmtWithTDS * 100) / 100).toFixed(2)
          airpayIncentive = (Math.round((cust_charges - incentiveAmtWithTDS) * 100) / 100).toFixed(2)
          data.amount = (Math.round(data.amount * 100) / 100).toFixed(2)
          revenueHead1 = `${emitraConst.revenueheds[data.transaction_type].Charges}-${data.amount}|${emitraConst.revenueheds[data.transaction_type].APComm}-${incentiveAmtWithTDS}`
          commType = '3'
          break
        }
        case '15':
        case '6' :
        case '17': revenueHead1 = data.amount
          revenueHead1 = '863-' + (revenueHead1)
          break
      }

      // Get SSO session details
      const SQLquery = `SELECT * from ma_integration_request where retailer_user_id='${data.aggregator_user_id}' and request_status = 'active' order by ma_integration_request_id desc limit 1`
      const ssoDetails = await this.rawQuery(SQLquery, connection)
      log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: ssoDetails })
      // Return if no valid token available
      if (ssoDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: 'No valid token found' }
      }

      // Get current timestamp
      const timestampCur = await this.getTimestamp()
      log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: timestampCur })

      // Create Request Checksum
      const stringHere = '{"SSOID":"' + ssoDetails[0].retailer_user_id + '","REQUESTID":"' + data.aggregator_order_id + '","REQTIMESTAMP":"' + timestampCur + '","SSOTOKEN":"' + ssoDetails[0].retailer_token + '"}'
      const checksum = await crypto.createHash('md5').update(stringHere).digest('hex')
      log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: checksum })
      // "REVENUEHEAD"   : "863-"+(revenueHead1)+"|865-"+incentiveAmtWithTDS,
      // Create transaction Request
      const connectionnew = await mySQLWrapper.getConnectionFromPool()
      const emitrareqins = {
        aggregator_order_id: data.aggregator_order_id,
        ma_user_id: data.ma_user_id,
        transaction_status: 'CANCEL',
        transaction_type: data.transaction_type,
        ssotoken: ssoDetails[0].retailer_token,
        service_id: '' + (util.aeps_emitra_reg_service == ssoDetails[0].service_id ? ssoDetails[0].service_id : emitraConst.emitraServices[data.transaction_type].serviceId)
      }
      const respinsert = await this.insert(connectionnew, {
        data: emitrareqins
      })
      connectionnew.release()
      const txnReq = {
        MERCHANTCODE: emitraConst.emitraMerchantCode,
        REQUESTID: data.aggregator_order_id,
        REQTIMESTAMP: timestampCur,
        SERVICEID: '' + (util.aeps_emitra_reg_service == ssoDetails[0].service_id ? ssoDetails[0].service_id : emitraConst.emitraServices[data.transaction_type].serviceId),
        SUBSERVICEID: '',
        REVENUEHEAD: revenueHead1,
        CONSUMERKEY: `${ssoDetails[0].retailer_user_id}-${data.aggregator_order_id}`,
        CONSUMERNAME: `${ssoDetails[0].retailer_user_id}`,
        COMMTYPE: commType,
        SSOID: ssoDetails[0].retailer_user_id,
        OFFICECODE: emitraConst.emitraOfficeCode,
        SSOTOKEN: ssoDetails[0].retailer_token,
        CHECKSUM: checksum
      }
      /* AEPS EMITRA CHANGES  */
      if (data.transaction_type == '5') {
        txnReq.PAYMODE = emitraConst.paymode[data.transaction_type] || ''
        txnReq.BANKREFNUMBER = data.aggregator_order_id
      }
      const res = await this.emitraTransaction(txnReq)
      log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: res })
      // success
      if (res.status == 200) {
        // const connectionupd = await mySQLWrapper.getConnectionFromPool()
        // const Updreqstatus = await this.rawQuery(`UPDATE ma_emitra_transaction_request set transaction_status='SUCCESS' where ma_emitra_transaction_request_id = '${respinsert.insertId}'`,connectionupd)
        // connectionupd.release()
        // Update transaction id

        const UpdateRes = await this.rawQuery(`UPDATE ma_transaction_master set transaction_id=${res.response.TRANSACTIONID} where aggregator_order_id = '${data.aggregator_order_id}'`, connection)

        const emitraId = await this.rawQuery('SELECT dt.profileid,dt.userid FROM ma_user_master dt join ma_user_master rt on dt.profileid=rt.distributer_user_master_id where rt.profileid=' + data.ma_user_id + ' limit 1', connection)
        /* AEPS EMITRA CHANGES : SEND MONEY NOT REQUIRED */
        if (data.transaction_type == '5') {
          return res
        }

        // sendMoney
        const sendMoneyData = {
          distributorId: emitraId[0].profileid,
          retailerId: data.ma_user_id,
          amount: totalAmount,
          orderid: data.aggregator_order_id,
          commissionType: 3,
          ma_status: 'S',
          userid: emitraId[0].userid
        }
        const sendMoney = await integrated.sendMoney(sendMoneyData, connection)
        console.log('Response of send Money *******************************', sendMoney)
        if (sendMoney.status == 400) {
          // Call refund api of emitra
          const refund = await this.refundAPI(data, connection)
        }
      }
      return res
    } catch (err) {
      console.log('Error happended hereeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', err)
      log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: err })
      /* Issue Fix : Error Object  */
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async generateOrderId () {
    const random = await this.generateOTP(4)
    const timestamp = await this.getExpiry('')
    const orderid = `${emitraConst.emitraTopupOrder}${random}${timestamp}`
    log.logger({ pagename: 'emitraController.js', action: 'generateOrderId', type: 'response', fields: orderid })
    return orderid
  }

  static async emitraEncryption (data) {
    const testdata = 'toBeEncrypt=' + JSON.stringify(data)

    var res = await axios({
      method: 'post',
      url: emitraConst.ENCRYPTION,
      data: testdata,
      headers: {
        'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
      }
    })
    console.log('Encrypted', res.data)
    return res.data
  }

  static async emitraDecryption (data) {
    // return data;
    const testdata = 'toBeDecrypt=' + data

    var res = await axios({
      method: 'post',
      url: emitraConst.DECRYPTION,
      data: testdata,
      headers: {
        'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
      }
    })
    console.log('Decrypted', res.data)
    return res.data
  }

  // Function to call Emitra's Back to Back Transaction API
  static async emitraTransaction (data) {
    log.logger({ pagename: 'emitraController.js', action: 'emitraTransaction', type: 'request', fields: data })

    try {
      // Encrypt the above request
      const encData = await common.encryptEmitra(JSON.stringify(data), util.emitrapass)
      const testdata = 'encData=' + encData
      console.log('Encrypted Request of string', JSON.stringify(data))
      log.logger({ pagename: 'emitraController.js', action: 'emitraTransaction', type: 'response', fields: testdata })

      // Call emitra Transaction API
      var res = await axios({
        method: 'post',
        url: emitraConst.BACK_TO_BACK_TXN_URL,
        data: testdata,
        timeout: 20000, // 20 seconds timeout
        headers: {
          'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
        }
      })
      log.logger({ pagename: 'emitraController.js', action: 'emitraTransaction', type: 'response', fields: res.data })

      // Handle Response from API
      if (res.data != '') {
      // Decrypt the request
        var resDec = await common.decryptEmitra(res.data, util.emitrapass)
        resDec = JSON.parse(resDec)
        console.log('Decrypted Request')
        log.logger({ pagename: 'emitraController.js', action: 'emitraTransaction', type: 'response', fields: resDec })

        // Verify response checksum
        if (resDec.TRANSACTIONID.length > 0 && resDec.EMITRATIMESTAMP.length > 0) {
        // Create response checksum
          var resStr = '{"SSOID": "' + data.SSOID + '","TRANSACTIONID": "' + resDec.TRANSACTIONID + '","EMITRATIMESTAMP": "' + resDec.EMITRATIMESTAMP + '","SSOTOKEN": "' + data.SSOTOKEN + '"}'
          const checksum = await crypto.createHash('md5').update(resStr).digest('hex')
          console.log('Checksum of response ___________', resStr, checksum)
          // Compare checksums
        /* if('CHECKSUM' in resDec && checksum != resDec.CHECKSUM) {
                    return {status:400, respcode:1001, message:errorMsg.responseCode[1001]+'- Invalid checksum'}
                } */
        } else {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '- Mandatory Fields missing' }
        }

        // Transaction Success
        if ('TRANSACTIONSTATUS' in resDec) {
          if (resDec.TRANSACTIONSTATUS.length > 0) {
            if (resDec.TRANSACTIONSTATUS == 'SUCCESS') {
            // Return the response for future use
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], response: resDec }
            }
          }
        }

        if ('TRANSACTIONSTATUSCODE' in resDec) {
          if (resDec.TRANSACTIONSTATUSCODE.length > 0) {
            if (resDec.TRANSACTIONSTATUSCODE == '307') {
              return { status: 401, message: errorMsg.responseCode[1009] + ' - SSO ID / SSO Token', respcode: 1009 }
            }
          }
        }

        // Call transaction Verification API
        const verifyres = await this.transactionVerification(data)
        log.logger({ pagename: 'emitraController.js', action: 'transactionVerification', type: 'response', fields: verifyres })
        return verifyres
      }
      // Empty Response from emitra
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - Empty Response' }
    } catch (error) {
      log.logger({ pagename: 'emitraController.js', action: 'emitraTransaction', type: 'catcherror', fields: error })
      // Incase of failure always verification the transaction
      const verifyres = await this.transactionVerification(data)
      log.logger({ pagename: 'emitraController.js', action: 'transactionVerification', type: 'response', fields: verifyres })
      return verifyres
    }
  }

  // Function to Call Emitra's Transaction Verification API
  static async emitraTransactionVerification (data) {
    log.logger({ pagename: 'emitraController.js', action: 'emitraTransactionVerification', type: 'request', fields: data })
    try {
      // Encrypt the above request
      const encData = await common.encryptEmitra(JSON.stringify(data), util.emitrapass)
      const testdata = 'encData=' + encData
      console.log('Encrypted Request', JSON.stringify(data))
      log.logger({ pagename: 'emitraController.js', action: 'emitraTransactionVerification', type: 'response', fields: testdata })

      // Call transaction verification API
      var res = await axios({
        method: 'post',
        url: emitraConst.TXN_VERIFY,
        data: testdata,
        timeout: 10000, // 10 seconds timeout
        headers: {
          'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
        }
      })
      log.logger({ pagename: 'emitraController.js', action: 'emitraTransactionVerification', type: 'response', fields: res.data })

      // Handle response
      if (res.data != '') {
        // Decrypt the request
        var resDec = await common.decryptEmitra(res.data, util.emitrapass)
        resDec = JSON.parse(resDec)
        console.log('Decrypted Request')
        log.logger({ pagename: 'emitraController.js', action: 'emitraTransactionVerification', type: 'response', fields: resDec })

        // Verify Response Checksum
        if (resDec.RECEIPTNO.length > 0 && resDec.EMITRATIMESTAMP.length > 0) {
          var resStr = '{"RECEIPTNO":"' + resDec.RECEIPTNO + '","REQUESTID":"' + data.REQUESTID + '","EMITRATIMESTAMP":"' + resDec.EMITRATIMESTAMP + '","SSOTOKEN":"' + data.SSOTOKEN + '"}'
          const checksum = await crypto.createHash('md5').update(resStr).digest('hex')
          console.log('Checksum of response ___________', checksum)
          // Compare checksums
          // if('CHECKSUM' in resDec && checksum != resDec.CHECKSUM) {
          //     return {status:400, respcode:1001, message:errorMsg.responseCode[1001]+'- Invalid checksum'}
          // }
        }

        // Check Transaction Status
        if ('TRANSACTIONSTATUS' in resDec) {
          if (resDec.TRANSACTIONSTATUS.length > 0) {
            if (resDec.TRANSACTIONSTATUS == 'SUCCESS') {
              // Return the response for future use
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], response: resDec }
            } else if (resDec.TRANSACTIONSTATUS == 'FAILURE') {
              // Return Failure response
              var emitraError = errorMsg.responseCode[1001] + '- Transcation Failed at emitra'
              if ('MSG' in resDec && resDec.MSG.length > 0) {
                emitraError = emitraError + ' - ' + resDec.MSG
              }
              return { status: 400, message: emitraError, respcode: 1001 }
            } else {
              // Return if any other status is provided
              return { status: 400, message: errorMsg.responseCode[1001] + '- Unknown Transcation Status', respcode: 1001 }
            }
          } else {
            // Empty transaction status
            return { status: 400, message: errorMsg.responseCode[1001] + '- Empty Transcation Status', respcode: 1001 }
          }
        } else {
          // Transaction status key missing in response
          return { status: 400, message: errorMsg.responseCode[1001] + '- Missing Transcation Status', respcode: 1001 }
        }
      }
      // Empty Response from emitra
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - Empty Response' }
    } catch (err) {
      log.logger({ pagename: 'emitraController.js', action: 'emitraTransactionVerification', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - ' + err }
    }
  }

  // Function to Post receipt
  static async emitraPostReceipt (data) {
    log.logger({ pagename: 'emitraController.js', action: 'emitraPostReceipt', type: 'request', fields: data })

    // Encrypt the above request
    const encData = await common.encryptEmitra(JSON.stringify(data), util.emitrapass)
    const testdata = 'encData=' + encData
    console.log('Encrypted Request', JSON.stringify(data))
    log.logger({ pagename: 'emitraController.js', action: 'emitraPostReceipt', type: 'response', fields: testdata })

    // Call emitra Transaction API
    var res = await axios({
      method: 'post',
      url: emitraConst.RECEIPT_UPDATE,
      data: testdata,
      timeout: 10000, // 10 seconds timeout
      headers: {
        'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
      }
    })
    log.logger({ pagename: 'emitraController.js', action: 'emitraPostReceipt', type: 'response', fields: res.data })

    // Handle Response from API
    if (res.data != '') {
      // Decrypt the request
      var resDec = await common.decryptEmitra(res.data, util.emitrapass)
      resDec = JSON.parse(resDec)
      console.log('Decrypted Request')
      log.logger({ pagename: 'emitraController.js', action: 'emitraPostReceipt', type: 'response', fields: resDec })

      if ('STATUSCODE' in resDec) {
        if (resDec.STATUSCODE == '200') {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        } else if ('MSG' in resDec) {
          return { status: 400, respcode: 1001, message: resDec.MSG }
        }
      }
    }

    return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
  }

  static async postReceipt (data, connection) {
    log.logger({ pagename: 'emitraController.js', action: 'postReceipt', type: 'request', fields: data })
    try {
      // Get the Txn details
      const txnDetails = await this.rawQuery(`Select * from ma_transaction_master where aggregator_order_id='${data.aggregator_order_id}'`, connection)

      if (txnDetails.length <= 0) {
        return { status: 400, message: 'Not found transcation', respcode: 1001 }
      }

      // Get SSO session details
      const SQLquery = `SELECT * from ma_integration_request where retailer_user_id='${data.aggregator_user_id}' and request_status = 'active' order by ma_integration_request_id desc limit 1`
      const ssoDetails = await this.rawQuery(SQLquery, connection)
      log.logger({ pagename: 'emitraController.js', action: 'postReceipt', type: 'response', fields: ssoDetails })
      // Return if no valid token available
      if (ssoDetails.length <= 0) {
        return { status: 400, respcode: 1001, message: 'No valid token found' }
      }

      // Get current timestamp
      const timestampCur = await this.getTimestamp()
      log.logger({ pagename: 'emitraController.js', action: 'postReceipt', type: 'response', fields: timestampCur })

      // Create Request Checksum
      const stringHere = '{"REQUESTID":"' + data.aggregator_order_id + '","TRANSACTIONID":"' + txnDetails[0].transaction_id + '","APPLICATIONID":"' + txnDetails[0].ma_transaction_master_id + '","SSOTOKEN":"' + ssoDetails[0].retailer_token + '"}'
      console.log('Checksum str', stringHere)
      const checksum = await crypto.createHash('md5').update(stringHere).digest('hex')
      log.logger({ pagename: 'emitraController.js', action: 'createTransaction', type: 'response', fields: checksum })

      const postData = {
        MERCHANTCODE: emitraConst.emitraMerchantCode,
        REQUESTID: data.aggregator_order_id,
        REQTIMESTAMP: timestampCur,
        TRANSACTIONID: `${txnDetails[0].transaction_id}`,
        APPLICATIONID: `${txnDetails[0].ma_transaction_master_id}`,
        SSOTOKEN: ssoDetails[0].retailer_token,
        CHECKSUM: checksum
      }

      const res = await this.emitraPostReceipt(postData)
      if (res.status == 200) {
        const UpdateResStatus = await this.rawQuery(`UPDATE ma_transaction_master set application_post_status= 'Y',application_post_count = application_post_count + 1  where aggregator_order_id = '${data.aggregator_order_id}'`, connection)
      } else {
        const UpdateResStatus = await this.rawQuery(`UPDATE ma_transaction_master set application_post_count = application_post_count + 1  where aggregator_order_id = '${data.aggregator_order_id}'`, connection)
      }
      log.logger({ pagename: 'emitraController.js', action: 'postReceipt', type: 'response', fields: res })
      return res
    } catch (err) {
      log.logger({ pagename: 'emitraController.js', action: 'postReceipt', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - ' + err }
    }
  }

  static async transactionVerification (data) {
    log.logger({ pagename: 'emitraController.js', action: 'transactionVerification', type: 'request', fields: data })
    try {
      // Request Checksum
      data.SSOTOKEN = '0'
      const str = '{"MERCHANTCODE":"' + data.MERCHANTCODE + '","REQUESTID":"' + data.REQUESTID + '","SSOTOKEN":"' + data.SSOTOKEN + '"}'
      const checksum = await crypto.createHash('md5').update(str).digest('hex')
      log.logger({ pagename: 'emitraController.js', action: 'transactionVerification', type: 'response', fields: checksum })

      const txnReq = {
        MERCHANTCODE: data.MERCHANTCODE,
        SERVICEID: data.SERVICEID,
        REQUESTID: data.REQUESTID,
        SSOTOKEN: data.SSOTOKEN,
        CHECKSUM: checksum
      }

      const res = await this.emitraTransactionVerification(txnReq)
      log.logger({ pagename: 'emitraController.js', action: 'transactionVerification', type: 'response', fields: res })
      return res
    } catch (err) {
      log.logger({ pagename: 'emitraController.js', action: 'transactionVerification', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - ' + err }
    }
  }

  static async refundAPI (data, connection) {
    log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'request', fields: data })
    try {
      const txnDetails = await this.rawQuery(`Select * from ma_transaction_master where aggregator_order_id='${data.aggregator_order_id}'`, connection)

      if (txnDetails.length <= 0) {
        return { status: 400, message: 'Not found transcation', respcode: 1001 }
      }

      const stringHere = '{"MERCHANTCODE":"' + emitraConst.emitraMerchantCode + '","REQUESTID":"' + data.aggregator_order_id + '","SSOTOKEN":"0"}'
      console.log('Checksum str', stringHere)
      const checksum = await crypto.createHash('md5').update(stringHere).digest('hex')
      log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'response', fields: checksum })

      const postData = {
        MERCHANTCODE: emitraConst.emitraMerchantCode,
        REQUESTID: data.aggregator_order_id,
        EMITRATOKEN: `${txnDetails[0].transaction_id}`,
        CANCELREMARK: 'Cancelled From Bank',
        ENTITYTYPEID: `${emitraConst.entitytype[txnDetails[0].transaction_type]}`,
        SSOTOKEN: '0',
        CHECKSUM: checksum
      }

      // Encrypt the above request
      const encData = await common.encryptEmitra(JSON.stringify(postData), util.emitrapass)
      const testdata = 'encData=' + encData
      console.log('Encrypted Request', JSON.stringify(postData))
      log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'response', fields: testdata })

      // Call emitra Refund API
      var res = await axios({
        method: 'post',
        url: emitraConst.REFUND,
        data: testdata,
        timeout: 10000, // 10 seconds timeout
        headers: {
          'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
        }
      })
      log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'response', fields: res.data })

      // Handle Response from API
      if (res.data != '') {
        // Decrypt the request
        var resDec = await common.decryptEmitra(res.data, util.emitrapass)
        resDec = JSON.parse(resDec)
        console.log('Decrypted Request')
        log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'response', fields: resDec })

        if ('CANCELSTATUSCODE' in resDec) {
          if (resDec.CANCELSTATUSCODE == '200') {
            /* UPDATE CANCEL API STATUS */
            const updateResult = await this.rawQuery(`UPDATE ma_emitra_transaction_request set cancel_api_status='C' where aggregator_order_id = '${data.aggregator_order_id}'`, connection)
            log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'update cancel api', fields: updateResult })

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
          } else if ('MSG' in resDec) {
            return { status: 400, respcode: 1001, message: resDec.MSG }
          }
        }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ': Refund Failed At Emitra' }
    } catch (err) {
      log.logger({ pagename: 'emitraController.js', action: 'refundAPI', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - ' + err }
    }
  }

  static async receiptPostCron () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = 'SELECT tm.aggregator_order_id,u.aggregator_user_id,tm.ma_user_id FROM ma_transaction_master tm JOIN users u ON u.user_type = \'integrated\' AND tm.userid = u.id JOIN ma_integration_user_master ium ON u.profileid = ium.ma_user_id AND ium.integration_code = \'EMITRA\' WHERE  tm.transaction_type in (\'2\',\'6\',\'15\',\'17\',\'21\') AND  tm.transaction_status = \'S\' AND tm.application_post_status = \'N\' AND tm.application_post_count < 3 AND tm.addedon>DATE_SUB(NOW(), INTERVAL 45 MINUTE) AND tm.addedon < DATE_SUB(NOW(), INTERVAL 10 MINUTE)'
      console.log('RECEIPT POST CRON', sql)
      const result = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'emitraController.js', action: 'receiptPostCron', type: 'response', fields: result })
      if (result.length > 0) {
        for (var k = 0; k < result.length; k++) {
          var receiptData = {}
          receiptData.aggregator_user_id = result[k].aggregator_user_id
          receiptData.aggregator_order_id = result[k].aggregator_order_id
          receiptData.ma_user_id = result[k].ma_user_id
          await this.postReceipt(receiptData, connection)
        }
      }
    } catch (err) {
      log.logger({ pagename: 'emitraController.js', action: 'receiptPostCron', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' - ' + err }
    } finally {
      connection.release()
    }
  }

  static async getTimestamp () {
    const today = new Date()
    var cMonth = today.getMonth() + 1
    if (cMonth < 10) {
      cMonth = `0${cMonth}`
    }
    var cDay = today.getDate()
    if (cDay < 10) {
      cDay = `0${cDay}`
    }
    const dayCondition = `${today.getFullYear()}${cMonth}${cDay}${today.getHours()}${today.getMinutes()}${today.getSeconds()}${today.getMilliseconds()}`
    return dayCondition
  }

  static async cancleTransaction (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // call verify api
      const transaction_type = fields.transaction_type

      const reqdata = {
        MERCHANTCODE: emitraConst.emitraMerchantCode,
        SERVICEID: '' + fields.service_id,
        REQUESTID: fields.aggregator_order_id,
        SSOTOKEN: fields.ssotoken
      }
      const verifyres = await this.transactionVerification(reqdata)
      if (verifyres.status == 200) {
        if (verifyres.response.VERIFYSTATUSCODE == 200) {
          // return await this.refundAPI(fields,connection)
          const stringHere = '{"MERCHANTCODE":"' + emitraConst.emitraMerchantCode + '","REQUESTID":"' + fields.aggregator_order_id + '","SSOTOKEN":"0"}'
          console.log('Checksum str', stringHere)
          const checksum = await crypto.createHash('md5').update(stringHere).digest('hex')
          log.logger({ pagename: 'emitraController.js', action: 'cancleTransaction', type: 'response', fields: checksum })
          const transaction_id = verifyres.response.TRANSACTIONID
          const postData = {
            MERCHANTCODE: emitraConst.emitraMerchantCode,
            REQUESTID: fields.aggregator_order_id,
            EMITRATOKEN: `${transaction_id}`,
            CANCELREMARK: 'Cancelled From Bank',
            ENTITYTYPEID: `${emitraConst.entitytype[transaction_type]}`,
            SSOTOKEN: '0',
            CHECKSUM: checksum
          }

          // Encrypt the above request
          const encData = await common.encryptEmitra(JSON.stringify(postData), util.emitrapass)
          const testdata = 'encData=' + encData
          console.log('Encrypted Request', JSON.stringify(postData))
          log.logger({ pagename: 'emitraController.js', action: 'cancleTransaction', type: 'response', fields: testdata })

          // Call emitra Refund API
          var res = await axios({
            method: 'post',
            url: emitraConst.REFUND,
            data: testdata,
            headers: {
              'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          })
          log.logger({ pagename: 'emitraController.js', action: 'cancleTransaction', type: 'response', fields: res.data })

          // Handle Response from API
          if (res.data != '') {
            // Decrypt the request
            var resDec = await common.decryptEmitra(res.data, util.emitrapass)
            resDec = JSON.parse(resDec)
            console.log('Decrypted Request')
            log.logger({ pagename: 'emitraController.js', action: 'cancleTransaction', type: 'response', fields: resDec })

            if ('CANCELSTATUSCODE' in resDec) {
              if (resDec.CANCELSTATUSCODE == '200') {
                return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
              } else if ('MSG' in resDec) {
                return { status: 400, respcode: 1001, message: resDec.MSG }
              }
            }
          }
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ': Refund Failed At Emitra' }
        }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ': Refund Failed At Emitra' }
      }
    } catch (err) {
      log.logger({ pagename: 'emitraController.js', action: 'cancleTransaction', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {{limit:number}} fields
   * @returns
   */
  static async processPendingTransaction (_, fields) {
    log.logger({ pagename: 'emitraController.js', action: 'processPendingTransaction', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* fetch all the last 'X' mins pending request from 'ma_emitra_transaction_request' */
      const limit = fields.limit || 10
      const fetchPendingRequestsQuery = `SELECT metr.* FROM ma_emitra_transaction_request metr LEFT JOIN ma_transaction_master mtm ON mtm.aggregator_order_id = metr.aggregator_order_id  WHERE metr.cancel_api_status = 'P' AND mtm.transaction_status = 'F' LIMIT ${limit}`
      // let fetchPendingRequestsQuery = 'SELECT * FROM ma_emitra_transaction_request WHERE cancel_api_status = \'P\' ORDER BY addedon DESC LIMIT 2'
      const fetchPendingRequests = await this.rawQuery(fetchPendingRequestsQuery, connection)
      if (fetchPendingRequests.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1000 }
      /* process request */
      const cancelRequests = fetchPendingRequests.map(pendingOrder => {
        const data = pendingOrder
        return this.refundAPI(data, connection)
      })
      /* REFUND API CALLs */
      const refundAPIResponses = await Promise.all(cancelRequests)
      log.logger({ pagename: 'emitraController.js', action: 'processPendingTransaction', type: 'refundAPIResponses', fields: refundAPIResponses })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: 'emitraController.js', action: 'processPendingTransaction', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }
}

module.exports = emitra
