const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const pointsLedger = require('../creditDebit/pointsLedgerController')

class integrated extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_integration_user_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_integration_user_master_id'
  }

  static async index (data, connection) {
    console.log('INtegrated index')
    // Get the code for integrated product
    var integratedCode = await this.rawQuery(`SELECT integration_code from ma_integration_user_master where  ma_user_id= ${data.ma_user_id}`, connection)

    console.log('Details ghere................', `SELECT integration_code from ma_integration_user_master where  ma_user_id= ${data.ma_user_id}`, integratedCode)
    // var res = {}
    if (integratedCode.length > 0) {
      switch (integratedCode[0].integration_code) {
        case 'EMITRA' : {
          const emitra = require('./emitraController')
          const res = await emitra.index(data, connection)
          console.log('emitra response', res)
          return res
        }
        default : return { status: 400, message: 'Product not integrated', respcode: 1001 }
      }
    }
    return { status: 200, message: 'Integration settings are missing', respcode: 1001 }
  }

  static async sendMoney (data, connection) {
    log.logger({ pagename: 'integratedController.js', action: 'sendMoney', type: 'request', fields: data })
    try {
      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: data.commissionType }, connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      let refundText = ''
      if (data.ma_status == 'R') {
        refundText = ' - Refund'
      } else if (data.ma_status == 'REV') {
        refundText = ' - Reverse'
      }
      const debitDesc = 'Debit - ' + descType + refundText
      const creditDesc = 'Credit - ' + descType + refundText

      // Debit from Distributer
      const distributorLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: data.distributorId,
        amount: data.amount,
        mode: 'dr',
        transaction_type: data.commissionType,
        description: debitDesc,
        ma_status: data.ma_status,
        orderid: data.orderid,
        userid: data.userid,
        corresponding_id: data.retailerId,
        connection
      })
      if (distributorLedgerId.status === 400) {
        return distributorLedgerId
      }

      // Credit In Retailer after subtracting  commission
      const retailerLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: data.retailerId,
        amount: data.amount,
        mode: 'cr',
        transaction_type: data.commissionType,
        ma_status: data.ma_status,
        userid: data.userid,
        description: creditDesc,
        orderid: data.orderid,
        corresponding_id: data.distributorId,
        connection
      })
      if (retailerLedgerId.status === 400) {
        return retailerLedgerId
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: 'integratedController.js', action: 'sendMoney', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.sqlMessage }
    }
  }

  static async sendMoneyReverse (data, connection) {
    log.logger({ pagename: 'integratedController.js', action: 'sendMoneyReverse', type: 'request', fields: data })
    try {
      const sendMoneyRec = await this.rawQuery('SELECT ma_user_id,amount,userid from ma_points_ledger_master where transaction_type = "3" AND parent_id="' + data.aggregator_order_id + '"', connection)
      log.logger({ pagename: 'integratedController.js', action: 'sendMoneyRev', type: 'request', fields: { sendMoneyRec } })
      if (sendMoneyRec.length == 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
      const emitraId = await this.rawQuery('SELECT dt.profileid,dt.userid FROM ma_user_master dt join ma_user_master rt on dt.profileid=rt.distributer_user_master_id where rt.profileid=' + data.ma_user_id + ' limit 1', connection)
      log.logger({ pagename: 'integratedController.js', action: 'sendMoneyRev', type: 'emitraId', fields: { emitraId } })
      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '3' }, connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      const refundText = ' - Reverse'
      const debitDesc = 'Debit - ' + descType + refundText
      const creditDesc = 'Credit - ' + descType + refundText

      // Debit In Retailer
      const retailerLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: data.ma_user_id,
        amount: Math.abs(sendMoneyRec[0].amount),
        mode: 'dr',
        transaction_type: '3',
        ma_status: 'REV',
        userid: emitraId[0].userid,
        description: debitDesc,
        orderid: 'REV-' + data.aggregator_order_id,
        corresponding_id: emitraId[0].profileid,
        credit_reversal_status: 'Y',
        connection
      })
      if (retailerLedgerId.status === 400) {
        return retailerLedgerId
      }

      // Credit Fo Distributer
      const distributorLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: emitraId[0].profileid,
        amount: Math.abs(sendMoneyRec[0].amount),
        mode: 'cr',
        transaction_type: '3',
        description: creditDesc,
        ma_status: 'REV',
        orderid: 'REV-' + data.aggregator_order_id,
        userid: emitraId[0].userid,
        corresponding_id: data.ma_user_id,
        credit_reversal_status: 'Y',
        connection
      })
      if (distributorLedgerId.status === 400) {
        return distributorLedgerId
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: 'integratedController.js', action: 'sendMoneyReverse', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.sqlMessage }
    }
  }
}

module.exports = integrated
