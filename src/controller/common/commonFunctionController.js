const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const { indexChecksum } = require('../../util/checksum')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const common_fns = require('../../util/common_fns')

class commonFunctionController extends DAO {
  /**
   * checkOtherConfiguration :: this function use for incentive slabwise configuration
   * fields : sql, fields, connection
   */
  static async checkOtherConfiguration (fieldsData) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'request', fields: common_fns.maskValue(fieldsData, 'customer_aadhaar') })
    // Validate the required fields
    const validateFieldsResponse = await this.validateFields(fieldsData, ['sql', 'fields'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }

    // create connection
    const tempConnection = !fieldsData.connection
    const connection = fieldsData.connection || await mySQLWrapper.getConnectionFromPool()
    try {
      // validate that fieldsData.fields.distributer_user_master_id is not null, not undefied and not less than 1
      if (!validator.definedVal(fieldsData.fields.distributerIdForGetDistribution) || fieldsData.fields.distributerIdForGetDistribution <= 0) {
        if ('ma_user_id' in fieldsData.fields) {
          let checkDtId = ''
          if ('userid' in fieldsData.fields) {
            checkDtId = `SELECT distributer_user_master_id FROM ma_user_master where profileid = ${fieldsData.fields.ma_user_id} and userid = ${fieldsData.fields.userid} LIMIT 1`
          } else {
            checkDtId = `SELECT distributer_user_master_id FROM ma_user_master where profileid = ${fieldsData.fields.ma_user_id} LIMIT 1`
          }

          log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'sql - checkDtId', fields: checkDtId })
          const checkDtIdResp = await this.rawQuery(checkDtId, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'response - checkDtIdResp', fields: checkDtIdResp })
          if (checkDtIdResp.length > 0) fieldsData.fields.distributerIdForGetDistribution = checkDtIdResp[0].distributer_user_master_id
          if (fieldsData.fields.distributerIdForGetDistribution <= 0) return { status: 400, respcode: 1019, message: errorMsg.responseCode[1019] }
        } else {
          return { status: 400, respcode: 1019, message: errorMsg.responseCode[1019] }
        }
      }

      let configurationResponse = {}
      // Check for distributor specific configuration
      const dtSdtSql = fieldsData.sql + ` and ma_dt_sdt_id = '${fieldsData.fields.distributerIdForGetDistribution}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'sql - dtSdt Specific', fields: dtSdtSql })
      configurationResponse = await this.rawQuery(dtSdtSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'response - configurationResponse', fields: configurationResponse })

      // this distributerIdForGetDistribution field used only for commonFunction.checkOtherConfiguration function
      // Delete this distributerIdForGetDistribution key from the fields (fieldsData.fields) object because it affects others
      delete fieldsData.fields.distributerIdForGetDistribution
      // return success data if dtSdtSqlResponse length greater than 0
      if (configurationResponse.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], configurationData: configurationResponse }
      }
      return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * validateFields :: validate required fields
   * fields : fileds, requiredParams
   */
  static async validateFields (fields, requiredParams = []) {
    for (const requiredParam of requiredParams) {
      if (!validator.definedVal(fields[requiredParam])) {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateFields', type: 'invalid request missing params', fields: { missingParams: [requiredParam] } })
        return { status: 400, respcode: 1019, message: errorMsg.responseCode[1019] }
      }
    }
    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
  }

  /**
   * Generate checksum for Index API
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async generateIndexChecksum (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'generateIndexChecksum', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const request_params = JSON.parse(Buffer.from(fields.req_params, 'base64').toString('utf-8'))
      if (request_params.mercid == null || request_params.mercid == 'undefined') {
        return { status: 400, message: errorMsg.responseCode[1137], respcode: 1137 }
      }
      const postData = {
        buyerEmail: request_params.buyerEmail ? request_params.buyerEmail : '',
        buyerFirstName: request_params.buyerFirstName ? request_params.buyerFirstName : '',
        buyerLastName: request_params.buyerLastName ? request_params.buyerLastName : '',
        buyerAddress: request_params.buyerAddress ? request_params.buyerAddress : '',
        buyerCity: request_params.buyerCity ? request_params.buyerCity : '',
        buyerState: request_params.buyerState ? request_params.buyerState : '',
        buyerCountry: request_params.buyerCountry ? request_params.buyerCountry : '',
        amount: request_params.amount ? request_params.amount : '',
        orderid: request_params.orderid ? request_params.orderid : '',
        uid: request_params.uid ? request_params.uid : ''
      }
      const checksumData = {
        ma_user_id: request_params.mercid,
        ...postData
      }
      const checksumResponse = await indexChecksum(this, checksumData)
      if (checksumResponse.status != 200) {
        return { status: checksumResponse.status, respcode: checksumResponse.respcode, message: checksumResponse.message }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], checksum: checksumResponse.checksum }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkOtherConfiguration', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      connection.release()
    }
  }
}

module.exports = commonFunctionController
