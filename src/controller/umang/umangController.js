const DAO = require('../../lib/dao')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const apiConstants = require('./constants')
const util = require('../../util/util')
const common = require('../../util/common')
const path = require('path')
const validator = require('../../util/validator')
const errorEmail = require('../../util/errorHandler')

class Umang extends DAO {
  static async getUmangIframe (_, fields) {
    log.logger({ pagename: 'umangController.js', action: 'getUmangIframe', type: 'request', fields: fields })
    // Set Table
    this.TABLE_NAME = 'ma_umang_user_details'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sqlMerchant = `SELECT mobile FROM users WHERE profileid = '${fields.ma_user_id}' and id = '${fields.userid}' limit 1`
      const userDetails = await this.rawQuery(sqlMerchant, connection)
      if (userDetails.length > 0) {
        const iframeString = await common.getSystemCodes(this, util.umang_system_code_id, connection)
        log.logger({ pagename: path.basename(__filename), action: 'iframeString', type: 'iframeString-response', fields: iframeString })
        const iframeVal = iframeString + '?tenantId=' + apiConstants.tenantId + '&token=' + apiConstants.token + userDetails[0].mobile
        log.logger({ pagename: path.basename(__filename), action: 'getUmangIframe', type: 'response', fields: iframeVal })
        const _result = await this.insert(connection, {
          data: {
            ma_user_id: fields.ma_user_id,
            merchant_mobile_no: userDetails[0].mobile,
            umang_iframe_url: iframeVal
          }
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'response', fields: _result })

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], umang_iframe_url: iframeVal, action_code: 1000 }
      } else {
        return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
      }
    } catch (err) {
      log.logger({ pagename: 'umangController.js', action: 'getUmangIframe', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getUmangReceiptDetails (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getUmangReceiptDetails', type: 'request', fields: fields })

    // create a new connection for API
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT trans.ma_transaction_master_id, transDet.bank_name, trans.mobile_number as customer_mobile, mad.service_name, trans.transaction_id,  trans.transaction_status, CONCAT(u.firstname, ' ', u.lastname) AS customer_name, u.address, FROM_UNIXTIME(UNIX_TIMESTAMP(trans.addedon),'%d-%m-%Y %r') AS transaction_time,trans.transaction_reason,trans.aggregator_order_id,mad.reference_txn_id as aggregator_txn_id, trans.amount
                FROM ma_transaction_master AS trans
                LEFT JOIN ma_transaction_master_details AS transDet ON trans.ma_transaction_master_id = transDet.ma_transaction_master_id
                LEFT JOIN ma_assesstive_details  AS mad ON mad.reference_txn_id = transDet.transaction_id 
                LEFT JOIN ma_user_master as u ON mad.ma_user_id = u.profileid
                WHERE trans.ma_transaction_master_id = '${fields.ma_transaction_master_id}'
                AND trans.transaction_type = '46'`
      console.log(sql)
      const transactionDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getUmangReceiptDetails', type: 'response', fields: transactionDetails })

      if (transactionDetails.length > 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getUmangReceiptDetails', type: 'response', fields: transactionDetails[0] })
        const res = transactionDetails[0]

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          customer_name: res.customer_name,
          agent_id: res.customer_mobile,
          tracker_id: res.transaction_id,
          department_name: res.bank_name,
          service_name: res.service_name,
          transaction_status: res.transaction_status,
          address: res.address,
          transaction_time: res.transaction_time,
          transaction_reason: res.transaction_reason,
          aggregator_order_id: res.aggregator_order_id,
          aggregator_txn_id: res.aggregator_txn_id,
          amount: res.amount

        }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'getUmangReceiptDetails', type: 'response', fields: {} })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getUmangReceiptDetails', type: 'catcherror', fields: err })
      const data = Object.assign({}, fields)
      delete data.connection
      errorEmail.notifyCatchErrorEmail({
        function: 'getUmangReceiptDetails',
        data: data,
        error: err
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  static async submitPanApplication (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'submitPanApplication', type: 'request', fields: fields })

    // create a new connection for API
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Expect Body { full_name, mobile_number, dob, isNew: yes/no, request_source: WEB/APP }

      const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'full_name', 'mobile_number', 'dob', 'isNew', 'request_source'])
      if (validateTransferParamRes.status != 200) return validateTransferParamRes

      if (fields.request_source) (fields.request_source).toUpperCase()
      /** Validate Mobile Number */
      const validate = validator.validateMobile(fields.mobile_number, 'IN')
      if (validate.status === false) {
        return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033] }
      }
      /** Validate Date Of Birth */
      const validateDate = validator.validatedate(fields.dob)
      if (validateDate === false) {
        return { status: 400, respcode: 1024, message: errorMsg.responseCode[1024] }
      }
      /** Validate Customer Name */
      const isValidCustomerName = validator.validInput('name', fields.full_name)
      if (isValidCustomerName === false) {
        return { status: 400, respcode: 1169, message: errorMsg.responseCode[1169].replace('#name', 'Customer Name'), action_code: 1001 }
      }
      const type = ['Yes', 'yes', 'YES'].includes(fields.isNew) ? 'New' : 'Correction'

      const insertDetails = `INSERT INTO ma_customer_pan_details (ma_user_id,userid,full_name,mobile_number,request_source,dob,type) values('${fields.ma_user_id}', '${fields.userid}', '${fields.full_name}', '${fields.mobile_number}', '${fields.request_source}', '${fields.dob}', '${type}')`
      log.logger({ pagename: require('path').basename(__filename), action: 'submitPanApplication', type: 'insertDetails', fields: insertDetails })
      const insertDetailsResp = await this.rawQuery(insertDetails, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'submitPanApplication', type: 'insertDetailsResp', fields: insertDetailsResp })

      if (insertDetailsResp.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'submitPanApplication', type: 'response', fields: {} })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'submitPanApplication', type: 'catcherror', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'submitPanApplication',
        error: err
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

}

module.exports = Umang
