/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
// const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class users extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_user_profiles_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_profiles_master_id'
  }

  /**
     * Returns TABLE_NAME details by its PRIMARY_KEY
     */
  static async getByID (_, { id }) {
    // eslint-disable-next-line no-return-await
    return await this.find(id)
  }

  /**
     * Returns a list of neft details matching the passed fields
     * @param {*} fields - Fields to be matched
     */
  static async findMatching (_, fields) {
    // Returns early with all cars if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching cars
    return this.findByFields({
      fields
    })
  }

  /**
     * Creates a new entry in users table
     */
  // eslint-disable-next-line camelcase
  // static async createEntry (_, queryFields, queryData, connection = null) {
  static async createEntry (_, queryFields, connection = null) {
    log.logger({ pagename: 'usersController.js', action: 'createEntry', type: 'request', fields: queryFields })
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        console.log('createConnection')
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // const _result = await this.insertBulk(connection, { fields: queryFields, data: queryData })
      const _result = await this.insert(connection, {
        data: {
          ma_user_profiles_master_id: queryFields.profileid
        }
      })
      console.log('result', _result)
      if (_result.affectedRows > 0) {
        var result = {}
        result.status = 200
        result.message = 'success'
        result.ma_user_master_id = _result.insertId
        log.logger({ pagename: 'usersController.js', action: 'createEntry', type: 'response', fields: result })
        return result
      } else {
        return { status: 400, message: 'fail' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) {
        connection.release()
      }
    }
  }
}

module.exports = users
