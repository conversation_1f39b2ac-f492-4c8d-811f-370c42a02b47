const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const util = require('../../util/util')
const common = require('../../util/common')

class MccLevelSettingController extends DAO {
  /**
   *  activation_level : 1- asm approved
   */
  static get MCC_ACTIVATION_LEVEL_1 () {
    return '1'
  }

  /**
   *  activation_level : 2-ops activated
   */
  static get MCC_ACTIVATION_LEVEL_2 () {
    return '2'
  }

  /**
   *  Global MCC Code
   */
  static async getGlobalMccCode ({ connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const mcc_global_code = await common.getSystemCodes(this, util.mcc_global_code, conn)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, mcc_global_code }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getActivationLevel', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  static get TABLE_NAME () {
    return 'ma_mcc_level_settings'
  }

  static get PRIMARY_KEY () {
    return 'ma_mcc_level_settings_id'
  }

  /**
   *
   * @param {Object} fields
   * @returns
   */
  static async findMatching (fields) {
    log.logger({ pagename: 'customerKycController.js', action: 'findMatching', type: 'request', fields: fields })
    // Returns early with all transactions if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching transactions
    const res = await this.findByFields({
      fields
    })
    log.logger({ pagename: 'customerKycController.js', action: 'findMatching', type: 'response', fields: res })
    return res
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async getActivationLevel ({ ma_user_id, userid, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getActivationLevel', type: 'request', fields: { ma_user_id, userid } })
    try {
      /* Check Activation Level */
      const userActivationLevelQuery = `SELECT mcc,activation_level FROM ma_user_master WHERE profileid = ${ma_user_id} LIMIT 1`
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getActivationLevel', type: 'userActivationLevelQuery', fields: userActivationLevelQuery })
      const userActivationLevelResult = await this.rawQuery(userActivationLevelQuery, conn)
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getActivationLevel', type: 'userActivationLevelResult', fields: userActivationLevelResult })
      if (userActivationLevelResult.length === 0) return { status: 400, message: errorMsg.responseCode[1113], respcode: 1113, action_code: 1001 }

      const activation_level = userActivationLevelResult[0].activation_level
      const mcc = userActivationLevelResult[0].mcc
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, activation_level, mcc }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getActivationLevel', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{mcc_code:string,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async getAllowedChannels ({ mcc_code, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getAllowedChannels', type: 'request', fields: { mcc_code } })
    try {
      const channelCodeQuery = `SELECT channels FROM ${this.TABLE_NAME} WHERE mcc_code = ${mcc_code} LIMIT 1`
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getAllowedChannels', type: 'channelCodeQuery', fields: channelCodeQuery })
      const channelResult = await this.rawQuery(channelCodeQuery, conn)

      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getAllowedChannels', type: 'channelResult', fields: channelResult })
      if (channelResult.length === 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 }
      const { channels: channelList } = channelResult[0]
      const channels = channelList.split(',').map(x => x.toLowerCase())
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1001, channels }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getAllowedChannels', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async getChannelList ({ ma_user_id, userid, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getChannelList', type: 'request', fields: { ma_user_id, userid } })
    try {
      const mccLevelSettingResult = await this.getMccLevelSetting({ ma_user_id, userid, connection })
      if (mccLevelSettingResult.status != 200) {
        return mccLevelSettingResult
      }
      const { mcc_code } = mccLevelSettingResult.setting

      const allowedChannelResult = await this.getAllowedChannels({
        mcc_code,
        connection: conn
      })
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getChannelList', type: 'allowedChannelResult', fields: allowedChannelResult })
      if (allowedChannelResult.status != 200) return allowedChannelResult
      const channelList = allowedChannelResult.channels
      /* CONDITION : similar to getChannelFromAPI  */
      if (channelList.includes('collectmoney')) channelList.push('upi')

      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getChannelList', type: 'channelList', fields: channelList })
      /* MAPPED CHANNEL NAME WITH FRONTED CHANNEL NAME */
      const channel_data = channelList.map(channel => (util.channel_data_code[channel] || channel))
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getChannelList', type: 'channelList-UPDATE', fields: channel_data })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, channel_data, channelList }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getChannelList', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async getMccLevelSetting ({ ma_user_id, userid, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'request', fields: { ma_user_id, userid } })
    try {
      let mcc_code = ''
      const userMccLevelSettingQuery = `SELECT mcc,ma_mcc_level_settings_id FROM ma_user_master JOIN ma_mcc_level_settings ON ma_user_master.mcc = ma_mcc_level_settings.mcc_code  WHERE profileid=${ma_user_id} AND activation_level='${this.MCC_ACTIVATION_LEVEL_1}' LIMIT 1`
      const userMccLevelSettingResult = await this.rawQuery(userMccLevelSettingQuery, conn)
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'userMccLevelSettingResult', fields: userMccLevelSettingResult })

      if (userMccLevelSettingResult.length > 0 && userMccLevelSettingResult[0].mcc && userMccLevelSettingResult[0].ma_mcc_level_settings_id !== null) {
        mcc_code = userMccLevelSettingResult[0].mcc
      } else {
        const globalMccCodeResult = await this.getGlobalMccCode({ connection: conn })
        log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'globalMccCodeResult', fields: globalMccCodeResult })
        if (globalMccCodeResult.status != 200) {
          return globalMccCodeResult
        }
        const { mcc_global_code } = globalMccCodeResult
        mcc_code = mcc_global_code
      }
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'mcc_code', fields: mcc_code })

      const mccLevelSettingQuery = `SELECT * FROM ${this.TABLE_NAME} WHERE mcc_code = '${mcc_code}' LIMIT 1`
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'mccLevelSettingQuery', fields: mccLevelSettingQuery })
      const mccLevelSettingResult = await this.rawQuery(mccLevelSettingQuery, conn)

      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getAllowedChannels', type: 'channelResult', fields: mccLevelSettingResult })

      if (mccLevelSettingResult.length === 0) {
        return { status: 400, message: `${errorMsg.responseCode[1028]} : mcc setting code missing`, respcode: 1002, action_code: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1001, setting: mccLevelSettingResult[0] }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async getLimit ({ ma_user_id, userid, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getLimit', type: 'request', fields: { ma_user_id, userid } })
    try {
      const mccLevelSettingResult = await this.getMccLevelSetting({ ma_user_id, userid, connection: conn })

      if (mccLevelSettingResult.status != 200) {
        return mccLevelSettingResult
      }
      const { transaction_limit, withdrawal_limit, mcc_code } = mccLevelSettingResult.setting
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1001, transaction_limit, withdrawal_limit, mcc_code }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getLimit', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async getTotalWithdrawalAmount ({ ma_user_id, userid, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'request', fields: { ma_user_id, userid } })
    try {
      let totalWithDrawalAmount = 0
      /* TOTAL INTRA TRANSFERS CONSIDER ONLY SUCCESS CAESES */
      const totalIntraTransferAmountQuery = `SELECT SUM(transfer_amount) AS totalAmount FROM ma_intra_transfer_request WHERE sender_mid=${ma_user_id}`
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'totalIntraTransferAmountQuery', fields: totalIntraTransferAmountQuery })
      const totalIntraTransferAmountResult = await this.rawQuery(totalIntraTransferAmountQuery, conn)
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'totalIntraTransferAmountResult', fields: totalIntraTransferAmountResult })
      if (totalIntraTransferAmountResult.length > 0) {
        totalWithDrawalAmount += parseFloat(totalIntraTransferAmountResult[0].totalAmount) || 0
      }
      /* TOTAL WITHDRAWAL REQUEST EXCEPT FOR REJECTION CONSIDER ALL STATUS */
      const totalWithdrawalAmountQuery = `SELECT SUM(amount) AS totalAmount FROM ma_withdrawal_request WHERE ma_user_id=${ma_user_id} AND withdrawal_status != 'R'`
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'totalIntraTransferAmountQuery', fields: totalWithdrawalAmountQuery })
      const totalWithdrawalAmountResult = await this.rawQuery(totalWithdrawalAmountQuery, conn)
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'totalWithdrawalAmountResult', fields: totalWithdrawalAmountResult })

      if (totalWithdrawalAmountResult.length > 0) {
        totalWithDrawalAmount += parseFloat(totalWithdrawalAmountResult[0].totalAmount) || 0
      }

      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'total WithDrawalAmount', fields: totalWithDrawalAmount })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1001, totalWithDrawalAmount }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,withDrawalAmount:number,connection:mySQLWrapper.getConnectionFromPool()}} param0
   * @returns
   */
  static async isWithDrawalLimitExhausted ({ ma_user_id, userid, withDrawalAmount, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: 'mccLevelSettingController.js', action: 'getTotalWithdrawalAmount', type: 'request', fields: { ma_user_id, userid, withDrawalAmount } })
    try {
      /* Check Activation Level */
      const activationLevelResult = await this.getActivationLevel({ ma_user_id, userid, connection: conn })

      if (activationLevelResult.status != 200) {
        return activationLevelResult
      }

      if (activationLevelResult.activation_level == this.MCC_ACTIVATION_LEVEL_2 || !activationLevelResult.activation_level) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
      }
      /* Calculated the total withDrawal Amount */
      const totalWithAmountResult = await this.getTotalWithdrawalAmount({ ma_user_id, userid, connection: conn })
      if (totalWithAmountResult.status != 200) return totalWithAmountResult

      /* Fetch the limit based upon mcc code */
      const limitResult = await this.getLimit({ ma_user_id, userid, connection: conn })
      if (limitResult.status != 200) return limitResult

      const totalAmount = parseFloat(totalWithAmountResult.totalWithDrawalAmount || 0) + parseFloat(withDrawalAmount || 0)
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'isWithDrawalLimitExhausted', type: 'totalAmount', fields: totalAmount })
      if (totalAmount > limitResult.withdrawal_limit) {
        return { status: 400, message: `${errorMsg.responseCode[1028]} : withdrawal limit exceed`, respcode: 1001, action_code: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: 'mccLevelSettingController.js', action: 'getMccLevelSetting', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = MccLevelSettingController
