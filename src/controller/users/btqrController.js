/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class btqrController extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_user_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_master_id'
  }

  static async btqrString (_, fields) {
    log.logger({ pagename: 'btqrController.js', action: 'btqrString', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT bt_qr_string FROM ma_user_master WHERE profileid = '${fields.ma_user_id}' AND mer_user = 'mer' LIMIT 1`
      const userData = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'btqrController.js', action: 'btqrString', type: 'userData', fields: userData })

      if (userData.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], btqrString: userData[0].bt_qr_string }
      }

      log.logger({ pagename: 'btqrController.js', action: 'btqrString', type: 'userData length', fields: userData.length })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'btqrString', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }
}

module.exports = btqrController
