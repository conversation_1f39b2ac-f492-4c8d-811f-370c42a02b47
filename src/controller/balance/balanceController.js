/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const util = require('../../util/util')

class Balance extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_cash_account'
  }

  static async getCashBalance (_, { ma_user_id, ma_status = '', connection = null, isMobile = false }) {
    log.logger({ pagename: 'balanceController.js', action: 'getCashBalance', type: 'request', fields: { ma_user_id: ma_user_id, ma_status: ma_status } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      var sql1 = ''
      var sql2 = ''
      var condition = ''
      if (ma_status !== '' && ma_status !== 'ACTUAL') {
        condition = ` AND ma_status IN('${ma_status}')`
      } else if (ma_status === 'ACTUAL') {
        condition = ' AND ma_status IN("S","R")'
      }
      sql1 = `SELECT SUM(amount) AS amt FROM ma_cash_ledger_master WHERE ma_user_id = ${ma_user_id}  ${condition};`
      sql2 = `SELECT SUM(amount) AS amt FROM ma_cash_account WHERE ma_user_id = ${ma_user_id} ${condition};`
      var _balance1 = await this.rawQuery(sql1, connection)
      var _balance2 = await this.rawQuery(sql2, connection)
      log.logger({ pagename: 'balanceController.js', action: 'getCashBalance', type: 'cashledger-response', fields: _balance1 })
      log.logger({ pagename: 'balanceController.js', action: 'getCashBalance', type: 'cashaccount-response', fields: _balance2 })
      var bal1 = 0
      var bal2 = 0
      if (_balance1[0].amt !== null) {
        bal1 = _balance1[0].amt
      }
      if (_balance2[0].amt !== null) {
        bal2 = _balance2[0].amt
      }
      const totalbal = bal1 + bal2
      if (isMobile) {
        return { status: 100, message: errorMsg.responseCode[1000], respcode: 1000, amount: totalbal, action_code: 1000 }
      } else {
        return { amount: totalbal }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCashBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getPointsBalance (_, { ma_user_id, ma_status = '', connection = null, isMobile = false }) {
    log.logger({ pagename: 'balanceController.js', action: 'getPointsBalance', type: 'request', fields: { ma_user_id: ma_user_id, ma_status: ma_status } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // var sql = ''
      // var condition = ''
      // if (ma_status !== '' && ma_status !== 'ACTUAL') {
      //   condition = ` AND ma_status IN('${ma_status}')`
      // } else if (ma_status === 'ACTUAL') {
      //   condition = ' AND ma_status IN("S","R")'
      // }
      // sql = `SELECT SUM(amt) AS amount FROM (SELECT SUM(amount) AS amt FROM ma_points_ledger_master WHERE ma_user_id = ${ma_user_id} ${condition}  UNION ALL SELECT SUM(amount) AS amt FROM ma_points_account WHERE ma_user_id = ${ma_user_id} ${condition}) AS temp;`
      // var _balance = await this.rawQuery(sql, connection)
      // log.logger({ pagename: 'balanceController.js', action: 'getPointsBalance', type: 'response', fields: _balance })
      // if (_balance[0].amount === null) {
      //   return { amount: 0 }
      // } else {
      //   return { amount: _balance[0].amount }
      // }
      const availableBalance = await this.getWalletBalancesDirect(_, {
        ma_user_id: ma_user_id,
        ma_status: ma_status,
        balance_flag: 'SUMMARY',
        connection
      })
      log.logger({ pagename: 'balanceController.js', action: 'getPointsBalance', type: 'response', fields: availableBalance })
      // checks if the amount is not negative, null and undefined and all status codes are OK
      if (availableBalance.status === 200 && availableBalance.respcode === 1000 && availableBalance.amount !== null && availableBalance.amount !== undefined && availableBalance.amount > 0) {
        if (isMobile) {
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, amount: availableBalance.amount, action_code: 1000 }
        } else {
          return { amount: availableBalance.amount }
        }
      } else {
        if (isMobile) {
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, amount: 0, action_code: 1000 }
        } else {
          return { amount: 0 }
        }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getPointsBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) connection.release()
    }
    // return _balance
  }

  static async getCashLedgerBalance (_, fields) {
    log.logger({ pagename: 'balanceController.js', action: 'getCashLedgerBalance', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_cash_ledger_master'
    var _balance = await this.findByFields({
      fields,
      limit: 1,
      order: {
        by: 'ma_cash_ledger_master_id',
        direction: 'desc'
      }
    })
    log.logger({ pagename: 'balanceController.js', action: 'getCashLedgerBalance', type: 'response', fields: _balance })
    if (_balance.length > 0) {
      return { balance: _balance[0].balance }
    } else {
      return { balance: 0 }
    }
  }

  static async getPointsLedgerBalance (_, fields) {
    log.logger({ pagename: 'balanceController.js', action: 'getPointsLedgerBalance', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_points_ledger_master'
    var _balance = await this.findByFields({
      fields,
      limit: 1,
      order: {
        by: 'ma_points_ledger_master_id',
        direction: 'desc'
      }
    })
    log.logger({ pagename: 'balanceController.js', action: 'getPointsLedgerBalance', type: 'response', fields: _balance })
    if (_balance.length > 0) {
      return { balance: _balance[0].balance }
    } else {
      return { balance: 0 }
    }
  }

  static async getWalletBalance (_, fields) {
    log.logger({ pagename: 'balanceController.js', action: 'getWalletBalance', type: 'request', fields: fields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      var sql
      if (fields.transactionType === '1') {
        sql = `SELECT TAB1.ma_user_id, SUM(TAB1.balance) balance, TAB1.wallet_type, TAB1.priority
        FROM (SELECT mp.ma_user_id, SUM(mp.amount) balance, TAB.wallet_type, TAB.priority FROM ma_points_ledger_master mp LEFT JOIN (SELECT ma_allow_withdrawal.wallet_type, ma_allow_withdrawal.priority, SUBSTRING_INDEX(SUBSTRING_INDEX(ma_allow_withdrawal.transaction_type, ',', numbers.n), ',', - 1) transaction_type FROM numbers INNER JOIN ma_allow_withdrawal ON CHAR_LENGTH(ma_allow_withdrawal.transaction_type) - CHAR_LENGTH(REPLACE(ma_allow_withdrawal.transaction_type, ',', '')) >= numbers.n - 1 WHERE ma_user_id = ${fields.ma_user_id} ORDER BY ma_allow_withdrawal.wallet_type , n) TAB ON mp.transaction_type = TAB.transaction_type WHERE ma_user_id = ${fields.ma_user_id} AND mp.ma_points_ledger_master_id not in (SELECT DISTINCT md.ma_points_ledger_master_id FROM ma_points_ledger_details md WHERE ma_user_id = ${fields.ma_user_id}) AND ma_status IN ('S' , 'R') GROUP BY mp.transaction_type UNION SELECT mpd.ma_user_id, SUM(mpd.amount) balance, mpd.wallet_type, maw.priority FROM ma_points_ledger_details mpd LEFT JOIN ma_allow_withdrawal maw ON mpd.ma_user_id = maw.ma_user_id AND mpd.wallet_type = maw.wallet_type WHERE mpd.ma_user_id = ${fields.ma_user_id} GROUP BY mpd.wallet_type) TAB1
        GROUP BY TAB1.wallet_type
        ORDER BY TAB1.priority`
      } else {
        sql = `SELECT TAB1.ma_user_id, sum(TAB1.balance) balance, TAB1.wallet_type, TAB1.priority FROM (SELECT mp.ma_user_id, SUM(mp.amount) balance, TAB.wallet_type, TAB.priority, TAB.allow_flag FROM ma_points_ledger_master mp LEFT JOIN (SELECT ma_allow_withdrawal.wallet_type, ma_allow_withdrawal.priority, ma_allow_withdrawal.allow_flag, SUBSTRING_INDEX(SUBSTRING_INDEX(ma_allow_withdrawal.transaction_type, ',', numbers.n), ',', - 1) transaction_type FROM numbers INNER JOIN ma_allow_withdrawal ON CHAR_LENGTH(ma_allow_withdrawal.transaction_type) - CHAR_LENGTH(REPLACE(ma_allow_withdrawal.transaction_type, ',', '')) >= numbers.n - 1 WHERE ma_allow_withdrawal.ma_user_id = ${fields.ma_user_id} AND ma_allow_withdrawal.allow_flag IN ('Y') ORDER BY ma_allow_withdrawal.wallet_type , n) TAB ON mp.transaction_type = TAB.transaction_type WHERE ma_user_id = ${fields.ma_user_id} AND mp.ma_points_ledger_master_id not in (SELECT DISTINCT md.ma_points_ledger_master_id FROM ma_points_ledger_details md WHERE ma_user_id = ${fields.ma_user_id}) AND ma_status IN ('S' , 'R') AND TAB.allow_flag IN ('Y') GROUP BY mp.transaction_type UNION SELECT mpd.ma_user_id, SUM(mpd.amount) balance, mpd.wallet_type, maw.priority, maw.allow_flag FROM ma_points_ledger_details mpd LEFT JOIN  ma_allow_withdrawal maw ON mpd.ma_user_id=maw.ma_user_id AND mpd.wallet_type = maw.wallet_type WHERE maw.allow_flag IN ('Y') AND mpd.ma_user_id = ${fields.ma_user_id} GROUP BY mpd.wallet_type) TAB1 GROUP BY TAB1.wallet_type ORDER BY TAB1.priority`
      }
      var walletBalances = await this.rawQuery(sql, fields.connection)
      log.logger({ pagename: 'balanceController.js', action: 'getWalletBalance', type: 'response', fields: walletBalances })
      var details = []
      var RequiredAmount = fields.amount
      if (walletBalances.length > 0) {
        const totalbalance = walletBalances.reduce((total, obj) => obj.balance + total, 0)
        if (RequiredAmount > totalbalance) {
          return { status: 400, message: 'Insufficient Balance', details }
        }
        for (var i = 0; i < walletBalances.length; i++) {
          if (walletBalances[i].balance > 0) {
            if (RequiredAmount >= walletBalances[i].balance) {
              RequiredAmount = RequiredAmount - walletBalances[i].balance
              details.push({
                wallet_type: walletBalances[i].wallet_type,
                deductionAmount: walletBalances[i].balance
              })
              if (RequiredAmount === 0) break
            } else if (RequiredAmount < walletBalances[i].balance) {
              details.push({
                wallet_type: walletBalances[i].wallet_type,
                deductionAmount: RequiredAmount
              })
              RequiredAmount = 0
              break
            }
          }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002], details }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) {
        fields.connection.release()
      }
    }
  }

  static async getWalletBalances (_, fields) {
    log.logger({ pagename: 'balanceController.js', action: 'getWalletBalances', type: 'request', fields: fields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // var sql
      // if (fields.balance_flag !== undefined && fields.balance_flag !== null) {
      //   sql = `SELECT TAB1.ma_user_id, sum(TAB1.balance) balance, TAB1.wallet_type, TAB1.priority FROM (SELECT mp.ma_user_id, SUM(mp.amount) balance, TAB.wallet_type, TAB.priority, TAB.allow_flag FROM ma_points_ledger_master mp LEFT JOIN (SELECT ma_allow_withdrawal.wallet_type, ma_allow_withdrawal.priority, ma_allow_withdrawal.allow_flag, SUBSTRING_INDEX(SUBSTRING_INDEX(ma_allow_withdrawal.transaction_type, ',', numbers.n), ',', - 1) transaction_type FROM numbers INNER JOIN ma_allow_withdrawal ON CHAR_LENGTH(ma_allow_withdrawal.transaction_type) - CHAR_LENGTH(REPLACE(ma_allow_withdrawal.transaction_type, ',', '')) >= numbers.n - 1 WHERE ma_allow_withdrawal.ma_user_id = ${fields.ma_user_id} AND ma_allow_withdrawal.allow_flag IN ('Y') ORDER BY ma_allow_withdrawal.wallet_type , n) TAB ON mp.transaction_type = TAB.transaction_type WHERE ma_user_id = ${fields.ma_user_id} AND mp.ma_points_ledger_master_id not in (SELECT DISTINCT md.ma_points_ledger_master_id FROM ma_points_ledger_details md WHERE ma_user_id = ${fields.ma_user_id}) AND ma_status IN ('S' , 'R') AND TAB.allow_flag IN ('Y') GROUP BY mp.transaction_type UNION SELECT mpd.ma_user_id, SUM(mpd.amount) balance, mpd.wallet_type, maw.priority, maw.allow_flag FROM ma_points_ledger_details mpd LEFT JOIN  ma_allow_withdrawal maw ON mpd.ma_user_id=maw.ma_user_id AND mpd.wallet_type = maw.wallet_type WHERE maw.allow_flag IN ('Y') AND mpd.ma_user_id = ${fields.ma_user_id} GROUP BY mpd.wallet_type) TAB1 GROUP BY TAB1.wallet_type ORDER BY TAB1.priority`
      // } else {
      //   sql = `SELECT TAB1.ma_user_id, SUM(TAB1.balance) amount, TAB1.wallet_type as wallet_type, TAB1.priority,TAB1.wallet_type as wallet
      //   FROM (SELECT mp.ma_user_id, SUM(mp.amount) balance, TAB.wallet_type, TAB.priority FROM ma_points_ledger_master mp LEFT JOIN (SELECT ma_allow_withdrawal.wallet_type, ma_allow_withdrawal.priority, SUBSTRING_INDEX(SUBSTRING_INDEX(ma_allow_withdrawal.transaction_type, ',', numbers.n), ',', - 1) transaction_type FROM numbers INNER JOIN ma_allow_withdrawal ON CHAR_LENGTH(ma_allow_withdrawal.transaction_type) - CHAR_LENGTH(REPLACE(ma_allow_withdrawal.transaction_type, ',', '')) >= numbers.n - 1 WHERE ma_user_id = ${fields.ma_user_id} ORDER BY ma_allow_withdrawal.wallet_type , n) TAB ON mp.transaction_type = TAB.transaction_type WHERE ma_user_id = ${fields.ma_user_id} AND mp.ma_points_ledger_master_id not in (SELECT DISTINCT md.ma_points_ledger_master_id FROM ma_points_ledger_details md WHERE ma_user_id = ${fields.ma_user_id}) AND ma_status IN ('S' , 'R') GROUP BY mp.transaction_type UNION SELECT mpd.ma_user_id, SUM(mpd.amount) balance, mpd.wallet_type, maw.priority FROM ma_points_ledger_details mpd LEFT JOIN ma_allow_withdrawal maw ON mpd.ma_user_id = maw.ma_user_id AND mpd.wallet_type = maw.wallet_type WHERE mpd.ma_user_id = ${fields.ma_user_id} GROUP BY mpd.wallet_type) TAB1
      //   GROUP BY TAB1.wallet_type
      //   ORDER BY TAB1.priority`
      // }

      // var walletBalances = await this.rawQuery(sql, fields.connection)
      // log.logger({ pagename: 'balanceController.js', action: 'getWalletBalances', type: 'response', fields: walletBalances })
      // if (walletBalances.length > 0) {
      //   const totalbalance = walletBalances.reduce((total, obj) => obj.balance + total, 0)
      //   console.log(fields.balance_flag, walletBalances)
      //   if (fields.balance_flag !== undefined && fields.balance_flag !== null) {
      //     console.log(fields.balance_flag, totalbalance)
      //     return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: totalbalance }
      //   } else {
      //     return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: walletBalances }
      //   }
      // } else {
      //   return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      // }
      const availableBalance = await this.getWalletBalancesDirect(_, {
        ma_user_id: fields.ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        transactionType: '2',
        payment_mode: fields.payment_mode
      })
      log.logger({ pagename: 'balanceController.js', action: 'getWalletBalances', type: 'response', fields: availableBalance })
      if (availableBalance.status === 200 && availableBalance.respcode === 1000 && availableBalance.amount !== null) {
        // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: availableBalance.amount }
        // Check Available Balance less then Credit balance, then show Available as credit balance - 31-01-2022
        const totalAvailableBalance = await this.getWalletBalancesDirect(_, {
          ma_user_id: fields.ma_user_id,
          ma_status: 'ACTUAL',
          balance_flag: 'SUMMARY',
          connection: fields.connection
        })
        log.logger({ pagename: 'balanceController.js', action: 'getWalletBalances', type: 'responsecheck', fields: totalAvailableBalance })
        // checks if the amount is not negative, null and undefined and all status codes are OK
        let creditBalanceAmount = ''
        if (totalAvailableBalance.status === 200 && totalAvailableBalance.respcode === 1000 && totalAvailableBalance.amount !== null && totalAvailableBalance.amount !== undefined && totalAvailableBalance.amount > 0) {
          if (totalAvailableBalance.amount < availableBalance.amount) {
            creditBalanceAmount = totalAvailableBalance.amount
          } else {
            creditBalanceAmount = availableBalance.amount
          }
        } else if (totalAvailableBalance.amount < 0) {
          creditBalanceAmount = 0
        } else {
          creditBalanceAmount = availableBalance.amount
        }
        log.logger({ pagename: 'balanceController.js', action: 'getWalletBalances', type: 'totalCreditBalance', fields: creditBalanceAmount })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: creditBalanceAmount }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002], amount: 0 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalances', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) {
        fields.connection.release()
      }
    }
  }

  static async getWalletBalanceDirect (_, fields) {
    // Cloning request and removing connection
    var requestFields = {}
    if (!(fields.connection === null || fields.connection === undefined)) {
      requestFields = (({ connection, ...o }) => o)(fields)
      requestFields.connectionThreadId = fields.connection.threadId
    }
    log.logger({ pagename: 'balanceController.js', action: 'getWalletBalanceDirect', type: 'request', fields: requestFields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      fields.ma_status = 'ACTUAL'
      const walletRes = await this.getWalletBalancesDirect(_, fields)
      log.logger({ pagename: 'balanceController.js', action: 'getWalletBalanceDirect', type: 'response', fields: walletRes })
      if (!(fields.amount === null || fields.amount === undefined)) {
        var RequiredAmount = fields.amount
      }
      console.log('walletRes', walletRes)
      if (walletRes.status == 400) {
        return walletRes
      }
      // if (walletRes.status === 200) {
      if (fields.balance_flag !== undefined && fields.balance_flag !== null) {
        // console.log(fields.balance_flag, totalbalance)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: walletRes.amount }
      }
      const walletBalances = walletRes.details
      var details = []
      const totalbalance = walletBalances.reduce((total, obj) => obj.amount + total, 0)
      console.log('Nirmala36p0dpq00b', RequiredAmount, totalbalance)
      if (RequiredAmount > totalbalance || !RequiredAmount) {
        return { status: 400, message: 'Insufficient Balance', details }
      }
      // console.log('walletBalances', walletBalances)
      if (!(fields.amount === null || fields.amount === undefined)) {
        for (var i = 0; i < walletBalances.length; i++) {
          if (walletBalances[i].amount > 0) {
            if (RequiredAmount >= walletBalances[i].amount) {
              RequiredAmount = parseFloat((RequiredAmount - walletBalances[i].amount).toFixed(4))
              details.push({
                wallet_type: walletBalances[i].wallet_type,
                deductionAmount: walletBalances[i].amount
              })
              if (RequiredAmount === 0) break
            } else if (RequiredAmount < walletBalances[i].amount) {
              details.push({
                wallet_type: walletBalances[i].wallet_type,
                deductionAmount: RequiredAmount
              })
              RequiredAmount = 0
              break
            }
          }
        }
      } else {
        details = walletBalances
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details }
      // } else {
      //   return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002], details }
      // }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalanceDirect', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) fields.connection.release()
    }
  }

  static async getWalletBalancesDirect (_, fields) {
    // Cloning request and removing connection
    var requestFields = {}
    if (!(fields.connection === null || fields.connection === undefined)) {
      requestFields = (({ connection, ...o }) => o)(fields)
      requestFields.connectionThreadId = fields.connection.threadId
    }
    log.logger({ pagename: 'balanceController.js', action: 'getWalletBalancesDirect', type: 'request', fields: requestFields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an achecrgument
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      // if (fields.ma_user_id === util.airpayUserId || fields.ma_user_id === util.airpayCommissionId) {
      //   return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: 0 }
      // }
      var condition = ''
      if (fields.transactionType !== null && fields.transactionType !== undefined) {
        if (fields.transactionType === '2') {
          condition = 'and allow_flag=\'Y\''
          if (fields.payment_mode) {
            if (fields.payment_mode === 'STD' || fields.payment_mode === 'Transfer to Network') {
              condition = ''
            }
          } 
        }
      }
      var balance_col = ' balance '
      if (!(fields.ma_status === null || fields.ma_status === undefined)) {
        if (fields.ma_status === 'ACTUAL') {
          balance_col = ' actual_balance '
        }
      }

      const getQuery = `SELECT wallet_type, wallet_type as wallet, ${balance_col} as amount,priority FROM ma_allow_withdrawal where ma_user_id = ${fields.ma_user_id} ${condition} order by priority, amount `
      console.log('q', getQuery)
      const walletBalances = await this.rawQuery(getQuery, fields.connection)
      log.logger({ pagename: 'balanceController.js', action: 'getWalletBalancesDirect', type: 'response', fields: walletBalances })
      // console.log('walletBalances', walletBalances)
      if (walletBalances.length > 0) {
        // console.log('walletBalances', walletBalances)
        const totalbalance = walletBalances.reduce((total, obj) => obj.amount + total, 0)
        if (fields.balance_flag !== undefined && fields.balance_flag !== null) {
          // console.log(fields.balance_flag, totalbalance)
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], amount: totalbalance }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: walletBalances }
      } else {
        return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], amount: 0 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalancesDirect', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) fields.connection.release()
    }
  }

  static async updateBalance (_, { ma_user_id, amount, transaction_type, ma_status, connection = null, wallet_type = null, transaction_status }) {
    try {
      log.logger({ pagename: 'balanceController.js', action: 'updateBalance', type: 'request', fields: { ma_user_id: ma_user_id, amount: amount, transaction_type: transaction_type, ma_status: ma_status, wallet_type: wallet_type, transaction_status: transaction_status } })
      var walletBalancesJson = []
      if (ma_user_id === util.airpayUserId || ma_user_id === util.airpayCommissionId) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, walletBalancesJson }
      }

      // Update balance of wallet
      var condition
      if (wallet_type === undefined || wallet_type === null) {
        // condition = (transaction_type !== '10')
        //   ? ` (transaction_type like '${transaction_type},%' or transaction_type like '%,${transaction_type}')`
        //   : ` transaction_type = ${transaction_type}`
        condition = `FIND_IN_SET('${transaction_type}', transaction_type)`
      } else {
        condition = ` wallet_type = ${wallet_type}`
      }
      // Lock the record
      const locksql = `SELECT balance,actual_balance FROM ma_allow_withdrawal WHERE ma_user_id = ${ma_user_id} and  ${condition} FOR UPDATE;`
      console.log('locksql', locksql)
      const balanceNow = await this.rawQuery(locksql, connection)
      console.log('locksql', balanceNow)
      var updateallowed = false
      // Changes added for allowing amount as 0 for AEPS BE/MS
      if (amount > 0) {
        updateallowed = true
      } else if (amount < 0 && (transaction_type == '67' || transaction_type == '68' || transaction_type == '69') && ma_status === 'REV') { // add condition for chargeback
        updateallowed = true
      } else if (amount < 0 && balanceNow[0].balance + amount >= 0 && !(ma_status === 'S' || ma_status === 'R')) {
        updateallowed = true
      } else if (amount < 0 && balanceNow[0].balance + amount >= 0 && balanceNow[0].actual_balance + amount >= 0 && ((ma_status === 'S' || ma_status === 'R') || (ma_status == 'REV' && transaction_type == '15'))) {
        updateallowed = true
      } else if (amount == 0 && (transaction_type == 40 || transaction_type == 42)) {
        updateallowed = true
      }

      if (!updateallowed) {
        return { status: 400, message: errorMsg.responseCode[1005], respcode: 1005 }
      }

      // console.log('Here i came')
      const sql = `UPDATE ma_allow_withdrawal set balance=balance+${amount} where ma_user_id = ${ma_user_id} and  ${condition}`
      await this.rawQuery(sql, connection)
      console.log('query', ma_user_id, ma_status, sql)
      if ((ma_status === 'S' || ma_status === 'R') ||
        (ma_status == 'REV' && transaction_type == '2') ||
        (ma_status == 'REV' && transaction_type == '4') ||
        (ma_status == 'REV' && transaction_type == '13') ||
        (ma_status == 'REV' && transaction_type == '15') ||
        (ma_status == 'REV' && transaction_type == '21') ||
        (ma_status == 'REV' && transaction_type == '6') ||
        (ma_status == 'REV' && transaction_type == '14') ||
        (ma_status == 'REV' && transaction_type == '17') ||
        (ma_status == 'REV' && transaction_type == '30') ||
        (ma_status == 'REV' && transaction_type == '24') ||
        (ma_status == 'REV' && transaction_type == '31') ||
        (ma_status == 'REV' && transaction_type == '32') ||
        (ma_status == 'REV' && transaction_type == '7') ||
        (ma_status == 'REV' && transaction_type == '28') ||
        (ma_status == 'REV' && transaction_type == '29') ||
        (ma_status == 'REV' && transaction_type == '34') ||
        (ma_status == 'REV' && transaction_type == '36') ||
        (ma_status == 'REV' && transaction_type == '38') ||
        (ma_status == 'REV' && transaction_type == '3') ||
        (ma_status == 'REV' && transaction_type == '40') ||
        (ma_status == 'REV' && transaction_type == '42') ||
        (ma_status == 'REV' && transaction_type == '44') ||
        /* FMT CHANGES */
        (ma_status == 'REV' && transaction_type == '50') ||
        (ma_status == 'REV' && transaction_type == '51') ||
        (ma_status == 'REV' && transaction_type == '52') ||
        (ma_status == 'REV' && transaction_type == '53') ||
        (ma_status == 'REV' && transaction_type == '54') ||
        (ma_status == 'REV' && transaction_type == '55') ||
        (ma_status == 'REV' && transaction_type == '56') ||
        (ma_status == 'REV' && transaction_type == '57') ||
        (ma_status == 'REV' && transaction_type == '58') ||
        /* Chargeback Transaction changes start */
        (ma_status == 'REV' && transaction_type == '67') ||
        (ma_status == 'REV' && transaction_type == '68') ||
        (ma_status == 'REV' && transaction_type == '69') ||
        /* Chargeback Transaction changes end */
        // Reverse commission for upi cc charge
        (ma_status == 'REV' && transaction_type == '70') ||
        (ma_status == 'REV' && transaction_type == '71') ||
        (ma_status == 'REV' && transaction_type == '72') ||
        (ma_status == 'REV' && transaction_type == '73') ||
        // Reverse commission for upi cc charge end
        // PPI wallet charges reversed
        (ma_status == 'REV' && transaction_type == '75') ||
        (ma_status == 'REV' && transaction_type == '76') ||
        (ma_status == 'REV' && transaction_type == '77') ||
        (ma_status == 'REV' && transaction_type == '79') ||
        (ma_status == 'REV' && transaction_type == '80')
      ) {
        const sql = `UPDATE ma_allow_withdrawal set actual_balance=actual_balance+${amount} where ma_user_id = ${ma_user_id} and  ${condition}`
        await this.rawQuery(sql, connection)
      }
      // Get balance of all wallets after updation
      const getQuery = `SELECT wallet_type,actual_balance, balance,priority FROM ma_allow_withdrawal where ma_user_id = ${ma_user_id}`
      const walletBalances = await this.rawQuery(getQuery, connection)
      // console.log('walletBalances', walletBalances)
      // Create json for storing
      if (walletBalances.length > 0) {
        for (var i = 0; i < walletBalances.length; i++) {
          var temp = {}
          temp.wallet = walletBalances[i].wallet_type
          temp.balance = walletBalances[i].balance
          temp.actual_balance = walletBalances[i].actual_balance
          temp.priority = walletBalances[i].priority
          walletBalancesJson.push(temp)
        }
      }
      log.logger({ pagename: 'balanceController.js', action: 'updateBalance', type: 'response', fields: walletBalancesJson })
      // return walletBalancesJson
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, walletBalancesJson }
    } catch (err) {
      console.log(err)
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getTodaysBalance (_, { ma_user_id, connection = null }) {
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromReadReplica()
        isSet = true
      }

      const today = new Date()
      var dd = today.getDate()
      var mm = today.getMonth() + 1
      var yyyy = today.getFullYear()
      if (dd < 10) {
        dd = `0${dd}`
      }
      if (mm < 10) {
        mm = `0${mm}`
      }
      var sql = ''
      /* var condition = `
      AND addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59') AND ((ma_status IN("S","P")  AND mode = 'dr') OR (ma_status IN("R","REV")  AND mode = 'cr'))  AND transaction_type NOT IN("13","14","7","30")
      ` */
      var condition = `
      AND mplm.addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59') AND ((mplm.ma_status IN("S","P")  AND mplm.mode = 'dr') OR (mplm.ma_status IN("R","REV")  AND mplm.mode = 'cr' AND mplm.transaction_type != "2"))  AND mplm.transaction_type NOT IN("13","14","7","30","76") AND mtm.transaction_status != 'F'
      `
      var condition2 = ''
      /* AND parent_id NOT IN (SELECT orderid FROM adhoc_entries WHERE
        addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59')
      )
      ` */

      sql = `SELECT  ABS(SUM(mplm.amount)) AS amount FROM ma_points_ledger_master mplm JOIN ma_transaction_master mtm ON mtm.aggregator_order_id = mplm.parent_id  WHERE mplm.ma_user_id = ${ma_user_id}  ${condition} ${condition2};`
      console.log('sql>>', sql)
      var _balance = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getTodaysBalance', type: '_balance', fields: _balance })
      if (_balance.length > 0) {
        if (_balance.length > 0 && _balance[0].amount === null) {
          return { amount: 0 }
        } else {
          return { amount: _balance[0].amount }
        }
      } else {
        return { amount: 0 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTodaysBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        connection.release()
      }
    }
    // return _balance
  }

  static async getAllBalance (_, { ma_user_id, maStatusForGetBalance = '', userid, getBalance = false, getTodaysBalance = false, getMonthBalance = false, from_date = '', to_date = '', deviceType = '' }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: 'request', fields: { ma_user_id, maStatusForGetBalance, userid, getBalance, getTodaysBalance } })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    // const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let { getBalanceAmount, getMonthBalanceAmount, getTodaysBalanceAmount } = 0
      const getOtherBalanceAmount = []

      // getBalance
      if (getBalance == true) {
        const availableBalance = await this.getWalletBalancesDirect(_, {
          ma_user_id: ma_user_id,
          ma_status: maStatusForGetBalance,
          balance_flag: 'SUMMARY',
          connection
        })
        log.logger({ pagename: 'balanceController.js', action: 'getAllBalance', type: 'response', fields: availableBalance })
        if (availableBalance.status === 200 && availableBalance.respcode === 1000 && availableBalance.amount !== null && availableBalance.amount > 0) {
          getBalanceAmount = availableBalance.amount
        } else {
          getBalanceAmount = 0
        }
      }

      // getTodaysBalance
      if (getTodaysBalance == true) {
        const _balance = await this.getTodaysBalance(_, { ma_user_id, connection })
        log.logger({ pagename: 'balanceController.js', action: 'getTodaysBalance', type: '_balance', fields: _balance })
        if (_balance.status == 400) {
          return _balance
        }
        getTodaysBalanceAmount = _balance.length > 0 || _balance.amount === null ? 0 : _balance.amount
      }

      const today = new Date()
      var dd = today.getDate()
      var mm = today.getMonth() + 1
      var yyyy = today.getFullYear()
      if (mm.length < 2) {
        mm = '0' + mm
      }
      if (dd.length < 2) {
        dd = '0' + dd
      }
      var fromDate = from_date != '' ? from_date + ' 00:00:00' : yyyy + '-' + mm + '-' + '01' + ' 00:00:00'
      var toDate = to_date != '' ? to_date + ' 23:59:59' : yyyy + '-' + mm + '-' + dd + ' 23:59:59'

      var sqlBalance = `SELECT  ABS(SUM(mplm.amount)) AS amount FROM ma_points_ledger_master mplm JOIN ma_transaction_master mtm ON mtm.aggregator_order_id = mplm.parent_id  WHERE mplm.ma_user_id = ${ma_user_id}  AND mplm.addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'${fromDate}') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'${toDate}') AND ((mplm.ma_status IN("S","P")  AND mplm.mode = 'dr') OR (mplm.ma_status IN("R","REV")  AND mplm.mode = 'cr' AND mplm.transaction_type NOT IN ("27","2") ))  AND mplm.transaction_type NOT IN("13","14","7","30","76") AND mtm.transaction_status != 'F'`

      /* Default Values */
      var _dmtBalance = [{ amount: null }]
      var _aepsBalance = [{ amount: null }]
      var _hatmBalance = [{ amount: null }]
      var _otherBalance = [{ amount: null }]

      if (deviceType != 'WEB') {
        // DMT Balance
        var sqlDmtBalance = `  ${sqlBalance} AND  mplm.transaction_type IN("2");`
        // DMT AEPS
        var sqlAepsBalance = `SELECT ABS(SUM(mplm.amount)) AS amount FROM ma_points_ledger_master mplm JOIN ma_transaction_master mtm ON mtm.aggregator_order_id = mplm.parent_id  WHERE mplm.corresponding_id = ${ma_user_id}  AND mplm.addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'${fromDate}') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'${toDate}') AND ((mplm.ma_status IN("S","P")  AND mplm.mode = 'dr') OR (mplm.ma_status IN("R","REV")  AND mplm.mode = 'cr' AND mplm.transaction_type NOT IN ("27","2"))) AND mplm.transaction_type NOT IN("13","14","7","30","76") AND mtm.transaction_status != 'F' AND mplm.transaction_type IN("5")`

        // DMT Human ATM
        var sqlHatmBalance = ` SELECT ABS(SUM(mplm.amount)) AS amount FROM ma_points_ledger_master mplm JOIN ma_transaction_master mtm ON mtm.aggregator_order_id = mplm.parent_id  WHERE mplm.corresponding_id = ${ma_user_id}  AND mplm.addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'${fromDate}') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'${toDate}') AND ((mplm.ma_status IN("S","P")  AND mplm.mode = 'dr') OR (mplm.ma_status IN("R","REV")  AND mplm.mode = 'cr' AND mplm.transaction_type NOT IN ("27","2"))) AND mplm.transaction_type NOT IN("13","14","7","30","76") AND mtm.transaction_status != 'F' AND  mplm.transaction_type IN("23")`

        var sqlOtherBalance = `  ${sqlBalance} AND  mplm.transaction_type NOT IN("2","5","23");`
        log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance sqlDmtBalance', fields: sqlDmtBalance })
        log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance sqlAepsBalance', fields: sqlAepsBalance })
        log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance sqlHatmBalance', fields: sqlHatmBalance })

        _dmtBalance = await this.rawQuery(sqlDmtBalance, connection)
        _aepsBalance = await this.rawQuery(sqlAepsBalance, connection)
        _hatmBalance = await this.rawQuery(sqlHatmBalance, connection)
        _otherBalance = await this.rawQuery(sqlOtherBalance, connection)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance _dmtBalance', fields: _dmtBalance })
      log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance _aepsBalance', fields: _aepsBalance })
      log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance _hatmBalance', fields: _hatmBalance })
      log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance _otherBalance', fields: _otherBalance })

      const dmtBalanceObj = {}
      const aepsBalanceObj = {}
      const hatmBalanceObj = {}
      const otherBalanceObj = {}

      if (_dmtBalance.length > 0 && _dmtBalance[0].amount === null) {
        dmtBalanceObj.label = 'DMT'
        dmtBalanceObj.value = '0.00'
      } else {
        dmtBalanceObj.label = 'DMT'
        dmtBalanceObj.value = _dmtBalance[0].amount.toFixed(2)
      }
      getOtherBalanceAmount.push(dmtBalanceObj)

      if (_aepsBalance.length > 0 && _aepsBalance[0].amount === null) {
        aepsBalanceObj.label = 'AEPS'
        aepsBalanceObj.value = '0.00'
      } else {
        aepsBalanceObj.label = 'AEPS'
        aepsBalanceObj.value = _aepsBalance[0].amount.toFixed(2)
      }
      getOtherBalanceAmount.push(aepsBalanceObj)

      if (_hatmBalance.length > 0 && _hatmBalance[0].amount === null) {
        hatmBalanceObj.label = 'H-ATM'
        hatmBalanceObj.value = '0.00'
      } else {
        hatmBalanceObj.label = 'H-ATM'
        hatmBalanceObj.value = _hatmBalance[0].amount.toFixed(2)
      }
      getOtherBalanceAmount.push(hatmBalanceObj)

      if (_otherBalance.length > 0 && _otherBalance[0].amount === null) {
        otherBalanceObj.label = 'Other'
        otherBalanceObj.value = '0.00'
      } else {
        otherBalanceObj.label = 'Other'
        otherBalanceObj.value = _otherBalance[0].amount.toFixed(2)
      }
      getOtherBalanceAmount.push(otherBalanceObj)

      // Month Balance
      if (getMonthBalance == true) {
        var sqlMonthBalance = ''
        sqlMonthBalance = `${sqlBalance};`
        log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_balance sqlMonthBalance', fields: sqlMonthBalance })
        var _monthbalance = await this.rawQuery(sqlMonthBalance, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getAllBalance', type: '_monthbalance', fields: _monthbalance })

        if (_monthbalance.length > 0) {
          getMonthBalanceAmount = _monthbalance[0].amount === null ? 0 : _monthbalance[0].amount
        } else {
          getMonthBalanceAmount = 0
        }
        console.log('Month Bal', getMonthBalanceAmount)
        console.log('aepsBalanceObj.value Bal', aepsBalanceObj.value)
        console.log('hatmBalanceObj Bal', hatmBalanceObj.value)
        getMonthBalanceAmount = (parseFloat(getMonthBalanceAmount) + parseFloat(aepsBalanceObj.value) + parseFloat(hatmBalanceObj.value)).toFixed(2)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getTodaysBalance', type: 'getTodaysBalanceAmount', fields: getTodaysBalanceAmount })
      log.logger({ pagename: require('path').basename(__filename), action: 'getMonthBalance', type: 'getMonthBalanceAmount', fields: getMonthBalanceAmount })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, getBalanceAmount: getBalanceAmount, getTodaysBalanceAmount: getTodaysBalanceAmount, getMonthBalanceAmount: getMonthBalanceAmount, getOtherBalanceAmount: getOtherBalanceAmount }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTodaysBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
    // return _balance
  }
}

module.exports = Balance
