/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const util = require('../../util/util')
const otp = require('../otp/otpController')
const common = require('../../util/common')
const sms = require('../../util/sms')
const validator = require('../../util/validator')

class SecurityPin extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_user_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_master_id'
  }

  /**
     * Returns a login by its ID
     */
  static getByID (_, { id }) {
    return this.find(id)
  }

  static isCommonPin (pinStr) {
    const commonPin = ['1234', '0123', '0012', '9876', '9870', '1230', '1212']
    return commonPin.indexOf(pinStr) > -1
  }

  static async getUserData (fields, connection) {
    const userDataSql = `
        SELECT 
        profileid,
        userid,
        user_status,
        mobile_id,
        security_pin,
        security_pin_attempts,
        IF(security_lock_expiry is not null,IF(security_lock_expiry > CURRENT_TIMESTAMP,TRUE,FALSE),FALSE) as security_lock,
        IF(security_pin_expiry is not null,IF(security_pin_expiry > CURRENT_TIMESTAMP,FALSE,TRUE),FALSE) as pin_expired
        FROM ma_user_master
        WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid}
        limit 1
      `
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'userDataSql', fields: userDataSql })
    const userData = await this.rawQuery(userDataSql, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'userData', fields: userData })

    const encryptDataSql = `
        SELECT 
        *
        FROM ma_user_secure_details
        WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid}
        limit 1
      `
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'encryptDataSql', fields: encryptDataSql })
    const encryptData = await this.rawQuery(encryptDataSql, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'encryptData', fields: encryptData })

    const answerDataSql = `
      SELECT 
      *
      FROM ma_user_security_answer_mapping
      WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid}      
    `
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'answerDataSql', fields: answerDataSql })
    const answerData = await this.rawQuery(answerDataSql, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'answerData', fields: answerData })

    const mobileDataSql = `SELECT mobile_id FROM ma_user_master WHERE profileid = ${fields.ma_user_id} AND mer_user = 'mer' limit 1`
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'mobileDataSql', fields: mobileDataSql })
    const mobileData = await this.rawQuery(mobileDataSql, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getUserData', type: 'mobileData', fields: mobileData })
    return { userData: userData, encryptData: encryptData, answerData: answerData, mobileData: mobileData }
  }

  /**
     * Creates a new setSecurityPinOtp
     */
  static async setNewSecurityPinOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'setNewSecurityPinOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const response = {}

      const userDataAll = await this.getUserData(fields, connection)
      const userData = userDataAll.userData
      if (userData.length > 0) {
        if (userData[0].user_status === 'Y') {
          if (userData[0].security_pin === null || userData[0].security_pin === '') {
            const securityQuestionSql = `
            SELECT * FROM ma_security_questions_master WHERE question_status = '1'
        `
            const securityQuestionList = await this.rawQuery(securityQuestionSql, connection)

            if (securityQuestionList.length > 0) {
              // const resp = await otp.sentOtp(userData[0].profileid, userData[0].userid, userData[0].mobile_id, 'SP', {})
              response.status = 200
              response.respcode = 1000
              response.message = errorMsg.responseCode[1000]
              // response.orderid = resp.data.aggregator_order_id
              response.orderid = ''
              response.securityQuestionList = securityQuestionList
              response.secretQsRequired = util.secretQsConfig.secretQsRequiredNew
              response.action_code = 1000
              return response
            } else {
              response.status = 400
              response.respcode = 1092
              response.message = errorMsg.responseCode[1092]
              response.orderid = ''
              response.action_code = 1001
              return response
            }
          } else {
            response.status = 400
            response.respcode = 1093
            response.message = errorMsg.responseCode[1093]
            response.orderid = ''
            response.action_code = 1001
            return response
          }
        } else {
          response.status = 400
          response.respcode = 1053
          response.message = errorMsg.responseCode[1053]
          response.orderid = ''
          response.action_code = 1001
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], orderid: '', action_code:1001 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'setNewSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, orderid: '', action_code:1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async checkSecurityPinSet (_, fields, prevconnection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkSecurityPinSet', type: 'request', fields: fields })
    let connection = null
    let isSet = false
    try {
      if (('connection' in fields) && fields.connection != null) {
        connection = fields.connection
        console.log('fields.connection')
      } else if (prevconnection != null && prevconnection != undefined && prevconnection.threadId != undefined) {
        connection = prevconnection
      } else {
        isSet = true
        connection = await mySQLWrapper.getConnectionFromPool()
      }
      const response = {}
      const userDataAll = await this.getUserData(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkSecurityPinSet', type: 'userDataAll', fields: userDataAll })
      const userData = userDataAll.userData
      console.log('userData', userData)
      if (userData.length > 0) {
        const isToLock = (userDataAll.userData[0].security_lock === 1 && userDataAll.userData[0].security_pin_attempts === util.secretQsConfig.secretPinMaxAttempt)
        if (userData[0].security_pin !== '' && userData[0].security_pin !== null) {
          response.status = 200
          response.respcode = 1093
          response.allow_new_pin = false
          response.pinExpired = userDataAll.userData[0].pin_expired
          response.security_pin_attempts = userDataAll.userData[0].security_pin_attempts
          response.accountLock = isToLock
          response.message = errorMsg.responseCode[1093]
          response.userDataAll = userDataAll
          response.action_code = 1000
          return response
        } else {
          response.status = 200
          response.respcode = 1094
          response.allow_new_pin = true
          response.pinExpired = userDataAll.userData[0].pin_expired
          response.accountLock = isToLock
          response.security_pin_attempts = userDataAll.userData[0].security_pin_attempts
          response.message = errorMsg.responseCode[1094]
          response.userDataAll = userDataAll
          response.action_code = 1009
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkSecurityPinSet', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Releases the connection
      if (isSet) {
        // Releases the connection
        connection.release()
      }
    }
  }

  static async verifyNewSecurityPinOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      let validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108] , action_code: 1001}
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      // Validate secretQsAns Json
      const validArr = await this.validateQuestionArr(fields, util.secretQsConfig.secretQsRequiredNew, false)
      if (validArr.status === 200) {
        validQuestionData = validArr.validQuestionData
      } else {
        return validArr
      }

      // Transaction Begins
      await mySQLWrapper.beginTransaction(connection)

      const verify = await otp.verifyOtp(fields.orderid, 'SP', fields.otp)
      if (verify.status === true) {
        const isNewPinData = await this.checkSecurityPinSet(null, {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid
        }, connection)

        if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === true) {
          // To do encrypt pin
          const encryptedPin = fields.newPin

          const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString())

          // const decryptData = await common.decryptRandomWithKey(encryptData.encryptData, encryptData.encryptKey)
          // console.log('decryptData', decryptData)
          //  return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
          // Store security Pin
          const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryType},${util.secretQsConfig.secretPinExpiryVal},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

          const updateSecretQueryRes = await this.rawQuery(sql, connection)

          if (updateSecretQueryRes.affectedRows <= 0) {
            // Rollback
            await mySQLWrapper.rollback(connection)
            // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
          }

          this.TABLE_NAME = 'ma_user_secure_details'
          const _resultEncryptId = await this.insert(connection, {
            data: {
              encrypt_key: encryptPinData.encryptKey,
              ma_user_id: fields.ma_user_id,
              userid: fields.userid
            }
          })

          const insertedIDs = []
          for (let index = 0; index < validQuestionData.length; index++) {
            const currtQsAns = validQuestionData[index]

            this.TABLE_NAME = 'ma_user_security_answer_mapping'
            const encryptAnsData = await common.encryptRandomWithKey(currtQsAns.secret_answer.toString(), encryptPinData.encryptKey)
            const _result = await this.insert(connection, {
              data: {
                question_id: currtQsAns.question_id,
                ma_user_id: currtQsAns.ma_user_id,
                secret_answer: encryptAnsData.encryptData,
                userid: currtQsAns.userid
              }
            })

            insertedIDs.push({ answer_id: _result.insertId })
          }

          if (insertedIDs.length === validQuestionData.length) {
            await mySQLWrapper.commit(connection)
            const mobilenumber = isNewPinData.userDataAll.userData[0].mobile_id
            var spSuccess = util.communication.SPSUCCESS
            spSuccess = spSuccess.replace('<Customer>', 'Customer')
            spSuccess = spSuccess.replace('<Salutation>', util.communication.Signature)
            await sms.sentSmsAsync(spSuccess, mobilenumber, util.templateid.SPSUCCESS)
            return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009], action_code:1000 }
          } else {
            // Rollback
            await mySQLWrapper.rollback(connection)
            return { status: 400, respcode: 1093, message: errorMsg.responseCode[1093], action_code:1001 }
          }
        } else {
          // Rollback
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1093, message: errorMsg.responseCode[1093], action_code:1001 }
        }
      } else {
        // Rollback
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: verify.respcode, message: verify.message, action_code:1001 }
      }
    } catch (err) {
      // Rollback
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async resentSecurityPinOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentSecurityPinOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const userDataAll = await this.getUserData(fields, connection)
      const userData = userDataAll.userData
      // const userData = userDataAll.userData
      const mobileData = userDataAll.mobileData
      if (userData.length > 0) {
        const otpResponse = await otp.resentOtp(mobileData[0].mobile_id, fields.orderid, 'SP')
        if (otpResponse.status === true) {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] + ' . Please check the registered mobile number - ' + mobileData[0].mobile_id.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*'), action_code:1000 }
        } else {
          return { status: 400, respcode: otpResponse.respcode, message: otpResponse.message, action_code:1001 }
        }
      }
      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], action_code:1001 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
    } finally {
      connection.release()
    }
  }

  static async validateQuestionArr (fields, noOfRequired) {
    // Validate secretQsAns Json
    try {
      const validQuestionData = []
      console.log('fields.secretQsAns', fields.secretQsAns)
      const secretQsAns = Buffer.from(fields.secretQsAns, 'base64').toString('ascii')
      console.log('secretQsAnsbase64', secretQsAns)
      fields.secretQsAnsArr = JSON.parse(secretQsAns)
      console.log('secretQsAnsArray', fields.secretQsAnsArr)
      const isValidArray = Object.prototype.toString.call(fields.secretQsAnsArr) === '[object Array]'
      // provided data match with required no fo question

      if (isValidArray && fields.secretQsAnsArr.length === noOfRequired) {
        for (let index = 0; index < fields.secretQsAnsArr.length; index++) {
          const currQs = fields.secretQsAnsArr[index]
          if (('question_id' in currQs) && currQs.question_id > 0 && ('secret_answer' in currQs) && (currQs.secret_answer !== '' && currQs.secret_answer !== null)) {
            const tempQs = {
              ...currQs,
              ma_user_id: fields.ma_user_id,
              userid: fields.userid
            }

            validQuestionData.push(tempQs)
          } else {
            return { status: 400, respcode: 1096, message: errorMsg.responseCode[1096], action_code: 1001 }
          }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], validQuestionData: validQuestionData, action_code: 1000 }
      } else {
        return { status: 400, respcode: 1097, message: errorMsg.responseCode[1097], action_code: 1001 }
      }
    } catch (error) {
      // Rollback
      console.log('secretQsAnsError', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateQuestionArr', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  /**
     * Creates a new changeSecurityPinOtp
     */
  static async changeSecurityPinOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'changeSecurityPinOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const response = {}

      const userDataAll = await this.getUserData(fields, connection)
      const userData = userDataAll.userData
      if (userData.length > 0) {
        if (userData[0].user_status === 'Y') {
          if (userData[0].security_pin !== '' && userData[0].security_pin !== null) {
            const isToLock = (userData[0].security_lock === 1 && (userData[0].security_pin_attempts === util.secretQsConfig.secretPinMaxAttempt))
            if (isToLock) {
              response.status = 400
              response.respcode = 1103
              response.message = errorMsg.responseCode[1103]
              response.orderid = ''
              response.action_code = 1001
              return response
            }

            const securityQuestionSql = `
              SELECT msqm.* FROM ma_security_questions_master as msqm
              JOIN ma_user_security_answer_mapping as musam ON musam.question_id = msqm.question_id
              WHERE msqm.question_status = '1' AND musam.ma_user_id = ${fields.ma_user_id} AND musam.userid = ${fields.userid} 
              GROUP BY musam.question_id
          `
            const securityQuestionList = await this.rawQuery(securityQuestionSql, connection)

            if (securityQuestionList.length > 0) {
              // const resp = await otp.sentOtp(userData[0].profileid, userData[0].userid, userData[0].mobile_id, 'SP', {})
              response.status = 200
              response.respcode = 1000
              response.message = errorMsg.responseCode[1000]
              // response.orderid = resp.data.aggregator_order_id
              response.orderid = ''
              response.securityQuestionList = securityQuestionList
              response.secretQsRequired = util.secretQsConfig.secretQsRequiredChange
              response.action_code = 1000
              return response
            } else {
              response.status = 400
              response.respcode = 1092
              response.message = errorMsg.responseCode[1092]
              response.orderid = ''
              response.action_code = 1001
              return response
            }
          } else {
            response.status = 400
            response.respcode = 1094
            response.message = errorMsg.responseCode[1094]
            response.orderid = ''
            response.action_code = 1001
            return response
          }
        } else {
          response.status = 400
          response.respcode = 1053
          response.message = errorMsg.responseCode[1053]
          response.orderid = ''
          response.action_code = 1001
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], orderid: '', action_code:1001 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'changeSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, orderid: '', action_code:1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async verifyChangeSecurityPinOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      let validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code:1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code:1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code:1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code:1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code:1001 }
      }

      // Validate secretQsAns Json
      const validArr = await this.validateQuestionArr(fields, util.secretQsConfig.secretQsRequiredChange, false)
      if (validArr.status === 200) {
        validQuestionData = validArr.validQuestionData
      } else {
        return validArr
      }

      // Transaction Begins
      await mySQLWrapper.beginTransaction(connection)

      const verify = await otp.verifyOtp(fields.orderid, 'SP', fields.otp)
      if (verify.status === true) {
        const isNewPinData = await this.checkSecurityPinSet(null, {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid
        }, connection)

        if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === false) {
          if (isNewPinData.accountLock) {
            await mySQLWrapper.rollback(connection)
            return { status: 400, respcode: 1103, message: errorMsg.responseCode[1103], action_code:1001 }
          }

          const answerData = isNewPinData.userDataAll.answerData
          const encryptDataStored = isNewPinData.userDataAll.encryptData
          const userDataTmp = isNewPinData.userDataAll.userData[0]
          console.log('isNewPinData.userDataAll', isNewPinData.userDataAll)
          if (encryptDataStored.length > 0) {
            const existingKey = encryptDataStored[0].encrypt_key
            const validAnsArr = await this.validateQuestionAnsArr(answerData, validQuestionData, existingKey, connection)
            if (validAnsArr.status === 200) {
              // To do encrypt pin
              const encryptedPin = fields.newPin

              const oldSecurityPin = userDataTmp.security_pin
              console.log('oldSecurityPin', oldSecurityPin)
              console.log('existingKey', existingKey)
              const oldPin = await common.decryptRandomWithKey(oldSecurityPin, existingKey)

              if (oldPin === encryptedPin.toString()) {
                return { status: 400, respcode: 1128, message: errorMsg.responseCode[1128], action_code:1001 }
              }

              const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString(), existingKey)
              // Store security Pin
              const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryType},${util.secretQsConfig.secretPinExpiryVal},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

              const updateSecretQueryRes = await this.rawQuery(sql, connection)

              if (updateSecretQueryRes.affectedRows <= 0) {
                // Rollback
                await mySQLWrapper.rollback(connection)
                // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
                return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
              }

              const pinAttemptsSql = `UPDATE ma_user_master SET security_pin_attempts = 0,security_lock_expiry = NULL WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

              const updateSecretPinAttemptRes = await this.rawQuery(pinAttemptsSql, connection)

              if (updateSecretPinAttemptRes.affectedRows <= 0) {
                // Rollback
                await mySQLWrapper.rollback(connection)
                // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretPinAttemptRes.sqlMessage }
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: updateSecretPinAttemptRes })
                return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
              }

              await mySQLWrapper.commit(connection)
              var spSuccess = util.communication.SPSUCCESS
              spSuccess = spSuccess.replace('<Customer>', 'Customer')
              spSuccess = spSuccess.replace('<Salutation>', util.communication.Signature)
              const mobilenumber = isNewPinData.userDataAll.userData[0].mobile_id
              await sms.sentSmsAsync(spSuccess, mobilenumber, util.templateid.SPSUCCESS)
              return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009] , action_code:1000}
            } else {
              await mySQLWrapper.rollback(connection)
              return validAnsArr
            }
          } else {
            // Rollback
            await mySQLWrapper.rollback(connection)

            return { status: 400, respcode: 1100, message: errorMsg.responseCode[1100], action_code:1001 }
          }
        } else {
          // Rollback
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1094, message: errorMsg.responseCode[1094], action_code:1001 }
        }
      } else {
        // Rollback
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: verify.respcode, message: verify.message, action_code:1001 }
      }
    } catch (err) {
      // Rollback
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
    } finally {
      connection.release()
    }
  }

  static async validateQuestionAnsArr (answerData, questionAnsArr, existingKey, connection) {
    // Validate secretQsAns Json
    try {
      const validQuestionData = []
      const isValidArray = Object.prototype.toString.call(questionAnsArr) === '[object Array]'
      // provided data match with required no fo question
      const isValidArrayAns = Object.prototype.toString.call(answerData) === '[object Array]'
      if (isValidArray && isValidArrayAns) {
        for (let index = 0; index < questionAnsArr.length; index++) {
          const currQs = questionAnsArr[index]
          if (('question_id' in currQs) && currQs.question_id > 0 && ('secret_answer' in currQs) && (currQs.secret_answer !== '' && currQs.secret_answer !== null)) {
            // To do decrypt secret_answer
            let previousEncryptedAns = ''
            for (let index2 = 0; index2 < answerData.length; index2++) {
              const tmpAns = answerData[index2]
              if (tmpAns.question_id == currQs.question_id) {
                previousEncryptedAns = tmpAns.secret_answer
                break
              }
            }

            const decryptedAns = await common.decryptRandomWithKey(previousEncryptedAns, existingKey)

            if (currQs.secret_answer.toLowerCase() == decryptedAns.toLowerCase()) {
              validQuestionData.push(currQs)
            } else {
              return { status: 400, respcode: 1099, message: errorMsg.responseCode[1099] }
            }
          } else {
            return { status: 400, respcode: 1096, message: errorMsg.responseCode[1096] }
          }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], validQuestionData: validQuestionData }
      } else {
        return { status: 400, respcode: 1097, message: errorMsg.responseCode[1097] }
      }
    } catch (error) {
      // Rollback
      console.log('secretQsAnsError', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateQuestionAnsArr', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async verifySecurePin (_, fields) {
    const inputParams = { ...fields }
    inputParams.connection = null
    log.logger({ pagename: require('path').basename(__filename), action: 'verifySecurePin', type: 'request', fields: inputParams })
    let connection = null
    let isSet = false
    try {
      if (!('security_pin' in fields) || fields.security_pin === null || fields.security_pin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107], action_code:1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.security_pin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':security_pin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      if (('connection' in fields) && fields.connection != null) {
        connection = fields.connection
      } else {
        isSet = true
        connection = await mySQLWrapper.getConnectionFromPool()
      }
      const PinData = await this.checkSecurityPinSet(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid
      }, connection)
      if (PinData.status === 200 && PinData.allow_new_pin === false) {
        if (PinData.accountLock) {
          return { status: 400, respcode: 1103, message: errorMsg.responseCode[1103], action_code:1001 }
        }

        const existingKey = PinData.userDataAll.encryptData[0].encrypt_key
        const oldSecurityPin = PinData.userDataAll.userData[0].security_pin
        const env = process.env.NODE_ENV
        const partial = util[env] ? util[env].partial : undefined
        if (!validator.definedVal(partial)) {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [SECURE_PIN_ENV]', action_code:1001 }
        }
        const oldPin = await common.decryptRandomWithKey(oldSecurityPin, existingKey)
        if (oldPin === fields.security_pin.toString()) {
          if (PinData.security_pin_attempts > 0) {
            const updateQuery = '0'
            await mySQLWrapper.beginTransaction(connection)
            const sql = `UPDATE ma_user_master SET security_pin_attempts = ${updateQuery} WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `
            const updateSecretPinAttemptRes = await this.rawQuery(sql, connection)
            await mySQLWrapper.commit(connection)
          }

          if (PinData.pinExpired) {
            return { status: 400, respcode: 1127, message: errorMsg.responseCode[1127], action_code:1010 }
          }
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code:1001 }
        } else {
          /** Reset Count After Pin Lock Expiry */
          let updateQuery = 'security_pin_attempts + 1'
          let resetAttemptCount = false
          if (PinData.security_pin_attempts >= util.secretQsConfig.secretPinMaxAttempt) {
            updateQuery = '1'
            PinData.security_pin_attempts = 1
            resetAttemptCount = true
          }
          // Transaction Begins
          await mySQLWrapper.beginTransaction(connection)
          const sql = `UPDATE ma_user_master SET security_pin_attempts = ${updateQuery},security_lock_expiry = TIMESTAMPADD(MINUTE,${util.secretQsConfig.accoutLockTimeInMin},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

          const updateSecretPinAttemptRes = await this.rawQuery(sql, connection)

          if (updateSecretPinAttemptRes.affectedRows <= 0) {
            // Rollback
            await mySQLWrapper.rollback(connection)
            // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretPinAttemptRes.sqlMessage }
            log.logger({ pagename: require('path').basename(__filename), action: 'verifySecurePin', type: 'catcherror', fields: updateSecretPinAttemptRes })
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
          }
          await mySQLWrapper.commit(connection)
          const attempted = resetAttemptCount ? PinData.security_pin_attempts : PinData.security_pin_attempts + 1

          let accountLockMsg = ''
          if (attempted === util.secretQsConfig.secretPinMaxAttempt) {
            accountLockMsg = ' ACCOUNT LOCKED '
          }
          return { status: 400, respcode: 1102, message: errorMsg.responseCode[1102] + ' Attempt ' + attempted + ' out of ' + util.secretQsConfig.secretPinMaxAttempt + accountLockMsg, action_code:1001 }
        }
      } else {
        return { status: 400, respcode: 1094, message: errorMsg.responseCode[1094], action_code:1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifySecurePin', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, orderid: '', action_code:1001 }
    } finally {
      if (isSet) {
        // Releases the connection
        connection.release()
      }
    }
  }

  static async checkAccountPinStatus (fields) {
    try {
      const securePinData = await this.checkSecurityPinSet(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid
      }, fields.connection)

      console.log('checkSecurityPinSet', securePinData)

      if (securePinData.status === 200 && securePinData.allow_new_pin === true) {
        return { status: 400, message: errorMsg.responseCode[1094], respcode: 1094 }
      } else if (securePinData.status === 200 && securePinData.allow_new_pin === false && securePinData.accountLock === true) {
        return { status: 400, respcode: 1103, message: errorMsg.responseCode[1103] }
      } else if (securePinData.status === 200 && securePinData.allow_new_pin === false && securePinData.pinExpired === true) {
        return { status: 400, respcode: 1127, message: errorMsg.responseCode[1127] }
      } else if (securePinData.status === 400) {
        return securePinData
      } else {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
    } catch (err) {
      console.log('checkAccountPinStatusErr', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkAccountPinStatus', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async sendOTPForSecurityPin (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOTPForSecurityPin', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      let validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code:1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code:1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code:1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code:1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code:1001 }
      }

      let noOfRequired = util.secretQsConfig.secretQsRequiredNew
      if (!fields.allow_new_pin) {
        noOfRequired = util.secretQsConfig.secretQsRequiredChange
      }
      // Validate secretQsAns Json
      const validArr = await this.validateQuestionArr(fields, noOfRequired, false)
      if (validArr.status === 200) {
        validQuestionData = validArr.validQuestionData
      } else {
        return validArr
      }

      const response = {}

      const userDataAll = await this.getUserData(fields, connection)
      const userData = userDataAll.userData
      const mobileData = userDataAll.mobileData
      if (userData.length > 0) {
        if (userData[0].user_status === 'Y') {
          const resp = await otp.sentOtp(userData[0].profileid, userData[0].userid, mobileData[0].mobile_id, 'SP', {})
          if (resp.status === true) {
            response.status = 200
            response.respcode = 2002
            response.message = errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + mobileData[0].mobile_id.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
            response.orderid = resp.data.aggregator_order_id
            response.action_code = 1000
            return response
          } else {
            response.status = 400
            response.respcode = resp.respcode
            response.message = resp.message
            response.orderid = ''
            response.action_code = 1001
            return response
          }
        } else {
          response.status = 400
          response.respcode = 1053
          response.message = errorMsg.responseCode[1053]
          response.orderid = ''
          response.action_code = 1001
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], orderid: '', action_code:1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOTPForSecurityPin', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, orderid: '', action_code:1001 }
    } finally {
      connection.release()
    }
  }

  static async setSecurityPin (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'setSecurityPin', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code:1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code:1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code:1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code:1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code:1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code:1001 }
      }

      await mySQLWrapper.beginTransaction(connection)

      const encryptedPin = fields.newPin

      const isNewPinData = await this.checkSecurityPinSet(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid
      }, connection)

      console.log('isNewPinData', isNewPinData)

      let existingKey = null
      if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === false) {
        const encryptDataStored = isNewPinData.userDataAll.encryptData
        existingKey = encryptDataStored[0].encrypt_key
      }

      const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString(), existingKey)
      console.log('encryptPinData', encryptPinData)

      const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryTypeExt},${util.secretQsConfig.secretPinExpiryValExt},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

      const updateSecretQueryRes = await this.rawQuery(sql, connection)

      if (updateSecretQueryRes.affectedRows <= 0) {
        // Rollback
        await mySQLWrapper.rollback(connection)
        // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
      }

      this.TABLE_NAME = 'ma_user_secure_details'

      if (existingKey == null) {
        const _resultEncryptId = await this.insert(connection, {
          data: {
            encrypt_key: encryptPinData.encryptKey,
            ma_user_id: fields.ma_user_id,
            userid: fields.userid
          }
        })
      }

      await mySQLWrapper.commit(connection)
      return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009], action_code:1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'setSecurityPin', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code:1001 }
    } finally {
      connection.release()
    }
  }

  static async getSecurityQuestionPin (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSecurityQuestionPin', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // allow_new_pin
      // if (userData[0].security_pin !== '' && userData[0].security_pin !== null) {}
      const response = {}
      const userDataAll = await this.getUserData(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSecurityQuestionPin', type: 'userDataAll', fields: userDataAll })
      const userData = userDataAll.userData
      if (userData.length > 0) {
        if (userData[0].user_status === 'Y') {
          if (userData[0].security_pin !== '' && userData[0].security_pin !== null) {
            const isToLock = (userData[0].security_lock === 1 && (userData[0].security_pin_attempts === util.secretQsConfig.secretPinMaxAttempt))
            if (isToLock) {
              response.status = 400
              response.respcode = 1103
              response.message = errorMsg.responseCode[1103]
              response.orderid = ''
              response.action_code = 1001
              response.allow_new_pin = false
              response.action_code = 1001
              return response
            }

            const securityQuestionSql = `
              SELECT msqm.* FROM ma_security_questions_master as msqm
              JOIN ma_user_security_answer_mapping as musam ON musam.question_id = msqm.question_id
              WHERE msqm.question_status = '1' AND musam.ma_user_id = ${fields.ma_user_id} AND musam.userid = ${fields.userid} 
              GROUP BY musam.question_id
            `
            log.logger({ pagename: require('path').basename(__filename), action: 'getSecurityQuestionPin', type: 'securityQuestionSql', fields: securityQuestionSql })
            const securityQuestionList = await this.rawQuery(securityQuestionSql, connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'getSecurityQuestionPin', type: 'securityQuestionList', fields: securityQuestionList })

            if (securityQuestionList.length > 0) {
              // const resp = await otp.sentOtp(userData[0].profileid, userData[0].userid, userData[0].mobile_id, 'SP', {})
              response.status = 200
              response.respcode = 1000
              response.message = errorMsg.responseCode[1000]
              // response.orderid = resp.data.aggregator_order_id
              response.orderid = ''
              response.securityQuestionList = securityQuestionList
              response.secretQsRequired = util.secretQsConfig.secretQsRequiredChange
              response.action_code = 1000
              response.allow_new_pin = false
              response.action_code = 1000
              return response
            } else {
              response.status = 400
              response.respcode = 1092
              response.message = errorMsg.responseCode[1092]
              response.orderid = ''
              response.action_code = 1001
              response.allow_new_pin = false
              response.action_code = 1001
              return response
            }
          } else {
            const securityQuestionSql1 = 'SELECT * FROM ma_security_questions_master WHERE question_status = "1"'
            const securityQuestionList1 = await this.rawQuery(securityQuestionSql1, connection)

            response.status = 200
            response.respcode = 1094
            response.message = 'Security pin is not set'
            response.orderid = ''
            response.action_code = 1000
            response.securityQuestionList = securityQuestionList1.length > 0 ? securityQuestionList1 : null
            response.secretQsRequired = util.secretQsConfig.secretQsRequiredNew
            response.allow_new_pin = true
            return response
          }
        } else {
          response.status = 400
          response.respcode = 1053
          response.message = errorMsg.responseCode[1053]
          response.orderid = ''
          response.action_code = 1001
          response.allow_new_pin = false
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], orderid: '', action_code: 1001, allow_new_pin: false }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSecurityQuestionPin', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, orderid: '', action_code: 1001, allow_new_pin: false }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async validateSecurityQuestions (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateSecurityQuestions', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      let validQuestionData = []

      let noOfRequired = util.secretQsConfig.secretQsRequiredNew
      if (!fields.allow_new_pin) {
        noOfRequired = util.secretQsConfig.secretQsRequiredChange
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'validateSecurityQuestions', type: 'noOfRequired', fields: noOfRequired })
      // Validate secretQsAns Json
      const validArr = await this.validateQuestionArr(fields, noOfRequired, false)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateSecurityQuestions', type: 'validArr', fields: validArr })
      if (validArr.status === 200) {
        validQuestionData = validArr.validQuestionData
      } else {
        return { ...validArr, action_code: 1001 }
      }

      const isNewPinData = await this.checkSecurityPinSet(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid
      }, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateSecurityQuestions', type: 'isNewPinData', fields: isNewPinData })

      if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === false) {
        if (isNewPinData.accountLock) {
          return { status: 400, respcode: 1103, message: errorMsg.responseCode[1103], action_code: 1001 }
        }

        const answerData = isNewPinData.userDataAll.answerData
        const encryptDataStored = isNewPinData.userDataAll.encryptData
        console.log('isNewPinData.userDataAll', isNewPinData.userDataAll)
        if (encryptDataStored.length > 0) {
          const existingKey = encryptDataStored[0].encrypt_key
          const validAnsArr = await this.validateQuestionAnsArr(answerData, validQuestionData, existingKey, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'validateSecurityQuestions', type: 'validAnsArr', fields: validAnsArr })
          if (validAnsArr.status === 200) {
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
          } else {
            return { ...validAnsArr, action_code: 1001 }
          }
        } else {
          return { status: 400, respcode: 1100, message: errorMsg.responseCode[1100], action_code: 1001 }
        }
      } else if (isNewPinData.status == 200 && isNewPinData.allow_new_pin == true) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
      } else {
        return { status: 400, respcode: 1094, message: errorMsg.responseCode[1094], action_code: 1001 }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOTPForSecurityPin', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001, orderid: '' }
    } finally {
      connection.release()
    }
  }

  static async sendOtpForPin (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpForPin', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      // let validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const response = {}

      const userDataAll = await this.getUserData(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpForPin', type: 'userDataAll', fields: userDataAll })
      const userData = userDataAll.userData
      const mobileData = userDataAll.mobileData
      if (userData.length > 0) {
        if (userData[0].user_status === 'Y') {
          if (userData[0].security_pin != '' && userData[0].security_pin != null) {
            const encryptDataStored = userDataAll.encryptData
            const userDataTmp = userDataAll.userData[0]
            if (encryptDataStored.length > 0) {
              const existingKey = encryptDataStored[0].encrypt_key
              // To do encrypt pin
              const encryptedPin = fields.newPin
              const oldSecurityPin = userDataTmp.security_pin
              log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpForPin', type: 'oldSecurityPin', fields: oldSecurityPin })
              log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpForPin', type: 'existingKey', fields: existingKey })
              const oldPin = await common.decryptRandomWithKey(oldSecurityPin, existingKey)
              log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpForPin', type: 'oldPin', fields: oldPin })
              if (oldPin === encryptedPin.toString()) {
                return { status: 400, respcode: 1128, message: errorMsg.responseCode[1128], action_code: 1001 }
              }
            }
          }
          const resp = await otp.sentOtp(userData[0].profileid, userData[0].userid, mobileData[0].mobile_id, 'SP', {})
          log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpForPin', type: 'resp', fields: resp })
          if (resp.status === true) {
            response.status = 200
            response.respcode = 2002
            response.message = errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + mobileData[0].mobile_id.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
            response.orderid = resp.data.aggregator_order_id
            response.action_code = 1000
            return response
          } else {
            response.status = 400
            response.respcode = resp.respcode
            response.message = resp.message
            response.orderid = ''
            response.action_code = 1001
            return response
          }
        } else {
          response.status = 400
          response.respcode = 1053
          response.message = errorMsg.responseCode[1053]
          response.orderid = ''
          response.action_code = 1001
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], orderid: '', action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOTPForSecurityPin', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001, orderid: '' }
    } finally {
      connection.release()
    }
  }

  static async verifyOtpForPin (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpForPin', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // check for new PIN
      const userDataAll = await this.getUserData(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSecurityQuestionPin', type: 'userDataAll', fields: userDataAll })
      const userData = userDataAll.userData
      if (userData.length > 0) {
        if (userData[0].user_status === 'Y') {
          if (userData[0].security_pin == '' || userData[0].security_pin == null) {
            fields.allow_new_pin = true
            const verifyOtpForNewPin = await this.verifyOtpForNewPin(fields)
            return verifyOtpForNewPin
          }
        }
      }
      // END check for new PIN ..............

      fields.allow_new_pin = false
      fields.secretQsAnsArr = []
      let validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      // Validate secretQsAns Json
      const validArr = await this.validateQuestionArr(fields, util.secretQsConfig.secretQsRequiredChange, false)
      if (validArr.status === 200) {
        validQuestionData = validArr.validQuestionData // validArr.validQuestionData = { ma_user_id: 100, question_id: 1, secret_answer: "yash", userid: 12 }
      } else {
        return { ...validArr, action_code: 1001 }
      }

      // Transaction Begins
      await mySQLWrapper.beginTransaction(connection)

      const verify = await otp.verifyOtp(fields.orderid, 'SP', fields.otp)
      if (verify.status === true) {
        const isNewPinData = await this.checkSecurityPinSet(null, {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid
        }, connection)

        if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === false) {
          if (isNewPinData.accountLock) {
            await mySQLWrapper.rollback(connection)
            return { status: 400, respcode: 1103, message: errorMsg.responseCode[1103], action_code: 1001 }
          }

          const answerData = isNewPinData.userDataAll.answerData
          const encryptDataStored = isNewPinData.userDataAll.encryptData
          const userDataTmp = isNewPinData.userDataAll.userData[0]
          console.log('isNewPinData.userDataAll', isNewPinData.userDataAll)
          if (encryptDataStored.length > 0) {
            const existingKey = encryptDataStored[0].encrypt_key
            const validAnsArr = await this.validateQuestionAnsArr(answerData, validQuestionData, existingKey, connection)
            if (validAnsArr.status === 200) {
              // To do encrypt pin
              const encryptedPin = fields.newPin

              const oldSecurityPin = userDataTmp.security_pin
              console.log('oldSecurityPin', oldSecurityPin)
              console.log('existingKey', existingKey)
              const oldPin = await common.decryptRandomWithKey(oldSecurityPin, existingKey)

              if (oldPin === encryptedPin.toString()) {
                return { status: 400, respcode: 1128, message: errorMsg.responseCode[1128], action_code: 1001 }
              }

              const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString(), existingKey)
              // Store security Pin
              const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryType},${util.secretQsConfig.secretPinExpiryVal},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

              const updateSecretQueryRes = await this.rawQuery(sql, connection)

              if (updateSecretQueryRes.affectedRows <= 0) {
                // Rollback
                await mySQLWrapper.rollback(connection)
                // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
                return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
              }

              const pinAttemptsSql = `UPDATE ma_user_master SET security_pin_attempts = 0,security_lock_expiry = NULL WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

              const updateSecretPinAttemptRes = await this.rawQuery(pinAttemptsSql, connection)

              if (updateSecretPinAttemptRes.affectedRows <= 0) {
                // Rollback
                await mySQLWrapper.rollback(connection)
                // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretPinAttemptRes.sqlMessage }
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: updateSecretPinAttemptRes })
                return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
              }

              await mySQLWrapper.commit(connection)
              var spSuccess = util.communication.SPSUCCESS
              spSuccess = spSuccess.replace('<Customer>', 'Customer')
              spSuccess = spSuccess.replace('<Salutation>', util.communication.Signature)
              const mobilenumber = isNewPinData.userDataAll.userData[0].mobile_id
              await sms.sentSmsAsync(spSuccess, mobilenumber, util.templateid.SPSUCCESS)
              return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009], action_code: 1000 }
            } else {
              await mySQLWrapper.rollback(connection)
              return { ...validAnsArr, action_code: 1001 }
            }
          } else {
            // Rollback
            await mySQLWrapper.rollback(connection)

            return { status: 400, respcode: 1100, message: errorMsg.responseCode[1100], action_code: 1001 }
          }
        } else {
          // Rollback
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1094, message: errorMsg.responseCode[1094], action_code: 1001 }
        }
      } else {
        // Rollback
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: verify.respcode, message: verify.message, action_code: 1001 }
      }
    } catch (err) {
      // Rollback
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async verifyOtpForNewPin (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      let validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      // Validate secretQsAns Json
      const validArr = await this.validateQuestionArr(fields, util.secretQsConfig.secretQsRequiredNew, false)
      if (validArr.status === 200) {
        validQuestionData = validArr.validQuestionData
      } else {
        return { ...validArr, action_code: 1001 }
      }

      // Transaction Begins
      await mySQLWrapper.beginTransaction(connection)

      const verify = await otp.verifyOtp(fields.orderid, 'SP', fields.otp)
      if (verify.status === true) {
        const isNewPinData = await this.checkSecurityPinSet(null, {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid
        }, connection)

        if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === true) {
          // To do encrypt pin
          const encryptedPin = fields.newPin

          const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString())

          // const decryptData = await common.decryptRandomWithKey(encryptData.encryptData, encryptData.encryptKey)
          // console.log('decryptData', decryptData)
          //  return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
          // Store security Pin
          const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryType},${util.secretQsConfig.secretPinExpiryVal},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

          const updateSecretQueryRes = await this.rawQuery(sql, connection)

          if (updateSecretQueryRes.affectedRows <= 0) {
            // Rollback
            await mySQLWrapper.rollback(connection)
            // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
          }

          this.TABLE_NAME = 'ma_user_secure_details'
          const _resultEncryptId = await this.insert(connection, {
            data: {
              encrypt_key: encryptPinData.encryptKey,
              ma_user_id: fields.ma_user_id,
              userid: fields.userid
            }
          })

          const insertedIDs = []
          for (let index = 0; index < validQuestionData.length; index++) {
            const currtQsAns = validQuestionData[index]

            this.TABLE_NAME = 'ma_user_security_answer_mapping'
            const encryptAnsData = await common.encryptRandomWithKey(currtQsAns.secret_answer.toString(), encryptPinData.encryptKey)
            const _result = await this.insert(connection, {
              data: {
                question_id: currtQsAns.question_id,
                ma_user_id: currtQsAns.ma_user_id,
                secret_answer: encryptAnsData.encryptData,
                userid: currtQsAns.userid
              }
            })

            insertedIDs.push({ answer_id: _result.insertId })
          }

          if (insertedIDs.length === validQuestionData.length) {
            await mySQLWrapper.commit(connection)
            const mobilenumber = isNewPinData.userDataAll.userData[0].mobile_id
            var spSuccess = util.communication.SPSUCCESS
            spSuccess = spSuccess.replace('<Customer>', 'Customer')
            spSuccess = spSuccess.replace('<Salutation>', util.communication.Signature)
            await sms.sentSmsAsync(spSuccess, mobilenumber, util.templateid.SPSUCCESS)
            return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009], action_code: 1001 }
          } else {
            // Rollback
            await mySQLWrapper.rollback(connection)
            return { status: 400, respcode: 1093, message: errorMsg.responseCode[1093], action_code: 1001 }
          }
        } else {
          // Rollback
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1093, message: errorMsg.responseCode[1093], action_code: 1001 }
        }
      } else {
        // Rollback
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: verify.respcode, message: verify.message, action_code: 1001 }
      }
    } catch (err) {
      // Rollback
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyNewSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async sendOTPForSecurityPin1 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOTPForSecurityPin', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // fields.secretQsAnsArr = []
      // const validQuestionData = []

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      let noOfRequired = util.secretQsConfig.secretQsRequiredNew
      if (!fields.allow_new_pin) {
        noOfRequired = util.secretQsConfig.secretQsRequiredChange
      }

      const response = {}

      const userDataAll = await this.getUserData(fields, connection)
      console.log('userDataAll>>>>', userDataAll)
      const userData = userDataAll.userData
      const mobileData = userDataAll.mobileData
      if (userData.length > 0) {
        const encrypTedData = userDataAll.encryptData
        let existingKey
        if (encrypTedData.length > 0) {
          existingKey = encrypTedData[0].encrypt_key
          console.log('existingKey>>>', existingKey)
        }
        const newPin = fields.newPin

        // to check the previous 3 pin

        const getOldSecurityPinsql = `Select security_pin from security_pin_histories where ma_user_id =${fields.ma_user_id} and user_id =${fields.userid} order by created_at desc limit 3`
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpForChangePin', type: 'getOldSecurityPinsql', fields: getOldSecurityPinsql })
        const getOldSecurityPinData = await this.rawQuery(getOldSecurityPinsql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpForChangePin', type: 'getOldSecurityPinData', fields: getOldSecurityPinData })
        if (getOldSecurityPinData.length > 0) {
          const oldPinArray = []
          let oldPin
          // const getOldSecurityPin = getOldSecurityPinData[0].security_pin
          console.log('getOldSecurityPin>>>', getOldSecurityPinData)
          for (const order in getOldSecurityPinData) {
            const pin = getOldSecurityPinData[order].security_pin
            console.log('pin>>>', pin)
            oldPin = await common.decryptRandomWithKey(pin, existingKey)
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpForChangePin', type: 'oldPin', fields: oldPin })
            oldPinArray.push(oldPin)
          }
          console.log('oldPinArray>>>', oldPinArray)
          if (oldPinArray.includes(newPin)) {
            return { status: 400, respcode: 1128, message: errorMsg.responseCode[1128], action_code: 1001 }
          }
        }
        if (userData[0].user_status === 'Y') {
          const resp = await otp.sentOtp(userData[0].profileid, userData[0].userid, mobileData[0].mobile_id, 'SP', {})
          if (resp.status === true) {
            response.status = 200
            response.respcode = 2002
            response.message = errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + mobileData[0].mobile_id.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
            response.orderid = resp.data.aggregator_order_id
            response.action_code = 1000
            return response
          } else {
            response.status = 400
            response.respcode = resp.respcode
            response.message = resp.message
            response.orderid = ''
            response.action_code = 1001
            return response
          }
        } else {
          response.status = 400
          response.respcode = 1053
          response.message = errorMsg.responseCode[1053]
          response.orderid = ''
          response.action_code = 1001
          return response
        }
      }

      return { status: 400, respcode: 1076, message: errorMsg.responseCode[1076], orderid: '', action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOTPForSecurityPin', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, orderid: '', action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async verifyChangeSecurityPinOtp1 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.secretQsAnsArr = []
      // let validQuestionData = []

      if (!('otp' in fields) || fields.otp === null || fields.otp === undefined || fields.otp == '') {
        return { status: 400, respcode: 1001, message: 'Please provide otp to proceed', action_code: 1001 }
      }

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      // Transaction Begins
      await mySQLWrapper.beginTransaction(connection)

      const verify = await otp.verifyOtp(fields.orderid, 'SP', fields.otp)
      if (verify.status === true) {
        const isNewPinData = await this.checkSecurityPinSet(null, {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid
        }, connection)

        if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === false) {
          if (isNewPinData.accountLock) {
            await mySQLWrapper.rollback(connection)
            return { status: 400, respcode: 1103, message: errorMsg.responseCode[1103], action_code: 1001 }
          }

          // const answerData = isNewPinData.userDataAll.answerData
          const encryptDataStored = isNewPinData.userDataAll.encryptData
          const userDataTmp = isNewPinData.userDataAll.userData[0]
          console.log('isNewPinData.userDataAll', isNewPinData.userDataAll)
          if (encryptDataStored.length > 0) {
            const existingKey = encryptDataStored[0].encrypt_key
            // To do encrypt pin
            const encryptedPin = fields.newPin

            const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString(), existingKey)
            // Store security Pin
            const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryType},${util.secretQsConfig.secretPinExpiryVal},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

            const updateSecretQueryRes = await this.rawQuery(sql, connection)

            if (updateSecretQueryRes.affectedRows <= 0) {
              // Rollback
              await mySQLWrapper.rollback(connection)
              // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
              log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
              return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
            }

            const pinAttemptsSql = `UPDATE ma_user_master SET security_pin_attempts = 0,security_lock_expiry = NULL WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

            const updateSecretPinAttemptRes = await this.rawQuery(pinAttemptsSql, connection)

            if (updateSecretPinAttemptRes.affectedRows <= 0) {
              // Rollback
              await mySQLWrapper.rollback(connection)
              // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretPinAttemptRes.sqlMessage }
              log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: updateSecretPinAttemptRes })
              return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
            }
            const inserQuerySql = `Insert into security_pin_histories (ma_user_id,user_id,security_pin) Values(${fields.ma_user_id},${fields.userid},'${encryptPinData.encryptData}') `
            const insertQuery = await this.rawQuery(inserQuerySql, connection)

            await mySQLWrapper.commit(connection)
            var spSuccess = util.communication.SPSUCCESS
            spSuccess = spSuccess.replace('<Customer>', 'Customer')
            spSuccess = spSuccess.replace('<Salutation>', util.communication.Signature)
            const mobilenumber = isNewPinData.userDataAll.userData[0].mobile_id
            await sms.sentSmsAsync(spSuccess, mobilenumber, util.templateid.SPSUCCESS)
            return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009], action_code: 1000 }
          } else {
            // Rollback
            await mySQLWrapper.rollback(connection)

            return { status: 400, respcode: 1100, message: errorMsg.responseCode[1100], action_code: 1001 }
          }
        } else {
          // Rollback
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1094, message: errorMsg.responseCode[1094], action_code: 1001 }
        }
      } else {
        // Rollback
        await mySQLWrapper.rollback(connection)
        if (verify.respcode == 1006) {
          return { status: 400, respcode: verify.respcode, message: verify.message + '. Kindly refresh your page and try again later.', action_code: 1001 }
        }
        return { status: 400, respcode: verify.respcode, message: verify.message, action_code: 1001 }
      }
    } catch (err) {
      // Rollback
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyChangeSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async newVerifyNewSecurityPinOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'newVerifyNewSecurityPinOtp', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_user_master'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (!('otp' in fields) || fields.otp === null || fields.otp === undefined || fields.otp == '') {
        return { status: 400, respcode: 1001, message: 'Please provide otp to proceed', action_code: 1001 }
      }

      if (!('newPin' in fields) || fields.newPin === null || fields.newPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':newPin', action_code: 1001 }
      }

      if (!('confirmNewPin' in fields) || fields.confirmNewPin === null || fields.confirmNewPin === undefined) {
        return { status: 400, respcode: 1107, message: errorMsg.responseCode[1107] + ':confirmNewPin', action_code: 1001 }
      }

      const isValidnewPin = validator.validInput('stringlength', fields.newPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidnewPin', isValidnewPin)
      if (!isValidnewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':newPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      const isValidReconfirmNewPin = validator.validInput('stringlength', fields.confirmNewPin.toString(), util.secretQsConfig.secretPinLength)
      console.log('isValidReconfirmNewPin', isValidReconfirmNewPin)
      if (!isValidReconfirmNewPin) {
        return { status: 400, respcode: 1105, message: errorMsg.responseCode[1105] + ':confirmNewPin:Required ' + util.secretQsConfig.secretPinLength + ' digit pin', action_code: 1001 }
      }

      //  new Pin and confirm New Pin number
      if (fields.newPin !== fields.confirmNewPin) {
        return { status: 400, respcode: 1098, message: errorMsg.responseCode[1098], action_code: 1001 }
      }

      const isValidCriteriaPin = validator.validInput('repeatnobytwice', fields.newPin.toString())
      console.log('isValidCriteriaPin', isValidCriteriaPin)
      if (isValidCriteriaPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      const isValidCommonPin = this.isCommonPin(fields.newPin.toString())
      console.log('isValidCommonPin', isValidCommonPin)
      if (isValidCommonPin) {
        return { status: 400, respcode: 1108, message: errorMsg.responseCode[1108], action_code: 1001 }
      }

      // Transaction Begins
      await mySQLWrapper.beginTransaction(connection)

      const verify = await otp.verifyOtp(fields.orderid, 'SP', fields.otp)
      if (verify.status === true) {
        const isNewPinData = await this.checkSecurityPinSet(null, {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid
        }, connection)

        console.log('isNewPinData>>>>', isNewPinData)

        if (isNewPinData.status === 200 && isNewPinData.allow_new_pin === true) {
          // To do encrypt pin
          const encryptedPin = fields.newPin

          const encryptPinData = await common.encryptRandomWithKey(encryptedPin.toString())

          // const decryptData = await common.decryptRandomWithKey(encryptData.encryptData, encryptData.encryptKey)
          // console.log('decryptData', decryptData)
          //  return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
          // Store security Pin
          const sql = `UPDATE ma_user_master SET security_pin = "${encryptPinData.encryptData}",security_pin_expiry = TIMESTAMPADD(${util.secretQsConfig.secretPinExpiryType},${util.secretQsConfig.secretPinExpiryVal},CURRENT_TIMESTAMP()) WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} `

          const updateSecretQueryRes = await this.rawQuery(sql, connection)

          if (updateSecretQueryRes.affectedRows <= 0) {
            // Rollback
            await mySQLWrapper.rollback(connection)
            // return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' ' + updateSecretQueryRes.sqlMessage }
            log.logger({ pagename: require('path').basename(__filename), action: 'newVerifyNewSecurityPinOtp', type: 'catcherror', fields: updateSecretQueryRes })
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
          }

          this.TABLE_NAME = 'ma_user_secure_details'
          const _resultEncryptId = await this.insert(connection, {
            data: {
              encrypt_key: encryptPinData.encryptKey,
              ma_user_id: fields.ma_user_id,
              userid: fields.userid
            }
          })

          const inserQuerySql = `Insert into security_pin_histories (ma_user_id,user_id,security_pin) Values(${fields.ma_user_id},${fields.userid},'${encryptPinData.encryptData}') `
          const insertQuery = await this.rawQuery(inserQuerySql, connection)

          await mySQLWrapper.commit(connection)
          const mobilenumber = isNewPinData.userDataAll.userData[0].mobile_id
          var spSuccess = util.communication.SPSUCCESS
          spSuccess = spSuccess.replace('<Customer>', 'Customer')
          spSuccess = spSuccess.replace('<Salutation>', util.communication.Signature)
          await sms.sentSmsAsync(spSuccess, mobilenumber, util.templateid.SPSUCCESS)
          return { status: 200, respcode: 2009, message: errorMsg.responseCode[2009], action_code: 1000 }
        } else {
          // Rollback
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1093, message: errorMsg.responseCode[1093], action_code: 1001 }
        }
      } else {
        // Rollback
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: verify.respcode, message: verify.message, action_code: 1001 }
      }
    } catch (err) {
      // Rollback
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'newVerifyNewSecurityPinOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }
}
module.exports = SecurityPin
