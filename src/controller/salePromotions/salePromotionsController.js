/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const path = require('path')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const common = require('../../util/common')
const util = require('../../util/util')

class salePromotions extends DAO {
  /**
         * Overrides TABLE_NAME with this class' backing table at MySQL
         */
  get TABLE_NAME () {
    return 'ma_marketing_carousel_images'
  }

  static get PRIMARY_KEY () {
    return 'ma_marketing_carousel_images'
  }

  static async offerBanners (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'offerBanners', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    // Validate All Required Fields - Add parameter name in array for field validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse
    try {
      const carouselChangeTime = await common.getSystemCodes(this, util.marketing_carousel_change_time, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'offerBanners', type: 'carouselChangeTime', fields: { carouselChangeTime: carouselChangeTime } })
      const sqlQuery = `SELECT user_type, state from ma_user_master where profileid = '${fields.ma_user_id}' and userid = '${fields.userid}' and user_status = 'Y' limit 1;`
      log.logger({ pagename: path.basename(__filename), action: 'offerBanners', type: 'sqlQuery', fields: sqlQuery })
      const sqlResult = await this.rawQuery(sqlQuery, connection)
      log.logger({ pagename: path.basename(__filename), action: 'offerBanners', type: 'sqlResult', fields: sqlResult })
      if (sqlResult.length <= 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
      const stateId = sqlResult[0].state
      const userType = sqlResult[0].user_type

      console.log('location', fields.banner_location)
      let bannerLocation
      if (fields.banner_location == '' || fields.banner_location == null || fields.banner_location == undefined) {
        bannerLocation = 'Homepage'
      } else {
        bannerLocation = fields.banner_location
      }
      // Check whether the banner is active or not,check whether the banner is only visible between the start and end time.
      const bannerquery = `Select ma_images_id, image_url, hyperlink, start_date_time,end_date_time,image_stat,states_id, show_in_app, show_in_web, link_type , user_type,alt_image from ma_marketing_carousel_images where ((now() between start_date_time and end_date_time) or (now() between start_date_time and  end_date_time  is null)) and image_stat= 'A' and (FIND_IN_SET('${userType}', user_type) OR
      user_type IS NULL OR user_type = '') and banner_location = '${bannerLocation}'`
      log.logger({ pagename: path.basename(__filename), action: 'offerBanners', type: 'bannerquery', fields: bannerquery })
      const bannerQueryResult = await this.rawQuery(bannerquery, connection)
      log.logger({ pagename: path.basename(__filename), action: 'offerBanners', type: 'bannerQueryResult', fields: bannerQueryResult })
      var offerBannersDetails = []
      if (bannerQueryResult.length > 0) {
        for (var i = 0; i < bannerQueryResult.length; i++) {
          var tempBanner = {}
          if (bannerQueryResult[i].states_id == '' || bannerQueryResult[i].states_id == null) {
            tempBanner.imageid = bannerQueryResult[i].ma_images_id
            tempBanner.campaign_id = bannerQueryResult[i].ma_images_id
            tempBanner.imagepath = bannerQueryResult[i].image_url
            tempBanner.hyperlink = bannerQueryResult[i].hyperlink
            tempBanner.show_in_app = bannerQueryResult[i].show_in_app
            tempBanner.show_in_web = bannerQueryResult[i].show_in_web
            tempBanner.link_type = bannerQueryResult[i].link_type
            tempBanner.imagename = bannerQueryResult[i].alt_image || ''
            tempBanner.campaign_name = bannerQueryResult[i].alt_image || ''
            offerBannersDetails.push(tempBanner)
          } else {
            if (stateId != '' && stateId != null) {
              var states_id = bannerQueryResult[i].states_id
              var finalArray = states_id.split(',')
              var filteredArray = finalArray.filter((str) => {
                return str.indexOf(stateId) >= 0
              })
              console.log('check value ', filteredArray)
              if (filteredArray.length > 0) {
                tempBanner.imageid = bannerQueryResult[i].ma_images_id
                tempBanner.campaign_id = bannerQueryResult[i].ma_images_id
                tempBanner.imagepath = bannerQueryResult[i].image_url
                tempBanner.hyperlink = bannerQueryResult[i].hyperlink
                tempBanner.show_in_app = bannerQueryResult[i].show_in_app
                tempBanner.show_in_web = bannerQueryResult[i].show_in_web
                tempBanner.link_type = bannerQueryResult[i].link_type
                tempBanner.imagename = bannerQueryResult[i].alt_image || ''
                tempBanner.campaign_name = bannerQueryResult[i].alt_image || ''
                offerBannersDetails.push(tempBanner)
              }
            } else {
              var user_types = bannerQueryResult[i].user_type
              console.log('Check:', user_types)
              console.log('Result:', bannerQueryResult[i].ma_images_id)
              if (user_types != '' && user_types != null) {
                var finalArrayResult = user_types.split(',')
                var filteredArrayResult = finalArrayResult.filter((str) => {
                  return str.indexOf(userType) >= 0
                })
                console.log('check value ', filteredArrayResult)
                if (filteredArrayResult.length > 0) {
                  tempBanner.imagepath = bannerQueryResult[i].image_url
                  tempBanner.imageid = bannerQueryResult[i].ma_images_id
                  tempBanner.campaign_id = bannerQueryResult[i].ma_images_id
                  tempBanner.hyperlink = bannerQueryResult[i].hyperlink
                  tempBanner.show_in_app = bannerQueryResult[i].show_in_app
                  tempBanner.show_in_web = bannerQueryResult[i].show_in_web
                  tempBanner.link_type = bannerQueryResult[i].link_type
                  tempBanner.imagename = bannerQueryResult[i].alt_image || ''
                  tempBanner.campaign_name = bannerQueryResult[i].alt_image || ''
                  offerBannersDetails.push(tempBanner)
                }
              } else {
                tempBanner.imagepath = bannerQueryResult[i].image_url
                tempBanner.imageid = bannerQueryResult[i].ma_images_id
                tempBanner.campaign_id = bannerQueryResult[i].ma_images_id
                tempBanner.hyperlink = bannerQueryResult[i].hyperlink
                tempBanner.show_in_app = bannerQueryResult[i].show_in_app
                tempBanner.show_in_web = bannerQueryResult[i].show_in_web
                tempBanner.link_type = bannerQueryResult[i].link_type
                tempBanner.imagename = bannerQueryResult[i].alt_image || ''
                tempBanner.campaign_name = bannerQueryResult[i].alt_image || ''
                offerBannersDetails.push(tempBanner)
              }
            }
          }
        }
        console.log('FINAL ARRAY', offerBannersDetails)
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, offerBannersList: offerBannersDetails, carouselChangeTime }
      }
      return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, offerBannersList: offerBannersDetails, carouselChangeTime }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'offerBanners', type: 'err', fields: err })
    } finally {
      if (connection) { connection.release() }
    }
  }

  static async commercialDetails (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'commercialDetails', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const sql = "Select title, pdf_path from ma_commerical_tab where record_status='Y' limit 1"
      log.logger({ pagename: path.basename(__filename), action: 'commercialDetails', type: 'commercialQuery', fields: sql })
      const data = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'commercialDetails', type: 'commercialQueryResult', fields: data })

      if (data.length <= 0) {
        return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, commercialList: null }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, commercialList: data[0] }
    } catch (error) {

    }
  }
}
module.exports = salePromotions
