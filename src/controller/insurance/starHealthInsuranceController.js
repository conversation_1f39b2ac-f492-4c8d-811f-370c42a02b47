const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const axios = require('axios')
const {basename} = require('path')
const validator = require('../../util/validator')
const transactionMaster = require('../transaction/transactionController')
const transactionDetail = require('../transaction/transactionDetailsController')
const common = require('../../util/common')
const configIns = require('./config')
const crypto = require('crypto')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const moment = require('moment')
const balanceController = require('../balance/balanceController')
const securePinCtrl = require('../securityPin/securityPinController')
const tz = require('moment-timezone')
const bitly = require('../../util/bitly')
const sms = require('../../util/sms')

 
class StarHealthInsuranceController extends DAO {
 
    get TABLE_NAME () {
        return 'ma_transaction_master'
    }
    static get PRIMARY_KEY () {
        return 'ma_transaction_master_id'
    }
    /**
   *
   * @param {*} _
   * @param {*} fields
   * @returns
   */
    static async starHealthGetDynamicForm(_, fields) {
    log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'request', fields })
 
    const conn = await mySQLWrapper.getConnectionFromPool()
    // TODO -DONE REMOVE comments
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    // const connectionRead = await mySQLWrapper.getConnectionFromPool()
    try {
      const isMerchantAllowed = await this.isMerchantAllowed({ ...fields, connectionRead })
      log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'isMerchantAllowed', fields: isMerchantAllowed })
      if (isMerchantAllowed.status != 200) return isMerchantAllowed
      const userChannelData = await this.checkUserChannel(fields.ma_user_id, conn)
      log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'channeldata', fields: userChannelData })
      if (userChannelData.status && userChannelData.status == 400) return userChannelData
      if (userChannelData == '') return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
 
      // TODO - DONE DMT category code and proposer mobile check
      if (fields.source == 'DMT') {
        const getDmtOrderIdSql = `SELECT aggregator_order_id,mobile_number FROM ma_transaction_master where aggregator_order_id = '${fields.orderid}' limit 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'starHealthGetDynamicForm', type: 'getDmtOrderIdSql', fields: getDmtOrderIdSql })
        const orderResult = await this.rawQuery(getDmtOrderIdSql, conn)
        log.logger({ pagename: require('path').basename(__filename), action: 'starHealthGetDynamicForm', type: 'orderResult', fields: orderResult })
        if (orderResult.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

        fields.category_code = 'STARHEALTH_PA'
        fields.proposer_mobile = orderResult[0].mobile_number
      }
 
     if (!fields.category_code) {
        return { status: 400, respcode: 1001, message: 'category code is required', action_code: 1001 }
      }
      if (!fields.proposer_mobile) {
        return { status: 400, respcode: 1001, message: 'proposer_mobile is required', action_code: 1001 }
      }
    let isPolicyNew = 'new'
    const isPolicyActive = await this.isPolicyActive({ source: fields.source, proposer_mobile: fields.proposer_mobile, connectionRead })
    log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'isPolicyActive', fields: isPolicyActive })
    if (isPolicyActive.status != 200) return isPolicyActive
 
    let isPolicyRenewalExist = ''
    isPolicyRenewalExist = await this.isPolicyRenewalExist({ ...fields, connectionRead })
    log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'isPolicyRenewalExist', fields: isPolicyRenewalExist })
    if (isPolicyRenewalExist.status != 200) return isPolicyRenewalExist
 
    const riskcovryFlag = fields.category_code == 'STARHEALTH_PA'
    const policyDetailsQuery = `SELECT category_name, category_code, expiry_period, amount, additional_info as plans_features, policy_wordings, policy_name from ma_insurance_category_master micm where category_code = '${fields.category_code}' and riskcovry_sachet = '${riskcovryFlag}'  and status = 'Active' limit 1`
    console.log('policy details query', policyDetailsQuery)
    const policyDetails = await this.rawQuery(policyDetailsQuery, conn)
    const tncUrl = policyDetails[0].plans_features
    console.log('tncUrl', tncUrl)
 
    log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'policyDetails', fields: policyDetails })
    if (policyDetails.length == 0) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Category is already disbaled' }
    if (policyDetails.length > 0) {
    policyDetails[0].description = `Buy ${policyDetails[0].policy_name} for a validity of ${policyDetails[0].expiry_period} days at a premium of Rs.${policyDetails[0].amount}`
    policyDetails[0].validity = `${policyDetails[0].expiry_period} days.`
    policyDetails[0].source = fields.source || ''
    policyDetails[0].tncUrl = policyDetails[0].plans_features
    policyDetails[0].policy_status = isPolicyNew
    }
    let getParamsQuery
    if (fields.category_code == 'STARHEALTH_PA') {
        const isInsuredReqSql = `SELECT is_proposer_details_required from ma_insurance_category_master micm where category_code = '${fields.category_code}' and riskcovry_sachet = 'true'`
        if (isInsuredReqSql[0].is_proposer_details_required == 'false') {
            getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'STAR HEALTH Sachet' and form_type = 'insured_form' and  isActive = 'Y'"
        } else {
            console.log('showing customer form')
            getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'STAR HEALTH Sachet' and form_type = 'customer form' and  isActive = 'Y'"
        }
    }
    const params = await this.rawQuery(getParamsQuery, conn)
    log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'getParamsQuery-response', fields: params })
    if (params.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    const form = JSON.parse(params[0].api_params)
    const prefilledData = await this.getPrefilledData(fields, conn)
    log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'prefilledData', fields: prefilledData })
    if (prefilledData.status != 200) return prefilledData
    if (prefilledData.status == 200 && Object.keys(prefilledData.formData).length != 0) {
      const PrefilledFormData = prefilledData.formData
      log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'prefilledData ', fields: prefilledData })
      form.forEach(function (section) {
        section.fields.forEach(function (field) {
          console.log('fields>>>>>', field, 'PrefilledFormData>>>', PrefilledFormData[field.postKey])
          if (PrefilledFormData[field.postKey] != null && PrefilledFormData[field.postKey] != '') {
            field.value = PrefilledFormData[field.postKey]
          }
        })
      })
    }
    if (isPolicyRenewalExist.respcode == 1000 && Object.keys(isPolicyRenewalExist.data).length > 0) {
      const renewalData = isPolicyRenewalExist.data
      log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'isPolicyRenewalExistData ', fields: renewalData })
      form.forEach(function (section) {
        section.fields.forEach(function (field) {
          console.log('fields>>>>>', field, 'renewalData>>>', renewalData[field.postKey])
          if (renewalData[field.postKey] != null && renewalData[field.postKey] != '') {
            field.value = renewalData[field.postKey]
            field.isEditable = false
          }
        })
      })
    }
    form.forEach(function (section) {
      section.fields.forEach(function (field) {
        if (field.postKey == 'proposer_mobile') {
          field.value = fields.proposer_mobile
          // field.iseditable == false
          field.isEditable = false
        }
        if (field.postKey == 'proposer_aadhar' || field.postKey == 'insured_aadhar') {
          field.value = ''
          // field.iseditable == false
          field.isEditable = true
        }
      })
    })
    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicForm: form, policyDetails, tncUrl }
    }catch (err) {
      log.logger({ pagename: basename(__filename), action: 'starHealthGetDynamicForm', type: 'generic catch error', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (conn) conn.release()
      if (connectionRead) connectionRead.release()
    }
    }
 
  /**
   *
   * @param {{proposer_mobile, connectionRead}} param0
   * @returns
   */
  static async isMerchantAllowed({ userid, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'isMerchantAllowed', type: 'request', fields: { userid } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const emitraUser = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${userid} LIMIT 1`
      const emitraUserRes = await this.rawQuery(emitraUser, connRead)
      log.logger({ pagename: basename(__filename), action: 'isCheckBoxVisible', type: 'query-response', fields: emitraUserRes })
      if (emitraUserRes.length > 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1191] }
 
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isPolicyActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }
 
  /**
   *
   * @param {number} ma_user_id
   * @param {Promise<mySQLWrapper.getConnectionFromPool()>} con
   * @returns
   */
  // TODO ENABLE STAHEALTHSACHET
  static async checkUserChannel(ma_user_id, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkUserChannel', type: 'request', fields: ma_user_id })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // get state & user type
      // get state details
      const sqlA = `SELECT state, user_type FROM ma_user_master WHERE profileid = ${ma_user_id} limit 1`
      const userADetails = await this.rawQuery(sqlA, connection)
      let state_id = 0
      let user_type = ''
      if (userADetails.length > 0) {
        state_id = userADetails[0].state
        user_type = userADetails[0].user_type
      }
 
      console.log('This is the user details: ', userADetails)
      // get channel list with respect to user id
      const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = 0 and ma_user_id = ${ma_user_id} and record_status = 'Y' and user_type = '${user_type}' `
      const channelDetails = await this.rawQuery(sqlCh, connection)
      var channel_arr = []
      var checkInsurance = ''
      console.log('Channnel data 1: ', channelDetails)
      if (channelDetails.length > 0) {
        for (var j = 0; j < channelDetails.length; j++) {
          channel_arr[j] = channelDetails[j].channel_name
        }
        console.log('check channel arr ', channel_arr)
        // console.log('check channel arr new ', check_channel_arr)
        if (channel_arr.includes('ICICILOMBARDSACHET')) {
          checkInsurance = 'yes'
        }
 
        console.log('inside user insurance channel condition >>>>>>>>>>', checkInsurance)
        return checkInsurance
      } else {
        // get channel list with respect to state
        const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = ${state_id} and ma_user_id = 0 and record_status = 'Y' and user_type = '${user_type}' `
        const channelStateDetails = await this.rawQuery(sqlCh, connection)
        // var final_channel_arr = {}
        console.log('Channnel data 2: ', channelStateDetails)
        if (channelStateDetails.length > 0) {
          for (var k = 0; k < channelStateDetails.length; k++) {
            channel_arr[k] = channelStateDetails[k].channel_name
          }
 
          if (channel_arr.includes('ICICILOMBARDSACHET')) {
            checkInsurance = 'yes'
          }
          console.log('inside state channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
          return checkInsurance
        } else {
          const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=2 and state_master_id =0 and record_status = 'Y' and ma_user_id = 0 and user_type = '${user_type}' `
          const channelGlobalDetails = await this.rawQuery(sqlCh, connection)
          // var final_channel_arr = {}
          if (channelGlobalDetails.length > 0) {
            for (var n = 0; n < channelGlobalDetails.length; n++) {
              channel_arr[n] = channelGlobalDetails[n].channel_name
            }
            console.log('channel_list ', channel_arr)
            if (channel_arr.includes('ICICILOMBARDSACHET')) {
              checkInsurance = 'yes'
            }
            console.log('inside Global channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
            return checkInsurance
          } else {
            checkInsurance = ''
            return checkInsurance
          }
        }
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'checkUserChannel', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) connection.release()
    }
  }
 
  /**
   *
   * @param {{proposer_mobile, connectionRead}} param0
   * @returns
   */
  static async isPolicyActive({ source, proposer_mobile, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'isPolicyActive', type: 'request', fields: { proposer_mobile, source } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isPolicyActiveQuery = `SELECT * FROM ma_lombard_customer_details WHERE DATE(policy_expire_date) >= DATE(NOW())  AND policy_status IN ('S','I','P') AND proposer_mobile='${proposer_mobile}' AND company_name='STARHEALTH'`
      console.log("isPolicyActiveQuery", isPolicyActiveQuery)
      const isPolicyActiveResult = await this.rawQuery(isPolicyActiveQuery, connRead)
      log.logger({ pagename: basename(__filename), action: 'isPolicyRenewalExist', type: 'isPolicyActiveResult', fields: isPolicyActiveResult })
 
      if (isPolicyActiveResult.length == 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: [] }
 
      if (source == 'DMT' && (isPolicyActiveResult[0].policy_status == 'I' || isPolicyActiveResult[0].policy_status == 'P' || isPolicyActiveResult[0].policy_status == 'PS')) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: [] }
      }
 
      // T1
      const expiredDate = tz.tz(isPolicyActiveResult[0].policy_expire_date, 'Asia/Kolkata')
      const today = tz.tz(moment(), 'Asia/Kolkata').format('YYYY-MM-DD')
      if ((moment(expiredDate).subtract(1, 'day').format('YYYY-MM-DD') == today || expiredDate.format('YYYY-MM-DD') == today) && isPolicyActiveResult[0].renewal_status == 'Y') return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: [] }
 
      return { status: 400, respcode: 1001, message: 'policy for this customer is still active', action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isPolicyActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }
 
  /**
   *
   * @param {{proposer_mobile, connectionRead}} param0
   * @returns
   */
  static async isPolicyRenewalExist({ proposer_mobile, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'isPolicyRenewalExist', type: 'request', fields: { proposer_mobile } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isPolicyActiveQuery = `SELECT * FROM ma_lombard_customer_details WHERE DATE(grace_period) >= DATE(NOW()) AND proposer_mobile='${proposer_mobile}' AND renewal_status='Y' AND DATE(policy_validity) > DATE(NOW()) AND company_name='STARHEALTH'`
      const isPolicyActiveResult = await this.rawQuery(isPolicyActiveQuery, connRead)
      log.logger({ pagename: basename(__filename), action: 'isPolicyRenewalExist', type: 'isPolicyActiveResultQuery', fields: isPolicyActiveQuery })
 
      log.logger({ pagename: basename(__filename), action: 'isPolicyRenewalExist', type: 'isPolicyActiveResult', fields: isPolicyActiveResult })
 
      if (isPolicyActiveResult.length > 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, data: isPolicyActiveResult[0] }
 
      return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1000, data: [] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isPolicyRenewalExist', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }
 
 
 
  static async getPrefilledData(fields, connection) {
    log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'request', fields })
 
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      let custObj = {}
      let formDataObj = {}
      if (fields.source == 'DMT') {
        const customerDetailsQuery = `Select remitter_name, mobile_number from ma_customer_details mcd where mobile_number = '${fields.proposer_mobile}' limit 1`
        log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'customerDetailsQuery', fields: customerDetailsQuery })
        const customerDetails = await this.rawQuery(customerDetailsQuery, conn)
        log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'customerDetails', fields: customerDetails })
        if (customerDetails.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        custObj = {
          insured_first_name: customerDetails[0].remitter_name.split(' ').slice(0, 1).join(' '),
          insured_last_name: customerDetails[0].remitter_name.split(' ').slice(1).join(' '),
          insured_mobile: customerDetails[0].mobile_number
        }
      }
      let formData = ''
      formData = `SELECT * FROM ma_lombard_customer_details where proposer_mobile = '${fields.proposer_mobile}' AND company_name = 'STARHEALTH' AND policy_number IS NOT NULL order by addedon desc limit 1`
      log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'formData', fields: formData })
      const formDataRes = await this.rawQuery(formData, conn)
      log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'formDataRes', fields: formDataRes[0] })
      if (formDataRes.length > 0) {
        formDataObj = formDataRes[0]
      }

      if (formDataObj.proposer_pannumber) {
        formDataObj.proposer_pannumber = ''
        console.log('proposer pan number', formDataObj.proposer_pannumber)
      }
      if (formDataObj.insured_pannumber) {
        formDataObj.insured_pannumber = ''
        console.log('insured pan number', formDataObj.insured_pannumber)
      }
      if (formDataObj.proposer_aadhar) {
        formDataObj.proposer_aadhar = ''
        console.log('proposer aadhar', formDataObj.proposer_aadhar)
      }
      if (formDataObj.insured_aadhar) {
        formDataObj.insured_aadhar = ''
        console.log('insured aadhar', formDataObj.insured_aadhar)
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], formData: { ...formDataObj, ...custObj } }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getPreFilledData', type: 'err', fields: err })
    } finally {
      if (isSet) conn.release()
    }
  }

/**
 *
 * @param {{proposer_mobile, connectionRead}} param0
 * @returns
 */
  static async transactionStatusCheck({ order_id, connection }) {
    log.logger({ pagename: basename(__filename), action: 'transactionStatusCheck', type: 'request', fields: { order_id } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const transactionDetailsSQL = `SELECT * from ma_transaction_master where aggregator_order_id = '${order_id}' limit 1`
      const transactionDetailsRes = await this.rawQuery(transactionDetailsSQL, conn)
      log.logger({ pagename: basename(__filename), action: 'transactionStatusCheck', type: 'transactionDetailsRes', fields: transactionDetailsRes })
      if (transactionDetailsRes.length == 0) return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1002 }
      if (transactionDetailsRes[0].transaction_status != 'P' && transactionDetailsRes[0].transaction_status != 'PS') return { status: 400, respcode: 1001, message: errorMsg.responseCode[1020], action_code: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, transaction: transactionDetailsRes[0] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionStatusCheck', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

/**
 *
 * @param {{*}} param0
 * @returns
 */
  static async isInsuranceCategoryActive({ category_code, connectionRead }) {
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    // TODO - DONE remove comments
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    // const connRead = isSetRead ? await mySQLWrapper.getConnectionFromPool() : connectionRead
    // var channelDetails = {}
    try {
      // get channel list with respect to user id
      const sqlCh = `SELECT category_flag, category_name,amount,expiry_period,category_code, ma_insurance_category_master_id FROM ma_insurance_category_master WHERE category_code= '${category_code}' and status = 'Active' limit 1`
      log.logger({ pagename: basename(__filename), action: 'isInsuranceCategoryActive', type: 'sqlCh', fields: sqlCh })
      const categoryDetails = await this.rawQuery(sqlCh, connRead)
      log.logger({ pagename: basename(__filename), action: 'isInsuranceCategoryActive', type: 'categoryDetails', fields: categoryDetails })

      if (categoryDetails.length > 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: categoryDetails[0] }

      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Category is already disbaled' }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'checkInsuranceCategory', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

static async calculatePaymodeCharges(fields) {
  log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'request', fields: fields })
  try{
      let data = ''
      data = await this.getDistribution(fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'collectMoney getDistribution data', fields: data })
      let charge = 0
      let bank_charge = 0
      let gst = 0
      let afterDeduct = 0
      let finalData = {}
      let isGstInclusive = false
      if (data.status == 200 && data[0].merchant_charges > 0) {
        // percentage
        if (data[0].merchant_charges_applied_type == '2') {
          charge = fields.amount * (data[0].merchant_charges / 100)
          console.log('charge: ' + charge)
        } else {
          charge = data[0].merchant_charges // for merchant_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        if (data[0].bank_charges_applied_type == '2') {
          bank_charge = fields.amount * (data[0].bank_charges / 100)
          console.log('bank_charge: ' + charge)
        } else {
          bank_charge = data[0].bank_charges // for bank_charges_applied_type = 1 (fixed amt)
          console.log('bank_charge: ' + charge)
        }
        // gst calculation
        if (data[0].gst_applied_calculation_type == 'E') {
          gst = charge * (data[0].gst_percentage / 100)
        } else if (data[0].gst_applied_calculation_type == 'I') {
          isGstInclusive = true
          gst = charge - (charge * (100 / (100 + data[0].gst_percentage)))
          gst = gst * -1 // Negating for deduction
        }

        // round off gst to 2 decimals
        gst = Math.round(gst * 100) / 100
        console.log('gst: ' + Math.abs(gst))

        afterDeduct = Number(fields.amount) - (charge + gst)
        console.log('afterDeduct: ' + afterDeduct);

        const totalAmount = charge + gst + bank_charge + fields.amount;
        console.log('totalAmount', charge, gst, bank_charge, fields.amount);
        console.log(totalAmount);
        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          type: data[0].type,
          isGstInclusive: isGstInclusive
        }
      } else {
          finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: 0,
          maxAmount: 0,
          type: '',
          isGstInclusive: isGstInclusive
        }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'calc result', fields: finalData })
      return finalData
  } catch (err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'error', fields: err })
    return err
  } 
}
  /**
   *
   * @param {*} fields
   * @param {*} connection
   * @returns
   */
  static async ledgerEntries({ fields, connection }) {
    log.logger({ pagename: basename(__filename), action: 'ledgerEntries', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const updateTransactionResult = await transactionMaster.updateWhereData(conn, {
        data: { transaction_reason: `Balance Deducted ${fields.amount}`, transaction_status: 'PS' },
        id: fields.orderid,
        where: 'aggregator_order_id'
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'updateTransactionResult', fields: updateTransactionResult })
      if (updateTransactionResult.status != 200) return updateTransactionResult

      var pointsDetailsEntries = {}
      pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        transactionType: '1',
        connection: conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'getWalletBalanceDirect', fields: pointsDetailsEntries })
      if (pointsDetailsEntries.status === 400) return pointsDetailsEntries

      // point leger entry
      const pointLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: 15,
        description: util.insurancePointsDebitDescription,
        ma_status: 'S',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        connection: conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'pointsLedger', fields: pointLedgerId })
      if (pointLedgerId.status === 400) return pointLedgerId

      // pointsDetails entry
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: pointLedgerId.id,
          orderid: fields.orderid,
          ma_status: 'S',
          connection: conn
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'pointsDetailsController', fields: entry })
        if (entry.status === 400) return entry
      }

      // airpay id credit entry

      const retailerLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: 15,
        ma_status: 'S',
        userid: fields.userid,
        description: util.insurancePointsCreditsDescription,
        orderid: fields.orderid,
        corresponding_id: fields.ma_user_id,
        conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'createEntry', fields: retailerLedgerId })
      if (retailerLedgerId.status === 400) return retailerLedgerId
      const { charge, gst, bankCharge, txnAmount, isGstInclusive } = await this.calculatePaymodeCharges({ amount: fields.amount, ma_user_id: fields.ma_user_id, userid: fields.userid, orderid: fields.orderid, category_master_id: fields.category_master_id, subcategory_code: fields.subcategory_code });

      const tempObj = { bankCharge, txnAmount, isGstInclusive }
      tempObj.platformFee = charge + gst
      tempObj.transaction_type = '15'

      if (charge > 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'rechargeLedgerEntries', type: 'paymode charge process', fields: { charge: charge, GST: gst, otherDetails: tempObj } })
        const chargesResponse = await this.chargesLedgerEntries(fields, conn, charge, gst, tempObj);
      } else {
        await this.paymodeChargesRecord(fields, conn, tempObj)
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'ledgerEntries', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }


  static async chargesLedgerEntries(fields, conn, charge, gst, tempObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'fields', fields: fields })
    log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'tempObj', fields: tempObj })
    const common = require('../../util/common')
    const BalanceController = require('../balance/balanceController')
    const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '70' }, fields.connection)
    const chargeDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '71' }, fields.connection)
    const gstDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '72' }, fields.connection)
    const pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
      ma_user_id: fields.ma_user_id,
      amount: tempObj.isGstInclusive ? charge : charge + gst,
      transactionType: '1',
      connection: fields.connection
    })
    log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
    if (pointsDetailsEntries.status === 400) {
      return pointsDetailsEntries
    }
    const pointsLedger = require('../creditDebit/pointsLedgerController')
    const chargePoints = await pointsLedger.createEntry('_', {
      ma_user_id: fields.ma_user_id,
      amount: tempObj.isGstInclusive ? charge : charge + gst,
      mode: 'dr',
      transaction_type: '70',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: descObj.code_desc ? descObj.code_desc : 'Platform Fee',
      orderid: fields.orderid,
      userid: fields.userid,
      corresponding_id: util.airpayCommissionId,
      ma_status: 'S',
      connection: conn
    }
    )
    if (chargePoints.status === 400) {
      return chargePoints
    }
    const PointsDetailsController = require('../creditDebit/pointsDetailsController')
    for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
      const entry = await PointsDetailsController.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: pointsDetailsEntries.details[i].deductionAmount,
        wallet_type: pointsDetailsEntries.details[i].wallet_type,
        ma_points_ledger_master_id: chargePoints.id,
        orderid: fields.aggregator_order_id,
        ma_status: 'S',
        connection: conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'PointsDetailsEntries', fields: entry })
      if (entry.status === 400) {
        return entry
      }
    }

    const customerCharge = await pointsLedger.createEntry('_', {
      ma_user_id: util.airpayCommissionId,
      amount: tempObj.isGstInclusive ? charge + gst : charge,
      mode: 'cr',
      transaction_type: '71',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: chargeDescObj.code_desc ? chargeDescObj.code_desc : 'Net Platform Fee',
      orderid: fields.orderid,
      userid: fields.userid,
      corresponding_id: util.airpayUserId,
      ma_status: 'S',
      connection: conn
    }
    )
    if (customerCharge.status === 400) {
      return customerCharge
    }

    const gstCharge = await pointsLedger.createEntry('_', {
      ma_user_id: util.airpayCommissionId,
      amount: Math.abs(gst),
      mode: 'cr',
      transaction_type: '72',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: gstDescObj.code_desc ? gstDescObj.code_desc : 'GST on Platform Fee',
      orderid: fields.orderid,
      userid: fields.userid,
      corresponding_id: util.airpayUserId,
      ma_status: 'S',
      connection: conn
    }
    )

    if (gstCharge.status === 400) {
      return gstCharge
    }

    return await this.paymodeChargesRecord(fields,conn, tempObj)
  }

  static async paymodeChargesRecord(fields,conn ,tempObj) {
    const common = require('../../util/common')


    const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: tempObj.transaction_type }, conn)
    const descType = descObj.code_desc ? descObj.code_desc : 'Collect Money'
    const platformFeeAmt = Math.abs(tempObj.platformFee)
    const bankName = tempObj.bankName ? tempObj.bankName : ''

    var sub_transaction_type = fields.provider_name;

    // insert bank_charges
    const bankChargesQuery = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges) VALUES (${fields.ma_user_id}, '${fields.orderid}', 'Bank charges', '${bankName}', '${descType}', '${sub_transaction_type}', ${tempObj.txnAmount}, ${tempObj.bankCharge})`
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'insert bank charges query', fields: bankChargesQuery })
    const bankChargesResult = await this.rawQuery(bankChargesQuery, conn)
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'bank charges query result', fields: bankChargesResult })

    // insert platform fee
    const platformFeeQuery = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges) VALUES (${fields.ma_user_id}, '${fields.orderid}', 'Platform Fee', '${bankName}', '${descType}', '${sub_transaction_type}', ${tempObj.txnAmount}, ${platformFeeAmt})`
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'insert bank charges query', fields: platformFeeQuery })
    const platformFeeResult = await this.rawQuery(platformFeeQuery, conn)
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'bank charges query result', fields: platformFeeResult })

    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
  }


/**
   *
   * @param {*} _
   * @param {*} fields
   * @returns
*/
  static async starHealthSachetInitiateTransaction(_, fields) {
    log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    // TODO - DONE remove comments
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    // const connectionRead = await mySQLWrapper.getConnectionFromPool()

    try{
      const { ma_user_id, userid, orderid, source, security_pin } = fields

      // TODO companyid and affiliateid
      const companyid = configIns.riskcovryKeys.companyid
      const affiliateid = configIns.riskcovryKeys.affiliateid

      const isMerchantAllowed = await this.isMerchantAllowed({ ...fields, connectionRead })
      if (isMerchantAllowed.status != 200) return isMerchantAllowed

      const userChannelData = await this.checkUserChannel(fields.ma_user_id, connection)
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'userChannelData', fields: userChannelData })
      if (userChannelData.status && userChannelData.status == 400) return userChannelData
      if (userChannelData == '') return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }

      const isPolicyActive = await this.isPolicyActive({ proposer_mobile: fields.proposer_mobile, connectionRead })
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'isPolicyActive', fields: isPolicyActive })
      if (isPolicyActive.status != 200) return isPolicyActive

      // Check if transaction already Initiated
      /* Transaction Check */

      const transactionStatus = await this.transactionStatusCheck({ order_id: fields.orderid, connection })
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'transactionStatus', fields: transactionStatus })
      if (transactionStatus.status != 200) return transactionStatus

      const checkInsuranceCategoryData = await this.isInsuranceCategoryActive({ category_code: fields.category_code, connection })
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'isInsuranceCategoryActive', fields: checkInsuranceCategoryData })
      if (checkInsuranceCategoryData.status != 200) return checkInsuranceCategoryData

      const { data } = checkInsuranceCategoryData
      fields.amount = data.amount // set the transaction amount equal to policy amount
      // Pin verify for flow from insurance tab
      if (source == 'INSURANCE') {
        const securePin = await securePinCtrl.verifySecurePin(null, {
          ma_user_id,
          userid,
          security_pin,
          connection
        })
        
        log.logger({ pagename: require('path').basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'securePin', fields: securePin })
        if (securePin.status != 200) return securePin
      }
      const { charge, gst, txnAmount, isGstInclusive } = await this.calculatePaymodeCharges({ amount: fields.amount, ma_user_id: fields.ma_user_id, userid: fields.userid, orderid: fields.orderid, category_master_id: data.ma_insurance_category_master_id, subcategory_code: data.category_code })

      const totalAmount = charge + txnAmount + (isGstInclusive ? 0 : gst);
      
      /* NEW CHANGES : LEIN BALANCE CHECK */
      const lienBalanceController = require('../lienBalance/lienBalanceController')
      /* NEW CHANGES : LEIN BALANCE CHECK */
      const isTransactionAmountAboveLienBalanceResp = await lienBalanceController.isTransactionAmountAboveLienBalance({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        transactionType: '15',
        transactionAmount: totalAmount,
        connectionRead: connectionRead
      })
      log.logger({ pagename: 'transactionController.js', action: 'isTransactionAmountAboveLienBalance', type: 'response', fields: isTransactionAmountAboveLienBalanceResp })
      if (isTransactionAmountAboveLienBalanceResp.status != 200) return isTransactionAmountAboveLienBalanceResp
      /* LEIN BALANCE CHECK END */

      // Check available balance
      const availableBalance = await balanceController.getWalletBalancesDirect(_, {
        ma_user_id: ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection: connection
      })

      if (availableBalance.amount < totalAmount) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1005] };

      if (availableBalance.status != 200) return availableBalance
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'balanceCheck', fields: availableBalance })
      if (availableBalance.amount < fields.amount) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1005] }
      const newFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        orderid: fields.orderid,
        provider_name: data.category_name,
        affiliated_id: affiliateid,
        company_id: companyid,
        amount: fields.amount,
        mobile_number: fields.proposer_mobile,
        subcategory_code: data.category_code,
        customer_mobile: fields.proposer_mobile ? fields.proposer_mobile : '0',
        category_master_id: data.ma_insurance_category_master_id
      }
      await mySQLWrapper.beginTransaction(connection)
      const transactionResponse = await this.createInsuranceTransaction({ fields: newFields, connection })
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'transactionResponse', fields: transactionResponse })
      if (transactionResponse.status != 200) {
        await mySQLWrapper.rollback(connection)
        return transactionResponse
      }

    const addPolicyData = await this.addPolicyData({ fields: { ...fields, ...data }, connection, connectionRead })
    log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'addPolicyData', fields: addPolicyData })
    if (addPolicyData.status != 200) {
      await mySQLWrapper.rollback(connection)
      return addPolicyData
    }
    // update ledger entries
    const ledgerEntries = await this.ledgerEntries({ fields: newFields, connection })
    log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'ledgerEntries', fields: ledgerEntries })
    if (ledgerEntries.status != 200) {
      await mySQLWrapper.rollback(connection)
      return ledgerEntries
    }

    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, orderid: fields.orderid }
    }catch(error){
      log.logger({ pagename: basename(__filename), action: 'starHealthSachetInitiateTransaction', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally{
      if (connection) connection.release()
      if (connectionRead) connectionRead.release()
    }

}
  /**
   *
   * @param {{fields, connection }} connection
   * @returns
   */
  static async createInsuranceTransaction({ fields, connection }) {
    log.logger({ pagename: basename(__filename), action: 'createInsuranceTransaction', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const sqlUser = `select mobile_id,ma_user_master_id,sales_id,state,user_type,userid from ma_user_master where profileid='${fields.ma_user_id}' AND mer_user = 'mer' limit 1`
      const user = await this.rawQuery(sqlUser, conn)
      log.logger({ pagename: basename(__filename), action: 'sqlMerchantResponse', type: '_userSQLResult', fields: user })

      if (user.length == 0) return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }

      const sales_id = user[0].sales_id

      const transactionData = await transactionMaster.initiateTransaction('', {
        connection: conn,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.orderid,
        transaction_id: fields.orderid,
        amount: fields.amount,
        commission_amount: 0,
        transaction_type: 15, // insurance
        remarks: 'STAR HEALTH Insurance Transaction',
        mobile_number: fields.mobile_number,
        provider_id: '0', // to do
        provider_name: fields.provider_name,
        utility_id: '0', // to do
        utility_name: 'insurance', // to do
        action_type: 'instapay',
        transaction_status: 'PS',
        affiliated_id: fields.affiliated_id,
        company_id: fields.company_id,
        subcategory_code: fields.subcategory_code,
        bank_name: 'STAR HEALTH',
        customer_mobile: fields.customer_mobile,
        category_master_id: fields.category_master_id,
        sales_id
      })

      log.logger({ pagename: basename(__filename), action: 'createInsuranceTransaction', type: 'transactionData', fields: transactionData })

      if (transactionData.status != 200) return transactionData
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, orderid: fields.orderid }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'createInsuranceTransaction', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
}

  /**
   *
   * @param {*} fields
   * @param {*} connection
   * @returns
   */
  static async addPolicyData({ fields, connection, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      // fetch existing policy
      const isPolicyRenewalExist = await this.isPolicyRenewalExist({ ...fields, connectionRead: connRead })
      log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'isPolicyRenewalExist', fields: isPolicyRenewalExist })
      // udpate for grace period policy
      if (isPolicyRenewalExist.status != 200) return isPolicyRenewalExist

      const { data } = isPolicyRenewalExist
      // insert for new policy
      if (isPolicyRenewalExist.respcode == 1002) {
        const resp = await this.generateNewPolicy({ fields, connection: conn })
        log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'generateNewPolicy', fields: resp })
        if (resp.status != 200) return resp
      } else if (isPolicyRenewalExist.respcode == 1009) {
        console.log('Hello')
        console.log("isPolicyRenewalExist2", isPolicyRenewalExist)
        const policy_expire_date = this.calculateExpiry(fields.expiry_period)
        const policy_validity = moment().tz('Asia/Kolkata').add(365, 'days').format('YYYY-MM-DD')
        const updateQuery = `update ma_lombard_customer_details set orderid = '${fields.orderid}', policy_expire_date = '${policy_expire_date}', policy_validity = '${policy_validity}' ,policy_status = 'PS' where orderid = '${isPolicyRenewalExist.data.orderid}' AND company_name='STARHEALTH'`
        log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'isPolicyRenewalExistQuery', fields: updateQuery })
        const updateRes = await this.rawQuery(updateQuery, conn)
        log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'isPolicyRenewalExistQueryRes', fields: updateRes })
      } else {
        const resp = await this.updateExistPolicy({ fields, renewalPolicyData: data, connection: conn })
        log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'updateExistPolicy', fields: resp })
        if (resp.status != 200) return resp
      }

      const generateNewTransactionPolicy = await this.generateNewTransactionPolicy({ fields, connection: conn })
      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'generateNewTransactionPolicy', fields: generateNewTransactionPolicy })
      if (generateNewTransactionPolicy.status != 200) return generateNewTransactionPolicy

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
}

 /**
   *
   * @param {*} fields
   * @param {*} connection
   * @returns
   */
  static async generateNewTransactionPolicy({ fields, connection }) {
    log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const policyTransactionData = {
        transaction_status: 'PS',
        order_id: fields.orderid,
        amount: fields.amount,
        source: fields.source
      }

      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'policyTransactionData', fields: policyTransactionData })
      this.TABLE_NAME = 'ma_lombard_policy_details'
      const policyTransactionDataResult = await this.insert(conn, { data: policyTransactionData })
      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'insert', fields: policyTransactionDataResult })

      if (policyTransactionDataResult.affectedRows == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, ma_lombard_policy_details_id: policyTransactionDataResult.insertId }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }


 /**
   *
   * @param {*} fields
   * @param {*} connection
   * @returns
   */
  static async updateExistPolicy({ fields, renewalPolicyData, connection }) {
    log.logger({ pagename: basename(__filename), action: 'updateExistPolicy', type: 'request', fields: { fields, renewalPolicyData } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const data = { policy_status: 'PS', orderid: fields.orderid, source: fields.source }
      const { ma_lombard_customer_id } = renewalPolicyData
      return await this.updatePolicyData({ data, id: ma_lombard_customer_id, where: 'ma_lombard_customer_id', connection: conn })
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'updateExistPolicy', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }


  /**
   *
   * @param {{data, id, where, connection, connectionRead}} param0
   * @returns
   */
  static async updatePolicyData({ data, id, where, connection }) {
    log.logger({ pagename: basename(__filename), action: 'isPolicyActive', type: 'request', fields: JSON.stringify({ data, id, where }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      this.TABLE_NAME = 'ma_lombard_customer_details'
      const updateRes = this.updateWhere(conn, { data, id, where })
      if (updateRes.affectedRows == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isPolicyActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }
  /**
   *
   * @param {*} fields
   * @param {*} connection
   * @returns
   */
  static async generateNewPolicy({ fields, connection }) {
    log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      let renewal_status = fields.category_code == ('STARHEALTH_PA') ? 'N' : 'Y'
      console.log("renewal_status", renewal_status);

      const data = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        proposer_mobile: fields.proposer_mobile,
        policy_status: 'PS',
        renewal_status,
        orderid: fields.orderid,
        amount: fields.amount,
        policy_name: fields.category_name,
        source: fields.source,
        policy_category_code: fields.category_code,
        policy_expire_date: this.calculateExpiry(fields.expiry_period)
      }

      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'data', fields: data })
      this.TABLE_NAME = 'ma_lombard_customer_details'
      const result = await this.insert(conn, { data })
      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'insert', fields: result })

      if (result.affectedRows == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, ma_lombard_customer_id: result.insertId }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }
  /**
   *
   * @param {number} expiry_period
   * @param {string|null} start_date
   * @returns
   */
  static calculateExpiry(expiry_period, start_date = null) {
    if (start_date) {
      const startDate = tz.tz(start_date, 'Asia/Kolkata').format('YYYY-MM-DD')
      return moment(startDate).add(expiry_period, 'days').format('YYYY-MM-DD')
    }
    const today = tz.tz(moment(), 'Asia/Kolkata').format('YYYY-MM-DD')
    console.log('today', today)
    return moment(today).add(expiry_period, 'days').format('YYYY-MM-DD')
  }

static async getDistribution(fields) {
  const common_fns = require('../../util/common_fns')
  const isSet = (fields.connection === null || fields.connection === undefined) 
  const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection
  log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'request', fields: fields })
  try{
      const checkOtherConfigurationFields = { ...fields }
      delete fields.distributerIdForGetDistribution
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var subSql = `SELECT ma_insurance_subcategory_master_id FROM ma_insurance_subcategory_master WHERE subcategory_code = '${fields.subcategory_code}';`;
      const resp = await this.rawQuery(subSql, conn);
      const ma_insurance_subcategory_master_id = resp[0]?.ma_insurance_subcategory_master_id;
      var sql = `SELECT * FROM ma_slab_distribution_insurance where ma_insurance_category_master_id = ${fields.category_master_id} and ma_insurance_subcategory_master_id = ${ma_insurance_subcategory_master_id} and record_status='Y'`;
      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerSql, conn)
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
      const commonFunction = require('../common/commonFunctionController')
      // Check other configuration (For now this is for distributor specific)
      result = {}
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields: checkOtherConfigurationFields, connection: conn })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      // Reinitializing variables
      ma_user_id = 0
      result = {}

      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        // state_master_id = 2 // FOR TESTING ONLY
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - State Specific', fields: result })
        result = await this.rawQuery(stateSql, conn)
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}

      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_dt_sdt_id = 0`
      result = await this.rawQuery(defaultSql, conn)
      log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
  } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
  }
}



static async starHealthCreateOrder(_, fields) {
  log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'request', fields })
  const connection = await mySQLWrapper.getConnectionFromPool()
  const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
  try {
    // Validate all required fields
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid', 'form_data', 'category_name', 'category_code', 'expiry_period', 'source', 'amount'])
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'validator-response', fields: validatorResponse })

    /* Transaction Check */
    const transactionStatus = await this.transactionStatusCheck({ order_id: fields.orderid, connection })
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'transactionStatus', fields: transactionStatus })
    if (transactionStatus.status != 200) return transactionStatus
    if (transactionStatus.respcode == 1002) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1001, action_code: 1001 }

    const { transaction } = transactionStatus

    /* merchant Check */
    const merchantDetailsSQL = `SELECT CONCAT(IFNULL(firstname, ''), IFNULL(lastname, '')) AS name,IFNULL(mobile_id, '') AS mobile_id, IFNULL(email_id, '') AS email_id FROM ma_user_master mum WHERE mum.profileid = '${fields.ma_user_id}' AND mum.userid = '${fields.userid}' LIMIT 1 `
    const merchantDetailsRes = await this.rawQuery(merchantDetailsSQL, connectionRead)
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'merchantDetailsRes', fields: merchantDetailsRes })
    if (merchantDetailsRes.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1003] }

    /* policy check */
    const checkPolicyStatus = await this.checkPolicyStatus({ ...fields, connectionRead })
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'checkPolicyStatus', fields: checkPolicyStatus })
    if (checkPolicyStatus.status != 200) return checkPolicyStatus

    const { policyData } = checkPolicyStatus

    /* policy category check */
    const checkInsuranceCategoryData = await this.isInsuranceCategoryActive({ category_code: policyData.policy_category_code, connectionRead })
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'checkInsuranceCategory', fields: checkInsuranceCategoryData })
    if (checkInsuranceCategoryData.status != 200) return checkInsuranceCategoryData

    const { data } = checkInsuranceCategoryData

    // validate form data and update the fields
    fields.proposer_mobile = policyData.proposer_mobile
    const formData = await this.getFormData({ fields, connection })
    console.log('form data', formData)
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'checkPolicyStatus', fields: checkPolicyStatus })
    if (formData.status != 200) return formData

    let isPolicyRenewal = ''
      isPolicyRenewal = await this.isPolicyRenewalExist({ proposer_mobile: policyData.proposer_mobile, connectionRead })
      log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'isPolicyRenewalExist', fields: isPolicyRenewal })
      if (isPolicyRenewal.status != 200) return isPolicyRenewal
    

    let policy_number = ''
    let coi = ''

    if (isPolicyRenewal.respcode == 1002) {
      const generateNewPolicy = await this.updateNewPolicy({ fields: { ...fields, ...data }, transaction, policyData, formData, connection, connectionRead })
      log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'generateNewPolicy', fields: generateNewPolicy })

      if (generateNewPolicy.status != 200) return generateNewPolicy
      policy_number = generateNewPolicy.policy_number
      coi = generateNewPolicy.coi
    }

    if (isPolicyRenewal.respcode == 1000) {
      const updateExistingPolicy = await this.updateRenewalPolicy({ fields: { ...fields, ...data }, transaction, policyData: isPolicyRenewal.data, formData, connection, connectionRead })
      log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'updateExistingPolicy', fields: updateExistingPolicy })
      if (updateExistingPolicy.status != 200) return updateExistingPolicy
      policy_number = isPolicyRenewal.data.policy_number
      if (policy_number == null || policy_number == '') {
        policy_number = this.generateReferenceID()
        const updateQuery = `update ma_lombard_customer_details set policy_number = '${policy_number}' where orderid = '${fields.orderid}' AND company_name='STARHEALTH'`
        log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'isPolicyRenewalExistQuery', fields: updateQuery })
        const updateRes = await this.rawQuery(updateQuery, connection)
        log.logger({ pagename: basename(__filename), action: 'addPolicyData', type: 'isPolicyRenewalExistQueryRes', fields: updateRes })
        const updateQuery1 = `update ma_lombard_policy_details set policy_number = '${policy_number}'where order_id = '${fields.orderid}'`
        log.logger({ pagename: basename(__filename), action: 'addPolicyDataTable', type: 'isPolicyRenewalExistQuery', fields: updateQuery })
        const updateRes1 = await this.rawQuery(updateQuery1, connection)
        log.logger({ pagename: basename(__filename), action: 'addPolicyDataTable', type: 'isPolicyRenewalExistQueryRes', fields: updateRes })
      }
      coi = isPolicyRenewal.data.coi
    }

    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, policy_number, coi }
  } catch (err) {
    log.logger({ pagename: basename(__filename), action: 'starHealthCreateOrder', type: 'err', fields: err })
    return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
  } finally {
    connection.release()
    connectionRead.release()
  }
}

  /**
   *
   * @param {{proposer_mobile, connectionRead}} param0
   * @returns
   */
  static async checkPolicyStatus({ orderid, category_code, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'isPolicyActive', type: 'request', fields: { orderid } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const policyDataQuery = `SELECT * FROM ma_lombard_customer_details WHERE orderid='${orderid}'`
      log.logger({ pagename: basename(__filename), action: 'isPolicyActive', type: 'policyDataQuery', fields: policyDataQuery })
      const policyDataResult = await this.rawQuery(policyDataQuery, connRead)
      if (policyDataResult.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      const policyData = policyDataResult[0]

      if (policyData.policy_status != 'I' && policyData.policy_status != 'PS') return { status: 400, message: `${errorMsg.responseCode[1028]} : policy still active`, respcode: 1001, action_code: 1001 }

      if (category_code != policyData.policy_category_code) return { status: 400, message: `${errorMsg.responseCode[1028]} : category_code mismatch`, respcode: 1001, action_code: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, policyData }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isPolicyActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async generateMaskedvalue(value) {
    try {
      let maskedNumber = value

      maskedNumber = maskedNumber.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, 'X')

      return maskedNumber
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'generateMaskedvalue', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   *
   * @param {*} _
   * @param {*} fields
   * @returns
   */

  static async encryptPAN(pan) {
    const iv = crypto.randomBytes(16);
    const secretKey = util.staging.secretKey
    const cipher = crypto.createCipheriv('aes-256-cbc', secretKey, iv)
    let encrypted = cipher.update(pan, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    console.log('encrypted', encrypted)
    return `${iv.toString('hex')}:${encrypted}`
  }

  static async getFormData({ fields, connection }) {
    log.logger({ pagename: basename(__filename), action: 'getFormData', type: 'request', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid', 'form_data'])
      if (validatorResponse.status != 200) return validatorResponse
      const formData = JSON.parse(Buffer.from(fields.form_data, 'base64').toString('ascii'))
      const start_date = tz.tz(moment(), 'Asia/Kolkata').format('DD-MM-YYYY')
      const additional_info = {}
      const customer_ref = fields.orderid
      let proposer
      formData.proposer_aadhar = await this.generateMaskedvalue(formData.proposer_aadhar) || null
      console.log('formData.proposer_aadhar', formData.proposer_aadhar)
      formData.insured_aadhar = await this.generateMaskedvalue(formData.insured_aadhar) || null
      console.log('formData.insured_aadhar', formData.insured_aadhar)
      if (formData.proposer_pannumber) {
        formData.proposer_pannumber = await this.encryptPAN(formData.proposer_pannumber)
        console.log("Proposer Encrypted PAN:", formData.proposer_pannumber)
      }
      if (formData.insured_pannumber) {
        formData.insured_pannumber = await this.encryptPAN(formData.insured_pannumber)
        console.log("Insured Encrypted PAN:", formData.insured_pannumber)
      }
      let insured_pre_existing_disease
      let proposer_pre_existing_disease
      let proposer_politically_exposed_person
      let insured_politically_exposed_person

      insured_pre_existing_disease = null
      proposer_pre_existing_disease = null
      proposer_politically_exposed_person = null
      insured_politically_exposed_person = null
      
      const validateProposer = validator.validateFields(formData, ['proposer_title', 'proposer_first_name', 'proposer_last_name', 'proposer_email', 'proposer_mobile', 'proposer_dob', 'proposer_gender', 'proposer_aadhar'])
      if (validateProposer.status == 200) {
        proposer = {
          title: formData.proposer_title,
          first_name: formData.proposer_first_name,
          last_name: formData.proposer_last_name,
          email: formData.proposer_email,
          phone_number: formData.proposer_mobile,
          dob: formData.proposer_dob,
          gender: formData.proposer_gender,
          aadhar: formData.proposer_aadhar,
          pan: formData.proposer_pannumber || null,
          abha: formData.proposer_abha || null,
          proposer_pre_existing_disease: proposer_pre_existing_disease,
          proposer_politically_exposed_person: proposer_politically_exposed_person
        }
      } else {
        proposer = {
          title: formData.insured_title,
          first_name: formData.insured_first_name,
          last_name: formData.insured_last_name,
          email: formData.insured_email,
          phone_number: formData.insured_mobile,
          dob: formData.insured_dob,
          gender: formData.insured_gender,
          aadhar: formData.insured_aadhar,
          pan: formData.insured_pannumber || null,
          abha: formData.insured_abha || null,
          proposer_pre_existing_disease: proposer_pre_existing_disease,
          proposer_politically_exposed_person: proposer_politically_exposed_person
        }
        formData.relation_with_proposer = 'self'
      }

      const insured = []
      const nominees = []

      insured.push({
        title: formData.insured_title,
        first_name: formData.insured_first_name,
        last_name: formData.insured_last_name,
        email: formData.insured_email,
        phone_number: formData.insured_mobile,
        dob: formData.insured_dob,
        gender: formData.insured_gender,
        aadhar: formData.insured_aadhar,
        pan: formData.insured_pannumber || '',
        abha: formData.insured_abha || '',
        insured_pre_existing_disease: insured_pre_existing_disease,
        insured_politically_exposed_person: insured_politically_exposed_person,
        address: {
          address_line_1: formData.insured_address_line_one.trim(),
          address_line_2: formData.insured_address_line_two.trim(),
          city: formData.insured_city.trim(),
          state: formData.insured_state,
          zipcode: formData.insured_pincode
        },
        relation_with_proposer: formData.relation_with_proposer
      })

      nominees.push({
        title: formData.nominee_title || '',
        first_name: formData.nominee_firstname || '',
        last_name: formData.nominee_lastname || '',
        email: formData.nominee_email ? formData.nominee_email : '',
        phone_number: formData.nominee_mobile ? formData.nominee_mobile : '',
        dob: formData.nominee_dob || '',
        gender: formData.nominee_gender || '',
        relation: formData.nominee_relation || ''
      })

      const customerData = {
        proposer,
        insured,
        nominees,
        start_date,
        customer_ref,
        additional_info
      }

      fields.proposer = proposer
      fields.insured = insured
      fields.nominees = nominees
      fields.company_name = fields.category_code === 'STARHEALTH_PA' ? 'STARHEALTH' :  null

      if (proposer.phone_number != fields.proposer_mobile) return { status: 400, message: `${errorMsg.responseCode[1028]} : phone number mismatch`, respcode: 1000, customerData }

      const updateFormData = await this.updateFormData({ fields, connection: conn })
      log.logger({ pagename: basename(__filename), action: 'getFormData', type: 'response', fields: updateFormData })
      if (updateFormData.status != 200) return updateFormData
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, customerData }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getFormData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object}} param0
   * @returns
   */
  static async updateFormData({ fields, connection }) {
    log.logger({ pagename: basename(__filename), action: 'updateFormData', type: 'request', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid', 'proposer', 'insured', 'nominees'])
      if (validatorResponse.status != 200) return validatorResponse
      const { orderid, proposer, insured, nominees, company_name } = fields
      const data = {
        proposer_title: proposer.title,
        proposer_first_name: proposer.first_name,
        proposer_last_name: proposer.last_name,
        proposer_dob: proposer.dob,
        proposer_mobile: proposer.phone_number,
        proposer_gender: proposer.gender,
        proposer_email: proposer.email,
        proposer_aadhar: proposer.aadhar,
        proposer_pannumber: proposer.pan,
        proposer_abha: proposer.abha,
        proposer_politically_exposed_person: proposer.proposer_politically_exposed_person,
        proposer_pre_existing_disease: proposer.proposer_pre_existing_disease,
        insured_title: insured[0].title,
        insured_first_name: insured[0].first_name,
        insured_last_name: insured[0].last_name,
        insured_dob: insured[0].dob,
        insured_mobile: insured[0].phone_number,
        insured_gender: insured[0].gender,
        insured_email: insured[0].email,
        insured_aadhar: insured[0].aadhar,
        insured_pannumber: insured[0].pan,
        insured_abha: insured[0].abha,
        insured_politically_exposed_person: insured[0].insured_politically_exposed_person,
        insured_pre_existing_disease: insured[0].insured_pre_existing_disease,
        insured_address_line_one: insured[0].address.address_line_1,
        insured_address_line_two: insured[0].address.address_line_2,
        insured_city: insured[0].address.city,
        insured_state: insured[0].address.state,
        insured_pincode: insured[0].address.zipcode,
        relation_with_proposer: insured[0].relation_with_proposer,
        nominee_title: nominees[0] ? nominees[0].title : null,
        nominee_firstname: nominees[0] ? nominees[0].first_name : null,
        nominee_lastname: nominees[0] ? nominees[0].last_name : null,
        nominee_dob: nominees[0] ? nominees[0].dob : null,
        nominee_gender: nominees[0] ? nominees[0].gender : null,
        nominee_mobile: nominees[0] ? nominees[0].phone_number : null,
        nominee_email: nominees[0] ? nominees[0].email : null,
        nominee_relation: nominees[0] ? nominees[0].relation : null,
        company_name: company_name || null
      }

      const updateFields = await this.updatePolicyData({ data, id: orderid, where: 'orderid', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'updateFormData', type: 'updateWhere', fields: updateFields })
      if (updateFields.status != 200) return updateFields

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'updateFormData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }
  /**
   *
   * @returns
   */
  static generateReferenceID() {
    let randomNumber = ''
    // Generate 10 random digits
    for (let i = 0; i < 10; i++) {
      const digit = Math.floor(Math.random() * 10)
      randomNumber += digit
    }
    return randomNumber
  }

  /**
   *
   * @param {{fields:Object,connection,connectionRead}} param0
   * @returns
   */
  static async updateNewPolicy({ fields, policyData, transaction, formData, connection, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'request', fields: JSON.stringify({ fields, policyData, formData, transaction }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const policy_number = this.generateReferenceID()
      const policy_start_date = tz.tz(moment(), 'Asia/Kolkata').format('YYYY-MM-DD')
      const policy_validity = moment().tz('Asia/Kolkata').add(365, 'days').format('YYYY-MM-DD') // all policy time period will be 1 yr
      const { policy_expire_date } = policyData

      const updatePolicyNumber = await this.updatePolicyData({ data: { policy_number }, id: fields.orderid, where: 'orderid', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'updatePolicyNumber', fields: updatePolicyNumber })
      if (updatePolicyNumber.status != 200) return updatePolicyNumber

      const insured_dob = formData.customerData.insured[0] && formData.customerData.insured[0].dob ? moment(formData.customerData.insured[0].dob, 'DD-MM-YYYY').format('YYYY-MM-DD') : 'N/A'
      const customerConsentFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        policy_number,
        proposer_title: formData.customerData.proposer.title,
        proposer_first_name: formData.customerData.proposer.first_name,
        proposer_last_name: formData.customerData.proposer.last_name,
        proposer_dob: formData.customerData.proposer.dob ? moment(formData.customerData.proposer.dob, 'DD-MM-YYYY').format('YYYY-MM-DD') : 'N/A',
        proposer_gender: formData.customerData.proposer.gender,
        proposer_mobile: formData.customerData.proposer.phone_number,
        proposer_email: formData.customerData.proposer.email,
        proposer_politically_exposed_person: formData.customerData.proposer.proposer_politically_exposed_person,
        proposer_pre_existing_disease: formData.customerData.proposer.proposer_pre_existing_disease,
        proposer_pan: formData.customerData.proposer.pan,
        proposer_aadhar: formData.customerData.proposer.aadhar,
        proposer_abha: formData.customerData.proposer.abha,
        insured_title: formData.customerData.insured[0].title,
        insured_email: formData.customerData.insured[0].email,
        insured_first_name: formData.customerData.insured[0].first_name,
        insured_last_name: formData.customerData.insured[0].last_name,
        insured_gender: formData.customerData.insured[0].gender,
        insured_dob: insured_dob || 'N/A',
        insured_mobile: formData.customerData.insured[0].phone_number,
        insured_address_line_one: formData.customerData.insured[0].address.address_line_1,
        insured_address_line_two: formData.customerData.insured[0].address.address_line_2,
        insured_politically_exposed_person: formData.customerData.insured[0].insured_politically_exposed_person,
        insured_pre_existing_disease: formData.customerData.insured[0].insured_pre_existing_disease,
        insured_aadhar: formData.customerData.insured[0].aadhar,
        insured_pan: formData.customerData.insured[0].pan,
        insured_abha: formData.customerData.insured[0].abha,
        relation_with_proposer: formData.customerData.insured[0].relation_with_proposer,
        insured_city: formData.customerData.insured[0].address.city,
        insured_state: formData.customerData.insured[0].address.state,
        insured_pincode: formData.customerData.insured[0].address.zipcode,
        nominee_firstname: formData.customerData.nominees[0] ? formData.customerData.nominees[0].first_name : 'N/A',
        nominee_lastname: formData.customerData.nominees[0] ? formData.customerData.nominees[0].last_name : 'N/A',
        nominee_dob: formData.customerData.nominees[0] && formData.customerData.nominees[0].dob ? (moment(formData.customerData.nominees[0].dob, 'DD-MM-YYYY').format('YYYY-MM-DD') || 'NA') : 'N/A',
        nominee_relation: formData.customerData.nominees[0] ? formData.customerData.nominees[0].relation : 'N/A',
        policy_validity: policy_validity,
        policy_start_date,
        policy_code: fields.category_code,
        insured_age: insured_dob ? this.calculateAge({ dob: insured_dob }) : 0
      }
      console.log('customerConsentFields', customerConsentFields)
      // hardcoded coi
      const fetchCertificate = {
        status: 200,
        coiurl: 'https://example.com/coi.pdf'
      }
      // const fetchCertificate = await this.getCustomerConsentDocPath({ fields: customerConsentFields })
      // log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'getCustomerConsentDocPath', fields: fetchCertificate })
      // await mySQLWrapper.beginTransaction(conn)
      // if (fetchCertificate.status != 200) {
      //   const resp = await this.updateFailedTransaction({ fields, transaction, connection: conn, connectionRead: connRead })
      //   log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'updateFailedTransaction', fields: resp })
      //   if (resp.status != 200) {
      //     await mySQLWrapper.rollback(conn)
      //     return resp
      //   }
      //   await mySQLWrapper.commit(conn)
      //   return fetchCertificate
      // }

      const gracePeriodResp = await this.calculateGracePeriod({ policy_expire: policy_expire_date, connectionRead: connRead })
      log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'graceperiodResp', fields: gracePeriodResp })
      if (gracePeriodResp.status != 200) {
        await mySQLWrapper.rollback(conn)
        return gracePeriodResp
      }
      const { grace_period } = gracePeriodResp

      const updatePolicyDetails = {
        policy_status: 'S',
        policy_expire: 'N',
        policy_validity,
        grace_period,
        coi: fetchCertificate.coiurl,
        policy_number
      }
      const updatePolicyData = await this.updatePolicyData({ data: updatePolicyDetails, id: fields.orderid, where: 'orderid', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'updatePolicyData', fields: updatePolicyData })
      if (updatePolicyData.status != 200) {
        await mySQLWrapper.rollback(conn)
        return updatePolicyData
      }

      /* next data for next request */
      fields.coi = fetchCertificate.coiurl
      fields.policy_number = policy_number
      fields.grace_period = grace_period
      fields.policy_validity = policy_validity
      const resp = await this.updateSuccessTransaction({ fields, policyData, connection: conn, connectionRead: connRead })
      log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'updateSuccessTransaction', fields: resp })
      if (resp.status != 200) {
        await mySQLWrapper.rollback(conn)
        return resp
      }
      await mySQLWrapper.commit(conn)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, policy_number, coi: fetchCertificate.coiurl }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

 /**
   *
   * @param {{policy_expire:string, connectionRead}} expiry_period
   * @returns
 */
  static async calculateGracePeriod({ policy_expire, connectionRead }) {
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const gracePeriodVal = await common.getSystemCodes(this, util.iciciLombardGracePeriod, connRead)
      const expiryDate = tz.tz(policy_expire, 'Asia/Kolkata').format('YYYY-MM-DD')
      const grace_period = moment(expiryDate).add(gracePeriodVal, 'days').format('YYYY-MM-DD')
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, grace_period }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'generateNewPolicy', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {string} param0
   * @returns
   */
  static calculateAge({ dob }) {
    const today = moment()
    return moment.duration(today.diff(dob)).years() || 0
  }

  /**
   *
   * @param {{fields:Object}} param0
   * @returns
   */
  // TODO UPDATE url to get star health coi
  static async getCustomerConsentDocPath({ fields }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerConsentDocPath', type: 'request', fields: fields })
    try {
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'

      const postData = JSON.stringify(fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerConsentDocPath', type: 'postDataRequest', fields: { postData } })
      const response = await axios({
        method: 'post',
        url: util[env].startHealthPDFURL,
        data: postData,
        headers: {
          'Content-Type': 'application/json'
        }
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerConsentDocPath', type: 'apiResponseData', fields: response })

      if (response == '' && response.status != 200) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in receiving Consent Doc Response', uploadList: {} }
      }

      const responseData = typeof (response.data) == 'string' && response.data ? JSON.parse(response.data) : response.data

      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerConsentDocPath', type: 'responseData', fields: responseData })

      if (responseData.status != 200) return { status: 400, respcode: 1028, message: responseData.message, action_code: 1001 }

      if (responseData.status == 'fail') return { status: 400, respcode: 1028, message: responseData.message, action_code: 1001 }

      if (!responseData.data || !responseData.data.full_path) return { status: 400, respcode: 1028, message: responseData.message, action_code: 1001 }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], coiurl: responseData.data.full_path }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerConsentDocPath', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  /**
   *
   * @param {{fields:Object,connection,connectionRead}} param0
   * @returns
   */
  static async updateFailedTransaction({ fields, transaction, connection, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'updateFailedTransaction', type: 'request', fields: { fields, transaction } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const updatePolicyDetail = {
        policy_status: 'F',
        // policy_status: 'PS',
        policy_expire: 'N',
        policy_expire_date: null
      }

      // if (fields.grace_period) {
      //   const gracePeriodDate = tz.tz(fields.grace_period, 'Asia/Kolkata').format('YYYY-MM-DD')
      //   const today = tz.tz(moment(), 'Asia/Kolkata').format('YYYY-MM-DD')
      //   if (gracePeriodDate > today) {
      //     updatePolicyDetail.policy_status = 'S'
      //   }
      // }

      const updatePolicyData = await this.updatePolicyData({ data: updatePolicyDetail, id: fields.orderid, where: 'orderid', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyTransactionData', fields: updatePolicyData })
      if (updatePolicyData.status != 200) return updatePolicyData

      const updatePolicyTransactionDetails = {
        transaction_status: 'F',
        order_status: 'F'
        // transaction_status: 'PS',
        // order_status: 'PS'
      }
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyTransactionDetails', fields: updatePolicyTransactionDetails })

      const updatePolicyTransactionData = await this.updatePolicyTransactionData({ data: updatePolicyTransactionDetails, id: fields.orderid, where: 'order_id', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyTransactionData', fields: updatePolicyTransactionData })
      if (updatePolicyTransactionData.status != 200) return updatePolicyTransactionData

      await transactionMaster.updateWhereData(conn, {
        data: { transaction_status: 'F' },
        // data: { transaction_status: 'PS' },
        id: fields.orderid,
        where: 'aggregator_order_id'
      })
      /* set data for next request */
      // fields.amount = transaction.amount
      // const reverseEntries = await this.reverseLedgerEntries({ fields, connection: conn })
      // if (reverseEntries.status != 200) return reverseEntries
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'updateFailedTransaction', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{data, id, where, connection, connectionRead}} param0
   * @returns
   */
  static async updatePolicyTransactionData({ data, id, where, connection }) {
    log.logger({ pagename: basename(__filename), action: 'isPolicyActive', type: 'request', fields: JSON.stringify({ data, id, where }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      this.TABLE_NAME = 'ma_lombard_policy_details'
      const updateRes = this.updateWhere(conn, { data, id, where })
      if (updateRes.affectedRows == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isPolicyActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }
  /**
   *
   * @param {{fields:Object,connection,connectionRead}} param0
   * @returns
   */
  static async updateSuccessTransaction({ fields, policyData, connection, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'updateSuccessTransaction', type: 'request', fields: { fields, policyData } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const updatePolicyTransactionDetails = {
        policy_number: fields.policy_number,
        policy_validity: tz.tz(fields.policy_validity, 'Asia/Kolkata').format('YYYY-MM-DD'),
        transaction_status: 'S',
        order_status: 'S',
        grace_period: fields.grace_period
      }
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyTransactionDetails', fields: updatePolicyTransactionDetails })
      const updatePolicyTransactionData = await this.updatePolicyTransactionData({ data: updatePolicyTransactionDetails, id: fields.orderid, where: 'order_id', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyTransactionData', fields: updatePolicyTransactionData })
      if (updatePolicyTransactionData.status != 200) return updatePolicyTransactionData

      const form_data = {
        policy_no: fields.policy_number,
        policy_name: fields.category_name,
        amount: fields.amount,
        plan_code: fields.category_name,
        product_code: fields.category_code
      }

      const transactionDetailUpdate = await transactionDetail.updateTransactionDetails({ form_data: JSON.stringify(form_data), aggregator_order_id: fields.orderid }, conn)
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'transactionDetailUpdate', fields: transactionDetailUpdate })
      if (transactionDetailUpdate.status != 200) return transactionDetailUpdate

      await transactionMaster.updateWhereData(conn, {
        data: { transaction_status: 'S' },
        id: fields.orderid,
        where: 'aggregator_order_id'
      })
    
        await this.notifyCustomer({ mobile_number: fields.proposer_mobile, coi: fields.coi, orderid: fields.orderid, connection })
      
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'updateSuccessTransaction', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }
  /**
   *
   * @param {{connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   */
  static async notifyCustomer({ mobile_number, coi, orderid, connection }) {
    log.logger({ pagename: basename(__filename), action: 'notifyCustomer', type: 'request', fields: { mobile_number } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const message = util.communication.LOMBARDCUSTOMERSUCCESS
      const template = util.templateid.LOMBARDCUSTOMERSUCCESS
      log.logger({ pagename: basename(__filename), action: 'notifyCustomer', type: 'request', fields: { message, template } })
      const query = await bitly.create(coi)
      log.logger({ pagename: basename(__filename), action: 'notifyCustomer', type: 'request', fields: query })

      if (query.status != 200) return query

      const shortUrl = query.url.replace('http', 'https').replace('www.arpy', 'arpy')
      const dt = tz.tz(moment(), 'Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss')
      const updateMsg = message.replace('<transaction id>', orderid).replace('<Date & Time>', dt).replace('<Bitly Link>', shortUrl)
      log.logger({ pagename: basename(__filename), action: 'notifyCustomer', type: 'update message', fields: updateMsg })
      await sms.sentSmsAsync(updateMsg, mobile_number, template, '', conn)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'notifyCustomer', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection,connectionRead}} param0
   * @returns
   */
  static async updateRenewalPolicy({ fields, policyData, formData, connection, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'updateRenewalPolicy', type: 'request', fields: { fields, policyData, formData } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const { coi, policy_number, orderid, policy_validity, policy_expire_date } = policyData

      const formatPolicyValidity = tz.tz(policy_validity, 'Asia/Kolkata').format('YYYY-MM-DD')
      console.log('formatPolicyValidity', formatPolicyValidity);

      await mySQLWrapper.beginTransaction(conn)
      const policy_expire = this.calculateExpiry(fields.expiry_period, policy_expire_date)
      console.log('policy expire date', policy_expire)

      const gracePeriodResp = await this.calculateGracePeriod({ policy_expire, connectionRead: connRead })
      log.logger({ pagename: basename(__filename), action: 'updateNewPolicy', type: 'graceperiodResp', fields: gracePeriodResp })
      if (gracePeriodResp.status != 200) {
        await mySQLWrapper.rollback(conn)
        return gracePeriodResp
      }
      const { grace_period } = gracePeriodResp

      const updatePolicyDetails = {
        renewal_status: 'Y',
        policy_status: 'S',
        policy_expire: 'N',
        policy_expire_date: policy_expire,
        grace_period
      }

      /* last month expire case handled */
      if (policy_expire >= formatPolicyValidity) {
        updatePolicyDetails.grace_period = formatPolicyValidity
        updatePolicyDetails.policy_expire_date = formatPolicyValidity
        updatePolicyDetails.renewal_status = 'N'
      }

      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyDetails', fields: updatePolicyDetails })
      const updatePolicyData = await this.updatePolicyData({ data: updatePolicyDetails, id: orderid, where: 'orderid', connection: conn })
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updatePolicyData', fields: updatePolicyData })
      if (updatePolicyData.status != 200) {
        await mySQLWrapper.rollback(conn)
        return updatePolicyData
      }
      /* next data for next request */
      fields.coi = coi
      fields.policy_number = policy_number
      fields.grace_period = grace_period
      fields.policy_validity = policy_validity
      const resp = await this.updateSuccessTransaction({ fields, policyData, connection: conn, connectionRead: connRead })
      log.logger({ pagename: basename(__filename), action: 'newPolicy', type: 'updateSuccessTransaction', fields: resp })
      if (resp.status != 200) {
        await mySQLWrapper.rollback(conn)
        return resp
      }

      await mySQLWrapper.commit(conn)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, policy_number, coi }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'updateRenewalPolicy', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async starHealthCheckActivePolicy (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'starHealthCheckActivePolicy', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const isMerchantAllowed = await this.isMerchantAllowed({ ...fields, connectionRead })
      if (isMerchantAllowed.status != 200) return isMerchantAllowed

      const userChannelData = await this.checkUserChannel(fields.ma_user_id, connection)
      log.logger({ pagename: basename(__filename), action: 'starHealthCheckActivePolicy', type: 'userChannelData', userChannelData })
      if (userChannelData.status && userChannelData.status == 400) return userChannelData
      if (userChannelData == '') return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }

      const isPolicyActive = await this.isPolicyActive({ proposer_mobile: fields.proposer_mobile, connectionRead })
      log.logger({ pagename: basename(__filename), action: 'starHealthCheckActivePolicy', type: 'isPolicyActive', isPolicyActive })

      if (isPolicyActive.status != 200) return isPolicyActive

      const activePolicyQuery = 'select category_name, category_code, category_type, category_flag, amount, expiry_period, app_icon, web_icon, policy_wordings, policy_name, additional_info from ma_insurance_category_master micm where redirection_flag = "false" and riskcovry_sachet = "true" and status = "Active"'
      const activePolicyRes = await this.rawQuery(activePolicyQuery, connectionRead)
      log.logger({ pagename: basename(__filename), action: 'starHealthCheckActivePolicy', type: 'activePolicyRes', activePolicyRes })

      if (activePolicyRes.length == 0) return { status: 400, respcode: 1001, message: `${errorMsg.responseCode[1028]}: No policies active.`, action_code: 1001 }

      const activePolicy = []
      for (var i = 0; i < activePolicyRes.length; i++) {
        activePolicy.push({
          category_name: activePolicyRes[i].category_name,
          policy_name: activePolicyRes[i].policy_name,
          category_code: activePolicyRes[i].category_code,
          category_type: activePolicyRes[i].category_type,
          category_flag: activePolicyRes[i].category_flag,
          amount: activePolicyRes[i].amount,
          expiry_period: activePolicyRes[i].expiry_period,
          description: `Buy ${activePolicyRes[i].policy_name} for a validity of ${activePolicyRes[i].expiry_period} days at a premium of Rs.${activePolicyRes[i].amount}`,
          validity: `${activePolicyRes[i].expiry_period} days`,
          app_icon: activePolicyRes[i].app_icon,
          web_icon: activePolicyRes[i].web_icon,
          tncUrl: activePolicyRes[i].additional_info,
          policy_wordings: activePolicyRes[i].policy_wordings
        })
      }
      log.logger({ pagename: basename(__filename), action: 'starHealthCheckActivePolicy', type: 'response', fields: activePolicy })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], activePolicy, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'starHealthCheckActivePolicy', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

}
 
module.exports = StarHealthInsuranceController