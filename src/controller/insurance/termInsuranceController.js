/* eslint-disable padded-blocks */
/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const configIns = require('./config')
// const errorMsg = require('../../util/error')
const errorMsg = require('./errorMessageInsurance')
const log = require('../../util/log')
const common = require('../../util/common')
const axios = require('axios')
const qs = require('querystring')
const balanceController = require('../balance/balanceController')
const validator = require('../../util/validator')
const securePinCtrl = require('../securityPin/securityPinController')
const transactionCtrl = require('../transaction/transactionController')
const nomineeCtrl = require('./nomineeController')
const airpaychecksum = require('../../util/airpaychecksum')
const { calculateAge } = require('../../util/common_fns')

class TermInsuranceController extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  static get TABLE_NAME () {
    return 'ma_user_policy_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_policy_detail_id'
  }

  /**
   * Returns TABLE_NAME details by its PRIMARY_KEY
   */
  static async getByID (_, { id }) {
    // eslint-disable-next-line no-return-await
    return await this.find(id)
  }

  static async checkActivePolicy (_, fields, prevconnection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'request', fields: fields })
    let isSet = false
    let connection = null
    if (prevconnection != null && prevconnection != undefined && prevconnection.threadId != undefined) {
      connection = prevconnection
    } else {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {

      if (typeof (fields.calculatePremium) === 'undefined') {
        fields.calculatePremium = false
      }

      const selectedPolicySql = ` SELECT * 
              FROM ma_policy_master 
              WHERE policy_code = "${fields.policy_code}" 
              AND status IN ('ACTIVE') limit 1`

      const selectedPolicyData = await this.rawQuery(selectedPolicySql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'selectedPolicyData', fields: selectedPolicyData })
      if (selectedPolicyData.length == 0) {
        return {
          status: 400,
          respcode: 1005,
          message: errorMsg.responseCode[1005]
        }
      }

      let policyStateData = []
      if (!fields.calculatePremium) {
        const policyStateSql = ` SELECT * 
              FROM ma_policy_state_master 
              WHERE state_status IN ('ACTIVE') order by state_name asc `

        policyStateData = await this.rawQuery(policyStateSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'policyStateData', fields: policyStateData })
        if (policyStateData.length == 0) {
          return {
            status: 400,
            respcode: 1007,
            message: errorMsg.responseCode[1007]
          }
        }
      }

      // ii.Fetch details from the details table for this mobile number (new table to contain status of the user and insurance details if any active or inactive) --Done

      const oldPolicyDetailsSql = ` SELECT *,
      IF(policy_expiry_date IS NOT NULL,IF(policy_expiry_date > CURRENT_TIMESTAMP,TRUE,FALSE),FALSE) as is_policy_active,
      TIMESTAMPDIFF(DAY,CURRENT_TIMESTAMP,policy_expiry_date) as pendingdays,
      IF(TIMESTAMPDIFF(DAY,CURRENT_TIMESTAMP,policy_expiry_date) > 0,TIMESTAMPADD(DAY,TIMESTAMPDIFF(DAY,CURRENT_TIMESTAMP,policy_expiry_date) + 1,CURRENT_TIMESTAMP),CURRENT_TIMESTAMP) as next_same_policy_commence_date,
      policy_expiry_date as current_policy_expiry_date,
      CURRENT_TIMESTAMP as next_policy_commence_date
      FROM ma_user_policy_details WHERE customer_mobile_number = "${fields.mobile_number}" 
      AND policy_payment_status = 'Y'
      AND policy_status IN ('VFY','PI') order by ma_user_policy_detail_id DESC limit 1 `

      console.log('oldPolicyDetailsSql >>', oldPolicyDetailsSql)
      const oldPolicyDetails = await this.rawQuery(oldPolicyDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'oldPolicyDetails', fields: oldPolicyDetails })
      const customer_details = {}
      let nominee_list = []
      let detailsExist = null
      let policyUpgrade = 'NEW'
      if (oldPolicyDetails.length > 0) {
        detailsExist = true

        // iv.If found, return basic details to frontend like name, gender, dob
        customer_details.customer_salutation_desc = oldPolicyDetails[0].customer_salutation_desc
        customer_details.customer_first_name = oldPolicyDetails[0].customer_first_name
        customer_details.customer_last_name = oldPolicyDetails[0].customer_last_name
        customer_details.customer_gender = oldPolicyDetails[0].customer_gender
        customer_details.customer_dob = oldPolicyDetails[0].customer_dob
        customer_details.ma_user_policy_detail_id = oldPolicyDetails[0].ma_user_policy_detail_id
        customer_details.current_policy_expiry_date = oldPolicyDetails[0].current_policy_expiry_date

        // Address Details
        customer_details.customer_email_id = oldPolicyDetails[0].customer_email_id
        customer_details.customer_address_one = oldPolicyDetails[0].customer_address_one
        customer_details.customer_address_two = oldPolicyDetails[0].customer_address_two
        customer_details.customer_state_code = oldPolicyDetails[0].customer_state_code
        customer_details.customer_pincode = oldPolicyDetails[0].customer_pincode

        console.log('customer_details.policy_expiry_date', customer_details.current_policy_expiry_date)

        customer_details.next_policy_commence_date = oldPolicyDetails[0].next_policy_commence_date

        if (oldPolicyDetails[0].policy_status == 'VFY' || oldPolicyDetails[0].policy_status == 'PI') {

          if (oldPolicyDetails[0].is_policy_active == 1) {

            // v.	Check if there is any active insurance same as the requested insurance and expiry is greater than 5 days, return “User has Active Insurance already”.

            if (oldPolicyDetails[0].policy_code == fields.policy_code) {
              policyUpgrade = 'RENEW'
              log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'policyUpgrade', fields: policyUpgrade })
              if (oldPolicyDetails[0].pendingdays > 5) {
                policyUpgrade = 'RENEW'
                console.log('Check if there is any active insurance same as the requested insurance and expiry is greater than 5 days, return “User has Active Insurance already”.')
                return {
                  status: 400,
                  respcode: 1003,
                  message: errorMsg.responseCode[1003]
                }
              // eslint-disable-next-line brace-style
              }
              else if (oldPolicyDetails[0].pendingdays <= 5) {

                customer_details.next_policy_commence_date = oldPolicyDetails[0].next_same_policy_commence_date
              // eslint-disable-next-line brace-style
              }
            } else if (oldPolicyDetails[0].policy_code !== fields.policy_code) {
              policyUpgrade = selectedPolicyData[0].policy_amount < oldPolicyDetails[0].policy_sum_assured_amount ? 'DOWNGRADE' : 'UPGRADE'
              log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'policyUpgrade', fields: policyUpgrade })
              if (selectedPolicyData[0].policy_amount < oldPolicyDetails[0].policy_sum_assured_amount && oldPolicyDetails[0].pendingdays > 5) {
                policyUpgrade = 'DOWNGRADE'
                console.log('Check if there is any active insurance and requested insurance is a downgrade and expiry is greater than 5 days, return “User cannot downgrade from Current Insurance”')
                return {
                  status: 400,
                  respcode: 1006,
                  message: errorMsg.responseCode[1006]
                }
              } else if (selectedPolicyData[0].policy_amount > oldPolicyDetails[0].policy_sum_assured_amount && oldPolicyDetails[0].pendingdays > 5) {
                policyUpgrade = 'UPGRADE'
              } else if (selectedPolicyData[0].policy_amount > oldPolicyDetails[0].policy_sum_assured_amount && oldPolicyDetails[0].pendingdays <= 5) {
                policyUpgrade = 'UPGRADE'
              }
            }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'policyUpgrade', fields: policyUpgrade })
          }

          // vi.	Check if there is any active insurance and requested insurance is a downgrade and expiry is greater than 5 days, return “User cannot downgrade from Current Insurance”

        }

        if (!fields.calculatePremium) {
          const nomineesListSql = ` SELECT * 
              FROM ma_users_policy_nominees 
              WHERE ma_user_policy_detail_id = ${oldPolicyDetails[0].ma_user_policy_detail_id} `

          const nomineesListData = await this.rawQuery(nomineesListSql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'nomineesListData', fields: nomineesListData })
          if (nomineesListData.length > 0) {
            nominee_list = nomineesListData
          }
        }

        customer_details.customer_nominee_list = nominee_list
      } else {
        // iii.If no details found, return flag = false thus frontend will take other details - Done
        detailsExist = false
      }

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        old_policy_holder: detailsExist,
        customer_details: customer_details,
        policy_state_data: policyStateData,
        selectedPolicyData: selectedPolicyData,
        oldPolicyDetails: oldPolicyDetails,
        policyUpgrade: policyUpgrade
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePolicy', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async calculatePremium (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      if (fields.countrycode == null || fields.countrycode == undefined) {
        fields.countrycode = 'IN'
      }
      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'mobileresponse', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1011, message: errorMsg.responseCode[1011] + fields.mobile_number }
      }

      const validateDate = validator.validatedate(fields.customer_dob)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'validateDate', fields: { isvalid: validateDate } })
      if (validate.status === false) {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + fields.validateDate }
      }

      // validate age
      const dobData = fields.customer_dob.split('-')
      const ageIs = calculateAge(dobData[0], dobData[1], dobData[2]) // birth_day, birth_month, birth_year
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'validateAge', fields: { isvalid: ageIs } })

      const miniAge = 18
      const maxAge = 50
      if (ageIs >= 18 && ageIs <= 50) {
        console.log('Valid Age >> ', ageIs, ' <<')
      } else {
        const agemessage = errorMsg.responseCode[1024].replace('#minage', miniAge).replace('#maxage', maxAge)
        return { status: 400, respcode: 1024, message: agemessage }
      }

      // customer name validation
      fields.customer_first_name = fields.customer_first_name.trim()
      const validateFirstCusomerName = validator.validInput('name', fields.customer_first_name)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'validateFirstCusomerName', fields: validateFirstCusomerName })
      if (validateFirstCusomerName === false) {
        return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013] }
      }

      fields.customer_last_name = fields.customer_last_name.trim()
      const validateLastCusomerName = validator.validInput('name', fields.customer_last_name)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'validateLastCusomerName', fields: validateLastCusomerName })
      if (validateLastCusomerName === false) {
        return { status: 400, respcode: 1013, message: errorMsg.responseCode[1022] }
      }

      fields.calculatePremium = true
      const checkActiveRes = await this.checkActivePolicy('_', fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'checkActiveResponse', fields: checkActiveRes })

      // Check State Code

      // state_code
      const stateCodeSql = ` SELECT * 
              FROM ma_policy_state_master 
              WHERE state_code = "${fields.state_code}"  AND state_status = "ACTIVE" limit 1`

      const stateCodeListData = await this.rawQuery(stateCodeSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'stateCodeListData', fields: stateCodeListData })
      if (stateCodeListData.length <= 0) {
        return { status: 400, respcode: 1008, message: errorMsg.responseCode[1008] }
      }

      if (checkActiveRes.status == 200) {
        const selectedPolicy = checkActiveRes.selectedPolicyData
        fields.policy_sum_assured_amount = selectedPolicy[0].policy_amount
        // fields.policy_code = fields.policy_code
        fields.policy_id = selectedPolicy[0].policy_id
        fields.policy_duration = selectedPolicy[0].policy_duration
        fields.policy_duration_type = selectedPolicy[0].policy_duration_type
        fields.policy_renewal_type = checkActiveRes.policyUpgrade
        fields.customer_dob = fields.customer_dob.split('-').reverse().join('-')
        fields.customer_salutation_desc = fields.customer_gender === 'M' ? 'Mr' : 'Mrs'

        let timetype = ''
        if (selectedPolicy[0].policy_duration_type == 'D') {
          timetype = 'DAY'
        } else if (selectedPolicy[0].policy_duration_type == 'M') {
          timetype = 'MONTH'
        } else {
          timetype = 'YEAR'
        }
        const Timepolicy_duration = selectedPolicy[0].policy_duration

        if (checkActiveRes.old_policy_holder === false) {
          const policyDateSql = ` SELECT FROM_UNIXTIME(UNIX_TIMESTAMP(CURRENT_TIMESTAMP),'%Y-%m-%d 00:00:00') as policy_commence_date,
          FROM_UNIXTIME(UNIX_TIMESTAMP(TIMESTAMPADD(${timetype},${Timepolicy_duration},CURRENT_TIMESTAMP)),'%Y-%m-%d 23:59:59')
           as policy_expiry_date
          `
          const policyDateData = await this.rawQuery(policyDateSql, connection)
          fields.policy_commence_date = policyDateData[0].policy_commence_date
          fields.policy_expiry_date = policyDateData[0].policy_expiry_date

          log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'Not_old_policy_holder_policyDateData', fields: policyDateData })
        } else {
          fields.policy_commence_date = checkActiveRes.customer_details.next_policy_commence_date
          const mysqlCommenceDate = this.toMysqlFormat(fields.policy_commence_date)
          const policyDateSql = ` SELECT FROM_UNIXTIME(UNIX_TIMESTAMP('${mysqlCommenceDate}'),'%Y-%m-%d 00:00:00') as policy_commence_date,
          FROM_UNIXTIME(UNIX_TIMESTAMP(TIMESTAMPADD(${timetype},${Timepolicy_duration},'${mysqlCommenceDate}')),'%Y-%m-%d 23:59:59') as policy_expiry_date `

          console.log('policyDateSql >> ', policyDateSql)
          const policyDateData = await this.rawQuery(policyDateSql, connection)
          fields.policy_commence_date = policyDateData[0].policy_commence_date
          fields.policy_expiry_date = policyDateData[0].policy_expiry_date

          log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'old_policy_holder_policyDateData', fields: policyDateData })
        }

        fields.affiliateid = configIns.affiliateid
        fields.companyid = configIns.companyid
        fields.customer_dob_format = fields.customer_dob.split('-').reverse().join('/')
        // let loancommencedate = fields.policy_commence_date.split(' ')
        // loancommencedate = loancommencedate[0].split('-').reverse().join('/')
        // fields.loancommencedate = loancommencedate

        const orderMSResponse = await this.createOrderAtMS('_', fields, connection)
        console.log('orderMSResponse >>', orderMSResponse)

        log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'orderMSResponse', fields: orderMSResponse })

        if (orderMSResponse.status != 200) {
          return orderMSResponse
        }

        fields.ms_reference_id = orderMSResponse.referenceid
        fields.order_request = orderMSResponse.request
        fields.order_response = orderMSResponse.response

        const orderData = orderMSResponse.response.data

        let totalPremiumAmount = 0
        let ageIn = 0
        if (('TotalPremium' in orderData) && (Object.keys(orderData).length > 0)) {
          const { TotalPremium, Age, CoverEndDate } = orderData
          fields.policy_premium_amount = totalPremiumAmount = parseFloat(TotalPremium)

          if (isNaN(TotalPremium) || !(totalPremiumAmount > 0 && totalPremiumAmount <= 99999999.99)) {
            const premiumMesg = errorMsg.responseCode[1009].replace('#amount', TotalPremium)
            log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'Wrong-total_premium', fields: { totalPremiumAmount: totalPremiumAmount } })
            return { status: 400, respcode: 1009, message: premiumMesg }
          }

          if (isNaN(Age) || !(Age > 0 && Age <= 120)) {
            const ageMesg = errorMsg.responseCode[1020].replace('#age', Age)
            log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'Wrong-total_premium', fields: { totalPremiumAmount: totalPremiumAmount } })
            return { status: 400, respcode: 1009, message: ageMesg }
          } else {
            ageIn = parseInt(Age)
          }

          if (typeof (CoverEndDate) != 'undefined' && CoverEndDate.indexOf('T') > 0) {
            const tempConverDate = CoverEndDate.split('T')
            fields.policy_expiry_date = tempConverDate[0] + ' 23:59:59'
            const formattedDate = tempConverDate[0].split('-').reverse().join('-')
            const validateDate = validator.validatedate(formattedDate)
            log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'validateDate', fields: { isvalid: validateDate } })
            if (validate === false) {
              return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' Cover end date' + CoverEndDate }
            }
          } else {
            return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' Cover end date' + CoverEndDate }
          }

        } else {
          return {
            status: 400, respcode: 1019, message: errorMsg.responseCode[1019]
          }
        }

        await mySQLWrapper.beginTransaction(connection)
        const entry = await this.createEntry('_', fields, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'createEntry', fields: entry })
        if (entry.status !== 200) {
          await mySQLWrapper.rollback(connection)
          return entry
        }
        
        await mySQLWrapper.commit(connection)

        const ma_user_policy_detail_id = entry.id

        fields.affiliateid = configIns.affiliateid
        fields.companyid = configIns.companyid
        fields.customer_dob_format = fields.customer_dob.split('-').reverse().join('/')
        let loancommencedate = fields.policy_commence_date.split(' ')
        loancommencedate = loancommencedate[0].split('-').reverse().join('/')
        fields.loancommencedate = loancommencedate
        // fields.referenceid = ma_user_policy_detail_id

        // const callPremiumMSResponse = await this.callPremiumMS('_', fields, connection)
        // console.log('callPremiumMSResponse >>', callPremiumMSResponse)

        // log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'callPremiumMSResponse', fields: callPremiumMSResponse })

        // if (callPremiumMSResponse.status !== 200) {
        //   return callPremiumMSResponse
        // }

        const availableBalance = await balanceController.getWalletBalancesDirect(_, {
          ma_user_id: fields.ma_user_id,
          ma_status: 'ACTUAL',
          balance_flag: 'SUMMARY',
          connection
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'availableBalance', fields: availableBalance })

        if (availableBalance.amount < totalPremiumAmount) {
          return { status: 400, respcode: 1010, message: errorMsg.responseCode[1010] }
        }

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          total_premium: totalPremiumAmount,
          ma_user_policy_detail_id: ma_user_policy_detail_id,
          aggregator_order_id: fields.aggregator_order_id,
          age: ageIn
        }
      } else {
        return checkActiveRes
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePremium', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async createEntry (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })

    let isSet = false
    if (connection == null) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }

    try {
      const _result = await this.insert(connection, {
        data: {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          order_id: fields.aggregator_order_id,
          customer_salutation_desc: fields.customer_salutation_desc,
          customer_mobile_number: fields.mobile_number,
          customer_first_name: fields.customer_first_name,
          customer_last_name: fields.customer_last_name,
          customer_gender: fields.customer_gender,
          customer_dob: fields.customer_dob,
          policy_code: fields.policy_code,
          policy_sum_assured_amount: fields.policy_sum_assured_amount,
          policy_id: fields.policy_id,
          policy_duration: fields.policy_duration,
          policy_duration_type: fields.policy_duration_type,
          policy_renewal_type: fields.policy_renewal_type,
          policy_commence_date: fields.policy_commence_date,
          policy_expiry_date: fields.policy_expiry_date,
          ma_transaction_master_id: 0,
          affiliated_id: fields.affiliateid,
          company_id: fields.companyid,
          order_request: JSON.stringify(fields.order_request),
          order_response: JSON.stringify(fields.order_response),
          sendapp_request: '{}',
          sendapp_response: '{}',
          ms_reference_id: fields.ms_reference_id,
          customer_state_code: fields.state_code,
          policy_status: 'I',
          policy_premium_amount: fields.policy_premium_amount,
          policy_response: '{}'
        }
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
 * You first need to create a formatting function to pad numbers to two digits…
 **/
  static twoDigits (d) {
    if (d >= 0 && d < 10) return '0' + d.toString()
    if (d > -10 && d < 0) return '-0' + (-1 * d).toString()
    return d.toString()
  }

  /**
* …and then create the method to output the date string as desired.
* Some people hate using prototypes this way, but if you are going
* to apply this to more than one Date object, having it as a prototype
* makes sense.
**/
  static toMysqlFormat (dateString) {
    const d = new Date(dateString)
    return d.getUTCFullYear() + '-' + this.twoDigits(1 + d.getUTCMonth()) + '-' + this.twoDigits(d.getUTCDate()) + ' ' + this.twoDigits(d.getUTCHours()) + ':' + this.twoDigits(d.getUTCMinutes()) + ':' + this.twoDigits(d.getUTCSeconds())
  };

  static async createOrderAtMS (_, fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createOrderAtMS', type: 'request', fields: fields })
    // const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      const postParams = {
        orderid: fields.aggregator_order_id ? fields.aggregator_order_id : '',
        affiliateid: configIns.affiliateid,
        companyid: configIns.companyid,
        dob: fields.customer_dob_format,
        sumassured: fields.policy_sum_assured_amount,
        borrowersalutationdesc: fields.customer_salutation_desc,
        borrowerstatecode: fields.state_code,
        mobile_no: fields.mobile_number
      }

      const createOrderAPIURL = configIns.insuranceURL
      const generatedChecksum = airpaychecksum.createMSCheckSum(postParams, configIns.SECRETKEY)

      const postParamsData = qs.stringify(postParams)

      console.time('TIMER_KOTAK_CREATE_ORDER_' + fields.aggregator_order_id)
      const response = await axios({
        method: 'post',
        url: createOrderAPIURL + 'api/ins/create_order',
        data: postParamsData,
        timeout: 60000, // 60 secs timeout,
        headers: {
          Accept: 'text/plain',
          'Content-Type': 'application/x-www-form-urlencoded',
          AFFKEY: configIns.AFFKEY,
          AIRPAYKEY: configIns.AIRPAYKEY,
          CHECKSUM: generatedChecksum
        }
      })

      console.timeEnd('TIMER_KOTAK_CREATE_ORDER_' + fields.aggregator_order_id)

      console.log(`INS_ORDER_[${fields.aggregator_order_id}]_[REQUEST-${JSON.stringify(postParams)}]_[STATUS-${response.status}]_[RESPONSE-${JSON.stringify(response.data)}]`)

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'createOrderAtMS', type: 'catcherror', fields: 'Server Status' + response.status })
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' order creation' }
      }

      const orderResponse = response.data
      log.logger({ pagename: require('path').basename(__filename), action: 'createOrderAtMS', type: 'orderResponse', fields: orderResponse })

      if (orderResponse.status == 200) {
        // {"status":"200","data":{"referenceid":967,"url":"bankingvbbb"},"message":"success"}
        return {
          status: 200,
          message: orderResponse.message,
          request: postParams,
          response: orderResponse,
          referenceid: orderResponse.referenceid
        }

      } else {
        return {
          status: 400,
          respcode: 1001,
          message: orderResponse.message ? orderResponse.message : errorMsg.responseCode[1001] + ' for Order [' + fields.aggregator_order_id + '] response'
        }
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createOrderAtMS', type: 'catcherror', fields: err })

      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // connection.release()
    }
  }

  static async doPolicyTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      if (fields.countrycode == null || fields.countrycode == undefined) {
        fields.countrycode = 'IN'
      }
      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'mobileresponse', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1011, message: errorMsg.responseCode[1011] + fields.mobile_number }
      }

      const validateDate = validator.validatedate(fields.customer_dob)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateDate', fields: { isvalid: validateDate } })
      if (validate === false) {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + fields.validateDate }
      }

      // customer name validation
      fields.customer_first_name = fields.customer_first_name.trim()
      const validateFirstCusomerName = validator.validInput('name', fields.customer_first_name)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateFirstCusomerName', fields: validateFirstCusomerName })
      if (validateFirstCusomerName === false) {
        return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013] }
      }

      fields.customer_last_name = fields.customer_last_name.trim()
      const validateLastCusomerName = validator.validInput('name', fields.customer_last_name)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateLastCusomerName', fields: validateLastCusomerName })
      if (validateLastCusomerName === false) {
        return { status: 400, respcode: 1013, message: errorMsg.responseCode[1022] }
      }

      // customer name validation
      fields.customer_email_id = fields.customer_email_id.trim()
      const validateCustomerEmail = validator.validInput('email', fields.customer_email_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateCustomerEmail', fields: { isvalid: validateCustomerEmail } })
      if (validateCustomerEmail === false) {
        return { status: 400, respcode: 1014, message: errorMsg.responseCode[1014] }
      }

      fields.address_one = fields.address_one.trim()
      const validateAddressOne = validator.validInput('alphanumericspacecomma', fields.address_one)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateAddressOne', fields: { isvalid: validateAddressOne } })
      if (validateAddressOne === false) {
        return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015].replace('#SR', 'one') }
      }

      fields.address_two = fields.address_two.trim()
      const validateAddressTwo = validator.validInput('alphanumericspacecomma', fields.address_two)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateAddressTwo', fields: { isvalid: validateAddressTwo } })
      if (validateAddressTwo === false) {
        return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015].replace('#SR', 'two') }
      }

      fields.pincode = fields.pincode.trim()
      const validatePincode = validator.validInput('indianpincode', fields.pincode)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'pincode', fields: { isvalid: validatePincode } })
      if (validatePincode === false) {
        return { status: 400, respcode: 1016, message: errorMsg.responseCode[1016] }
      }

      fields.nominee_list = JSON.parse(JSON.stringify(fields.nominee_list))

      const finalNomiee = []
      for (const key in fields.nominee_list) {

        const currentNomiee = fields.nominee_list[key]

        // customer name validation
        currentNomiee.nominee_first_name = currentNomiee.nominee_first_name.trim()
        const validateNomineeFirstName = validator.validInput('alphasinglespace', currentNomiee.nominee_first_name)
        log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateNomineeFirstName' + key, fields: { isvalid: validateNomineeFirstName } })
        if (validateNomineeFirstName === false) {
          return { status: 400, respcode: 1017, message: errorMsg.responseCode[1017] }
        }

        currentNomiee.nominee_last_name = currentNomiee.nominee_last_name.trim()
        const validateNomineeLastName = validator.validInput('alphasinglespace', currentNomiee.nominee_last_name)
        log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateNomineeLastName' + key, fields: { isvalid: validateNomineeLastName } })
        if (validateNomineeLastName === false) {
          return { status: 400, respcode: 1023, message: errorMsg.responseCode[1023] }
        }

        const validateDob = validator.validatedate(currentNomiee.nominee_dob)
        log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'validateDob_' + key, fields: { isvalid: validateDob } })
        if (validateDob === false) {
          return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' nominee dob' }
        }

        currentNomiee.nominee_dob = currentNomiee.nominee_dob.split('-').reverse().join('-')
        currentNomiee.ma_user_policy_detail_id = fields.ma_user_policy_detail_id

        currentNomiee.nominee_salutation_desc = currentNomiee.nominee_gender == 'M' ? 'Mr' : 'Mrs'
        currentNomiee.customer_mobile_number = fields.mobile_number

        finalNomiee.push(currentNomiee)
      }

      // state_code
      const stateCodeSql = ` SELECT * 
              FROM ma_policy_state_master 
              WHERE state_code = "${fields.state_code}" AND state_status = "ACTIVE" limit 1`

      const stateCodeListData = await this.rawQuery(stateCodeSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'stateCodeListData', fields: stateCodeListData })
      if (stateCodeListData.length <= 0) {
        return { status: 400, respcode: 1008, message: errorMsg.responseCode[1008] }
      }

      fields.state_name = stateCodeListData[0].state_name

      // state_code
      const policyCodeSql = ` SELECT * 
              FROM ma_policy_master 
              WHERE policy_code = "${fields.policy_code}" AND status = "ACTIVE" limit 1`

      const policyCodeData = await this.rawQuery(policyCodeSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyCodeData', fields: policyCodeData })
      if (policyCodeData.length <= 0) {
        return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
      }

      // state_code
      const policyDetailsSql = ` SELECT * 
              FROM ma_user_policy_details 
              WHERE ma_user_policy_detail_id = "${fields.ma_user_policy_detail_id}" AND order_id = "${fields.aggregator_order_id}" AND customer_mobile_number = "${fields.mobile_number}" limit 1`

      const policyDetailData = await this.rawQuery(policyDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyDetailData', fields: policyDetailData })
      if (policyDetailData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' policy request' }
      }

      // Verify Pin Valid , Locked, Exired
      const securePinData = await securePinCtrl.verifySecurePin(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        security_pin: fields.security_pin,
        connection: connection
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'securePinData', fields: securePinData })

      if (securePinData.status === 400) {
        return securePinData
      }

      const availableBalance = await balanceController.getWalletBalancesDirect(_, {
        ma_user_id: fields.ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'availableBalance', fields: availableBalance })

      const policy_premium_amount = policyDetailData[0].policy_premium_amount
      if (availableBalance.amount < policy_premium_amount) {
        return { status: 400, respcode: 1010, message: errorMsg.responseCode[1010] }
      }

      var sql = `SELECT * from ma_transaction_master where aggregator_order_id='${fields.aggregator_order_id}' limit 1`
      const getTxnDetails = await this.rawQuery(sql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'getTxnDetails', fields: getTxnDetails })

      if (getTxnDetails.length > 0) {
        const oldTranMesg = errorMsg.responseCode[1018].replace('#orderid', fields.aggregator_order_id)
        return { status: 400, respcode: 1018, message: oldTranMesg }
      }

      var catsql = `SELECT * 
      from ma_insurance_category_master where category_code='${configIns.defaultCode}' limit 1`
      const policyCatMaster = await this.rawQuery(catsql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyCatMaster', fields: policyCatMaster })

      if (policyCatMaster.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' policy category' }
      }

      fields.affiliated_id = configIns.affiliateid // insurance
      fields.company_id = configIns.companyid // insurance

      fields.customer_name = fields.customer_first_name + ' ' + fields.customer_last_name
      const transactionData = await transactionCtrl.initiateTransaction(_, {
        connection: connection,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.aggregator_order_id,
        amount: policy_premium_amount,
        transaction_type: 15, // insurance
        remarks: policyCodeData[0].policy_desc,
        mobile_number: fields.mobile_number,
        provider_id: policyCodeData[0].policy_id, // to do
        provider_name: policyCodeData[0].policy_provider, // to do
        utility_id: '0', // to do
        utility_name: 'insurance', // to do
        action_type: 'instapay',
        transaction_status: 'I',
        affiliated_id: fields.affiliated_id,
        category_master_id: policyCatMaster[0].ma_insurance_category_master_id,
        subcategory_code: fields.policy_code, // to do static
        customer_name: fields.customer_name,
        customer_mobile: fields.mobile_number,
        customer_email: fields.customer_email_id,
        bank_name: 'KOTAK',
        company_id: fields.company_id
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'transactionData', fields: transactionData })

      if (transactionData.status === 400) {
        return transactionData
      }

      fields.ma_transaction_master_id = transactionData.transaction_id

      // insert nominees
      await mySQLWrapper.beginTransaction(connection)
      try {
        for (const key in finalNomiee) {
          const currentNomiee = finalNomiee[key]
          const _result = await nomineeCtrl.insertData(connection, {
            data: currentNomiee
          })
        }
      } catch (e) {
        console.log('NomieeInsertError', e)
        log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'catcherror', fields: e })
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
      // await mySQLWrapper.commit(connection)

      // update latest given data
      // await mySQLWrapper.beginTransaction(connection)
      try {
        const updateRequestDetails = await this.updateWhere(connection, {
          data: {
            customer_first_name: fields.customer_first_name,
            customer_last_name: fields.customer_last_name,
            customer_gender: fields.customer_gender,
            customer_email_id: fields.customer_email_id,
            customer_address_one: fields.address_one,
            customer_address_two: fields.address_two,
            customer_pincode: fields.pincode,
            customer_state_code: fields.state_code,
            ma_transaction_master_id: fields.ma_transaction_master_id,
            policy_status: 'CP'
          },
          id: fields.ma_user_policy_detail_id,
          where: 'ma_user_policy_detail_id'
        })
      } catch (e) {
        console.log('UpdatePolicyDetailError', e)
        log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'catcherror', fields: e })
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
      await mySQLWrapper.commit(connection)

      fields.nominee_list = finalNomiee
      fields.ms_reference_id = policyDetailData[0].ms_reference_id
      const sendAppDataApiData = await this.sendAppDataApi('', fields, connection)
      console.log('sendAppDataApiData>>', sendAppDataApiData)
      if (sendAppDataApiData.status == 200) {
        console.log('||sendAppDataApiData||')
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          ma_user_policy_detail_id: fields.ma_user_policy_detail_id,
          dogh: sendAppDataApiData.dogh
        }
      } else {
        console.log('||sendAppDataApiDataElse||')
      }

      return sendAppDataApiData
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'catcherror', fields: err })
      console.log('err >>', err)
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async sendAppDataApi (_, fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendAppDataApi', type: 'request', fields: fields })
    // const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      const customerid = await this.generateUIC(fields.mobile_number)

      // const borrowerNameArray = fields.customer_name.split(' ')
      // const borrowerfname = borrowerNameArray[0]
      const borrowerlname = fields.customer_last_name
      const borrowerfname = fields.customer_first_name

      const nomineeData = fields.nominee_list[0] // to do need to ask in case of multiple nomineed
      // const nomnameArray = nomineeData.nominee_name.split(' ')
      // const nomfirstname = nomnameArray[0]
      const nomsurname = nomineeData.nominee_last_name
      const nomfirstname = nomineeData.nominee_first_name

      const postParams2 = {
        referenceid: fields.ms_reference_id, // to do static
        customerid: customerid, // to do & gender not present
        borrowerfname: borrowerfname,
        borrowerlname: borrowerlname,
        emailid: fields.customer_email_id,
        addressline1: fields.address_one,
        addressline2: fields.address_two,
        addressline3: fields.state_code, // to do need to asked
        addressline4: '',
        addressline5: '',
        statename: fields.state_name,
        pincode: fields.pincode,
        nomsalutation: nomineeData.nominee_salutation_desc,
        nomfirstname: nomfirstname,
        nomsurname: nomsurname,
        nomdob: nomineeData.nominee_dob + 'T12:00:00',
        nomrelation: nomineeData.nominee_relationship,
        nompcnt: '100',
        mobile_no: fields.mobile_number
      }

      const updateRequestDetails = await this.updateWhere(connection, {
        data: {
          sendapp_request: JSON.stringify(postParams2)
        },
        id: fields.ma_user_policy_detail_id,
        where: 'ma_user_policy_detail_id'
      })
      // return updateQueue

      const createSentAppAPIURL = configIns.insuranceURL
      const generatedChecksum = airpaychecksum.createMSCheckSum(postParams2, configIns.SECRETKEY)

      const postParamsData = qs.stringify(postParams2)

      console.time('TIMER_KOTAK_SEND_DATA_' + fields.aggregator_order_id)
      const response = await axios({
        method: 'post',
        url: createSentAppAPIURL + 'api/ins/sendapp_data',
        data: postParamsData,
        headers: {
          Accept: 'text/plain',
          'Content-Type': 'application/x-www-form-urlencoded',
          AFFKEY: configIns.AFFKEY,
          AIRPAYKEY: configIns.AIRPAYKEY,
          CHECKSUM: generatedChecksum
        },
        // timeout: 20000 // 20 secs timeout
      })
      console.timeEnd('TIMER_KOTAK_SEND_DATA_' + fields.aggregator_order_id)

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'sendAppDataApi', type: 'catcherror', fields: 'Server Status' + response.status })
        const updateQueue = await this.updateWhere(connection, {
          data: {
            sendapp_response: JSON.stringify({ status: 500, message: 'Server Error' })
          },
          id: fields.referenceid,
          where: 'ma_user_policy_detail_id'
        })
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      const sendAppDataApiResponse = response.data
      log.logger({
        pagename: require('path').basename(__filename),
        action: 'sendAppDataApi',
        type: 'sendAppDataApiRequestResponse',
        fields: [
          fields.aggregator_order_id,
          JSON.stringify(postParams2),
          JSON.stringify(sendAppDataApiResponse)
        ].join('__')
      })

      if (sendAppDataApiResponse.status === 200) {

        // const { SumAssured, Premium, ServiceTax, TotalPremium } = orderResponse.customerdetails

        const updateDetailsRes = await this.updateWhere(connection, {
          data: {
            sendapp_response: JSON.stringify(sendAppDataApiResponse),
            policy_status: 'SA' // to do need to asked
          },
          id: fields.ma_user_policy_detail_id,
          where: 'ma_user_policy_detail_id'
        })

        // {"status":200,"message":"Success","data":{"PolicyNo":"GO000002","FIName":"Demo Client","LoanID":"17589308745105","CustomerID":"42171830535129","BorrowerSalutationDesc":"Mr","BorrowerFirstName":"Sanjay","BorrowerSurName":"Sinalkar","EmailID":"<EMAIL>","GenderDesc":"Male","BranchName":"Tirupur","BranchCode":"932","RegOfficeName":"Coimbatore","RegionId":"4","AddressLine1":"Flat 502, Bhoomi Parth","AddressLine2":"Plot 14, Sector 7","AddressLine3":"Navi Mumbai","AddressLine4":"","AddressLine5":"","StateName":"Maharashtra","PinCode":"400701","NomPcnt":"","AppointeeSalutationDesc":"","AppointeeFirstName":"","AppointeeSurName":"","AppointeeGenderDesc":"","AppointeeMartialStatusDesc":"","AppointeeDOB":"1753-01-01T00:00:00","AppointeeID":"542668","AppointeeRelationDesc":"","ProposerSalutationDesc":"","ProposerFirstName":"","ProposerSurName":"","ProposerGenderDesc":"","ProposerMartialStatusDesc":"","ProposerDOB":"1753-01-01T00:00:00","ProposerID":"","SecSalutationDesc":"","SecondaryMemberFirstName":"","SecondaryMemberSirName":"0","SecCoverAmt":"0.00","SecondaryMemID":"","SecLoanID":"","IsDOGHSubmitted":"89","UTRNo":"39958832","Remarks":"","LoanTypeDesc":"0","DateOfCommencement":"2020-11-05T00:00:00","DateOfBirth":"1970-01-01T00:00:00","SantionAmt":"300000.00","IsFundedBySIB":"78","LoanTenureYrs":"0","RoleAssociated":"Single Life","SecInsuredDOB":"0001-01-01T00:00:00","SecondaryInsAge":"0","SecondaryKotakPremium":"0"},"referenceid":"1028"}

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          response: sendAppDataApiResponse,
          dogh: sendAppDataApiResponse.dogh
        }

      } else {

        const updateDetailsRes = await this.updateWhere(connection, {
          data: {
            sendapp_response: JSON.stringify({ failed: sendAppDataApiResponse }),
            policy_status: 'F' // to do need to asked
          },
          id: fields.ma_user_policy_detail_id,
          where: 'ma_user_policy_detail_id'
        })

        const updateTranResponse = await transactionCtrl.updateWhereData(connection, {
          data: {
            transaction_status: 'F'
          },
          id: fields.ma_transaction_master_id,
          where: 'ma_transaction_master_id'
        })

        return {
          status: 400,
          respcode: 1001,
          message: (sendAppDataApiResponse.message ? sendAppDataApiResponse.message : 'Something went Wrong SendAppData') + ' [' + fields.aggregator_order_id + '] '
        }
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendAppDataApi', type: 'catcherror', fields: err })

      const updateQueue = await this.updateWhere(connection, {
        data: {
          sendapp_response: JSON.stringify({ status: 500, error: 'catcherror', message: err.message }),
          policy_status: 'F' // to do need to asked
        },
        id: fields.ma_user_policy_detail_id,
        where: 'ma_user_policy_detail_id'
      })

      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request in progress, Please try later!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // connection.release()
    }
  }

  static async generateUIC (mobile_number) {
    var digits = '0123456789'
    var random = ''
    for (var i = 0; i < 5; i++) {
      random += digits[Math.floor(Math.random() * 10)]
    }
    return `${mobile_number}${random}`
  }

  static async verifyKotakOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyKotakOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      if (fields.countrycode == null || fields.countrycode == undefined) {
        fields.countrycode = 'IN'
      }
      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyKotakOtp', type: 'mobileresponse', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1011, message: errorMsg.responseCode[1011] + fields.mobile_number }
      }

      const policyDetailsSql = ` SELECT * 
              FROM ma_user_policy_details 
              WHERE ma_user_policy_detail_id = "${fields.ma_user_policy_detail_id}" AND order_id = "${fields.aggregator_order_id}" AND customer_mobile_number = "${fields.mobile_number}" limit 1`

      const policyDetailData = await this.rawQuery(policyDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyDetailData', fields: policyDetailData })
      if (policyDetailData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' policy request' }
      }

      if (policyDetailData[0].policy_status == 'VFY' || policyDetailData[0].policy_status == 'PI') {
        return { status: 400, respcode: 1021, message: errorMsg.responseCode[1021] + ' [' + policyDetailData[0].policy_status + ']' + ' for order ' + fields.aggregator_order_id }
      }

      if (policyDetailData[0].policy_status == 'F') {
        return { status: 400, respcode: 1025, message: errorMsg.responseCode[1025] + ' for order ' + fields.aggregator_order_id }
      }

      // {"mobile_no":"7277722291","referenceid":"1028","OTP":"164008"}
      const postParams = {
        mobile_no: fields.mobile_number,
        referenceid: policyDetailData[0].ms_reference_id,
        OTP: fields.otp
      }

      const verifyOrderAPIURL = configIns.insuranceURL
      const generatedChecksum = airpaychecksum.createMSCheckSum(postParams, configIns.SECRETKEY)

      console.time('TIMER_KOTAK_VERIFY_OTP_' + fields.aggregator_order_id)
      const response = await axios({
        method: 'post',
        url: verifyOrderAPIURL + 'api/ins/otp_check',
        data: postParams,
        timeout: 60000, // 60 secs timeout,
        headers: {
          AFFKEY: configIns.AFFKEY,
          AIRPAYKEY: configIns.AIRPAYKEY,
          CHECKSUM: generatedChecksum
        }
      })
      console.timeEnd('TIMER_KOTAK_VERIFY_OTP_' + fields.aggregator_order_id)

      console.log(`INS_VERIFYOTP_[${fields.aggregator_order_id}]_[REQUEST-${JSON.stringify(postParams)}]_[STATUS-${response.status}]_[RESPONSE-${JSON.stringify(response.data)}]`)

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyKotakOtp', type: 'catcherror', fields: 'Server Status' + response.status })
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' order creation' }
      }
      /* {
      "status": "200",
    "coi": "https://insurance.airpay.ninja/api/file/f0fb505196f4bf106fbfe26ce3fc5e66.pdf",
    "dogh": "https://insurance.airpay.ninja/api/file/297d60d79fa65d80a609e7a2adfdc0f3.pdf",
    "message": "success"
} */

      const orderResponse = response.data
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyKotakOtp', type: 'verifyKotakOtpResponse', fields: orderResponse })

      if (orderResponse.status == 200) {
        // {"status":"200","data":{"referenceid":967,"url":"bankingvbbb"},"message":"success"}

        const updateDetailsRes = await this.updateWhere(connection, {
          data: {
            policy_response: JSON.stringify(orderResponse),
            policy_status: 'VFY' // to do need to asked
          },
          id: fields.ma_user_policy_detail_id,
          where: 'ma_user_policy_detail_id'
        })

        const updateTranResponse = await transactionCtrl.updateWhereData(connection, {
          data: {
            transaction_status: 'P'
          },
          id: policyDetailData[0].ma_transaction_master_id,
          where: 'ma_transaction_master_id'
        })

        return {
          status: 200,
          message: orderResponse.message,
          request: postParams,
          response: orderResponse.data,
          referenceid: orderResponse.referenceid,
          coi: orderResponse.coi
        }

      } else {

        // wrong otp exceeded limit or payment failure mark transaction failure
        if (orderResponse.status == 301 || (orderResponse.status == 400 && (orderResponse.message == 'FAIL' || orderResponse.message == 'FAILURE'))) {

          const updateDetailsRes = await this.updateWhere(connection, {
            data: {
              policy_response: JSON.stringify(orderResponse),
              policy_status: 'F' // to do need to asked
            },
            id: fields.ma_user_policy_detail_id,
            where: 'ma_user_policy_detail_id'
          })

          const updateTranResponse = await transactionCtrl.updateWhereData(connection, {
            data: {
              transaction_status: 'F'
            },
            id: fields.aggregator_order_id,
            where: 'aggregator_order_id'
          })

          return {
            status: 400,
            respcode: 1001,
            message: orderResponse.message ? errorMsg.responseCode[1025] + '' + fields.aggregator_order_id + ' :: ' + orderResponse.message : errorMsg.responseCode[1001] + ' at Verify Otp'
          }
        }

        return {
          status: 400,
          respcode: 1001,
          message: orderResponse.message ? orderResponse.message : errorMsg.responseCode[1001] + ' at Verify Otp'
        }
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyKotakOtp', type: 'catcherror', fields: err })

      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async resentSuccessSMS (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentSuccessSMS', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      if (fields.countrycode == null || fields.countrycode == undefined) {
        fields.countrycode = 'IN'
      }
      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
      log.logger({ pagename: require('path').basename(__filename), action: 'resentSuccessSMS', type: 'mobileresponse', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1011, message: errorMsg.responseCode[1011] + fields.mobile_number }
      }

      const policyDetailsSql = ` SELECT * 
              FROM ma_user_policy_details 
              WHERE ma_user_policy_detail_id = "${fields.ma_user_policy_detail_id}" AND order_id = "${fields.aggregator_order_id}" limit 1`

      const policyDetailData = await this.rawQuery(policyDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyDetailData', fields: policyDetailData })
      if (policyDetailData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' policy request' }
      }

      // if (policyDetailData[0].policy_status != 'PI') {
      //   return { status: 400, respcode: 1021, message: errorMsg.responseCode[1021] + ' [' + policyDetailData[0].policy_status + ']' }
      // }

      if (policyDetailData[0].policy_status == 'F') {
        return { status: 400, respcode: 1025, message: errorMsg.responseCode[1025] }
      }

      // {"referenceid":"1048","mobile_no":"9664332419"}
      const postParams = {
        mobile_no: fields.mobile_number,
        referenceid: policyDetailData[0].ms_reference_id
      }

      const resentSuccessSMSAPIURL = configIns.insuranceURL
      const generatedChecksum = airpaychecksum.createMSCheckSum(postParams, configIns.SECRETKEY)

      console.time('TIMER_KOTAK_RESENT_SUCCESS_SMS_' + fields.aggregator_order_id)
      const response = await axios({
        method: 'post',
        url: resentSuccessSMSAPIURL + 'api/ins/otp_sendsms',
        data: postParams,
        timeout: 60000, // 60 secs timeout,
        headers: {
          AFFKEY: configIns.AFFKEY,
          AIRPAYKEY: configIns.AIRPAYKEY,
          CHECKSUM: generatedChecksum
        }
      })
      console.timeEnd('TIMER_KOTAK_RESENT_SUCCESS_SMS_' + fields.aggregator_order_id)

      console.log(`INS_RESENDSUCCESSOTP_[${fields.aggregator_order_id}]_[REQUEST-${JSON.stringify(postParams)}]_[STATUS-${response.status}]_[RESPONSE-${JSON.stringify(response.data)}]`)

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'resentSuccessSMS', type: 'catcherror', fields: 'Server Status' + response.status })
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' order creation' }
      }
      /* {"status":200,"message":"success"} */

      const orderResponse = response.data
      log.logger({ pagename: require('path').basename(__filename), action: 'resentSuccessSMS', type: 'resentSuccessSMSResponse', fields: orderResponse })

      if (orderResponse.status == 200) {
        return {
          status: 200,
          respcode: 1000,
          message: orderResponse.message
        }

      } else {
        return {
          status: 400,
          respcode: 1001,
          message: orderResponse.message ? orderResponse.message : errorMsg.responseCode[1001] + ' at Resent Policy SMS'
        }
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentSuccessSMS', type: 'catcherror', fields: err })

      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async resentKotakOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      if (fields.countrycode == null || fields.countrycode == undefined) {
        fields.countrycode = 'IN'
      }
      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
      log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'mobileresponse', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1011, message: errorMsg.responseCode[1011] + fields.mobile_number }
      }

      const policyDetailsSql = ` SELECT * 
              FROM ma_user_policy_details 
              WHERE ma_user_policy_detail_id = "${fields.ma_user_policy_detail_id}" AND order_id = "${fields.aggregator_order_id}" AND customer_mobile_number = "${fields.mobile_number}" limit 1`

      const policyDetailData = await this.rawQuery(policyDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyDetailData', fields: policyDetailData })
      if (policyDetailData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' policy request' }
      }

      if (policyDetailData[0].policy_status == 'F') {
        return { status: 400, respcode: 1025, message: errorMsg.responseCode[1025] + ' for order ' + fields.aggregator_order_id }
      }

      // {"referenceid":"1048","mobile_no":"9664332419"}
      const postParams = {
        mobile_no: fields.mobile_number,
        referenceid: policyDetailData[0].ms_reference_id
      }

      const resentKotakOtpAPIURL = configIns.insuranceURL
      const generatedChecksum = airpaychecksum.createMSCheckSum(postParams, configIns.SECRETKEY)

      console.time('TIMER_KOTAK_RESENT_OTP_' + fields.aggregator_order_id)
      const response = await axios({
        method: 'post',
        url: resentKotakOtpAPIURL + 'api/ins/resendotp_send',
        data: postParams,
        timeout: 60000, // 60 secs timeout,
        headers: {
          AFFKEY: configIns.AFFKEY,
          AIRPAYKEY: configIns.AIRPAYKEY,
          CHECKSUM: generatedChecksum
        }
      })
      console.timeEnd('TIMER_KOTAK_RESENT_OTP_' + fields.aggregator_order_id)

      console.log(`INS_RESENDKOTAKOTP_[${fields.aggregator_order_id}]_[REQUEST-${JSON.stringify(postParams)}]_[STATUS-${response.status}]_[RESPONSE-${JSON.stringify(response.data)}]`)

      if (response.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'catcherror', fields: 'Server Status' + response.status })
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' order creation' }
      }
      /* {
    "status": 200,
    "message": "OTP has been sent successfully"
} */

      const orderResponse = response.data
      log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'resentKotakOtpResponse', fields: orderResponse })

      if (orderResponse.status == 200) {
        return {
          status: 200,
          respcode: 1000,
          message: orderResponse.message
        }

      } else {

        // resent otp exceeded limit
        if (orderResponse.status == 301) {
          const updateDetailsRes = await this.updateWhere(connection, {
            data: {
              policy_response: JSON.stringify(orderResponse),
              policy_status: 'F' // to do need to asked
            },
            id: fields.ma_user_policy_detail_id,
            where: 'ma_user_policy_detail_id'
          })

          const updateTranResponse = await transactionCtrl.updateWhereData(connection, {
            data: {
              transaction_status: 'F'
            },
            id: fields.aggregator_order_id,
            where: 'aggregator_order_id'
          })
        }

        return {
          status: 400,
          respcode: 1001,
          message: orderResponse.message ? orderResponse.message : errorMsg.responseCode[1001] + ' at Resent Policy SMS'
        }
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'catcherror', fields: err })

      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getPolicyPlans (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {

      const policyDetailsSql = ` SELECT * 
              FROM ma_policy_master 
              WHERE status = "ACTIVE" order by policy_amount desc`

      const policyPlansData = await this.rawQuery(policyDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'doPolicyTransaction', type: 'policyPlansData', fields: policyPlansData })
      if (policyPlansData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' policy plans' }
      }

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        policyPlans: policyPlansData
      }

    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentKotakOtp', type: 'catcherror', fields: err })

      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = TermInsuranceController
