const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const axios = require('axios')
const { basename } = require('path')
const common = require('../../util/common')
const sms = require('../../util/sms')
const tz = require('moment-timezone')
const LombardInsuranceController = require('./lombardInsuranceController')

class lombardInsuranceCronController extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  get TABLE_NAME () {
    return 'ma_transaction_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_transaction_master_id'
  }

  static async iciciLombardActivePoliciesCron () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()

    try {
      const sql = `select mlcd.userid,
                    mlcd.ma_user_id,
                    mlcd.policy_number,
                    mlcd.amount,
                    mlcd.expiry,
                    mlcd.policy_status,
                    mlcd.orderid,
                    mlcd.policy_validity,
                    mlcd.policy_category_code,
                    mlcd.grace_period,
                    mlpd.order_id,
                    mlcd.addedon,
                    mlpd.transaction_status
                FROM
                    ma_lombard_customer_details as mlcd
                JOIN
                    ma_lombard_policy_details as mlpd
                ON
                    mlcd.orderid=mlpd.order_id
                WHERE
                    mlcd.policy_status IN ('I','P')
              AND mlpd.added_on < DATE_SUB(NOW(), INTERVAL 1 HOUR)
          ORDER BY
              mlcd.addedon DESC
          LIMIT 100`

      /* const sql = `select mlcd.userid,
          mlcd.ma_user_id,
          mlcd.policy_number,
          mlcd.amount,
          mlcd.expiry,
          mlcd.policy_status,
          mlcd.orderid,
          mlcd.policy_validity,
          mlcd.policy_category_code,
          mlcd.grace_period,
          mlpd.order_id,
          mlcd.addedon,
          mlpd.transaction_status
      FROM
          ma_lombard_customer_details as mlcd
      JOIN
          ma_lombard_policy_details as mlpd
      ON
          mlcd.orderid=mlpd.order_id
      WHERE
          mlcd.policy_status IN ('I','P')
      ORDER BY
          mlcd.addedon DESC
      LIMIT 100` */

      const pendingPolicies = await this.rawQuery(sql, connectionRead)

      if (pendingPolicies.length < 1) return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] }
      log.logger({ pagename: basename(__filename), action: 'iciciLombardActivePoliciesCron', type: 'request', fields: pendingPolicies })

      const orderIds = pendingPolicies.map(policy => policy.orderid)

      const updateRiskQueries = `UPDATE ma_lombard_customer_details SET policy_status = 'P' where orderid IN ("${orderIds.join('","')}")`
      const updateRisk = await this.rawQuery(updateRiskQueries, connection)
      if (updateRisk.affectedRows == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }

      /* BATCH PROCESSING */
      const AllbatchProcess = []
      let smallBatch = []
      const batchSize = 10 // Max Request

      for (let i = 0; i < pendingPolicies.length; i++) {
        smallBatch.push(this.markAsFailed({ transactionDetails: pendingPolicies[i], connection, connectionRead }))
        if (smallBatch.length == batchSize || i >= (pendingPolicies.length - 1)) {
          /* clear */
          AllbatchProcess.push(smallBatch)
          smallBatch = []
        }
      }

      /* PROCESS THE REQUEST */
      for (let index = 0; index < AllbatchProcess.length; index++) {
        const batchProcess = AllbatchProcess[index]
        const apiRequests = await Promise.allSettled(batchProcess)
        log.logger({ pagename: basename(__filename), action: 'iciciLombardNotificationCron', type: 'activePolicies', fields: apiRequests })
      }

      return { status: 200, respcode: 1000, message: 'Pending transaction marked as failed!!!' }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'iciciLombardActivePoliciesCron', type: 'err', fields: err })
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{transactionDetails:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async markAsFailed ({ transactionDetails, connection, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'markAsFailed', type: 'request', fields: transactionDetails })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const transactionDataQuery = `SELECT * from ma_transaction_master where aggregator_order_id = '${transactionDetails.order_id}' limit 1`
      const transactionDataResult = await this.rawQuery(transactionDataQuery, connRead)
      log.logger({ pagename: basename(__filename), action: 'markAsFailed', type: 'transactionDataResult', fields: transactionDataResult })
      if (transactionDataResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1001, action_code: 1001 }

      await mySQLWrapper.beginTransaction(conn)
      const resp = await LombardInsuranceController.updateFailedTransaction({
        fields: transactionDetails,
        transaction: transactionDataResult[0],
        connection: conn,
        connectionRead: connRead
      })

      log.logger({ pagename: basename(__filename), action: 'markAsFailed', type: 'updateFailedTransaction', fields: resp })

      if (resp.status != 200) {
        await mySQLWrapper.rollback(conn)
        return resp
      }
      await mySQLWrapper.commit(conn)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'markAsFailed', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   * Notify Pending policy
   * @returns
   */
  static async iciciLombardNotificationCron () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const sql = 'SELECT mlcd.*,mlpd.transaction_status FROM ma_lombard_customer_details mlcd LEFT JOIN ma_lombard_policy_details mlpd ON mlcd.orderid=mlpd.order_id  WHERE policy_status = "I" AND mlcd.source="DMT" AND addedon > DATE_SUB(NOW(), INTERVAL 1 HOUR)'

      const activePolicies = await this.rawQuery(sql, connectionRead)
      log.logger({ pagename: basename(__filename), action: 'iciciLombardNotificationCron', type: 'activePolicies', fields: activePolicies })

      if (activePolicies.length < 1) return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] }
      /* BATCH PROCESSING */
      const AllbatchProcess = []
      let smallBatch = []
      const batchSize = 20 // Max Request

      for (let i = 0; i < activePolicies.length; i++) {
        const transactionDetails = {
          txnId: activePolicies[i].orderid,
          ma_user_id: activePolicies[i].ma_user_id,
          userid: activePolicies[i].userid,
          amount: activePolicies[i].amount,
          addedon: tz.tz(activePolicies[i].addedon, 'UTC').format('LLLL'),
          transaction_type: 15,
          transaction_status: activePolicies[i].transaction_status,
          mobile_number: activePolicies[i].proposer_mobile
        }
        smallBatch.push(this.sendNotification({ transactionDetails, connectionRead }))
        if (smallBatch.length == batchSize || i >= (activePolicies.length - 1)) {
          /* clear */
          AllbatchProcess.push(smallBatch)
          smallBatch = []
        }
      }

      /* PROCESS THE REQUEST */
      for (let index = 0; index < AllbatchProcess.length; index++) {
        const batchProcess = AllbatchProcess[index]
        const apiRequests = await Promise.allSettled(batchProcess)
        log.logger({ pagename: basename(__filename), action: 'iciciLombardNotificationCron', type: 'activePolicies', fields: apiRequests })
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'iciciLombardNotificationCron', type: 'err', fields: err })
    } finally {
      connection.release()
    }
  }

  /**
   *
   * @param {{transactionDetails:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async sendNotification ({ transactionDetails, connectionRead }) {
    log.logger({ pagename: basename(__filename), action: 'sendNotification', type: 'request', fields: transactionDetails })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const appNotificationTemplate = `Hi, icici lombard policy purchase of Rs. ${transactionDetails.amount} done at ${transactionDetails.addedon} against order # ${transactionDetails.txnId} is Pending. Please click to fill up the policy details and complete the transaction.`

      const gcmKey = await common.getGcmKey(transactionDetails.userid, this, connRead)

      if (gcmKey == '') return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      const message = {
        notification_id: parseInt(transactionDetails.txnId.replace(/^\D+/g, '')),
        body: appNotificationTemplate,
        body_web: appNotificationTemplate,
        title: 'ICICI LOMBARD TRANSACTION',
        icon: 'ic_launcher',
        sound: 'default',
        priority: 'high',
        badge: '1',
        image: '',
        action: '1',
        aggregator_order_id: transactionDetails.txnId,
        'action-url': '',
        deep_link: `airpay://app/riskcovry_form_route?order_id=${transactionDetails.txnId}&mobile=${transactionDetails.mobile_number}`,
        module: 'Riskcovry',
        action_code: 1000,
        ma_user_id: transactionDetails.ma_user_id,
        transaction_type: transactionDetails.transaction_type,
        transaction_status: transactionDetails.transaction_status,
        mobile: transactionDetails.mobile_number
      }
      const headers = { Authorization: 'key=' + util.NOTIFICATION_API_ACCESS_KEY, 'Content-Type': 'application/json' }
      const postData = {
        registration_ids: [gcmKey],
        data: message
      }
      console.log('---SEND NOTIFICATION DATA---', postData)
      const notificationResp = await axios({
        method: 'post',
        url: 'https://fcm.googleapis.com/fcm/send',
        data: postData,
        headers: headers
      })
      console.log('---SEND NOTIFICATION RESPONSE---')
      log.logger({ pagename: basename(__filename), action: 'sendNotification', type: 'notify-res', fields: notificationResp.data })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'sendNotification', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async iciciLombardSMSNotificationCron(){
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try{
      const sql = 'SELECT mlcd.*,mlpd.transaction_status FROM ma_lombard_customer_details mlcd LEFT JOIN ma_lombard_policy_details mlpd ON mlcd.orderid=mlpd.order_id  WHERE policy_status = "PS" AND mlcd.source="DMT" AND addedon > DATE_SUB(NOW(), INTERVAL 1 HOUR)'

      const activePolicies = await this.rawQuery(sql, connectionRead)
      log.logger({ pagename: basename(__filename), action: 'iciciLombardSMSNotificationCron', type: 'activePoliciesForSMS', fields: activePolicies })

      if (activePolicies.length < 1) return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] }
      /* BATCH PROCESSING */
      const AllbatchProcess = []
      let smallBatch = []
      const batchSize = 20 // Max Request

      for (let i = 0; i < activePolicies.length; i++) {
        const mobileNumber = activePolicies[i].proposer_mobile;
        const orderId = activePolicies[i].orderid;

        // Check if notification already sent
        const checkSql = `SELECT * FROM ma_lombard_sent_notifications WHERE order_id = '${orderId}'`
        const checkResult = await this.rawQuery(checkSql, connectionRead)
        log.logger({ pagename: basename(__filename), action: 'iciciLombardSMSNotificationCron', type: 'checkResult', fields: checkResult })
        if(checkResult.length == 0){
          const transactionDetails = {
            txnId: activePolicies[i].orderid,
            ma_user_id: activePolicies[i].ma_user_id,
            userid: activePolicies[i].userid,
            amount: activePolicies[i].amount,
            addedon: tz.tz(activePolicies[i].addedon, 'UTC').format('LLLL'),
            transaction_type: 15,
            transaction_status: activePolicies[i].transaction_status,
            mobile_number: activePolicies[i].proposer_mobile
          }
          smallBatch.push(this.sendSMSNotification({ transactionDetails, connection }))

          // Record the notification sent
          const insertSql = `INSERT INTO  ma_lombard_sent_notifications (mobile_number, addedon , order_id, sms_flag) VALUES ('${mobileNumber}',now(),'${orderId}','Y')`;
          const insertSqlResult = await this.rawQuery(insertSql,connection)
          log.logger({ pagename: basename(__filename), action: 'iciciLombardSMSNotificationCron', type: 'insertSqlResult', fields: insertSqlResult })                                                                                                                                                
        }
        if (smallBatch.length == batchSize || i >= (activePolicies.length - 1)) {
          /* clear */
          AllbatchProcess.push(smallBatch)
          smallBatch = []
        }
      }

      /* PROCESS THE REQUEST */
      for (let index = 0; index < AllbatchProcess.length; index++) {
        const batchProcess = AllbatchProcess[index]
        const apiRequests = await Promise.allSettled(batchProcess)
        log.logger({ pagename: basename(__filename), action: 'iciciLombardSMSNotificationCron', type: 'activePoliciesForSMS', fields: apiRequests })
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }

    }catch (err) {
      log.logger({ pagename: basename(__filename), action: 'iciciLombardSMSNotificationCron', type: 'err', fields: err })
    } finally {
      connection.release()
    }

  }
  static async sendSMSNotification ({ transactionDetails, connection }) {
    log.logger({ pagename: basename(__filename), action: 'sendSMSNotification', type: 'request', fields: transactionDetails })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const message = util.communication.LOMBARDCUSTOMERSMS
      const template = util.templateid.LOMBARDCUSTOMERSMS
      log.logger({ pagename: basename(__filename), action: 'sendSMSNotification', type: 'request', fields: { message, template } })
      await sms.sentSmsAsync(message,transactionDetails.mobile_number, template, '', conn)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'sendNotification', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }
}
module.exports = lombardInsuranceCronController
