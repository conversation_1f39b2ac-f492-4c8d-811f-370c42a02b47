const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const transactionMaster = require('../transaction/transactionController')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const airpaychecksum = require('../../util/airpaychecksum')
const axios = require('axios')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const balanceController = require('../balance/balanceController')
const qs = require('querystring')
const configIns = require('./config')
const { basename } = require('path')
const validator = require('../../util/validator')
const securePinCtrl = require('../securityPin/securityPinController')
const common = require('../../util/common')

const sms = require('../../util/sms')
const moment = require('moment')
const tz = require('moment-timezone')

class RiskCovrySachetController extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  get TABLE_NAME () {
    return 'ma_transaction_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_transaction_master_id'
  }

  static async checkActivePolicy (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'checkActivePolicy', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      const checkBox = await this.isCheckBoxVisible(_, fields)
      if (checkBox.status != 200) return checkBox
      const userChannelData = await this.checkUserChannel(fields.ma_user_id, conn)
      log.logger({ pagename: basename(__filename), action: 'checkActivePolicy', type: 'userChannelData', fields: userChannelData })
      if (userChannelData == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
      }

      // Get active riskcovry sachet policy
      const activePolicyQuery = 'select category_name, category_code, category_type, category_flag, amount, expiry_period, app_icon, web_icon, policy_wordings, policy_name from ma_insurance_category_master micm where riskcovry_sachet = "true" and status = "Active"'
      const activePolicyRes = await this.rawQuery(activePolicyQuery, conn)

      if (activePolicyRes.length < 1) return { status: 400, respcode: 1001, message: `${errorMsg.responseCode[1028]}: No policies active.` }

      const activePolicy = []
      for (var i = 0; i < activePolicyRes.length; i++) {
        activePolicy.push({
          category_name: activePolicyRes[i].category_name,
          policy_name: activePolicyRes[i].policy_name,
          category_code: activePolicyRes[i].category_code,
          category_type: activePolicyRes[i].category_type,
          category_flag: activePolicyRes[i].category_flag,
          amount: activePolicyRes[i].amount,
          expiry_period: activePolicyRes[i].expiry_period,
          description: `Buy ${activePolicyRes[i].policy_name} for a validity of ${activePolicyRes[i].expiry_period} days at a premium of Rs.${activePolicyRes[i].amount}`,
          validity: `${activePolicyRes[i].expiry_period} days`,
          app_icon: activePolicyRes[i].app_icon,
          web_icon: activePolicyRes[i].web_icon,
          tncUrl: util.riskCovryTNC,
          policy_wordings: activePolicyRes[i].policy_wordings
        })
      }
      log.logger({ pagename: basename(__filename), action: 'checkActivePolicy', type: 'response', fields: activePolicy })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], activePolicy }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'checkActivePolicy', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   * Created method for sachet controller
   * riskcovrySachetInitiateTransaction description - create transaction for riskcovry sachet
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, companyId: number, retailId: number, orderid: string, pmode: number, provider_name: string, cat: string, amount: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, orderid: string, companyid: number, pmode: number, url: string }>}
   */
  static async riskcovrySachetInitiateTransaction (_, args) {
    log.logger({ pagename: basename(__filename), action: 'riskcovrySachetInitiateTransaction', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const { ma_user_id, userid, orderid, amount, source, security_pin } = args
      const companyid = configIns.riskcovryKeys.companyid
      const affiliateid = configIns.riskcovryKeys.affiliateid

      // Check if transaction already Initiated
      const transactionDetailsSQL = `SELECT aggregator_order_id, transaction_status from ma_transaction_master where aggregator_order_id = '${orderid}' limit 1`
      const transactionDetailsRes = await this.rawQuery(transactionDetailsSQL, connection)
      log.logger({ pagename: basename(__filename), action: 'riskcovrySachetInitiateTransaction', type: 'transactionDetails-RES', fields: transactionDetailsRes })
      if (transactionDetailsRes.length > 1) {
        if (transactionDetailsRes[0].transaction_status == 'P') return { status: 400, respcode: 1001, message: errorMsg.responseCode[1133] }
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1118] }
      }

      const userChannelData = await this.checkUserChannel(args.ma_user_id, connection)

      if (userChannelData == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
      }

      const checkInsuranceCategoryData = await this.checkInsuranceCategory(args.category_code, connection)

      if (checkInsuranceCategoryData == 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Category is already disbaled' }
      }

      const subCategoryCode = await this.getInsuranceSubCategoryCode(args.category_code, connection)
      // const provider_name = checkInsuranceCategoryData.category_name

      // Pin verify for flow from insurance tab
      if (source == 'INSURANCE') {
        const securePin = await securePinCtrl.verifySecurePin(null, {
          ma_user_id,
          userid,
          security_pin,
          connection
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'riskcovrySachetInitiateTransaction', type: 'securePin', fields: securePin })
        if (securePin.status != 200) return securePin
      }

      // Check available balance
      const availableBalance = await balanceController.getWalletBalancesDirect(_, {
        ma_user_id: ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection: connection
      })

      if (availableBalance.status != 200) return availableBalance
      log.logger({ pagename: basename(__filename), action: 'availableBalance', type: 'balanceCheck', fields: availableBalance })
      if (availableBalance.amount < args.amount) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1005] }

      const sqlUser = `select mobile_id,ma_user_master_id,sales_id,state,user_type,userid from ma_user_master where profileid='${args.ma_user_id}' AND mer_user = 'mer' limit 1`
      let mobile = 0
      const _user = await this.rawQuery(sqlUser, connection)
      log.logger({ pagename: basename(__filename), action: 'sqlMerchantResponse', type: '_userSQLResult', fields: _user })
      var sales_id = 0
      if (_user.length > 0) {
        mobile = _user[0].mobile_id
        sales_id = _user[0].sales_id
        args.userid = args.userid ? args.userid : _user[0].userid
      } else {
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
      }

      const transactionData = await transactionMaster.initiateTransaction(_, {
        connection: connection,
        ma_user_id,
        userid: args.userid,
        aggregator_order_id: orderid,
        transaction_id: orderid,
        amount: args.amount,
        commission_amount: 0,
        transaction_type: 15, // insurance
        remarks: 'Riskcovry Sachet Insurance Transaction',
        mobile_number: mobile,
        provider_id: '0', // to do
        provider_name: checkInsuranceCategoryData.category_name,
        utility_id: '0', // to do
        utility_name: 'insurance', // to do
        action_type: 'instapay',
        transaction_status: 'I',
        affiliated_id: affiliateid,
        company_id: companyid,
        subcategory_code: subCategoryCode.category_code,
        bank_name: 'Riskcovry',
        customer_mobile: args.proposer_mobile ? args.proposer_mobile : '0',
        category_master_id: checkInsuranceCategoryData.ma_insurance_category_master_id
      })

      log.logger({ pagename: basename(__filename), action: 'createTransaction', type: 'transactionData', fields: transactionData })

      if (transactionData.status == 200) {
        await mySQLWrapper.beginTransaction(connection)

        const transactionReason = `Balance Deducted ${args.amount}`
        // const updateTransaction = `UPDATE ma_transaction_master set transaction_status = 'P', amount = '${args.amount}', transaction_reason = '${transactionReason}' where aggregator_order_id = '${orderid}'`
        // await this.rawQuery(updateTransaction, connection)
        /* RISK MANAGEMENT Changes */
        const data = {
          transaction_reason: transactionReason,
          transaction_status: 'P'
        }

        const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
          data,
          id: orderid,
          where: 'aggregator_order_id'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

        var pointsDetailsEntries = {}
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
          ma_user_id,
          amount: amount,
          transactionType: '1',
          connection: connection
        })
        if (pointsDetailsEntries.status === 400) {
          return pointsDetailsEntries
        }

        // this.TABLE_NAME = 'ma_points_ledger_master'
        // point leger entry
        const pointLedgerId = await pointsLedger.createEntry(_, {
          ma_user_id,
          amount: amount,
          mode: 'dr',
          transaction_type: 15,
          description: util.insurancePointsDebitDescription,
          ma_status: 'S',
          orderid,
          userid,
          corresponding_id: util.airpayUserId,
          connection
        })

        if (pointLedgerId.status === 400) {
          await mySQLWrapper.rollback(connection)
          return pointLedgerId
        }

        // pointsDetails entry
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry(_, {
            ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: pointLedgerId.id,
            orderid,
            ma_status: 'S',
            connection: connection
          })
          if (entry.status === 400) {
            await mySQLWrapper.rollback(connection)
            return entry
          }
        }

        // airpay id credit entry

        const retailerLedgerId = await pointsLedger.createEntry(_, {
          ma_user_id: util.airpayUserId,
          amount: amount,
          mode: 'cr',
          transaction_type: 15,
          ma_status: 'S',
          userid,
          description: util.insurancePointsCreditsDescription,
          orderid,
          corresponding_id: ma_user_id,
          connection
        })
        if (retailerLedgerId.status === 400) {
          await mySQLWrapper.rollback(connection)
          return retailerLedgerId
        }

        // Riskcovry Customer details table entry
        const addPolicyDetailsQuery = `INSERT INTO ma_riskcovry_customer_details(ma_user_id, userid, orderid, source, amount, policy_name, policy_category_code, transaction_status, proposer_mobile) VALUES 
        (${ma_user_id}, ${userid}, '${orderid}', '${source}', ${amount}, '${checkInsuranceCategoryData.category_name}', '${args.category_code}', 'P', '${args.proposer_mobile}')`
        const addPolicyDetailsRes = await this.rawQuery(addPolicyDetailsQuery, connection)

        if (addPolicyDetailsRes.affectedRows == 0) await mySQLWrapper.rollback(connection)

        await mySQLWrapper.commit(connection)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], orderid }
      } else {
        // return { status: 400, respcode: 1028, message: `${errorMsg.responseCode[1028]} : trasaction can't be created` }
        /* NEW CHANGES : LEIN BALANCE CHECK */
        return transactionData
      }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'addInsUser', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (connection) connection.release()
    }
  }

  static async checkUserChannel (ma_user_id, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // get state & user type
      // get state details
      const sqlA = `SELECT state, user_type FROM ma_user_master WHERE profileid = ${ma_user_id} limit 1`
      const userADetails = await this.rawQuery(sqlA, connection)
      let state_id = 0
      let user_type = ''
      if (userADetails.length > 0) {
        state_id = userADetails[0].state
        user_type = userADetails[0].user_type
      }

      console.log('This is the user details: ', userADetails)
      // get channel list with respect to user id
      const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = 0 and ma_user_id = ${ma_user_id} and record_status = 'Y' and user_type = '${user_type}' `
      const channelDetails = await this.rawQuery(sqlCh, connection)
      var channel_arr = []
      var checkInsurance = ''
      console.log('Channnel data 1: ', channelDetails)
      if (channelDetails.length > 0) {
        for (var j = 0; j < channelDetails.length; j++) {
          channel_arr[j] = channelDetails[j].channel_name
        }
        console.log('check channel arr ', channel_arr)
        // console.log('check channel arr new ', check_channel_arr)
        if (channel_arr.includes('RISCOVERYSACHET')) {
          checkInsurance = 'yes'
        }

        console.log('inside user insurance channel condition >>>>>>>>>>', checkInsurance)
        return checkInsurance
      } else {
        // get channel list with respect to state
        const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = ${state_id} and ma_user_id = 0 and record_status = 'Y' and user_type = '${user_type}' `
        const channelStateDetails = await this.rawQuery(sqlCh, connection)
        // var final_channel_arr = {}
        console.log('Channnel data 2: ', channelStateDetails)
        if (channelStateDetails.length > 0) {
          for (var k = 0; k < channelStateDetails.length; k++) {
            channel_arr[k] = channelStateDetails[k].channel_name
          }

          if (channel_arr.includes('RISCOVERYSACHET')) {
            checkInsurance = 'yes'
          }
          console.log('inside state channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
          return checkInsurance
        } else {
          const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=2 and state_master_id =0 and record_status = 'Y' and ma_user_id = 0 and user_type = '${user_type}' `
          const channelGlobalDetails = await this.rawQuery(sqlCh, connection)
          // var final_channel_arr = {}
          if (channelGlobalDetails.length > 0) {
            for (var n = 0; n < channelGlobalDetails.length; n++) {
              channel_arr[n] = channelGlobalDetails[n].channel_name
            }
            console.log('channel_list ', channel_arr)
            if (channel_arr.includes('RISCOVERYSACHET')) {
              checkInsurance = 'yes'
            }
            console.log('inside Global channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
            return checkInsurance
          } else {
            checkInsurance = ''
            return checkInsurance
          }
        }
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'checkUserChannel', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async checkInsuranceCategory (category_code, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    // var channelDetails = {}
    try {
      // get channel list with respect to user id
      const sqlCh = `SELECT category_flag, category_name, ma_insurance_category_master_id FROM ma_insurance_category_master WHERE category_code= '${category_code}' and riskcovry_sachet = 'true' limit 1`
      const categoryDetails = await this.rawQuery(sqlCh, connection)
      let category_flag = 0

      if (categoryDetails.length > 0) {
        category_flag = categoryDetails[0].category_flag
        const category_name = categoryDetails[0].category_name
        const ma_insurance_category_master_id = categoryDetails[0].ma_insurance_category_master_id
        console.log('inside check insurance category condition >>>>>>>>>>', category_flag)
        return { category_code, category_name, ma_insurance_category_master_id }
      }
      return category_flag
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'checkInsuranceCategory', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * @param {String} provider_name
   * @param {Connection Object} con
   * @returns {String}
   */
  static async getInsuranceSubCategoryCode (category_code, con) {
    log.logger({ pagename: basename(__filename), action: 'getInsuranceSubCategoryCode', type: 'request', fields: { provider_name: category_code } })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // get subcategory code
      const sqlCh = `select subcategory_code from ma_insurance_subcategory_master mism join ma_insurance_category_master micm
          where mism.ma_insurance_category_master_id = micm.ma_insurance_category_master_id
          and micm.category_code = '${category_code}' and micm.riskcovry_sachet = 'true' limit 1`

      const subcategoryDetails = await this.rawQuery(sqlCh, connection)
      let subcategory_code = null

      if (subcategoryDetails.length > 0) {
        subcategory_code = subcategoryDetails[0].subcategory_code
        log.logger({ pagename: basename(__filename), action: 'getInsuranceSubCategoryCode', type: 'response', fields: subcategoryDetails })
      }
      return { status: 400, message: errorMsg.responseCode[1000], respcode: 1000, category_code: subcategory_code }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getInsuranceSubCategoryCode', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, category_code: null }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getFormData (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'getFormData', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection
    // Validate all required fields

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid', 'form_data'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const formData = JSON.parse(Buffer.from(fields.form_data, 'base64').toString('ascii'))
      const start_date = tz.tz(moment(), 'Asia/Kolkata').format('DD-MM-YYYY')
      const additional_info = {}
      const customer_ref = fields.orderid

      // formData Validation
      // const formDataValidator = validator.validateFields(formData, ['proposer', 'insured', 'nominees'])
      // if (formDataValidator.status != 200) return formDataValidator

      // const dataInsert = `INSERT INTO ma_riskcovry_customer_details(ma_user_id, userid, orderid, proposer_title, proposer_first_name, proposer_last_name, proposer_dob, proposer_mobile, proposer_gender, proposer_email, insured_title, insured_first_name, insured_last_name, insured_dob, insured_mobile, insured_gender, insured_email, insured_address_line_one, insured_address_line_two, insured_city, insured_state, insured_pincode,nominee_firstname, nominee_lastname, nominee_dob, nominee_gender, nominee_relation)
      // VALUES (${ma_user_id}, ${userid}, '${orderid}', '${proposer[0].title}', '${proposer[0].first_name}', '${proposer[0].last_name}', ${proposer[0].dob}, '${proposer[0].phone_number}', '${proposer[0].gender}', '${proposer[0].email}', '${insured[0].title}', '${insured[0].first_name}', '${insured[0].last_name}', ${insured[0].dob}, '${insured[0].phone_number}', '${insured[0].gender}', '${insured[0].email}', '${insured[0].address.address_line_1}', '${insured[0].address.address_line_2}', '${insured[0].address.city}', '${insured[0].address.state}', ${insured[0].address.zipcode}, '${nominees[0].first_name}', '${nominees[0].last_name}', ${nominees[0].dob}, '${nominees[0].gender}', '${nominees[0].relation}')`
      let proposer
      const validateProposer = validator.validateFields(formData, ['proposer_title', 'proposer_first_name', 'proposer_last_name', 'proposer_email', 'proposer_mobile', 'proposer_dob', 'proposer_gender', 'proposer_aadhar'])
      if (validateProposer.status == 200) {
        proposer = {
          title: formData.proposer_title,
          first_name: formData.proposer_first_name,
          last_name: formData.proposer_last_name,
          email: formData.proposer_email,
          phone_number: formData.proposer_mobile,
          dob: formData.proposer_dob,
          gender: formData.proposer_gender,
          aadhar: formData.proposer_aadhar
        }
      } else {
        proposer = {
          title: formData.insured_title,
          first_name: formData.insured_first_name,
          last_name: formData.insured_last_name,
          email: formData.insured_email,
          phone_number: formData.insured_mobile,
          dob: formData.insured_dob,
          gender: formData.insured_gender,
          aadhar: formData.insured_aadhar
        }
        formData.relation_with_proposer = 'self'
      }

      const insured = []
      const nominees = []

      insured.push({
        title: formData.insured_title,
        first_name: formData.insured_first_name,
        last_name: formData.insured_last_name,
        email: formData.insured_email,
        phone_number: formData.insured_mobile,
        dob: formData.insured_dob,
        gender: formData.insured_gender,
        aadhar: formData.insured_aadhar,
        address: {
          address_line_1: formData.insured_address_line_one.trim(),
          address_line_2: formData.insured_address_line_two.trim(),
          city: formData.insured_city.trim(),
          state: formData.insured_state,
          zipcode: formData.insured_pincode
        },
        relation_with_proposer: formData.relation_with_proposer
      })

      const validateNominee = validator.validateFields(formData, ['nominee_title', 'nominee_firstname', 'nominee_lastname', 'nominee_gender', 'nominee_relation', 'nominee_dob'])
      if (validateNominee.status == 200) {
        nominees.push({
          title: formData.nominee_title,
          first_name: formData.nominee_firstname,
          last_name: formData.nominee_lastname,
          email: formData.nominee_email ? formData.nominee_email : '',
          phone_number: formData.nominee_mobile ? formData.nominee_mobile : '',
          dob: formData.nominee_dob,
          gender: formData.nominee_gender,
          relation: formData.nominee_relation
        })
      }

      const customerData = {
        proposer,
        insured,
        nominees,
        start_date,
        customer_ref,
        additional_info
      }

      fields.proposer = proposer
      fields.insured = insured
      fields.nominees = nominees

      const updateFormData = await this.updateFormData(_, fields)
      if (updateFormData.status != 200) return updateFormData
      log.logger({ pagename: basename(__filename), action: 'getFormData', type: 'response', fields: customerData })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, customerData }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getFormData', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async riskcovryCreateOrder (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    // Validate all required fields
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid', 'form_data', 'category_name', 'category_code', 'expiry_period', 'source', 'amount'])
    log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'validator-response', fields: validatorResponse })
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const companyid = configIns.riskcovryKeys.companyid
      const affiliateid = configIns.riskcovryKeys.affiliateid

      const merchantDetailsSQL = `SELECT CONCAT(IFNULL(firstname, ''), IFNULL(lastname, '')) AS name, 
      IFNULL(mobile_id, '') AS mobile_id, IFNULL(email_id, '') AS email_id FROM ma_user_master mum 
      WHERE mum.profileid = '${fields.ma_user_id}' AND mum.userid = '${fields.userid}' LIMIT 1 `

      const merchantDetailsRes = await this.rawQuery(merchantDetailsSQL, conn)
      log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'merchantDetails-RES', fields: merchantDetailsRes })
      if (merchantDetailsRes.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1003] }

      const transactionDetailsSQL = `SELECT aggregator_order_id, transaction_status, ma_transaction_master_id from ma_transaction_master where aggregator_order_id = '${fields.orderid}' limit 1`
      const transactionDetailsRes = await this.rawQuery(transactionDetailsSQL, conn)
      log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'transactionDetails-RES', fields: transactionDetailsRes })
      if (transactionDetailsRes.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1012] }
      if (transactionDetailsRes[0].transaction_status != 'P') return { status: 400, respcode: 1001, message: errorMsg.responseCode[1020] }

      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      const secretkey = util[env].secretkey

      const formData = await this.getFormData(_, fields)
      if (formData.status != 200) return formData

      const info = JSON.stringify(formData.customerData)

      const updateTxnDetailsSql = `UPDATE ma_transaction_master_details SET customer_name = CONCAT(IFNULL('${formData.customerData.proposer.first_name}', ''), ' ', IFNULL('${formData.customerData.proposer.last_name}', '')), customer_mobile = '${formData.customerData.proposer.phone_number}' where ma_transaction_master_id = ${transactionDetailsRes[0].ma_transaction_master_id}`
      const updateTxnDetailsRes = await this.rawQuery(updateTxnDetailsSql, conn)

      // const cust_data = Buffer.from(`{"email": "${merchantDetailsRes[0].email_id}", "mobile_no": "${merchantDetailsRes[0].mobile_id}", "name": "${merchantDetailsRes[0].name}", "partner_user_id" : "${fields.ma_user_id}"}`).toString('base64')
      let postdata = `orderid=${fields.orderid}&affiliateid=${affiliateid}&providerid=${companyid}&plan_code=${fields.category_name}&product_code=${fields.category_code}&dob=${formData.customerData.insured[0].dob}&info=${info}`

      if (fields.cat) {
        postdata += `&cat=${fields.cat}`
      }
      const generatedChecksum = airpaychecksum.airpaychecksum(fields.orderid + '|' + affiliateid + '|' + companyid + '|' + fields.category_name + '|' + fields.category_code + '|' + formData.customerData.insured[0].dob + '|' + info + ',' + secretkey)
      const config = {
        method: 'POST',
        url: util[env].createorderins,
        headers: {
          Accept: 'text/plain',
          'Content-Type': 'application/x-www-form-urlencoded',
          AFFKEY: configIns.riskcovryKeys.AFFKEY,
          AIRPAYKEY: configIns.AIRPAYKEY,
          CHECKSUM: generatedChecksum
        },
        data: postdata
        /* data: {
              orderid: orderid,
              affiliateid: affiliateid,
              providerid : companyid,
              cat: cat
                } */
      }
      log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'createOrder-request', fields: config })
      const res = await axios(config)
      log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'createOrder-response', fields: res.data })
      if (res.data.status == 1) {
        const refid = res.data.refno
        const coi = res.data.coi
        const bank_rrn = res.data.payment_reference
        const expiryDate = tz.tz(moment().add(`${fields.expiry_period}`, 'days'), 'Asia/Kolkata').format('YYYY-MM-DD')
        const policySql = `SELECT policy_name from ma_insurance_category_master where category_code = '${fields.category_code}' and riskcovry_sachet = 'true' and status = 'Active' limit 1`
        const policyName = await this.rawQuery(policySql, conn)
        const policyData = {
          policy_no: refid,
          policy_name: policyName[0].policy_name,
          amount: fields.amount,
          plan_code: fields.category_name,
          product_code: fields.category_code
        }

        // const updateTransaction = `UPDATE ma_transaction_master set transaction_status = 'S' where aggregator_order_id = '${fields.orderid}' `
        // const updateTransactionRes = await this.rawQuery(updateTransaction, conn)
        // log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'update-txn', fields: updateTransactionRes })
        /* RISK MANAGEMENT Changes */
        const data = {
          transaction_status: 'S'
        }

        const updateTransactionResult = await transactionMaster.updateWhereData(conn, {
          data,
          id: fields.orderid,
          where: 'aggregator_order_id'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

        const updateRecord = `UPDATE ma_riskcovry_customer_details set policy_number = '${refid}', expiry = STR_TO_DATE('${expiryDate}', '%Y-%m-%d'), coi = '${coi}', transaction_status = 'S' where orderid = '${fields.orderid}'`
        const updateRecordRes = await this.rawQuery(updateRecord, conn)
        log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'update-record', fields: updateRecordRes })

        const updateDetails = `UPDATE ma_transaction_master_details set form_data = '${JSON.stringify(policyData)}', bank_rrn = '${bank_rrn}' where ma_transaction_master_id = ${transactionDetailsRes[0].ma_transaction_master_id}`
        const updateDetailsRes = await this.rawQuery(updateDetails, conn)
        log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'update-details', fields: updateDetailsRes })

        log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'response', fields: refid })
        let riskCovrySMS = configIns.communication.MerchantInsuranceSuccess
        const merchant_templateid = configIns.templateid.MerchantInsuranceSuccess
        const dt = tz.tz(moment(), 'Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss')
        riskCovrySMS = riskCovrySMS.replace('<policy name>', policyData.policy_name)
        riskCovrySMS = riskCovrySMS.replace('<amount>', fields.amount)
        riskCovrySMS = riskCovrySMS.replace('<date time>', dt)

        const smsRes = await sms.sentSmsAsync(riskCovrySMS, formData.customerData.proposer.phone_number, merchant_templateid)
        log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'sms-response', fields: smsRes })
        return { status: 200, respcode: 1000, message: `${errorMsg.responseCode[130005]} ${refid}`, refid, coi }
      } else {
        // Update transaction status to 'F' and also reverse the ledger entries
        await mySQLWrapper.beginTransaction(conn)

        /*
        const reverseEntry = await pointsLedger.createEntry(_, {
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          mode: 'cr',
          transaction_type: 15,
          ma_status: 'REV',
          orderid: 'REV-' + fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayId,
          description: 'Riskcovry Sachet Transaction Reversed',
          connection: conn
        })
        if (reverseEntry.status != 200) {
          await mySQLWrapper.rollback(conn)
          return reverseEntry
        }

        const retailLedgerEntry = await pointsLedger.createEntry(_, {
          ma_user_id: util.airpayId,
          amount: parseFloat(fields.amount),
          mode: 'dr',
          transaction_type: 15,
          ma_status: 'REV',
          userid: fields.userid,
          description: 'Debit Riskcovry Transaction - Reversed',
          orderid: 'REV-' + fields.orderid,
          corresponding_id: fields.ma_user_id,
          connection: conn
        })

        if (retailLedgerEntry.status != 200) {
          await mySQLWrapper.rollback(conn)
          return retailLedgerEntry
        }
         */
        const reverseEntries = await this.reverseEntries(_, fields)
        if (reverseEntries.status != 200) await mySQLWrapper.rollback(conn)

        // const updateQuery = `update ma_transaction_master set transaction_status = 'F' where aggregator_order_id = '${fields.orderid}'`
        // const updateRes = await this.rawQuery(updateQuery, conn)
        /* RISK MANAGEMENT Changes */
        const data = {
          transaction_status: 'F'
        }

        const updateTransactionResult = await transactionMaster.updateWhereData(conn, {
          data,
          id: fields.orderid,
          where: 'aggregator_order_id'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

        const updateRiskTxnQuery = `UPDATE ma_riskcovry_customer_details set transaction_status = 'F' where orderid = '${fields.orderid}'`
        const updateRiskTxn = await this.rawQuery(updateRiskTxnQuery, conn)
        // if (updateRes.affectedRows == 0) await mySQLWrapper.rollback(conn)

        await mySQLWrapper.commit(conn)
        return { status: 400, respcode: 1001, message: res.data.message || errorMsg.responseCode[1001] }
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'riskcovryCreateOrder', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async updateFormData (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'updateFormData', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'orderid', 'proposer', 'insured', 'nominees'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const { ma_user_id, userid, orderid, proposer, insured, nominees, category_name, amount, source } = fields
      /*
      const insertQuery = `INSERT INTO ma_riskcovry_customer_details(ma_user_id, userid, orderid, proposer_title, proposer_first_name, proposer_last_name, proposer_dob, proposer_mobile, proposer_gender, proposer_email, proposer_aadhar, insured_title, insured_first_name, insured_last_name, insured_dob, insured_mobile, insured_gender, insured_email, insured_aadhar, insured_address_line_one, insured_address_line_two, insured_city, insured_state, insured_pincode,nominee_title ,nominee_firstname, nominee_lastname, nominee_dob, nominee_gender, nominee_mobile, nominee_email, nominee_relation, policy_name, amount, source) VALUES
      (${ma_user_id}, ${userid}, '${orderid}', '${proposer.title}', '${proposer.first_name}', '${proposer.last_name}', ${proposer.dob}, '${proposer.phone_number}', '${proposer.gender}', '${proposer.email}','${proposer.aadhar}', '${insured[0].title}', '${insured[0].first_name}', '${insured[0].last_name}', ${insured[0].dob}, '${insured[0].phone_number}', '${insured[0].gender}', '${insured[0].email}','${insured[0].aadhar}', '${insured[0].address.address_line_1}', '${insured[0].address.address_line_2}', '${insured[0].address.city}', '${insured[0].address.state}', ${insured[0].address.zipcode}, '${nominees[0].title}', '${nominees[0].first_name}', '${nominees[0].last_name}', ${nominees[0].dob}, '${nominees[0].gender}', '${nominees[0].phone_number}', '${nominees[0].email}', '${nominees[0].relation}', '${category_name}', ${amount}, '${source}')`
      */

      // const insertQueryResponse = await this.rawQuery(insertQuery, conn)
      // if (insertQueryResponse.affectedRows == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], fields: insertQueryResponse }
      // fields.proposer_mobile = proposer.phone_number
      // const expiry = await this.checkExpiry(_, fields)
      // if (!expiry) return { status: 400, respcode: 1001, message: errorMsg.responseCode[130004] }
      this.TABLE_NAME = 'ma_riskcovry_customer_details'
      const data = {
        proposer_title: proposer.title,
        proposer_first_name: proposer.first_name,
        proposer_last_name: proposer.last_name,
        proposer_dob: proposer.dob,
        proposer_mobile: proposer.phone_number,
        proposer_gender: proposer.gender,
        proposer_email: proposer.email,
        proposer_aadhar: proposer.aadhar,
        insured_title: insured[0].title,
        insured_first_name: insured[0].first_name,
        insured_last_name: insured[0].last_name,
        insured_dob: insured[0].dob,
        insured_mobile: insured[0].phone_number,
        insured_gender: insured[0].gender,
        insured_email: insured[0].email,
        insured_aadhar: insured[0].aadhar,
        insured_address_line_one: insured[0].address.address_line_1,
        insured_address_line_two: insured[0].address.address_line_2,
        insured_city: insured[0].address.city,
        insured_state: insured[0].address.state,
        insured_pincode: insured[0].address.zipcode,
        relation_with_proposer: insured[0].relation_with_proposer,
        nominee_title: nominees[0] ? nominees[0].title : null,
        nominee_firstname: nominees[0] ? nominees[0].first_name : null,
        nominee_lastname: nominees[0] ? nominees[0].last_name : null,
        nominee_dob: nominees[0] ? nominees[0].dob : null,
        nominee_gender: nominees[0] ? nominees[0].gender : null,
        nominee_mobile: nominees[0] ? nominees[0].phone_number : null,
        nominee_email: nominees[0] ? nominees[0].email : null,
        nominee_relation: nominees[0] ? nominees[0].relation : null
      }
      const updateData = await this.updateWhere(conn, {
        data,
        id: orderid,
        where: 'orderid'
      })
      log.logger({ pagename: basename(__filename), action: 'updateFormData', type: 'updateWhere', fields: updateData })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'updateFormData', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async checkExpiry (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'checkExpiry', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      const CURRENT_TIMESTAMP = tz.tz(moment(), 'Asia/Kolkata').format('YYYY-MM-DD')

      const expiryQuery = `SELECT cast(expiry as char) as expiry, transaction_status from ma_riskcovry_customer_details where proposer_mobile = '${fields.proposer_mobile}' and transaction_status != 'F' order by addedon desc limit 1`
      const expiry = await this.rawQuery(expiryQuery, conn)
      // ALLow to purchase policy
      if (expiry.length < 1) return true
      if (expiry[0].transaction_status == 'S') {
        const expiryDate = tz.tz(expiry[0].expiry, 'Asia/Kolkata').format('YYYY-MM-DD')
        log.logger({ pagename: basename(__filename), action: 'checkExpiry', type: 'expiry-response', fields: expiryDate })

        if (CURRENT_TIMESTAMP <= expiryDate) return false // Don't allow to purchase policy
        else return true // Allow to purchase policy
      } else {
        return false // Don't allow to purchase policy
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'checkExpiry', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async getPrefilledData (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      const formData = `SELECT * FROM ma_riskcovry_customer_details where proposer_mobile = '${fields.proposer_mobile}'  and transaction_status = 'S' order by addedon desc limit 1`
      const formDataRes = await this.rawQuery(formData, conn)
      if (formDataRes.length > 0) {
        log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'response', fields: formDataRes[0] })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], formData: formDataRes[0] }
      } else {
        const customerDetailsQuery = `Select remitter_name, mobile_number from ma_customer_details mcd where mobile_number = '${fields.proposer_mobile}' limit 1`
        const customerDetails = await this.rawQuery(customerDetailsQuery, conn)
        // console.log('custDetails', customerDetails[0].remitter_name)
        if (customerDetails.length < 1) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
        const custObj = {
          insured_first_name: customerDetails[0].remitter_name.split(' ').slice(0, 1).join(' '),
          insured_last_name: customerDetails[0].remitter_name.split(' ').slice(1).join(' '),
          insured_mobile: customerDetails[0].mobile_number
        }
        log.logger({ pagename: basename(__filename), action: 'getPrefilledData', type: 'response', fields: custObj })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], formData: custObj }
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getPreFilledData', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async getDynamicForm (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      if (fields.orderid == '') {
        const userChannelData = await this.checkUserChannel(fields.ma_user_id, conn)
        log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'channeldata', fields: userChannelData })
        if (userChannelData == '') {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
        }
        const activePolicy = await this.checkExpiry(_, fields)
        log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'channeldata', fields: activePolicy })
        if (!activePolicy) return { status: 400, respcode: 1001, message: `${errorMsg.responseCode[1028]}: Policy Still Active` }
      }
      const tncUrl = util.riskCovryTNC
      // Get Policy Details
      let policyDetails
      if (fields.orderid == '' && fields.category_code != '') {
        const policyDetailsQuery = `SELECT category_name, category_code, expiry_period, amount, additional_info as plans_features, policy_wordings, policy_name from ma_insurance_category_master micm where category_code = '${fields.category_code}' and riskcovry_sachet = 'true' and status = 'Active' limit 1`
        policyDetails = await this.rawQuery(policyDetailsQuery, conn)
        log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'policyDetails', fields: policyDetails })
        if (policyDetails.length > 0) {
          policyDetails[0].description = `Buy ${policyDetails[0].policy_name} for a validity of ${policyDetails[0].expiry_period} days at a premium of Rs.${policyDetails[0].amount}`
          policyDetails[0].validity = `${policyDetails[0].expiry_period} days.`
          policyDetails[0].source = ''
          policyDetails[0].tncUrl = policyDetails[0].plans_features
        } else {
          policyDetails = null
        }
      } else {
        const policyDetailQuery = `SELECT mrcd.policy_name AS category_name, mrcd.policy_category_code AS category_code, micm.expiry_period AS expiry_period, mrcd.source AS source, mrcd.amount AS amount, micm.additional_info as plans_features, micm.policy_wordings, micm.policy_name as policy_name FROM ma_riskcovry_customer_details mrcd JOIN ma_insurance_category_master micm ON mrcd.policy_category_code = micm.category_code WHERE mrcd.orderid = '${fields.orderid}' and micm.riskcovry_sachet = 'true' limit 1`
        policyDetails = await this.rawQuery(policyDetailQuery, conn)
        log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'policyDetails', fields: policyDetails })
        if (policyDetails.length > 0) {
          policyDetails[0].description = `Buy ${policyDetails[0].policy_name} for a validity of ${policyDetails[0].expiry_period} days at a premium of Rs.${policyDetails[0].amount}`
          policyDetails[0].validity = `${policyDetails[0].expiry_period} days.`
          policyDetails[0].tncUrl = policyDetails[0].plans_features
        } else {
          policyDetails = null
        }
      }

      let getParamsQuery
      let isInsuredReqSql
      if (fields.category_code == '') {
        isInsuredReqSql = `select micm.is_proposer_details_required from ma_insurance_category_master micm left join ma_riskcovry_customer_details mrcd on micm.category_code = mrcd.policy_category_code where mrcd.orderid = '${fields.orderid}' and micm.riskcovry_sachet = 'true'`
      } else {
        isInsuredReqSql = `SELECT is_proposer_details_required from ma_insurance_category_master micm where category_code = '${fields.category_code}' and riskcovry_sachet = 'true'`
      }
      const res = await this.rawQuery(isInsuredReqSql, conn)
      if (res.length > 0) {
        if (res[0].is_proposer_details_required == 'false') {
          getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'Riskcovry Sachet' and form_type = 'insured_form' and  isActive = 'Y'"
        } else {
          getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'Riskcovry Sachet' and form_type = 'customer form' and  isActive = 'Y'"
        }
      } else {
        getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'Riskcovry Sachet' and form_type = 'customer form' and  isActive = 'Y'"
      }

      const params = await this.rawQuery(getParamsQuery, conn)
      log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'params-response', fields: params })
      if (params.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      const form = JSON.parse(params[0].api_params)
      const prefilledData = await this.getPrefilledData(_, fields)
      if (prefilledData.status == 200) {
        // const prefilledForm = form.map(element => {
        //   element.value = prefilledData.formData[`${element.postKey}`]
        //   return element
        // })
        const prefilledForm = form.map(element => {
          element.fields.map(i => {
            i.value = prefilledData.formData[`${i.postKey}`] ? prefilledData.formData[`${i.postKey}`] : null
            if (i.label == 'Proposer Phone Number') {
              i.isEditable = false
            } else if (i.label == 'Insured Phone Number') {
              i.isEditable = false
            }
            return i
          })
          return element
        })
        log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'prefilledForm-Response', fields: prefilledForm })

        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicForm: prefilledForm, policyDetails, tncUrl }
      }

      if (form[0].title == 'Proposer Details') {
        form[0].fields.find(x => {
          if (x.label == 'Proposer Phone Number') {
            x.value = fields.proposer_mobile
            x.isEditable = false
          }
        })
      } else if (form[0].title == 'Insured Details') {
        form[0].fields.find(x => {
          if (x.label == 'Insured Phone Number') {
            x.value = fields.proposer_mobile
            x.isEditable = false
          }
        })
      }

      log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'response', fields: form })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicForm: form, policyDetails, tncUrl }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getDynamicForm', type: 'request', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async isCheckBoxVisible (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'isCheckBoxVisible', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      const expiry = await this.checkExpiry(_, fields)
      console.log('THIS IS THE CHECKBOX RESPONSE: ', expiry)
      if (expiry) {
        const emitraUser = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid} LIMIT 1`
        const emitraUserRes = await this.rawQuery(emitraUser, conn)
        log.logger({ pagename: basename(__filename), action: 'isCheckBoxVisible', type: 'query-response', fields: emitraUserRes })
        if (emitraUserRes.length > 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1191] }
      }
      if (!expiry) return { status: 400, respcode: 1001, message: errorMsg.responseCode[130004] }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'isCheckBoxVisible', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async reverseEntries (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'reverseEntries', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      const reverseEntry = await pointsLedger.createEntry(_, {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: 15,
        ma_status: 'REV',
        orderid: 'REV-' + fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        description: 'Riskcovry Sachet Transaction Reversed',
        connection: conn
      })
      if (reverseEntry.status != 200) {
        return reverseEntry
      }

      const retailLedgerEntry = await pointsLedger.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount: parseFloat(fields.amount),
        mode: 'dr',
        transaction_type: 15,
        ma_status: 'REV',
        userid: fields.userid,
        description: 'Debit Riskcovry Transaction - Reversed',
        orderid: 'REV-' + fields.orderid,
        corresponding_id: fields.ma_user_id,
        connection: conn
      })

      if (retailLedgerEntry.status != 200) {
        return retailLedgerEntry
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'reverseEntries', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async activePoliciesCron () {
    const conn = await mySQLWrapper.getConnectionFromPool()

    try {
      const sqlCurrentTime = 'SELECT DATE_ADD(DATE_FORMAT(CURRENT_TIMESTAMP,"%Y-%m-%d %H:%i:%s"),interval - 60 minute) as currentTime'
      const resultTime = await this.rawQuery(sqlCurrentTime, conn)
      console.log('resultTime>>', resultTime)
      const beforeOneHourTimeStamp = resultTime[0].currentTime
      // const sql = `SELECT aggregator_order_id, ma_user_id, userid, amount,transaction_type,transaction_status, addedon from ma_transaction_master where transaction_type = 15 and transaction_status = "P" and remarks like "Riskcovry Sachet Insurance Transaction%" and addedon < '${beforeOneHourTimeStamp}' order by addedon desc limit 100`
      const sql = `SELECT orderid as aggregator_order_id, ma_user_id, userid, amount, transaction_status, addedon from ma_riskcovry_customer_details where transaction_status = 'P' and addedon < '${beforeOneHourTimeStamp}' order by addedon desc limit 100`
      const pendingPolicies = await this.rawQuery(sql, conn)

      if (pendingPolicies.length < 1) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      log.logger({ pagename: basename(__filename), action: 'activePoliciesCron', type: 'request', fields: pendingPolicies })
      const policies = []
      // creating a new connection for updating
      var connection = await mySQLWrapper.getConnectionFromPool()
      for (var i in pendingPolicies) {
        await mySQLWrapper.beginTransaction(connection)
        const fields = {}
        fields.ma_user_id = pendingPolicies[i].ma_user_id
        fields.userid = pendingPolicies[i].userid
        fields.amount = pendingPolicies[i].amount
        fields.orderid = pendingPolicies[i].aggregator_order_id
        fields.connection = connection
        const reverseEntries = await this.reverseEntries('_', fields)
        if (reverseEntries.status != 200) await mySQLWrapper.rollback(connection)
        // const updateQueries = `update ma_transaction_master set transaction_status = 'F' where aggregator_order_id = '${pendingPolicies[i].aggregator_order_id}'`
        // const updateRes = await this.rawQuery(updateQueries, connection)
        /* RISK MANAGEMENT Changes */
        const data = {
          transaction_status: 'F'
        }

        const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
          data,
          id: pendingPolicies[i].aggregator_order_id,
          where: 'aggregator_order_id'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

        const updateRiskQueries = `update ma_riskcovry_customer_details set transaction_status = 'F' where orderid = '${pendingPolicies[i].aggregator_order_id}'`
        const updateRisk = await this.rawQuery(updateRiskQueries, connection)
        // console.log('affectedRows: ', updateRes)
        // if (updateRes.affectedRows == 0) await mySQLWrapper.rollback(conn)
        policies.push(pendingPolicies[i].aggregator_order_id)
        await mySQLWrapper.commit(connection)
      }
      log.logger({ pagename: basename(__filename), action: 'activePolciesCron', type: 'failed-policies', fields: policies })
      return { status: 200, respcode: 1000, message: 'Pending transaction marked as failed!!!' }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'activePoliciesCron', type: 'err', fields: err })
    } finally {
      connection.release()
      conn.release()
    }
  }

  static async notificationCron () {
    const conn = await mySQLWrapper.getConnectionFromPool()
    try {
      // const sql = 'SELECT mtm.aggregator_order_id as aggregator_order_id , mtm.ma_user_id as ma_user_id , mtm.userid as userid , mtm.amount as amount ,mtm.transaction_type as transaction_type ,mtm.transaction_status as transaction_status , mtm.addedon as addedon, mtmd.customer_mobile as mobile_number from ma_transaction_master as mtm left join ma_transaction_master_details as mtmd on mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id where mtm.transaction_type = 15 and mtm.transaction_status = "P" and mtm.remarks like "Riskcovry Sachet Insurance Transaction%" and mtm.addedon > DATE_SUB(NOW(), INTERVAL 1 HOUR) limit 100'
      const sql = 'SELECT orderid as aggregator_order_id, ma_user_id, userid, amount, transaction_status, addedon, proposer_mobile as mobile_number from ma_riskcovry_customer_details where transaction_status = "P" and  addedon > DATE_SUB(NOW(), INTERVAL 1 HOUR) limit 100'

      const activePolicies = await this.rawQuery(sql, conn)
      log.logger({ pagename: basename(__filename), action: 'notificationCron', type: 'activePolicies-RES', fields: activePolicies })

      if (activePolicies.length < 1) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      const notifications = []
      for (var i in activePolicies) {
        const transactionDetails = {
          txnId: activePolicies[i].aggregator_order_id,
          ma_user_id: activePolicies[i].ma_user_id,
          userid: activePolicies[i].userid,
          amount: activePolicies[i].amount,
          addedon: tz.tz(activePolicies[i].addedon, 'UTC').format('LLLL'),
          transaction_type: 15,
          transaction_status: activePolicies[i].transaction_status,
          mobile_number: activePolicies[i].mobile_number
        }

        const notificationResult = await this.sendNotification({ transactionDetails })
        log.logger({ pagename: basename(__filename), action: 'notificationCron', type: 'response', fields: notificationResult })
        if (notificationResult.data.success == 1) notifications.push(activePolicies[i].aggregator_order_id)
      }
      log.logger({ pagename: basename(__filename), action: 'notificationCron', type: 'notified-transactions', fields: notifications })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'notificationCron', type: 'err', fields: err })
    } finally {
      conn.release()
    }
  }

  static async sendNotification ({ transactionDetails }) {
    log.logger({ pagename: basename(__filename), action: 'notificationCron', type: 'request', fields: transactionDetails })

    const conn = await mySQLWrapper.getConnectionFromPool()
    try {
      const appNotificationTemplate = `Hi, Riskcovry policy purchase of Rs. ${transactionDetails.amount} done at ${transactionDetails.addedon} against order # ${transactionDetails.txnId} is Pending. Please click to fill up the policy details and complete the transaction.`

      const gcmKey = await common.getGcmKey(transactionDetails.userid, this, conn)
      if (gcmKey != '') {
        // await common.sendNotifications({
        //   recipient: gcmKey,
        //   message: {
        //     notification_id: parseInt(transactionDetails.txnId.replace(/^\D+/g, '')),
        //     body: appNotificationTemplate,
        //     body_web: appNotificationTemplate,
        //     title: 'RISKCOVRY SACHET TRANSACTION',
        //     icon: 'ic_launcher',
        //     sound: 'default',
        //     priority: 'high',
        //     badge: '1',
        //     image: '',
        //     action: '1',
        //     aggregator_order_id: transactionDetails.txnId,
        //     'action-url': '',
        //     deep_link: `airpay://app/riskcovry_form_route?order_id=${transactionDetails.txnId}&mobile=${transactionDetails.mobile_number}`,
        //     module: 'Riskcovry',
        //     action_code: 1000,
        //     ma_user_id: transactionDetails.ma_user_id,
        //     transaction_type: transactionDetails.transaction_type,
        //     transaction_status: transactionDetails.transaction_status,
        //     mobile: transactionDetails.mobile_number
        //   }
        // })
        const message = {
          notification_id: parseInt(transactionDetails.txnId.replace(/^\D+/g, '')),
          body: appNotificationTemplate,
          body_web: appNotificationTemplate,
          title: 'RISKCOVRY SACHET TRANSACTION',
          icon: 'ic_launcher',
          sound: 'default',
          priority: 'high',
          badge: '1',
          image: '',
          action: '1',
          aggregator_order_id: transactionDetails.txnId,
          'action-url': '',
          deep_link: `airpay://app/riskcovry_form_route?order_id=${transactionDetails.txnId}&mobile=${transactionDetails.mobile_number}`,
          module: 'Riskcovry',
          action_code: 1000,
          ma_user_id: transactionDetails.ma_user_id,
          transaction_type: transactionDetails.transaction_type,
          transaction_status: transactionDetails.transaction_status,
          mobile: transactionDetails.mobile_number
        }
        const headers = { Authorization: 'key=' + util.NOTIFICATION_API_ACCESS_KEY, 'Content-Type': 'application/json' }
        const postData = {
          registration_ids: [gcmKey],
          data: message
        }
        console.log('---SEND NOTIFICATION DATA---', postData)
        const notificationResp = await axios({
          method: 'post',
          url: 'https://fcm.googleapis.com/fcm/send',
          data: postData,
          headers: headers
        })
        console.log('---SEND NOTIFICATION RESPONSE---')
        log.logger({ pagename: basename(__filename), action: 'sendNotification', type: 'notify-res', fields: notificationResp.data })
        return notificationResp
        // console.log('this is inside notif: ', notify.success)
        // log.logger({ pagename: basename(__filename), action: 'notify-res', type: 'notify-res', fields: notify })
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'sendNotification', type: 'err', fields: err })
    } finally {
      conn.release()
    }
  }

  static async riskcovryTransactionDetails (_, fields) {
    log.logger({ pagename: basename(__filename), action: 'riskcovryTransactionDetails', type: 'request', fields })

    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    try {
      let sqlCondtion = ''
      if (fields.aggregator_order_id) {
        sqlCondtion = ` t.aggregator_order_id = '${fields.aggregator_order_id}' `
      } else {
        sqlCondtion = ` t.ma_transaction_master_id = '${fields.ma_transaction_master_id}' `
      }
      const sql = `SELECT CONCAT(IFNULL(ri.insured_first_name, ''), ' ', IFNULL(ri.insured_last_name, '')) AS name,
                          micm.policy_name as policy_name,
                          ri.amount as amount,
                          ri.coi as coi,
                          ri.policy_number as policy_number,
                          FROM_UNIXTIME(UNIX_TIMESTAMP(t.addedon),'%d-%m-%Y %h:%i:%s') as transaction_time,
                          t.aggregator_order_id as orderid,
                          FROM_UNIXTIME(UNIX_TIMESTAMP(ri.expiry),'%d-%m-%Y') as expiry_date,
                          t.transaction_status as transaction_status,
                          ri.insured_mobile as customer_mobile
                          FROM ma_riskcovry_customer_details AS ri
                          inner join ma_transaction_master as t on t.aggregator_order_id = ri.orderid
                          inner join ma_insurance_category_master as micm on micm.category_code = ri.policy_category_code
                          where micm.riskcovry_sachet = 'true' and ${sqlCondtion} 
                          `
      const response = await this.rawQuery(sql, conn)
      if (response.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      log.logger({ pagename: basename(__filename), action: 'riskcovryTransactionDetails', type: 'response', fields: response })
      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        name: response[0].name,
        policy_name: response[0].policy_name,
        amount: response[0].amount,
        coi: response[0].coi,
        policy_number: response[0].policy_number,
        transaction_time: response[0].transaction_time,
        orderid: response[0].orderid,
        expiry_date: response[0].expiry_date,
        transaction_status: response[0].transaction_status,
        logo: util.riskCovryLogoUrl,
        customer_mobile: response[0].customer_mobile
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'riskcovryTransactionDetails', type: 'err', fields: err })
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = RiskCovrySachetController
