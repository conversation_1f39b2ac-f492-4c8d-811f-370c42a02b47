const responseCode = {
  1000: 'Success',
  1001: 'Fail: Something went wrong',
  1002: 'Fail: No Records',
  1003: 'Fail: User has Active Insurance already',
  1004: 'Fail: User has Insurance already in process',
  1005: 'Fail: Policy not found',
  1006: 'Fail: User cannot downgrade from Current Insurance',
  1007: 'Fail: No Records for policy state',
  1008: 'Fail: State code not found',
  1009: 'Fail: Invalid premium amount [#amount] !',
  1010: 'Fail: Insufficient balance',
  1011: 'Fail: Mobile number not valid ',
  1012: 'Fail: Invalid date ',
  1013: 'Fail: Invalid Customer first name provided',
  1014: 'Fail: Invalid email id provided',
  1015: 'Fail: Invalid address #SR provided',
  1016: 'Fail: Invalid pincode provided',
  1017: 'Fail: Invalid nominee first name provided',
  1018: 'Fail: Previous transaction [#orderid] already in process',
  1019: 'Fail: Premium details not available',
  1020: 'Fail: Invalid Age [#age] !',
  1021: 'Fail: Policy is already verified/insured for this mobile number',
  1022: 'Fail: Invalid Customer last name provided',
  1023: 'Fail: Invalid nominee last name provided',
  1024: 'Fail: Valid age for term insurance is between #minage to #maxage ',
  1025: 'Fail: Transaction is failed',
  1028: 'Fail' // Append error msg here
}
module.exports = { responseCode }
