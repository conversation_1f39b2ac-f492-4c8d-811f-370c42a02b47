const DAO = require('../../lib/dao')

class NomineeController extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  static get TABLE_NAME () {
    return 'ma_users_policy_nominees'
  }

  static get PRIMARY_KEY () {
    return 'nominee_id'
  }

  static async insertData (connection, insertObject) {
    // this.TABLE_NAME = 'ma_users_policy_nominees'
    const result = await this.insert(connection, insertObject)
    return result
  }

  static async updateWhereData (connection, { data, id, where }) {
   // this.TABLE_NAME = 'ma_users_policy_nominees'
    const updateQueue = await this.updateWhere(connection, { data, id: id, where: where })
    return updateQueue
  }
}

module.exports = NomineeController
