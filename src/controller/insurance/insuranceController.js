const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const transactionMaster = require('../transaction/transactionController')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const airpaychecksum = require('../../util/airpaychecksum')
// const common = require('../../util/common')
const axios = require('axios')
// const cashAccount = require('../creditDebit/cashAccountController')
// const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
// const cashLedger = require('../creditDebit/cashLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
// const commissionController = require('../commission/commissionController')
// const incentiveController = require('../incentive/incentiveController')
const insuranceController = require('../incentive/insuranceIncentiveController')
const balanceController = require('../balance/balanceController')
const qs = require('querystring')
const configIns = require('./config')
// const riskCovrySachetController = require('./riskcovrySachetController')
const LombardInsuranceController = require('./lombardInsuranceController')

const sms = require('../../util/sms')
const moment = require('moment')
/* NEW CHANGES : LEIN BALANCE CHECK */
const lienBalanceController = require('../lienBalance/lienBalanceController')
class insurance extends DAO {
  /**
   * Overrides TABLE_NAME with this class' backing table at MySQL
   */
  static get TABLE_NAME () {
    return 'ma_transaction_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_transaction_master_id'
  }

  /**
   * Returns TABLE_NAME details by its PRIMARY_KEY
   */
  static async getByID (_, { id }) {
    // eslint-disable-next-line no-return-await
    return await this.find(id)
  }

  /**
   * @deprecated - created new method with named v2 & added new changed in version 2
   * @param {*} _
   * @param {*} args
   * @returns
   */
  static async addInsUser (_, args) {
    log.logger({ pagename: 'insuranceController.js', action: 'addInsUser', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const orderid = args.orderId
      const companyid = args.companyId
      const affiliateid = args.retailId
      // process.env.NODE_ENV = 'staging'

      // Check channel for user

      const userChannelData = await this.checkUserChannel(args.ma_user_id, connection)

      if (userChannelData == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
      }

      const checkInsuranceCategoryData = await this.checkInsuranceCateory(args.provider_name, connection)

      if (checkInsuranceCategoryData == 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Category is already disbaled' }
      }

      const airpayKey = util[process.env.NODE_ENV].airpayKey
      const secretkey = util[process.env.NODE_ENV].secretkey
      const affkey = util[process.env.NODE_ENV].affkey

      const generatedChecksum = airpaychecksum.airpaychecksum(orderid + affiliateid + companyid + secretkey)

      const postdata = 'orderid=' + orderid + '&affiliateid=' + affiliateid + '&companyid=' + companyid
      const config = {
        method: 'POST',
        url: util[process.env.NODE_ENV].createorderins,
        headers: {
          Accept: 'text/plain',
          'Content-Type': 'application/x-www-form-urlencoded',
          AFFKEY: affkey,
          AIRPAYKEY: airpayKey,
          CHECKSUM: generatedChecksum
        },
        data: postdata
        /* data: {
              orderid: orderid,
              affiliateid: affiliateid,
              companyid : companyid
                } */
      }
      log.logger({ pagename: 'insuranceController.js', action: 'addInsUserRequest', type: 'request', fields: config })
      const res = await axios(config)
      log.logger({ pagename: 'insuranceController.js', action: 'addInsUserResponse', type: 'response', fields: res.data })

      // console.log(res.data)
      if (res.data.status == '200' && res.data.message == 'success') {
      // transaction inita

        /* const sqlUser = `Select mobile_id,ma_user_master_id,sales_id,state,user_type from ma_user_master where profileid='${args.ma_user_id}'`
        var mobile = 0
        const _user = await this.rawQuery(sqlUser, connection)
        log.logger({ pagename: 'insuranceController.js', action: 'addInsUserResponse', type: 'ma_user_master_data', fields: _user })
        var sales_id = 0
        if (_user.length > 0) {
          mobile = _user[0].mobile_id
          sales_id = _user[0].sales_id
        }

        var hierarchy_id = 0

        if (sales_id != undefined && sales_id != null && sales_id > 0) {
          var hierarchySql = `select sales_hierarchy_details_id from sales_hierarchy_details where TSM_id=${sales_id} and record_status='active'`
          const hierarchyDetails = await this.rawQuery(hierarchySql, connection)
          log.logger({ pagename: 'insuranceController.js', action: 'addInsUserResponse', type: 'response-hierarchyDetails', fields: hierarchyDetails })
          if (hierarchyDetails.length > 0) {
            hierarchy_id = hierarchyDetails[0].sales_hierarchy_details_id
          }
        }

        const refid = res.data.data.referenceid
        const transaction_data = {
          ma_user_id: args.ma_user_id,
          userid: affiliateid,
          aggregator_order_id: orderid,
          transaction_id: orderid,
          amount: 0,
          commission_amount: 0,
          mobile_number: mobile,
          transaction_status: 'I',
          transaction_type: 15, // 15 user for insurance
          remarks: refid,
          gcmkey: companyid,
          parent_id: orderid,
          sales_id: sales_id,
          hierarchy_id: hierarchy_id
        }

        // Check merchant has the channels assigned
        if (_user[0].user_type == 'RT') {
          const transactionChannelExists = await transactionMaster.checkChannelExists(transaction_data.transaction_type, transaction_data.ma_user_id, connection, null)
          console.log('transactionChannelExists : ' + transactionChannelExists)
          if (transactionChannelExists === false) {
            log.logger({ pagename: 'insuranceController.js', action: 'addInsUser - transaction channel exists', type: 'response', fields: {} })
            return { status: 400, respcode: 1028, message: 'Transaction not allowed as channel is not assigned' }
          }
        }
        const trans_result = await this.createInsTransaction('', transaction_data, connection)
        // console.log('trans result ',trans_result)
        */

        const sqlUser = `select mobile_id,ma_user_master_id,sales_id,state,user_type,userid from ma_user_master where profileid='${args.ma_user_id}' AND mer_user = 'mer' limit 1`
        var mobile = 0
        const _user = await this.rawQuery(sqlUser, connection)
        log.logger({ pagename: 'insuranceController.js', action: 'addInsUserResponse', type: '_userSQLResult', fields: _user })
        var sales_id = 0
        if (_user.length > 0) {
          mobile = _user[0].mobile_id
          sales_id = _user[0].sales_id
          args.userid = args.userid ? args.userid : _user[0].userid
        } else {
          return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
        }

        const refid = res.data.data.referenceid
        const transactionData = await transactionMaster.initiateTransaction(_, {
          connection: connection,
          ma_user_id: args.ma_user_id,
          userid: args.userid,
          aggregator_order_id: orderid,
          transaction_id: orderid,
          amount: 0,
          commission_amount: 0,
          transaction_type: 15, // insurance
          remarks: refid,
          mobile_number: mobile,
          provider_id: '0', // to do
          provider_name: args.provider_name ? args.provider_name : '', // to do
          utility_id: '0', // to do
          utility_name: 'insurance', // to do
          action_type: 'instapay',
          transaction_status: 'I',
          affiliated_id: affiliateid,
          company_id: companyid,
          bank_name: 'GIBL'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'insuranceController.js', type: 'transactionData', fields: transactionData })

        if (transactionData.status == 200) {
          var InCat = args.cat || ''
          const InsuranceURL = `https://gibl.in/wallet/?cat=${InCat}&umc=${configIns.UMC}&urc=${configIns.URC}&ak=${orderid}&fname=${configIns.FNAME}&lname=${configIns.LNAME}&email=${configIns.EMAIL}&phno=${configIns.PHONE}&pin=${configIns.PIN}&pmode=${args.pmode}`
          // sucess initiate
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], orderid: orderid, referenceid: refid, companyid: companyid, pmode: args.pmode, url: InsuranceURL }
        } else {
          // return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "trasaction can't create", orderid: '', referenceid: null, companyid: null, pmode: null }
          /* NEW CHANGES : LEIN BALANCE CHECK */
          return { status: 400, respcode: 1028, message: transactionData.message || errorMsg.responseCode[1028] + ' : ' + "trasaction can't create", orderid: '', referenceid: null, companyid: null, pmode: null }
        }
      } else {
        log.logger({ pagename: 'insuranceController.js', action: 'addInsUser', type: 'response', fields: res.data.message })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + res.data.message, orderid: '', referenceid: null, companyid: null, pmode: null }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addInsUser', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async checkUserChannel (ma_user_id, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    // var channelDetails = {}
    try {
      // get state & user type
      // get state details
      const sqlA = `SELECT state, user_type FROM ma_user_master WHERE profileid = ${ma_user_id} limit 1`
      const userADetails = await this.rawQuery(sqlA, connection)
      let state_id = 0
      let user_type = ''
      if (userADetails.length > 0) {
        state_id = userADetails[0].state
        user_type = userADetails[0].user_type
      }

      // get channel list with respect to user id
      const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = 0 and ma_user_id = ${ma_user_id} and record_status = 'Y' and user_type = '${user_type}' `
      const channelDetails = await this.rawQuery(sqlCh, connection)
      var final_channel_arr = {}
      var channel_arr = []
      var check_channel_arr = []
      var checkInsurance = ''
      if (channelDetails.length > 0) {
        for (var j = 0; j < channelDetails.length; j++) {
          channel_arr[j] = channelDetails[j].channel_name
        }
        console.log('check channel arr ', channel_arr)
        // console.log('check channel arr new ', check_channel_arr)
        if (channel_arr.includes('INSURANCE')) {
          checkInsurance = 'yes'
        }

        console.log('inside user insurance channel condition >>>>>>>>>>', checkInsurance)
        return checkInsurance
      } else {
        // get channel list with respect to state
        const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = ${state_id} and ma_user_id = 0 and record_status = 'Y' and user_type = '${user_type}' `
        const channelStateDetails = await this.rawQuery(sqlCh, connection)
        // var final_channel_arr = {}
        if (channelStateDetails.length > 0) {
          for (var k = 0; k < channelStateDetails.length; k++) {
            channel_arr[k] = channelStateDetails[k].channel_name
          }

          if (channel_arr.includes('INSURANCE')) {
            checkInsurance = 'yes'
          }
          console.log('inside state channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
          return checkInsurance
        } else {
          const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=2 and state_master_id =0 and record_status = 'Y' and ma_user_id = 0 and user_type = '${user_type}' `
          const channelGlobalDetails = await this.rawQuery(sqlCh, connection)
          // var final_channel_arr = {}
          if (channelGlobalDetails.length > 0) {
            for (var n = 0; n < channelGlobalDetails.length; n++) {
              channel_arr[n] = channelGlobalDetails[n].channel_name
            }
            console.log('channel_list ', channel_arr)
            if (channel_arr.includes('INSURANCE')) {
              checkInsurance = 'yes'
            }
            console.log('inside Global channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
            return checkInsurance
          } else {
            checkInsurance = ''
            return checkInsurance
          }
        }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkUserChannel', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async checkInsuranceCateory (provider_name, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    // var channelDetails = {}
    try {
      // get channel list with respect to user id
      const sqlCh = `SELECT category_flag FROM ma_insurance_category_master WHERE category_name= '${provider_name}' `
      const categoryDetails = await this.rawQuery(sqlCh, connection)
      let category_flag = 0

      if (categoryDetails.length > 0) {
        category_flag = categoryDetails[0].category_flag
        console.log('inside check insurance category condition >>>>>>>>>>', category_flag)
      }
      return category_flag
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkInsuranceCateory', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * Created new method & named with v2(Version 2)
   * addInsUser_v2 description - create order for insurance
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, companyId: number, retailId: number, orderid: string, pmode: number, provider_name: string, cat: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, orderid: string, referenceid: number, companyid: number, pmode: number, url: string }>}
   */
  static async addInsUser_v2 (_, args) {
    log.logger({ pagename: 'insuranceController.js', action: 'addInsUser', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const orderid = args.orderId
      const companyid = args.companyId
      const affiliateid = 28
      // const cat = args.cat
      // process.env.NODE_ENV = 'staging'

      // Check channel for user

      const userChannelData = await this.checkUserChannel(args.ma_user_id, connection)

      if (userChannelData == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
      }

      const checkInsuranceCategoryData = await this.checkInsuranceCateory(args.provider_name, connection)

      if (checkInsuranceCategoryData == 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Category is already disbaled' }
      }

      const subCategoryCode = await this.getInsuranceSubCategoryCode(args.provider_name, connection)

      const merc_details_sql = `Select CONCAT(IFNULL(firstname, ''), IFNULL(lastname, '')) as name, 
      IFNULL(mobile_id, '') as mobile_id, IFNULL(email_id, '') as email_id from ma_user_master mum 
      WHERE mum.profileid = '${args.ma_user_id}'`

      const merc_details = await this.rawQuery(merc_details_sql, connection)

      if (merc_details.length == 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Merchant details not found' }
      }

      const airpayKey = util[process.env.NODE_ENV].airpayKey
      const secretkey = util[process.env.NODE_ENV].secretkey
      const affkey = util[process.env.NODE_ENV].affkey

      const generatedChecksum = airpaychecksum.airpaychecksum('|' + companyid + '|,' + secretkey)

      const cust_data = Buffer.from(`{"email": "${merc_details[0].email_id}", "mobile_no": "${merc_details[0].mobile_id}", "name": "${merc_details[0].name}", "partner_user_id" : "${args.ma_user_id}"}`).toString('base64')
      let postdata = `orderid=${orderid}&affiliateid=${affiliateid}&providerid=${companyid}&custom_data=${cust_data}`

      if (args.cat) {
        postdata += `&cat=${args.cat}`
      }
      const config = {
        method: 'POST',
        url: util[process.env.NODE_ENV].createorderins,
        headers: {
          Accept: 'text/plain',
          'Content-Type': 'application/x-www-form-urlencoded',
          AFFKEY: affkey,
          AIRPAYKEY: airpayKey,
          CHECKSUM: generatedChecksum
        },
        data: postdata
        /* data: {
              orderid: orderid,
              affiliateid: affiliateid,
              providerid : companyid,
              cat: cat
                } */
      }
      log.logger({ pagename: 'insuranceController.js', action: 'addInsUserRequest', type: 'request', fields: config })
      const res = await axios(config)
      log.logger({ pagename: 'insuranceController.js', action: 'addInsUserResponse', type: 'response', fields: res.data })

      if (res.data.message == 'success') {
        const sqlUser = `select mobile_id,ma_user_master_id,sales_id,state,user_type,userid from ma_user_master where profileid='${args.ma_user_id}' AND mer_user = 'mer' limit 1`
        var mobile = 0
        const _user = await this.rawQuery(sqlUser, connection)
        log.logger({ pagename: 'insuranceController.js', action: 'addInsUserResponse', type: '_userSQLResult', fields: _user })
        var sales_id = 0
        if (_user.length > 0) {
          mobile = _user[0].mobile_id
          sales_id = _user[0].sales_id
          args.userid = args.userid ? args.userid : _user[0].userid
        } else {
          return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }
        }

        const refid = res.data.data.referenceid
        const transactionData = await transactionMaster.initiateTransaction(_, {
          connection: connection,
          ma_user_id: args.ma_user_id,
          userid: args.userid,
          aggregator_order_id: orderid,
          transaction_id: orderid,
          amount: 0,
          commission_amount: 0,
          transaction_type: 15, // insurance
          remarks: `Insurance Transaction Reference Number : ${refid}`,
          mobile_number: mobile,
          provider_id: '0', // to do
          provider_name: args.provider_name ? args.provider_name : '', // to do
          utility_id: '0', // to do
          utility_name: 'insurance', // to do
          action_type: 'instapay',
          transaction_status: 'I',
          affiliated_id: affiliateid,
          company_id: companyid,
          subcategory_code: subCategoryCode.category_code,
          bank_name: 'Riskcovry'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'insuranceController.js', type: 'transactionData', fields: transactionData })

        if (transactionData.status == 200) {
          // sucess initiate
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], orderid: orderid, referenceid: refid, companyid: companyid, pmode: args.pmode, url: res.data.data.url }
        } else {
          // return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "trasaction can't create", orderid: '', referenceid: null, companyid: null, pmode: null }
          /* NEW CHANGES : LEIN BALANCE CHECK */
          return { status: 400, respcode: 1028, message: transactionData.message || errorMsg.responseCode[1028] + ' : ' + "trasaction can't create", orderid: '', referenceid: null, companyid: null, pmode: null }
        }
      } else {
        log.logger({ pagename: 'insuranceController.js', action: 'addInsUser', type: 'response', fields: res.data.message })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + res.data.message, orderid: '', referenceid: null, companyid: null, pmode: null }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addInsUser', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (connection) connection.release()
    }
  }

  static async premiumDeduction (_, req) {
    log.logger({ pagename: 'insuranceController.js', action: 'premiumDeduction', type: 'request', fields: req })

    // checksum calculation
    var fields = JSON.parse(JSON.stringify(qs.parse(req.data)))
    log.logger({ pagename: 'insuranceController.js', action: 'premiumDeduction', type: 'request', fields: fields })

    var checksum = ''
    if (req.headers) {
      if (req.headers.checksum) {
        checksum = req.headers.checksum
      } else {
        checksum = req.headers.CHECKSUM
      }
    }

    // valiadation of rest api parameter
    if (fields.orderid == null || fields.orderid == undefined || fields.orderid == 0 || fields.orderid == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "orderId can't empty" }
    }

    if (fields.banktxnid == null || fields.banktxnid == undefined || fields.banktxnid == 0 || fields.banktxnid == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "banktxnid can't empty" }
    }

    if (checksum == null || checksum == undefined || checksum == 0 || checksum == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "checksum can't empty" }
    }

    const res = isNaN(fields.amount)
    if (res) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Enter valid amount' }
    } else {
      if (fields.amount > 0 && fields.amount <= ********.99) {
      } else {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Enter amount greater than 0 and less than ********.99' }
      }
    }

    const orderId = fields.orderid
    const amount = fields.amount
    const banktxnid = fields.banktxnid
    const secretkey = util[process.env.NODE_ENV].secretkey

    // checksum calculation
    const generatedChecksum = airpaychecksum.airpaychecksum(orderId + amount + banktxnid + secretkey)

    console.log('generatedChecksum >>', generatedChecksum)

    if (checksum != generatedChecksum) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'checksum not match', orderid: orderId }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // get ma_userid from transaction master table
      const sql = `SELECT m.ma_transaction_master_id, m.ma_user_id,m.userid,d.affiliated_id as affiliateid,d.company_id as companyid, m.remarks as refid  from ma_transaction_master as m 
      JOIN ma_transaction_master_details as d on d.ma_transaction_master_id = m.ma_transaction_master_id 
      where m.aggregator_order_id='${fields.orderid}' and m.transaction_status='I' order by m.ma_transaction_master_id desc limit 1`
      const getTxnDetails = await this.rawQuery(sql, connection)

      if (getTxnDetails.length > 0) {
        var isSet = true

        const maUserId = getTxnDetails[0].ma_user_id
        const affiliateid = getTxnDetails[0].affiliateid
        const companyid = getTxnDetails[0].companyid
        const refid = getTxnDetails[0].refid + '-' + banktxnid
        const userId = getTxnDetails[0].userid
        const ma_transaction_master_id = getTxnDetails[0].ma_transaction_master_id

        // await mySQLWrapper.beginTransaction(connection)

        const availableBalance = await balanceController.getWalletBalancesDirect(_, { ma_user_id: maUserId, ma_status: 'ACTUAL', balance_flag: 'SUMMARY', connection })
        console.log('1. total points balance', availableBalance)

        if (availableBalance.amount < amount) {
          // await mySQLWrapper.rollback(connection)
          await mySQLWrapper.beginTransaction(connection)
          // remarks='${refid}',
          // const $updatebanktxnid = `update ma_transaction_master set transaction_status='F',amount = '${amount}',transaction_reason = 'Insufficient balance' where aggregator_order_id='${orderId}'`
          // await this.rawQuery($updatebanktxnid, connection)
          /* RISK MANAGEMENT Changes */
          const data = {
            transaction_reason: 'Insufficient balance',
            amount,
            transaction_status: 'F'
          }

          const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
            data,
            id: orderId,
            where: 'aggregator_order_id'
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

          const $updatebanktxndeatailsid = `update ma_transaction_master_details set bank_rrn='${banktxnid}' where ma_transaction_master_id=${ma_transaction_master_id}`
          await this.rawQuery($updatebanktxndeatailsid, connection)

          const $updatepolicydetailsid = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id=${ma_transaction_master_id}`
          await this.rawQuery($updatepolicydetailsid, connection)

          await mySQLWrapper.commit(connection)

          return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], balance: 'NO', orderid: orderId }
        }

        const transaction_reason = 'Balance deducted ' + amount

        await mySQLWrapper.beginTransaction(connection)
        // update banktxnid and transaction status
        // remarks='${refid}',
        // const $updatebanktxnid = `update ma_transaction_master set transaction_status='P',amount = '${amount}',transaction_reason = '${transaction_reason}'  where aggregator_order_id='${orderId}'`
        // await this.rawQuery($updatebanktxnid, connection)
        /* RISK MANAGEMENT Changes */
        const data = {
          transaction_reason,
          amount,
          transaction_status: 'P'
        }

        const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
          data,
          id: orderId,
          where: 'aggregator_order_id'
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

        const $updatebanktxndeatailsid = `update ma_transaction_master_details set bank_rrn='${banktxnid}' where ma_transaction_master_id=${ma_transaction_master_id}`
        await this.rawQuery($updatebanktxndeatailsid, connection)

        const $updatepolicydetailsid = `update ma_user_policy_details set policy_payment_status = 'Y' where ma_transaction_master_id=${ma_transaction_master_id}`
        await this.rawQuery($updatepolicydetailsid, connection)

        var pointsDetailsEntries = {}
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
          ma_user_id: maUserId,
          amount: amount,
          transactionType: '1',
          connection: connection
        })
        if (pointsDetailsEntries.status === 400) {
          return pointsDetailsEntries
        }

        // this.TABLE_NAME = 'ma_points_ledger_master'
        // point leger entry
        const pointLedgerId = await pointsLedger.createEntry(_, {
          ma_user_id: maUserId,
          amount: amount,
          mode: 'dr',
          transaction_type: 15,
          description: util.insurancePointsDebitDescription,
          ma_status: 'S',
          orderid: orderId,
          userid: userId,
          corresponding_id: util.airpayUserId,
          connection
        })

        if (pointLedgerId.status === 400) {
          await mySQLWrapper.rollback(connection)
          return pointLedgerId
        }

        // pointsDetails entry
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry(_, {
            ma_user_id: maUserId,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: pointLedgerId.id,
            orderid: orderId,
            ma_status: 'S',
            connection: connection
          })
          if (entry.status === 400) {
            await mySQLWrapper.rollback(connection)
            return entry
          }
        }

        // airpay id credit entry

        const retailerLedgerId = await pointsLedger.createEntry(_, {
          ma_user_id: util.airpayUserId,
          amount: amount,
          mode: 'cr',
          transaction_type: 15,
          ma_status: 'S',
          userid: userId,
          description: util.insurancePointsCreditsDescription,
          orderid: orderId,
          corresponding_id: maUserId,
          connection
        })
        if (retailerLedgerId.status === 400) {
          await mySQLWrapper.rollback(connection)
          return retailerLedgerId
        }

        await mySQLWrapper.commit(connection)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], balance: 'YES', orderid: orderId }
      } else {
        const checksql = `SELECT ma_points_ledger_master_id from ma_points_ledger_master where ma_status in ('S') and  orderid='${orderId}' and mode = 'dr' limit 1 `
        const checkTxnDetails = await this.rawQuery(checksql, connection)

        log.logger({ pagename: 'insuranceController.js', action: 'premiumDeduction', type: 'checkTxnDetails', fields: checkTxnDetails })
        if (checkTxnDetails.length > 0) {
          return { status: 301, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Balanced Already Deducted', balance: 'YES', orderid: fields.orderid }
        }
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Transaction already updated', orderid: fields.orderid }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'premiumDeduction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection) connection.release()
    }
  }

  /**
 * @deprecated - created new method with named v2 & added new changes in version 2
 * @param {*} _
 * @param {*} req
 * @returns
 */
  static async policyConfirmation (_, req) {
    if (req.type == 1) {
      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'request', fields: req })

      var fields = req
    } else {
      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'request', fields: req.data })

      fields = JSON.parse(JSON.stringify(qs.parse(req.data)))
      // valiadation of rest api parameter

      var checksum = ''
      if (req.headers.checksum) {
        checksum = req.headers.checksum
      } else {
        checksum = req.headers.CHECKSUM
      }

      if (checksum == null || checksum == undefined || checksum == 0 || checksum == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "checksum can't empty" }
      }
    }

    if (fields.orderid == null || fields.orderid == undefined || fields.orderid == 0 || fields.orderid == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "orderId can't empty" }
    }

    if (fields.banktxnid == null || fields.banktxnid == undefined || fields.banktxnid == 0 || fields.banktxnid == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "banktxnid can't empty" }
    }

    if (fields.banktxntime == null || fields.banktxntime == undefined || fields.banktxntime == 0 || fields.banktxntime == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "banktxntime can't empty" }
    }

    if (fields.policystatus == null || fields.policystatus == undefined || fields.policystatus == 0 || fields.policystatus == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "policystatus can't empty" }
    }

    if (fields.policytype == null || fields.policytype == undefined || fields.policytype == 0 || fields.policytype == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "policytype can't empty" }
    }

    const res = isNaN(fields.amount)
    if (res) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Enter valid amount' }
    } else {
      if (fields.amount > 0 && fields.amount <= ********.99) {
      } else {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Enter amount greater than 0 and less than ********.99' }
      }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get codeDetails from ma_insurance_category_master master table
      var codesql = `SELECT * FROM ma_insurance_category_master WHERE category_code = '${fields.policytype}' limit 1`
      const codeDetails = await this.rawQuery(codesql, connection)

      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'codeDetailsData', fields: codeDetails })

      if (codeDetails.length <= 0) {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' code : ' + fields.policytype }
      }

      // get ma_userid from transaction master table
      var sql = `SELECT m.ma_transaction_master_id,m.transaction_status,m.ma_user_id ,m.userid, d.affiliated_id as affiliateid, d.company_id as companyid,remarks 
      from ma_transaction_master as m 
      JOIN ma_transaction_master_details as d on d.ma_transaction_master_id = m.ma_transaction_master_id  
      where m.aggregator_order_id='${fields.orderid}' order by m.ma_transaction_master_id desc limit 1`
      const getTxnDetails = await this.rawQuery(sql, connection)

      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'getTxnDetails', fields: getTxnDetails })

      if (getTxnDetails.length > 0 && codeDetails.length > 0) {
        var isSet = true
        const orderId = fields.orderid
        const amount = fields.amount
        const banktxnid = fields.banktxnid
        const banktxntime = fields.banktxntime
        const status = fields.policystatus
        const catId = fields.policytype
        const maUserId = getTxnDetails[0].ma_user_id
        const affiliateid = getTxnDetails[0].affiliateid
        const companyid = getTxnDetails[0].companyid
        const secretkey = util[process.env.NODE_ENV].secretkey
        const remarks = getTxnDetails[0].remarks + '-' + banktxntime
        const transactionStatus = getTxnDetails[0].transaction_status
        const maTransactionMasterId = getTxnDetails[0].ma_transaction_master_id
        const ma_insurance_category_master_id = codeDetails[0].ma_insurance_category_master_id
        const coi = fields.coi

        if (req.type != 1) {
          // checksum calculation
          // console.log('generatedChecksum>>', generatedChecksum)
          const checkSumString = orderId + affiliateid + amount + catId + companyid + secretkey
          console.log('checkSumString>>', checkSumString)

          const generatedChecksum = airpaychecksum.airpaychecksum(checkSumString)

          console.log('generatedChecksum>>', generatedChecksum)

          if (checksum != generatedChecksum) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'checksum not match', orderid: orderId }
          }
        }

        var checksql = `SELECT * from ma_points_ledger_master where ma_status in ('S') and  orderid='${orderId}' and mode = 'dr' limit 1 `
        const checkTxnDetails = await this.rawQuery(checksql, connection)

        log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'checkTxnDetails', fields: checkTxnDetails })
        if (checkTxnDetails.length > 0) {
          // check allready success entry
          if (transactionStatus == 'S' || transactionStatus == 'F') {
            return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013], orderid: orderId }
          }
          // update trasaction

          if (status == 'success' || status == 'SUCCESS') {
            // update pending status to success
            // const $updatesql = `update ma_points_ledger_master set ma_status='S' where orderid='${orderId}'`
            // await this.rawQuery($updatesql, connection)

            // update category and transaction_status
            // const $updateReconStaus = `update ma_transaction_master u  LEFT JOIN ma_insurance_category_master c ON c.category_code = '${catId}' set transaction_status='S',amount='${amount}', remarks ='${remarks}',aggregator_txn_id=c.ma_insurance_category_master_id where aggregator_order_id='${orderId}'`
            // await this.rawQuery($updateReconStaus, connection)

            // const $updateReconStaus = `update ma_transaction_master set transaction_status='S',amount='${amount}', remarks ='${remarks}',transaction_reason = CONCAT(transaction_reason,' ',' ${status} ') where ma_transaction_master_id='${maTransactionMasterId}'`
            // await this.rawQuery($updateReconStaus, connection)

            /* RISK MANAGEMENT Changes */
            const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderId}' LIMIT 1`
            const transactionData = await this.rawQuery(transactionsql, connection)

            if (transactionData.length == 0) {
              return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
            }

            const data = {
              remarks,
              transaction_reason: `${transactionData[0].transaction_reason} ${status}`,
              amount,
              transaction_status: 'S'
            }

            const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
              data,
              id: orderId,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            const $updateDetailsStaus = `update ma_transaction_master_details set category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)

            let coiUpdateStr = ''
            if (typeof (coi) != 'undefined' && coi != null && coi != '' && coi.length > 0) {
              coiUpdateStr = JSON.stringify({
                coi: coi
              })
              coiUpdateStr = ",policy_response = '" + coiUpdateStr + "'"
            }
            const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'PI' ${coiUpdateStr} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updatePolicyDetailsStaus, connection)

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'S', orderid: orderId }
          } else if (status == 'fail' || status == 'FAIL') {
            // reverse the entry

            // Check if integrated merchant
            /* const userSQL =  `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${getTxnDetails[0].userid}`
            const integratedMer = await this.rawQuery(userSQL, fields.connection)
            console.log(userSQL)
            if(integratedMer.length > 0  ) {
              fields.action = 'REFUND'
              fields.aggregator_user_id = integratedMer[0].aggregator_user_id
              fields.aggregator_order_id = fields.orderid
              fields.ma_user_id = getTxnDetails[0].ma_user_id
              const res = await integrated.index(fields,fields.connection)
              console.log('integrated returned this', res)
              if(res.status != 200) {
                return res
              }
            } */

            await mySQLWrapper.beginTransaction(connection)

            const pointLedgerEntryDebitDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'dr' AND transaction_type = '15' AND parent_id = '${orderId}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryDebitDataSql)
            const pointLedgerEntryDebitData = await this.rawQuery(pointLedgerEntryDebitDataSql, fields.connection)

            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'pointLedgerEntryDebitData', fields: pointLedgerEntryDebitData })

            const pointLedgerEntryRevDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'cr' AND transaction_type = '15' AND parent_id = '${orderId}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryRevDataSql)
            const pointLedgerEntryRevData = await this.rawQuery(pointLedgerEntryRevDataSql, fields.connection)

            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'pointLedgerEntryRevData', fields: pointLedgerEntryRevData })

            if (pointLedgerEntryRevData.length == 0 && pointLedgerEntryDebitData.length > 0) {
              console.log('Ledgers Reverses>>>>>')
              // point leger entry
              const pointLedgerId = await pointsLedger.createEntry(_, {
                ma_user_id: maUserId,
                amount: parseFloat(amount),
                mode: 'cr',
                transaction_type: 15,
                // description: 'Reversed - '+util.insurancePointsCreditsDescription,
                description: util.insurancePointsCreditsDescription + ' - Reversed',
                ma_status: 'REV',
                orderid: 'REV-' + orderId,
                userid: 1,
                corresponding_id: util.airpayUserId,
                connection
              })
              if (pointLedgerId.status === 400) {
                await mySQLWrapper.rollback(connection)
                return pointLedgerId
              }

              // airpay id credit entry

              const retailerLedgerId = await pointsLedger.createEntry(_, {
                ma_user_id: util.airpayUserId,
                amount: parseFloat(amount),
                mode: 'dr',
                transaction_type: 15,
                ma_status: 'REV',
                userid: 1,
                // description: 'Reversed - '+util.insurancePointsDebitDescription,
                description: util.insurancePointsDebitDescription + ' - Reversed',
                orderid: 'REV-' + orderId,
                corresponding_id: maUserId,
                connection
              })

              if (retailerLedgerId.status === 400) {
                await mySQLWrapper.rollback(connection)
                return retailerLedgerId
              }
            }

            // update pending status to failed
            // const $updatesql = `update ma_points_ledger_master set ma_status='S' where orderid='${orderId}'`
            // await this.rawQuery($updatesql, connection)

            // update category and transaction_status
            // const $updateReconStaus = `update ma_transaction_master set transaction_status='F',remarks ='${remarks}',transaction_reason = ,transaction_reason = CONCAT(transaction_reason,' ',' ${status} ') where aggregator_order_id='${orderId}'`
            // await this.rawQuery($updateReconStaus, connection)
            /* RISK MANAGEMENT Changes */
            const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderId}' LIMIT 1`
            const transactionData = await this.rawQuery(transactionsql, connection)

            if (transactionData.length == 0) {
              return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
            }

            const data = {
              remarks,
              transaction_reason: `${transactionData[0].transaction_reason} ${status}`,
              transaction_status: 'F'
            }

            const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
              data,
              id: orderId,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            const $updateDetailsStaus = `update ma_transaction_master_details set category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)

            const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updatePolicyDetailsStaus, connection)

            await mySQLWrapper.commit(connection)
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'F', orderid: orderId }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + 'Invalid status ie. success/fail', orderid: orderId }
          }
        } else {
          await mySQLWrapper.beginTransaction(connection)

          // const $updateReconStaus = `update ma_transaction_master set transaction_status='F',remarks ='${remarks}',transaction_reason = CONCAT_WS('-',transaction_reason, ' ',' Payment not found ') where ma_transaction_master_id='${maTransactionMasterId}'`
          // await this.rawQuery($updateReconStaus, connection)
          /* RISK MANAGEMENT Changes */
          const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderId}' LIMIT 1`
          const transactionData = await this.rawQuery(transactionsql, connection)

          if (transactionData.length == 0) {
            return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
          }

          const data = {
            remarks,
            transaction_reason: `${transactionData[0].transaction_reason} - Payment not found `,
            transaction_status: 'F'
          }

          const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
            data,
            id: maTransactionMasterId,
            where: 'ma_transaction_master_id'
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

          if (ma_insurance_category_master_id > 0) {
            const $updateDetailsStaus = `update ma_transaction_master_details set category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)
          }

          const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id='${maTransactionMasterId}'`
          await this.rawQuery($updatePolicyDetailsStaus, connection)

          await mySQLWrapper.commit(connection)
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'F', orderid: orderId }
          // return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "trasaction can't found" , orderid : orderId}
        }
      } else {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' orderid : ' + fields.orderid }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'policyConfirmation', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection) connection.release()
    }
  }

  /**
   * Created new method & named with v2(Version 2)
   * policyConfirmation_v2 description - update the policy details
   * @param {null} _
   * @param {{ orderid: string, banktxnid: number, banktxntime: string, policystatus: string, policytype: string, amount: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, transaction_status: string, orderid : string}>}
   */
  static async policyConfirmation_v2 (_, req) {
    if (req.type == 1) {
      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'request', fields: req })

      var fields = req
    } else {
      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'request', fields: req.data })

      fields = JSON.parse(JSON.stringify(qs.parse(req.data)))
      // valiadation of rest api parameter

      var checksum = ''
      if (req.headers.checksum) {
        checksum = req.headers.checksum
      } else {
        checksum = req.headers.CHECKSUM
      }

      if (checksum == null || checksum == undefined || checksum == 0 || checksum == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "checksum can't empty" }
      }
    }

    if (fields.orderid == null || fields.orderid == undefined || fields.orderid == 0 || fields.orderid == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "orderId can't empty" }
    }

    if (fields.banktxnid == null || fields.banktxnid == undefined || fields.banktxnid == 0 || fields.banktxnid == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "banktxnid can't empty" }
    }

    if (fields.banktxntime == null || fields.banktxntime == undefined || fields.banktxntime == 0 || fields.banktxntime == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "banktxntime can't empty" }
    }

    if (fields.policystatus == null || fields.policystatus == undefined || fields.policystatus == 0 || fields.policystatus == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "policystatus can't empty" }
    }

    if (fields.policytype == null || fields.policytype == undefined || fields.policytype == 0 || fields.policytype == '') {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "policytype can't empty" }
    }

    const res = isNaN(fields.amount)
    if (res) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Enter valid amount' }
    } else {
      if (fields.amount > 0 && fields.amount <= ********.99) {
      } else {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'Enter amount greater than 0 and less than ********.99' }
      }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get codeDetails from ma_insurance_category_master master table
      var codesql = `SELECT * FROM ma_insurance_category_master WHERE category_code = '${fields.policytype}' limit 1`
      const codeDetails = await this.rawQuery(codesql, connection)

      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'codeDetailsData', fields: codeDetails })

      if (codeDetails.length <= 0) {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' code : ' + fields.policytype }
      }

      // get ma_userid from transaction master table
      var sql = `SELECT m.ma_transaction_master_id,m.transaction_type,m.transaction_status,m.ma_user_id ,m.userid,d.provider_name,
      m.mobile_number,m.amount,m.updatedon, d.affiliated_id as affiliateid, d.company_id as companyid,remarks,d.form_data 
      from ma_transaction_master as m 
      JOIN ma_transaction_master_details as d on d.ma_transaction_master_id = m.ma_transaction_master_id  
      where m.aggregator_order_id='${fields.orderid}' order by m.ma_transaction_master_id desc limit 1`
      const getTxnDetails = await this.rawQuery(sql, connection)

      log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'getTxnDetails', fields: getTxnDetails })

      if (getTxnDetails.length > 0 && codeDetails.length > 0) {
        const orderId = fields.orderid
        const amount = fields.amount
        const banktxntime = fields.banktxntime
        const status = fields.policystatus
        const catId = fields.policytype
        const maUserId = getTxnDetails[0].ma_user_id
        const userId = getTxnDetails[0].userid
        const affiliateid = getTxnDetails[0].affiliateid
        const companyid = getTxnDetails[0].companyid
        const secretkey = util[process.env.NODE_ENV].secretkey
        const remarks = getTxnDetails[0].remarks + '-' + banktxntime
        const transactionStatus = getTxnDetails[0].transaction_status
        const transactionType = getTxnDetails[0].transaction_type
        const maTransactionMasterId = getTxnDetails[0].ma_transaction_master_id
        const ma_insurance_category_master_id = codeDetails[0].ma_insurance_category_master_id
        const coi = fields.coi
        const policy_data = this.parseJson(getTxnDetails[0].form_data) || {}

        if (req.type != 1) {
          // checksum calculation
          // console.log('generatedChecksum>>', generatedChecksum)
          const checkSumString = orderId + affiliateid + amount + catId + companyid + secretkey
          console.log('checkSumString>>', checkSumString)

          const generatedChecksum = airpaychecksum.airpaychecksum(checkSumString)

          console.log('generatedChecksum>>', generatedChecksum)

          if (checksum != generatedChecksum) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + 'checksum not match', orderid: orderId }
          }
        }

        var checksql = `SELECT * from ma_points_ledger_master where ma_status in ('S') and  orderid='${orderId}' and mode = 'dr' limit 1 `
        const checkTxnDetails = await this.rawQuery(checksql, connection)

        log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'checkTxnDetails', fields: checkTxnDetails })
        if (checkTxnDetails.length > 0) {
          // check allready success entry
          if (transactionStatus == 'S' || transactionStatus == 'F') {
            return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013], orderid: orderId }
          }
          // update trasaction

          /* NEW CHANGES : LEIN BALANCE CHECK */
          const isTransactionAmountAboveLienBalanceResp = await lienBalanceController.isTransactionAmountAboveLienBalance({
            ma_user_id: maUserId,
            userid: userId,
            transactionType: transactionType,
            transactionAmount: parseFloat(fields.amount) + parseFloat(fields.commission_amount || 0),
            connectionRead: connection
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionAmountAboveLienBalance', type: 'response', fields: isTransactionAmountAboveLienBalanceResp })
          if (isTransactionAmountAboveLienBalanceResp.status != 200) {
            await mySQLWrapper.beginTransaction(connection)

            const pointLedgerEntryDebitDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'dr' AND transaction_type = '15' AND parent_id = '${orderId}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryDebitDataSql)
            const pointLedgerEntryDebitData = await this.rawQuery(pointLedgerEntryDebitDataSql, connection)

            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'pointLedgerEntryDebitData', fields: pointLedgerEntryDebitData })

            const pointLedgerEntryRevDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'cr' AND transaction_type = '15' AND parent_id = '${orderId}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryRevDataSql)
            const pointLedgerEntryRevData = await this.rawQuery(pointLedgerEntryRevDataSql, connection)

            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'pointLedgerEntryRevData', fields: pointLedgerEntryRevData })

            if (pointLedgerEntryRevData.length == 0 && pointLedgerEntryDebitData.length > 0) {
              console.log('Ledgers Reverses>>>>>')
              // point leger entry
              const pointLedgerId = await pointsLedger.createEntry(_, {
                ma_user_id: maUserId,
                amount: parseFloat(amount),
                mode: 'cr',
                transaction_type: 15,
                // description: 'Reversed - '+util.insurancePointsCreditsDescription,
                description: util.insurancePointsCreditsDescription + ' - Reversed',
                ma_status: 'REV',
                orderid: 'REV-' + orderId,
                userid: 1,
                corresponding_id: util.airpayUserId,
                connection
              })
              if (pointLedgerId.status === 400) {
                await mySQLWrapper.rollback(connection)
                return pointLedgerId
              }

              // airpay id credit entry

              const retailerLedgerId = await pointsLedger.createEntry(_, {
                ma_user_id: util.airpayUserId,
                amount: parseFloat(amount),
                mode: 'dr',
                transaction_type: 15,
                ma_status: 'REV',
                userid: 1,
                // description: 'Reversed - '+util.insurancePointsDebitDescription,
                description: util.insurancePointsDebitDescription + ' - Reversed',
                orderid: 'REV-' + orderId,
                corresponding_id: maUserId,
                connection
              })

              if (retailerLedgerId.status === 400) {
                await mySQLWrapper.rollback(connection)
                return retailerLedgerId
              }
            }
            const data = {
              transaction_reason: isTransactionAmountAboveLienBalanceResp.message,
              transaction_status: 'F'
            }

            const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
              data,
              id: orderId,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            let cust_details = ''
            if (fields.customer_name) {
              cust_details = ` customer_name = '${fields.customer_name}',`
            }
            if (fields.customer_mobile_number) {
              cust_details += ` customer_mobile = '${fields.customer_mobile_number}',`
            }
            if (fields.customer_email_id) {
              cust_details += ` customer_email = '${fields.customer_email_id}',`
            }

            const $updateDetailsStaus = `update ma_transaction_master_details set ${cust_details} category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)

            const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updatePolicyDetailsStaus, connection)

            await mySQLWrapper.commit(connection)
            return { status: 200, respcode: 1000, message: isTransactionAmountAboveLienBalanceResp.message, transaction_status: 'F', orderid: orderId }
          }
          /* NEW CHANGES : LEIN BALANCE CHECK */

          if (status == 'success' || status == 'SUCCESS') {
            // remarks ='${remarks}',
            // const $updateReconStaus = `update ma_transaction_master set transaction_status='S',amount='${amount}', transaction_reason = CONCAT(transaction_reason,' ',' ${status} ') where ma_transaction_master_id='${maTransactionMasterId}'`
            // await this.rawQuery($updateReconStaus, connection)
            /* RISK MANAGEMENT Changes */
            const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderId}' LIMIT 1`
            const transactionData = await this.rawQuery(transactionsql, connection)

            if (transactionData.length == 0) {
              return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
            }

            const data = {
              transaction_reason: `${transactionData[0].transaction_reason} ${status}`,
              transaction_status: 'S',
              amount
            }

            const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
              data,
              id: maTransactionMasterId,
              where: 'ma_transaction_master_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            let cust_details = ''
            if (fields.customer_name) {
              cust_details = ` customer_name = '${fields.customer_name}',`
            }
            if (fields.customer_mobile_number) {
              cust_details += ` customer_mobile = '${fields.customer_mobile_number}',`
            }
            if (fields.customer_email_id) {
              cust_details += ` customer_email = '${fields.customer_email_id}',`
            }

            if (fields.policy_no) {
              policy_data.policy_no = fields.policy_no
            }

            if (fields.policy_name) {
              policy_data.policy_name = fields.policy_name
            }

            const $updateDetailsStaus = `update ma_transaction_master_details set ${cust_details} category_master_id = ${ma_insurance_category_master_id}, form_data='${JSON.stringify(policy_data)}' where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)

            let coiUpdateStr = ''
            if (typeof (coi) != 'undefined' && coi != null && coi != '' && coi.length > 0) {
              coiUpdateStr = JSON.stringify({
                coi: coi
              })
              coiUpdateStr = ",policy_response = '" + coiUpdateStr + "'"
            }
            const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'PI' ${coiUpdateStr} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updatePolicyDetailsStaus, connection)

            // Send SMS to Merchant & Customer
            const sms_queue = []
            if (fields.policy_name) {
              let billTxnDetail = {}
              let merchant_message = ''
              let merchant_templateid = ''

              merchant_message = configIns.communication.MerchantInsuranceSuccess
              merchant_templateid = configIns.templateid.MerchantInsuranceSuccess

              billTxnDetail = getTxnDetails[0]

              const dt = new Date(billTxnDetail.updatedon)
              const datetimeformat = dt.toLocaleString('en-IN')
              merchant_message = merchant_message.replace('<policy name>', fields.policy_name)
              merchant_message = merchant_message.replace('<amount>', fields.amount)
              merchant_message = merchant_message.replace('<date time>', datetimeformat)

              sms_queue.push(sms.sentSmsAsync(merchant_message, billTxnDetail.mobile_number, merchant_templateid))
              if (fields.customer_mobile_number) {
                let cust_message = ''
                let cust_templateid = ''

                cust_message = configIns.communication.CustomerInsuranceSuccess
                cust_templateid = configIns.templateid.CustomerInsuranceSuccess

                cust_message = cust_message.replace('<policy name>', fields.policy_name)
                cust_message = cust_message.replace('<amount>', fields.amount)
                cust_message = cust_message.replace('<date>', moment(billTxnDetail.updatedon, 'YYYY-MM-DD hh:mm:ss').format('DD/MM/YYYY'))

                sms_queue.push(sms.sentSmsAsync(cust_message, fields.customer_mobile_number, cust_templateid))
              }
            }

            const sms_response = await Promise.all(sms_queue)
            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'sent_sms_response', fields: sms_response })

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'S', orderid: orderId }
          } else if (status == 'fail' || status == 'FAIL') {
            // reverse the entry

            await mySQLWrapper.beginTransaction(connection)

            const pointLedgerEntryDebitDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'dr' AND transaction_type = '15' AND parent_id = '${orderId}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryDebitDataSql)
            const pointLedgerEntryDebitData = await this.rawQuery(pointLedgerEntryDebitDataSql, fields.connection)

            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'pointLedgerEntryDebitData', fields: pointLedgerEntryDebitData })

            const pointLedgerEntryRevDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'cr' AND transaction_type = '15' AND parent_id = '${orderId}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryRevDataSql)
            const pointLedgerEntryRevData = await this.rawQuery(pointLedgerEntryRevDataSql, fields.connection)

            log.logger({ pagename: 'insuranceController.js', action: 'policyConfirmation', type: 'pointLedgerEntryRevData', fields: pointLedgerEntryRevData })

            if (pointLedgerEntryRevData.length == 0 && pointLedgerEntryDebitData.length > 0) {
              console.log('Ledgers Reverses>>>>>')
              // point leger entry
              const pointLedgerId = await pointsLedger.createEntry(_, {
                ma_user_id: maUserId,
                amount: parseFloat(amount),
                mode: 'cr',
                transaction_type: 15,
                // description: 'Reversed - '+util.insurancePointsCreditsDescription,
                description: util.insurancePointsCreditsDescription + ' - Reversed',
                ma_status: 'REV',
                orderid: 'REV-' + orderId,
                userid: 1,
                corresponding_id: util.airpayUserId,
                connection
              })
              if (pointLedgerId.status === 400) {
                await mySQLWrapper.rollback(connection)
                return pointLedgerId
              }

              // airpay id credit entry

              const retailerLedgerId = await pointsLedger.createEntry(_, {
                ma_user_id: util.airpayUserId,
                amount: parseFloat(amount),
                mode: 'dr',
                transaction_type: 15,
                ma_status: 'REV',
                userid: 1,
                // description: 'Reversed - '+util.insurancePointsDebitDescription,
                description: util.insurancePointsDebitDescription + ' - Reversed',
                orderid: 'REV-' + orderId,
                corresponding_id: maUserId,
                connection
              })

              if (retailerLedgerId.status === 400) {
                await mySQLWrapper.rollback(connection)
                return retailerLedgerId
              }
            }

            // update pending status to failed
            // const $updatesql = `update ma_points_ledger_master set ma_status='S' where orderid='${orderId}'`
            // await this.rawQuery($updatesql, connection)

            // update category and transaction_status
            // remarks ='${remarks}',
            // const $updateReconStaus = `update ma_transaction_master set transaction_status='F',transaction_reason = CONCAT(transaction_reason,' ',' ${status} ') where aggregator_order_id='${orderId}'`
            // await this.rawQuery($updateReconStaus, connection)
            /* RISK MANAGEMENT Changes */
            const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderId}' LIMIT 1`
            const transactionData = await this.rawQuery(transactionsql, connection)

            if (transactionData.length == 0) {
              return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
            }

            const data = {
              transaction_reason: `${transactionData[0].transaction_reason} ${status}`,
              transaction_status: 'F'
            }

            const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
              data,
              id: orderId,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            let cust_details = ''
            if (fields.customer_name) {
              cust_details = ` customer_name = '${fields.customer_name}',`
            }
            if (fields.customer_mobile_number) {
              cust_details += ` customer_mobile = '${fields.customer_mobile_number}',`
            }
            if (fields.customer_email_id) {
              cust_details += ` customer_email = '${fields.customer_email_id}',`
            }

            const $updateDetailsStaus = `update ma_transaction_master_details set ${cust_details} category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)

            const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updatePolicyDetailsStaus, connection)

            await mySQLWrapper.commit(connection)
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'F', orderid: orderId }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + 'Invalid status ie. success/fail', orderid: orderId }
          }
        } else {
          await mySQLWrapper.beginTransaction(connection)
          // remarks ='${remarks}',
          // const $updateReconStaus = `update ma_transaction_master set transaction_status='F',transaction_reason = CONCAT_WS('-',transaction_reason, ' ',' Payment not found ') where ma_transaction_master_id='${maTransactionMasterId}'`
          // await this.rawQuery($updateReconStaus, connection)
          /* RISK MANAGEMENT Changes */
          const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderId}' LIMIT 1`
          const transactionData = await this.rawQuery(transactionsql, connection)

          if (transactionData.length == 0) {
            await mySQLWrapper.rollback(connection)
            return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
          }

          const data = {
            transaction_reason: `${transactionData[0].transaction_reason} - Payment not found`,
            transaction_status: 'F'
          }

          const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
            data,
            id: maTransactionMasterId,
            where: 'ma_transaction_master_id'
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

          if (ma_insurance_category_master_id > 0) {
            const $updateDetailsStaus = `update ma_transaction_master_details set category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updateDetailsStaus, connection)
          }

          const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id='${maTransactionMasterId}'`
          await this.rawQuery($updatePolicyDetailsStaus, connection)

          await mySQLWrapper.commit(connection)
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], transaction_status: 'F', orderid: orderId }
          // return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "trasaction can't found" , orderid : orderId}
        }
      } else {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] + ' orderid : ' + fields.orderid }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'policyConfirmation', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection) connection.release()
    }
  }

  static async orderVerification (_, fields) {
    log.logger({ pagename: 'insuranceController.js', action: 'orderVerification', type: 'request', fields: fields })

    var isSet = true
    const connection = await mySQLWrapper.getConnectionFromReadReplica()

    try {
      var sql = `SELECT
      m.aggregator_order_id ,
      d.affiliated_id,
      d.company_id,
      m.remarks,
      m.ma_transaction_master_id,
      m.addedon
    FROM
      ma_transaction_master as m
    JOIN ma_transaction_master_details as d ON
      d.ma_transaction_master_id = m.ma_transaction_master_id
    JOIN ma_insurance_category_master as micm on
      micm.category_name = d.provider_name
    WHERE
      m.transaction_type = '15'
      and micm.riskcovry_sachet = 'false'
      AND ((m.transaction_status = 'P'
        AND TIMESTAMPDIFF(MINUTE,
        m.updatedon,
        CURRENT_TIMESTAMP) >= 30)
        OR (m.transaction_status = 'I'
          AND TIMESTAMPDIFF(MINUTE,
          m.updatedon,
          CURRENT_TIMESTAMP) >= 30))
    order by
      addedon DESC
    limit 100;`
      const getTxnDetails = await this.rawQuery(sql, connection)
      // Release connection after fetching data

      console.log('No of Record found >> ', getTxnDetails.length)
      if (getTxnDetails.length > 0) {
        var conn = await mySQLWrapper.getConnectionFromPool()
        for (const value of getTxnDetails) {
          console.log('Current Record >> ', value)

          const orderid = value.aggregator_order_id
          const affiliateid = value.affiliated_id
          const companyid = value.company_id
          const maTransactionMasterId = value.ma_transaction_master_id

          const airpayKey = util[process.env.NODE_ENV].airpayKey
          const secretkey = util[process.env.NODE_ENV].secretkey
          const affkey = util[process.env.NODE_ENV].affkey

          const generatedChecksum = airpaychecksum.airpaychecksum(orderid + affiliateid + companyid + secretkey)

          const postdata = 'orderid=' + orderid + '&affiliateid=' + affiliateid + '&companyid=' + companyid
          const config = {

            method: 'POST',
            url: util[process.env.NODE_ENV].verifyorderins,
            headers: {
              Accept: 'text/plain',
              'Content-Type': 'application/x-www-form-urlencoded',
              AFFKEY: affkey,
              AIRPAYKEY: airpayKey,
              CHECKSUM: generatedChecksum
            },
            data: postdata
          }

          console.log('VerifyOrderPostParameter>> ', config)

          console.time('TIMER_INSURANCE_VERIFY_ORDER_' + orderid)
          const res = await axios(config)
          console.timeEnd('TIMER_INSURANCE_VERIFY_ORDER_' + orderid)

          if (res.status !== 200) {
            console.log('Server ERROR ' + res.status)
            log.logger({
              pagename: require('path').basename(__filename),
              action: 'createOrderAtMS',
              type: 'catcherror',
              fields: {
                servererror: 500
              }
            })
            continue
          }
          console.log('VERIFY_ORDER_' + orderid + '_' + JSON.stringify(postdata) + '_' + JSON.stringify(res.data))

          console.log('VerifyOrderPostResponse >> ', typeof (res.data), '||', res.data)

          // res.data.orderidR = orderid
          log.logger({ pagename: 'insuranceController.js', action: 'orderVerificationRes', type: 'verify_order_response', fields: res.data })

          if (res.data.status == '200' && res.data.message == 'SUCCESS') {
            log.logger({ pagename: 'insuranceController.js', action: 'orderVerificationRes', type: 'RESPONSE_SUCCESS', fields: { orderid } })
            // transaction inita
            const polycyorderid = res.data.data.morderid
            const polycyamount = res.data.data.amount
            const banktxnid = res.data.data.referenceid
            const banktxntime = res.data.data.banktxntime
            const policystatus = res.data.data.status
            const policytype = res.data.data.policy_type
            const coi = res.data.data.coi ? res.data.data.coi : ''

            const transResult = await this.policyConfirmation_v2(_, {
              orderid: polycyorderid,
              amount: polycyamount,
              banktxnid: banktxnid,
              banktxntime: banktxntime,
              policystatus: policystatus,
              policytype: policytype,
              policy_name: res.data.data.policy_name || '',
              customer_name: res.data.data.customer_name || '',
              customer_mobile_number: res.data.data.customer_mobile_number || '',
              customer_email_id: res.data.data.customer_email_id || '',
              policy_no: res.data.data.policy_no || '',
              type: 1,
              coi: coi
            })

            log.logger({ pagename: 'insuranceController.js', action: 'orderVerificationPolicyRes', type: 'response', fields: transResult })
          } else if (res.data.status == '400' && res.data.message && res.data.message.indexOf('Details Not Found') > -1) {
            // transaction inita
            log.logger({ pagename: 'insuranceController.js', action: 'orderVerificationRes', type: 'Detail Not Found', fields: 'Detail Not Found ' + orderid })
            await mySQLWrapper.beginTransaction(conn)

            const remarks = value.remarks + '-' + res.data.message

            // const $updateReconStaus = `update ma_transaction_master set transaction_status='F',remarks ='${remarks}',transaction_reason = CONCAT_WS('-',transaction_reason, '400','${res.data.message} ') where ma_transaction_master_id='${maTransactionMasterId}'`
            // await this.rawQuery($updateReconStaus, conn)
            /* RISK MANAGEMENT Changes */
            const transactionsql = `SELECT transaction_reason FROM ma_transaction_master WHERE aggregator_order_id = '${orderid}' LIMIT 1`
            const transactionData = await this.rawQuery(transactionsql, connection)

            if (transactionData.length == 0) {
              await mySQLWrapper.rollback(connection)
              return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
            }

            const data = {
              remarks,
              transaction_reason: `${transactionData[0].transaction_reason}-400-${res.data.message}`,
              transaction_status: 'F'
            }

            const updateTransactionResult = await transactionMaster.updateWhereData(connection, {
              data,
              id: maTransactionMasterId,
              where: 'ma_transaction_master_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            // if (ma_insurance_category_master_id > 0) {
            //   const $updateDetailsStaus = `update ma_transaction_master_details set category_master_id = ${ma_insurance_category_master_id} where ma_transaction_master_id='${maTransactionMasterId}'`
            //   await this.rawQuery($updateDetailsStaus, connection)
            // }

            const $updatePolicyDetailsStaus = `update ma_user_policy_details set policy_status = 'F' where ma_transaction_master_id='${maTransactionMasterId}'`
            await this.rawQuery($updatePolicyDetailsStaus, conn)

            await mySQLWrapper.commit(conn)

            // log.logger({ pagename: 'insuranceController.js', action: 'orderVerificationPolicyRes', type: 'response', fields: transResult })
          }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'orderVerification', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      conn.release()
      if (isSet) connection.release()
    }
  }

  static async updateInsRecon (_, fields) {
    /* log.logger({ pagename: 'insuranceController.js', action: 'updateInsRecon', type: 'request', fields: fields }) */

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // check allready incentive update or not
      var checkIncentive = `SELECT ma_monthly_incentives_id FROM ma_monthly_incentives WHERE 
        ma_from_date >= DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01') AND ma_to_date >= DATE_FORMAT(NOW() ,'%Y-%m-01') ORDER BY ma_monthly_incentives_id DESC LIMIT 1 `

      const getIncentiveDetails = await this.rawQuery(checkIncentive, connection)
      if (getIncentiveDetails.length > 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Incentive allready done.' }
      }

      var sql = `SELECT ma_user_id,aggregator_txn_id AS catId,bank_rrn AS subCatId,userid,aggregator_order_id AS orderId,COUNT(ma_transaction_master_id) AS transaction_count ,SUM(amount) AS amount,transaction_status,transaction_type, DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01') AS from_date,DATE_FORMAT(NOW() ,'%Y-%m-01') AS to_date FROM ma_transaction_master WHERE addedon >= DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01') AND addedon < DATE_FORMAT(NOW() ,'%Y-%m-01') AND transaction_type = '15' AND  recon_status = 'Y' AND transaction_status = 'S' 
        GROUP BY ma_user_id,aggregator_txn_id ,bank_rrn`

      const getTxnDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'insuranceController.js', action: 'updateInsRecon', type: 'response', fields: getTxnDetails })
      var resp
      if (getTxnDetails.length > 0) {
        for (const value of getTxnDetails) {
          /* // get  amount wise subCategory
                if(value.subCatId == '999'){

                 const checkSubcatId = `select ma_insurance_subcategory_master_id FROM ma_insurance_subcategory_master WHERE ma_insurance_category_master_id = ${value.catId} AND min_amount<=${value.amount} AND max_amount>=${value.amount}`

                  console.log(checkSubcatId)
                  const getSubCatId = await this.rawQuery(checkSubcatId, connection)
                    if (getSubCatId.length > 0) {

                      value.subCatId = getSubCatId[0].ma_insurance_subcategory_master_id
                    }
                    else{

                        log.logger({ pagename: 'insuranceController.js', action: 'updateInsRecon', type: 'getSubCatId', fields: checkSubcatId })
                    }
                } */

          // Process transaction for settlement
          fields.userid = value.userid
          var providedtxnType = value.transaction_type
          if (value.transaction_status === 'S') {
            value.connection = connection
            resp = await this.calculateIncentive(_, value)
            // resp.transaction_status = value.transaction_status
          } else {
            resp = { status: 400, respcode: 1016, message: errorMsg.responseCode[1016], transaction_status: getTxnDetails[0].transaction_status }
          }

          /* var newsql = `SELECT * from ma_transaction_master where aggregator_order_id='${fields.orderId}'`
            //console.log('query', newsql)
            const getLatestStatus = await this.rawQuery(newsql,connection)
            //console.log('getLatest', getLatestStatus)
            resp.transaction_status = getLatestStatus[0].transaction_status */
          // return resp
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        /* } else {
          return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015], transaction_status: getTxnDetails[0].transaction_status }
        } */
      } else {
        return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateInsRecon', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async calculateIncentive (_, fields) {
    log.logger({ pagename: 'insuranceController.js', action: 'calculateIncentive', type: 'request', fields: fields })
    // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    var isSet = false
    var connection = fields.connection
    try {
      const executionResponse = {}
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // await mySQLWrapper.beginTransaction(connection)
      // PAN DECRYPTION
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      const sql = `Select profileid,userid,distributer_user_master_id,user_type,state,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan from ma_user_master where profileid=${fields.ma_user_id} AND user_status = "Y"`
      // const sql = 'Select profileid,userid,distributer_user_master_id,user_type,state,pan from ma_user_master where profileid=' + fields.ma_user_id + ' AND user_status = "Y"'
      const _user = await this.rawQuery(sql, connection)
      if (_user.length > 0) {
        const incfields = {

          amount: fields.amount,
          ma_user_id: _user[0].profileid,
          orderid: fields.orderId,
          userid: _user[0].userid,
          user_type: _user[0].user_type,
          distributer_user_master_id: _user[0].distributer_user_master_id,
          stateid: _user[0].state,
          pan: _user[0].pan,
          catId: fields.catId,
          transaction_type: fields.transaction_type,
          subCatId: fields.subCatId,
          transaction_count: fields.transaction_count,
          from_date: fields.from_date,
          to_date: fields.to_date,
          connection
        }
        const response = await insuranceController.incentiveDistribution(incfields)
        if (response.status === 200) {
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
        } else {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] }
        }
        // return  response
        // return response
      } else {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + "ma user can't find " }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculateIncentive', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release connection
      if (isSet) {
        connection.release()
      }
    }
  }

  /**
     * Creates a new transaction
     */
  static async createInsTransaction (_, fields, connection = null) {
    log.logger({ pagename: 'insuranceController.js', action: 'createTransaction', type: 'request', fields: fields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      /* // Check if User exists or not
      var sql = `select ma_user_master_id from ma_user_master where profileid=${fields.ma_user_id} AND userid=${fields.userid}`
      const userDetails = await this.rawQuery(sql, connection)
      console.log('length = ',userDetails);
      if (userDetails.length <= 0) {
        return { status: 400, message: 'Invalid user' }
      } */

      const _result = await this.insert(connection, {
        data: fields
      })
      log.logger({ pagename: 'insuranceController.js', action: 'createTransaction', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        // Create transaction details
        const detailsData = {
          ma_transaction_master_id: _result.insertId,
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          bank_name: 'GIBL'
        }
        await this.createInsTransactionDetails(detailsData, connection)

        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transaction_id: _result.insertId }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028], respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  static async createInsTransactionDetails (fields, connection) {
    log.logger({ pagename: 'insuranceController.js', action: 'createInsTransactionDetails', type: 'request', fields: fields })

    try {
      const sql = `INSERT INTO ma_transaction_master_details 
                  ( ma_transaction_master_id, ma_user_id, userid, bank_name )
                  VALUE
                  ( '${fields.ma_transaction_master_id}', 
                  '${fields.ma_user_id}', 
                  '${fields.userid}', 
                  ${(fields.bank_name) ? `'${fields.bank_name}'` : null}
                  )`
      const _result = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'insuranceController.js', action: 'createInsTransactionDetails', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transaction_id: _result.insertId }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028], respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createInsTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, insurancelist: {category_code: string, category_name: string, provider_id: number, category_id: string, app_icon: string, web_icon: string }[] }>}
   */
  static async getInsuranceList (_, args) {
    log.logger({ pagename: 'insuranceController.js', action: 'getInsuranceList', type: 'request', fields: args })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      let sqlInsurance
      // check if riskcovry sachet channel is assigned or not.
      const checkChannel = await LombardInsuranceController.checkUserChannel(args.ma_user_id, connection)
      if (checkChannel == '') {
        sqlInsurance = 'SELECT category_code, category_name, provider_id, category_id, app_icon, web_icon, riskcovry_sachet, expiry_period, amount, policy_name,redirection_flag FROM ma_insurance_category_master WHERE category_flag = "1" and riskcovry_sachet = "false"'
      } else {
        sqlInsurance = 'SELECT category_code, category_name, provider_id, category_id, app_icon, web_icon, riskcovry_sachet, expiry_period, amount, policy_name,redirection_flag FROM ma_insurance_category_master WHERE category_flag = "1"'
      }
      log.logger({ pagename: 'insuranceController.js', action: 'getInsuranceList', type: 'sqlInsurance', fields: sqlInsurance })
      const insuranceDetails = await this.rawQuery(sqlInsurance, connection)
      log.logger({ pagename: 'insuranceController.js', action: 'getInsuranceList', type: 'insuranceresponse', fields: insuranceDetails })
      const insurance_arr = []
      if (insuranceDetails && insuranceDetails.length > 0) {
        for (let p = 0; p < insuranceDetails.length; p++) {
          insurance_arr.push({
            category_code: insuranceDetails[p].category_code,
            category_name: insuranceDetails[p].category_name,
            provider_id: insuranceDetails[p].provider_id,
            category_id: insuranceDetails[p].category_id,
            app_icon: insuranceDetails[p].app_icon ? insuranceDetails[p].app_icon : '',
            web_icon: insuranceDetails[p].web_icon ? insuranceDetails[p].web_icon : '',
            redirection: insuranceDetails[p].redirection_flag == 'true',
            type: insuranceDetails[p].riskcovry_sachet == 'true' ? 'riskcovry_sachet' : 'lombard_sachet',
            amount: insuranceDetails[p].amount ? insuranceDetails[p].amount : null,
            expiry_period: insuranceDetails[p].expiry_period ? insuranceDetails[p].expiry_period : null,
            description: insuranceDetails[p].riskcovry_sachet == 'true' ? `Buy ${insuranceDetails[p].policy_name} for a validity of ${insuranceDetails[p].expiry_period} days at a premium of Rs.${insuranceDetails[p].amount}` : `Buy ${insuranceDetails[p].category_name.replace(/_/g, ' ')} for a validity of ${insuranceDetails[p].expiry_period} days at a premium of Rs.${insuranceDetails[p].amount}`,
            policy_name: insuranceDetails[p].riskcovry_sachet == 'true' ? insuranceDetails[p].policy_name : insuranceDetails[p].category_name.replace(/_/g, ' '),
            validity: `${insuranceDetails[p].expiry_period} days.`
          })
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, insurancelist: insurance_arr }
      } else {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, insurancelist: insurance_arr }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getInsuranceList', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /**
   * @param {String} provider_name
   * @param {Connection Object} con
   * @returns {String}
   */
  static async getInsuranceSubCategoryCode (provider_name, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getInsuranceSubCategoryCode', type: 'request', fields: { provider_name: provider_name } })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // get subcategory code
      const sqlCh = `select subcategory_code from ma_insurance_subcategory_master mism join ma_insurance_category_master micm 
      where mism.ma_insurance_category_master_id = micm.ma_insurance_category_master_id 
      and micm.category_name = '${provider_name}'`

      const subcategoryDetails = await this.rawQuery(sqlCh, connection)
      let subcategory_code = null

      if (subcategoryDetails.length > 0) {
        subcategory_code = subcategoryDetails[0].subcategory_code
        log.logger({ pagename: require('path').basename(__filename), action: 'getInsuranceSubCategoryCode', type: 'response', fields: subcategoryDetails })
      }
      return { status: 400, message: errorMsg.responseCode[1000], respcode: 1000, category_code: subcategory_code }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getInsuranceSubCategoryCode', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, category_code: null }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   *
   * @param {JSON Object} data
   * @returns {parse json}
   */
  static parseJson (data) {
    try {
      return JSON.parse(data)
    } catch (error) {
      return {}
    }
  }
}

module.exports = insurance
