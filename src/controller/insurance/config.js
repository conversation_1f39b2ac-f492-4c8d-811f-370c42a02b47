module.exports = {
  insuranceURL: process.env.NODE_ENV === 'production' ? 'https://insurance.airpay.co.in/' : 'https://insurance.airpay.ninja/',
  affiliateid: process.env.NODE_ENV === 'production' ? '4' : '13', // to do
  companyid: process.env.NODE_ENV === 'production' ? '6' : '11', // to do
  defaultCode: 'TK',
  receiptParams: {
    'Policy No': ['PolicyNo'],
    'Customer ID': ['CustomerID'],
    'Branch Code': ['BranchCode'],
    'UTR No': ['UTRNo']
  },
  AFFKEY: process.env.NODE_ENV === 'production' ? 'UkVUQUlM' : 'YWltdGM=', // to do,
  AIRPAYKEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
  SECRETKEY: 'b0B1c#dsaf45ADSF5645adf0215',
  COMPANY_ID: '2',
  RETAIL_ID: '4',
  PMODE: '12',
  UMC: process.env.NODE_ENV === 'production' ? '' : 118504,
  URC: process.env.NODE_ENV === 'production' ? '' : 9820021497,
  FNAME: 'Kunal',
  LNAME: 'Jhunjhunwala',
  EMAIL: '<EMAIL>',
  PHONE: '9820021497',
  PIN: '400023',
  BaseUrl: 'http://gibl.in/wallet/',
  communication: {
    MerchantInsuranceSuccess: 'Success! Purchase of Insurance Policy - <policy name>, was successful on <date time>. <amount> credits were debited from your wallet. Airpay Vyaapaar',
    CustomerInsuranceSuccess: 'Success! Your <policy name> policy was issued. <date>, INR<amount>. Check sms/email for details. Airpay Vyaapaar'
  },
  templateid: {
    MerchantInsuranceSuccess: '1107164266196445620',
    CustomerInsuranceSuccess: '1107164266202206690'
  },
  defaultParam: {
    label: 'Proposer Title',
    value: '',
    type: 'String',
    isEditable: true,
    isVisible: true,
    validation: {
      regex: ''
    },
    postKey: 'proposer_title',
    isRequired: true
  },
  riskcovryKeys: {
    affiliateid: process.env.NODE_ENV === 'production' ? 33 : 45,
    companyid: process.env.NODE_ENV === 'production' ? 10 : 30,
    AFFKEY: process.env.NODE_ENV === 'production' ? 'YWltdGM=' : 'YWltdGM='
  },
  ICICILOMBARD_PA_YEARLY: 'ICICILOMBARD_PA_YEARLY',
  ICICILOMBARD_PA: 'ICICILOMBARD_PA'
}
