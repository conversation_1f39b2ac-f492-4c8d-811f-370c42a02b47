const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const balanceController = require('../balance/balanceController')
const log = require('../../util/log')

class pointsDetails extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_points_ledger_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_points_ledger_details_id'
  }

  /**
     * Creates a new cash ledger entry
     *
     */
  static async createEntry (_, fields) {
    // Cloning request and removing connection
    var requestFields = {}
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: 'pointsDetailsController.js', action: 'createEntry', type: 'request', fields: requestFields })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      fields.amount = -fields.amount
      // console.log('PD details', fields.ma_user_id,fields.amount,fields.transaction_type,fields.wallet_type, fields.txnDetails, fields.transaction_status, fields.ma_status)
      // Updating balance as per wallet
      var walletBalancesJson = await balanceController.updateBalance(_, { ma_user_id: fields.ma_user_id, amount: fields.amount, transaction_type: fields.transaction_type, connection: fields.connection, wallet_type: fields.wallet_type, ma_status: fields.ma_status })
      if (walletBalancesJson.status === 400) {
        return walletBalancesJson
      }
      const pointsLedger = require('./pointsLedgerController')
      const data = { balance_json: JSON.stringify(walletBalancesJson.walletBalancesJson) }
      await pointsLedger.updateWhere(fields.connection, {
        data,
        id: fields.ma_points_ledger_master_id,
        where: 'ma_points_ledger_master_id'
      })
      const _result = await this.insert(fields.connection, {
        data: {
          ma_user_id: fields.ma_user_id,
          ma_points_ledger_master_id: fields.ma_points_ledger_master_id,
          amount: fields.amount,
          wallet_type: fields.wallet_type,
          orderid: fields.orderid,
          ma_status: fields.ma_status
        }
      })
      log.logger({ pagename: 'pointsDetailsController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) fields.connection.release()
    }
  }
}

module.exports = pointsDetails
