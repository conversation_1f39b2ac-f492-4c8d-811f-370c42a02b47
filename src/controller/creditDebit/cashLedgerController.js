/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const pointsLedger = require('./pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const cashAccount = require('./cashAccountController')
const pointsAccount = require('./pointsAccountController')
const pointsRate = require('../pointsRate/pointsRateController')
const balanceController = require('../balance/balanceController')
const transactionController = require('../transaction/transactionController')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common = require('../../util/common')
const sms = require('../../util/sms')
class cashLedger extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_cash_ledger_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_cash_ledger_master_id'
  }

  /**
     * Returns a CashLedger by its ID
     */
  static async getByID (_, { id }) {
    const res = await this.find(id)
    return res
  }

  static async topup (_, fields) {
    log.logger({ pagename: 'cashLedgerController.js', action: 'topup', type: 'request', fields: fields })
    var isSet = false
    var sql
    try {
      // Create connection if not passed
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      sql = `SELECT * from ma_transaction_master where aggregator_order_id='${fields.orderid}'`
      const txndetails = await this.rawQuery(sql, fields.connection)
      // Return error if transaction not found
      if (txndetails.length <= 0) {
        return { status: 400, respcode: 1020, message: errorMsg.responseCode[1020] }
      }
      log.logger({ pagename: 'cashLedgerController.js', action: 'topup', type: 'response', fields: txndetails })

      // If airpay id is not provided
      if (fields.aggregator_txn_id === undefined || fields.aggregator_txn_id === null || !(fields.aggregator_txn_id > 0)) {
        return { status: 400, respcode: 1021, message: errorMsg.responseCode[1021] }
      }

      // If amount mismatches
      if (fields.amount != txndetails[0].amount) {
        return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015] }
      }

      if (txndetails[0].transaction_status === 'I') {
        if (isSet) await mySQLWrapper.beginTransaction(fields.connection)
        const updateRes = await transactionController.updateTransaction(_, {
          aggregator_order_id: fields.orderid,
          aggregator_txn_id: fields.aggregator_txn_id,
          transaction_status: fields.transaction_status,
          amount: fields.amount,
          bank_rrn: fields.rrn,
          connection: fields.connection
        })
        if (updateRes.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return updateRes
        }
        if (fields.transaction_status === 'S') {
          const ledgerFields = {
            amount: fields.amount,
            orderid: fields.orderid,
            transaction_status: 'S',
            userid: txndetails[0].userid,
            connection: fields.connection

          }
          const ledger = await this.topupLedgerEntries('_', ledgerFields)
          if (ledger.status === 400) {
            if (isSet) await mySQLWrapper.rollback(fields.connection)
            return ledgerFields
          }

          const topupinc = require('../incentive/topupIncentiveController')
          const fieldsinc = {
            aggregator_order_id: fields.orderid,
            amount: fields.amount,
            ma_user_id: txndetails[0].ma_user_id,
            userid: txndetails[0].userid,
            connection: fields.connection
          }
          const incentive_distribution = await topupinc.incentiveDistribution(fieldsinc)
          if (incentive_distribution.status === 400) {
            if (isSet) await mySQLWrapper.rollback(fields.connection)
            return incentive_distribution
          }
          const maUserId = fields.ma_user_id
          const conn = fields.connection
          var balance = await balanceController.getPointsBalance(_, { ma_user_id: maUserId, ma_status: 'ACTUAL', conn })
          balance = balance.amount.toFixed(2)
          // Add topup SMS
          const userSql = `SELECT mobile_id from ma_user_master where profileid='${txndetails[0].ma_user_id}' AND userid='${txndetails[0].userid}' limit 1`
          const userDetails = await this.rawQuery(userSql, fields.connection)
          if (userDetails.length > 0) {
            let topupSMS = util.communication.TOPUP
            topupSMS = topupSMS.replace('<amount>', fields.amount)
            topupSMS = topupSMS.replace('<credits>', 'credits')
            topupSMS = topupSMS.replace('<date>', common.getCurrentDateCMS())
            topupSMS = topupSMS.replace('<orderid>', fields.orderid)
            topupSMS = topupSMS.replace('<balance>', balance)
            topupSMS = topupSMS.replace('<signature>', util.communication.Signature)
            await sms.sentSmsAsync(topupSMS, userDetails[0].mobile_id, util.templateid.TOPUP)
          }
        }
        if (isSet) await mySQLWrapper.commit(fields.connection)
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'topup', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // release connection
      if (isSet) fields.connection.release()
    }
  }

  static async topupLedgerEntries (_, fields) {
    // Cloning request and removing connection
    var requestFields = {}
    requestFields = (({ connection, ...o }) => o)(fields)
    requestFields.connectionThreadId = fields.connection.threadId
    log.logger({ pagename: 'cashLedgerController.js', action: 'topupLedgerEntries', type: 'request', fields: requestFields })
    var isSet = false
    var topupmerchant
    var receivermerchant
    var receiverid
    var sql
    let topupOrderid = fields.orderid
    try {
      // Create connection if not passed
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      // Get the existing txn of topup
      sql = `SELECT * from ma_transaction_master where aggregator_order_id='${topupOrderid}'`
      const txndetails = await this.rawQuery(sql, fields.connection)

      // Error if transaction not found
      if (txndetails.length <= 0) {
        return { status: 400, respcode: 1020, message: errorMsg.responseCode[1020] }
      }
      log.logger({ pagename: 'cashLedgerController.js', action: 'topupLedgerEntries', type: 'response', fields: txndetails })

      // Check if distributor/retailer is doing topup for Description part
      receiverid = util.airpayUserId
      receivermerchant = 'SuperDistributor'
      sql = `select user_type,distributer_user_master_id from ma_user_master where profileid=${txndetails[0].ma_user_id}`
      const userDetails = await this.rawQuery(sql, fields.connection)
      topupmerchant = userDetails[0].user_type === 'DT' ? 'Distributor' : userDetails[0].user_type === 'SDT' ? 'Super Distributor User' : 'Retailer'

      if (isSet) await mySQLWrapper.beginTransaction(fields.connection)

      // Get cash to points rate
      const cashToPointsRate = await pointsRate.getGlobalPointsRate(_, { connection: fields.connection })
      if (cashToPointsRate.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return cashToPointsRate
      }

      var debit = 'dr'
      var credit = 'cr'
      var cash = 'CL'// Cash load
      var transactionStatus = fields.transaction_status
      var reversalOrRefund = false
      // We have to reverse all the transactions
      if (fields.transaction_status === 'R') {
        cash = 'CW' // Cash withdraw
        topupOrderid = fields.transaction_status + '-' + topupOrderid
        reversalOrRefund = true
      }

      const pointsAmount = fields.amount * cashToPointsRate.points_value

      // For Refund
      var pointsDetailsEntries = {}
      if (transactionStatus === 'R') {
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
          ma_user_id: txndetails[0].ma_user_id,
          amount: fields.amount,
          transactionType: '1',
          connection: fields.connection 
        })
        if (pointsDetailsEntries.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return pointsDetailsEntries
        }
        console.log('Wallet balances', pointsDetailsEntries)
      }

      // Cash account entry
      const cashCredit = await cashAccount.createEntry(_, {
        ma_user_id: txndetails[0].ma_user_id,
        amount: fields.amount,
        ma_status: transactionStatus,
        transaction_type: cash,
        orderid: topupOrderid,
        userid: fields.userid,
        corresponding_id: receiverid,
        connection: fields.connection
      })
      if (cashCredit.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return cashCredit
      }
      // Points account entry
      const pointsCredit = await pointsAccount.createEntry(_, {
        ma_user_id: receiverid,
        amount: pointsAmount,
        ma_status: transactionStatus,
        transaction_type: cash,
        orderid: topupOrderid,
        userid: fields.userid,
        corresponding_id: txndetails[0].ma_user_id,
        connection: fields.connection
      })
      if (pointsCredit.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return pointsCredit
      }
      // Cash Ledger entry for RT/DT
      const merchantCash = await this.createEntry(_,
        {
          ma_user_id: txndetails[0].ma_user_id,
          amount: fields.amount,
          mode: reversalOrRefund ? credit : debit,
          transaction_type: 1,
          description: (reversalOrRefund ? util.topupCashCredit : util.topupCashDebit) + topupmerchant,
          orderid: topupOrderid,
          userid: fields.userid,
          corresponding_id: receiverid,
          ma_status: transactionStatus,
          connection: fields.connection
        }
      )
      if (merchantCash.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return merchantCash
      }

      // Cash Ledger entry for Airpay
      const airpayCash = await this.createEntry(_,
        {
          ma_user_id: receiverid,
          amount: fields.amount,
          mode: reversalOrRefund ? debit : credit,
          transaction_type: 1,
          description: (reversalOrRefund ? util.topupCashDebit : util.topupCashCredit) + receivermerchant,
          orderid: topupOrderid,
          userid: fields.userid,
          corresponding_id: txndetails[0].ma_user_id,
          ma_status: transactionStatus,
          connection: fields.connection
        }
      )
      if (airpayCash.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return airpayCash
      }

      let refundText = ''
      if (reversalOrRefund) {
        if (fields.transaction_status === 'R') {
          refundText = ' - Refund'
        } else if (fields.transaction_status === 'REV') {
          refundText = ' - Reversed'
        }
      }

      // Points Ledger entry for Airpay
      const airpayPoints = await pointsLedger.createEntry(_,
        {
          ma_user_id: receiverid,
          amount: pointsAmount,
          mode: reversalOrRefund ? credit : debit,
          transaction_type: 1,
          // description: (reversalOrRefund ? util.topupPointsCredit : util.topupPointsDebit) + receivermerchant,
          description: reversalOrRefund ? (util.topupPointsCreditNew + refundText) : (util.topupPointsDebitNew + refundText),
          orderid: topupOrderid,
          userid: fields.userid,
          corresponding_id: txndetails[0].ma_user_id,
          ma_status: transactionStatus,
          connection: fields.connection
        }
      )
      if (airpayPoints.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return airpayPoints
      }

      // Points Ledger entry for RT/DT
      const merchantPoints = await pointsLedger.createEntry(_,
        {
          ma_user_id: txndetails[0].ma_user_id,
          amount: pointsAmount,
          mode: reversalOrRefund ? debit : credit,
          transaction_type: 1,
          // description: (reversalOrRefund ? util.topupPointsDebit : util.topupPointsCredit) + topupmerchant,
          description: reversalOrRefund ? (util.topupPointsDebitNew + refundText) : (util.topupPointsCreditNew + refundText),
          orderid: topupOrderid,
          userid: fields.userid,
          corresponding_id: receiverid,
          ma_status: transactionStatus,
          connection: fields.connection
        }
      )
      if (merchantPoints.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return merchantPoints
      }
      if (transactionStatus === 'R') {
        // Entries for points Details Table
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry(_, {
            ma_user_id: txndetails[0].ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: merchantPoints.id,
            orderid: topupOrderid,
            ma_status: 'S',
            transaction_status: transactionStatus,
            connection: fields.connection
          })
          if (entry.status === 400) {
            if (isSet) await mySQLWrapper.rollback(fields.connection)
            return entry
          }
        }
      }
      if (isSet) await mySQLWrapper.commit(fields.connection)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'topupLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // release connection
      if (isSet) fields.connection.release()
    }
  }

  static async topupOld (_, fields) {
    log.logger({ pagename: 'cashLedgerController.js', action: 'topup', type: 'request', fields: fields })
    var isSet = false
    var topupmerchant
    var receivermerchant
    var receiverid
    var sql
    let topupOrderid = fields.orderid
    try {
      // Create connection if not passed
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      // Get the existing txn of topup for update
      sql = `SELECT * from ma_transaction_master where aggregator_order_id='${topupOrderid}' and `
      if (fields.transaction_status === 'REV' || fields.transaction_status === 'RC') {
        sql = sql + 'transaction_status=\'F\''
      } else if (fields.transaction_status === 'R') {
        sql = sql + 'transaction_status=\'S\''
      } else {
        sql = sql + `ma_user_id=${fields.ma_user_id}`
      }
      const txndetails = await this.rawQuery(sql, fields.connection)

      // Return error if transaction not found
      if (txndetails.length <= 0) {
        return { status: 400, respcode: 1020, message: errorMsg.responseCode[1020] }
      }
      log.logger({ pagename: 'cashLedgerController.js', action: 'topup', type: 'response', fields: txndetails })
      // Check if distributor/retailer is doing topup
      receiverid = util.airpayUserId
      receivermerchant = 'SuperDistributor'
      sql = `select user_type,distributer_user_master_id from ma_user_master where profileid=${txndetails[0].ma_user_id}`
      const userDetails = await this.rawQuery(sql, fields.connection)
      if (userDetails[0].user_type === 'DT') {
        topupmerchant = 'Distributor'
      } else {
        topupmerchant = 'Retailer'
      }

      if (isSet) await mySQLWrapper.beginTransaction(fields.connection)

      // Get cash to points rate
      const cashToPointsRate = await pointsRate.getGlobalPointsRate(_, { connection: fields.connection })
      if (cashToPointsRate.status === 400) {
        if (isSet) await mySQLWrapper.rollback(fields.connection)
        return cashToPointsRate
      }
      if (fields.amount != txndetails[0].amount) {
        return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015] }
      }

      if (fields.transaction_status === 'F') {
        if (txndetails[0].transaction_status === 'I') {
          await transactionController.updateTransaction(_, {
            aggregator_order_id: topupOrderid,
            aggregator_txn_id: 0,
            transaction_status: fields.transaction_status,
            amount: fields.amount,
            bank_rrn: fields.rrn,
            connection: fields.connection
          })
          if (isSet) await mySQLWrapper.commit(fields.connection)
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        } else {
          return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013] }
        }
      } else {
        if (fields.aggregator_txn_id === undefined || fields.aggregator_txn_id === null || !(fields.aggregator_txn_id > 0)) {
          return { status: 400, respcode: 1021, message: errorMsg.responseCode[1021] }
        }
        // For success and reverse txn following entries will be made
        var debit = 'dr'
        var credit = 'cr'
        var cash = 'CL'// Cash load
        var transactionStatus = fields.transaction_status
        if (fields.transaction_status === 'RC') transactionStatus = 'S'
        var reversalOrRefund = false
        // We have to reverse all the transactions
        if (fields.transaction_status === 'R' || fields.transaction_status === 'REV') {
          cash = 'CW' // Cash withdraw
          topupOrderid = fields.transaction_status + '-' + topupOrderid
          reversalOrRefund = true
        }
        // Update transaction to status 'P'
        await transactionController.updateTransaction(_, {
          aggregator_order_id: topupOrderid,
          aggregator_txn_id: fields.aggregator_txn_id,
          transaction_status: transactionStatus,
          amount: fields.amount,
          bank_rrn: fields.rrn,
          connection: fields.connection
        })

        const pointsAmount = fields.amount * cashToPointsRate.points_value

        var pointsDetailsEntries = {}
        if (transactionStatus === 'R') {
          pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
            ma_user_id: txndetails[0].ma_user_id,
            amount: fields.amount,
            transactionType: '1',
            connection: fields.connection
          })
          if (pointsDetailsEntries.status === 400) {
            if (isSet) await mySQLWrapper.rollback(fields.connection)
            return pointsDetailsEntries
          }
        }
        console.log('Balance', pointsDetailsEntries)

        // Cash account
        const cashCredit = await cashAccount.createEntry(_, {
          ma_user_id: txndetails[0].ma_user_id,
          amount: fields.amount,
          ma_status: transactionStatus,
          transaction_type: cash,
          orderid: topupOrderid,
          userid: fields.userid,
          corresponding_id: receiverid,
          connection: fields.connection
        })
        if (cashCredit.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return cashCredit
        }
        // Points account
        const pointsCredit = await pointsAccount.createEntry(_, {
          ma_user_id: receiverid,
          amount: pointsAmount,
          ma_status: transactionStatus,
          transaction_type: cash,
          orderid: topupOrderid,
          userid: fields.userid,
          corresponding_id: txndetails[0].ma_user_id,
          connection: fields.connection
        })
        if (pointsCredit.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return pointsCredit
        }
        // Cash Ledger Merchant
        const merchantCash = await this.createEntry(_,
          {
            ma_user_id: txndetails[0].ma_user_id,
            amount: fields.amount,
            mode: reversalOrRefund ? credit : debit,
            transaction_type: 1,
            description: (reversalOrRefund ? util.topupCashCredit : util.topupCashDebit) + topupmerchant,
            orderid: topupOrderid,
            userid: fields.userid,
            corresponding_id: receiverid,
            ma_status: transactionStatus,
            connection: fields.connection
          }
        )
        if (merchantCash.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return merchantCash
        }
        // Cash Ledger Airpay/DT
        const airpayCash = await this.createEntry(_,
          {
            ma_user_id: receiverid,
            amount: fields.amount,
            mode: reversalOrRefund ? debit : credit,
            transaction_type: 1,
            description: (reversalOrRefund ? util.topupCashDebit : util.topupCashCredit) + receivermerchant,
            orderid: topupOrderid,
            userid: fields.userid,
            corresponding_id: txndetails[0].ma_user_id,
            ma_status: transactionStatus,
            connection: fields.connection
          }
        )
        if (airpayCash.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return airpayCash
        }
        let desc = ''
        if (reversalOrRefund) {
          desc = util.topupPointsCreditNew
        } else {
          desc = util.topupPointsDebitNew
        }
        // Points Ledger Airpay/DT
        const airpayPoints = await pointsLedger.createEntry(_,
          {
            ma_user_id: receiverid,
            amount: pointsAmount,
            mode: reversalOrRefund ? credit : debit,
            transaction_type: 1,
            // description: (reversalOrRefund ? util.topupPointsCredit : util.topupPointsDebit) + receivermerchant,
            description: desc,
            orderid: topupOrderid,
            userid: fields.userid,
            corresponding_id: txndetails[0].ma_user_id,
            ma_status: transactionStatus,
            connection: fields.connection
          }
        )
        if (airpayPoints.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return airpayPoints
        }
        if (reversalOrRefund) {
          desc = util.topupPointsDebitNew
        } else {
          desc = util.topupPointsCreditNew
        }
        // Points Ledger Merchant
        const merchantPoints = await pointsLedger.createEntry(_,
          {
            ma_user_id: txndetails[0].ma_user_id,
            amount: pointsAmount,
            mode: reversalOrRefund ? debit : credit,
            transaction_type: 1,
            // description: (reversalOrRefund ? util.topupPointsDebit : util.topupPointsCredit) + topupmerchant,
            description: desc,
            orderid: topupOrderid,
            userid: fields.userid,
            corresponding_id: receiverid,
            ma_status: transactionStatus,
            connection: fields.connection
          }
        )
        if (merchantPoints.status === 400) {
          if (isSet) await mySQLWrapper.rollback(fields.connection)
          return merchantPoints
        }
        if (transactionStatus === 'R') {
          // Entries for points Details
          for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
            const entry = await pointsDetailsController.createEntry(_, {
              ma_user_id: txndetails[0].ma_user_id,
              amount: pointsDetailsEntries.details[i].deductionAmount,
              wallet_type: pointsDetailsEntries.details[i].wallet_type,
              ma_points_ledger_master_id: merchantPoints.id,
              orderid: topupOrderid,
              ma_status: 'S',
              transaction_status: transactionStatus,
              connection: fields.connection
            })
            if (entry.status === 400) {
              if (isSet) await mySQLWrapper.rollback(fields.connection)
              return entry
            }
          }
        }
        if (isSet) await mySQLWrapper.commit(fields.connection)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
    } catch (err) {
      console.log(err, 'err here')
      if (isSet) await mySQLWrapper.rollback(fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'topup', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // release connection
      if (isSet) fields.connection.release()
    }
  }

  /**
     * Returns a list of CashLedger matching the passed fields
     * @param {*} fields - Fields to be matched
     */
  static async findMatching (_, fields) {
    // Returns early with all CashLedger if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching CashLedgers
    return this.findByFields({
      fields
    })
  }

  /**
     * Creates a new cash ledger entry
     */
  static async createEntry (_, { ma_user_id, amount, mode, transaction_type, description, orderid, userid, corresponding_id, ma_status, connection = null }) {
    log.logger({ pagename: 'cashLedgerController.js', action: 'createEntry', type: 'request', fields: { ma_user_id, amount, mode, transaction_type, description, orderid, userid, corresponding_id, ma_status } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      if (mode === 'dr') amount = -amount
      // var balance = await balanceController.getCashBalance(_, { ma_user_id, connection })
      // balance = balance.amount + amount
      const balance = 0

      // Get points factor
      const pointsRate = require('../pointsRate/pointsRateController')
      const cashToPointsRate = await pointsRate.getGlobalPointsRate(_, { connection })
      if (cashToPointsRate.status === 400) {
        cashToPointsRate.points_value = 1
      }
      if (corresponding_id === null || corresponding_id === undefined) {
        corresponding_id = util.airpayUserId
      }
      var sql =''
      var sales_id=0;

      if(ma_user_id!=util.airpayCommissionId && ma_user_id !=util.airpayUserId){
        sql = `select ma_user_master_id,sales_id from ma_user_master where profileid=${ma_user_id}`
      } else if(corresponding_id!=util.airpayCommissionId && corresponding_id !=util.airpayUserId){
        sql = `select ma_user_master_id,sales_id from ma_user_master where profileid=${corresponding_id}`
      }
      if(sql!=''){
        const userDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: 'cashLedgerController.js', action: 'createEntry', type: 'response-UserDetails', fields: userDetails })
        
        if(userDetails.length>0){
          sales_id = userDetails[0].sales_id
        }
      }
      // Condition for parentid
      let parent_id = ''
      if (orderid.includes('-')) {
        parent_id = (orderid).substr(orderid.lastIndexOf('-') + 1)
      } else {
        parent_id = orderid
      }
      const parent_transaction_master_id = await transactionController.getTransactionDetailsByParentId(parent_id, connection)

      const _result = await this.insert(connection, {
        data: {
          ma_user_id,
          amount,
          mode,
          transaction_type,
          description,
          orderid,
          userid,
          corresponding_id,
          balance,
          ma_status,
          points_factor: cashToPointsRate.points_value,
          sales_id,
          parent_id,
          parent_transaction_master_id
        }
      })
      log.logger({ pagename: 'cashLedgerController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

}

module.exports = cashLedger
