/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const balanceController = require('../balance/balanceController')
const pointsRate = require('../pointsRate/pointsRateController')
const commissionController = require('../commission/commissionController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const log = require('../../util/log')
const securePinCtrl = require('../securityPin/securityPinController')
const sms = require('../../util/sms')
const common = require('../../util/common')

class pointsLedger extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_points_ledger_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_points_ledger_master_id'
  }

  /**
     * Returns a PointsLedger by its ID
     */
  static async getByID (_, { id }) {
    const res = await this.find(id)
    return res
  }

  /**
     * Creates a new cash ledger entry
     */
  static async createEntry (_, { ma_user_id, amount, mode, transaction_type, description, orderid, userid, corresponding_id, ma_status, connection = null, cms_merchant = false, credit_reversal_status = 'N' }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'createEntry', type: 'request', fields: { ma_user_id, amount, mode, transaction_type, description, orderid, userid, corresponding_id, ma_status, cms_merchant, credit_reversal_status } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      if (mode === 'dr') amount = -amount

      // Updating balance as per wallet
      var walletBalancesJson = []
      var balance = 0

      if (cms_merchant === false) { // [SKIP WALLET PART FOR CMS][CHANGE ADDED BY MAJID N. FOR CMS TRANSACTION]
        // This will have balance not actual balance
        // var balance = await balanceController.getPointsBalance(_, { ma_user_id, connection })
        if (util.airpaymerchantconfig.includes(ma_user_id) == false) {
          balance = await balanceController.getWalletBalancesDirect(_, { ma_user_id, balance_flag: 'SUMMARY', connection })
          console.log('Details balance :', balance)
          console.log('Details balance amount :', typeof (balance.amount))
          console.log('Order Amount : ', amount, typeof (amount))
          balance = parseFloat(balance.amount) + parseFloat(amount)
          console.log('Total balance :', balance)
        }
        console.log('balance', balance)

        // console.log("Research ::::",ma_user_id,util.airpayUserId,ma_user_id ,util.airpayCommissionId ,mode,ma_status,transaction_type)
        if (util.airpaymerchantconfig.includes(ma_user_id) == false && mode === 'cr') {
          console.log('Called update on credit right ::::')
          walletBalancesJson = await balanceController.updateBalance(_, { ma_user_id, amount, transaction_type, ma_status, connection })
        } else if (util.airpaymerchantconfig.includes(ma_user_id) == false && mode === 'dr' && ma_status === 'REV' && !(['8', '41', '43'].includes(transaction_type))) { // Update wallet balance if REV entries are made, Exluding AEPS Incenive
          walletBalancesJson = await balanceController.updateBalance(_, { ma_user_id, amount, transaction_type, ma_status, connection })
        }

        if (walletBalancesJson.status === 400) {
          return walletBalancesJson
        }
      }

      // Get points factor
      const cashToPointsRate = await pointsRate.getGlobalPointsRate(_, { connection })

      if (cashToPointsRate.status === 400) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      if (corresponding_id === null || corresponding_id === undefined) {
        corresponding_id = util.airpayUserId
      }
      var sql = ''
      var sales_id = 0

      if (ma_user_id != util.airpayCommissionId && ma_user_id != util.airpayUserId) {
        sql = `select ma_user_master_id,sales_id from ma_user_master where profileid=${ma_user_id} limit 1`
      } else if (corresponding_id != util.airpayCommissionId && corresponding_id != util.airpayUserId) {
        sql = `select ma_user_master_id,sales_id from ma_user_master where profileid=${corresponding_id} limit 1`
      }
      if (sql != '') {
        const userDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: 'pointsLedgerController.js', action: 'createEntry', type: 'response-UserDetails', fields: userDetails })

        if (userDetails.length > 0) {
          sales_id = userDetails[0].sales_id
        }
      }
      // Condition for parent_id
      let parent_id = ''
      if (orderid.includes('-')) {
        parent_id = (orderid).substr(orderid.lastIndexOf('-') + 1)
      } else {
        parent_id = orderid
      }
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(parent_id, connection)

      const _result = await this.insert(connection, {
        data: {
          ma_user_id,
          amount,
          mode,
          transaction_type: String(transaction_type),
          description,
          orderid,
          userid,
          corresponding_id,
          balance,
          ma_status,
          points_factor: cashToPointsRate.points_value,
          balance_json: JSON.stringify(walletBalancesJson.walletBalancesJson),
          sales_id,
          parent_id,
          parent_transaction_master_id,
          credit_reversal_status
        }
      })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }

  /**
     * sendMoney
     */
  static async sendMoney (_, { distributorId, retailerId, amount, orderid, commissionType, txnStatus, userid, connection = null, security_pin = null }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'sendMoney', type: 'request', fields: { distributorId, retailerId, amount, orderid, commissionType, txnStatus, userid } })
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const ma_status = txnStatus
      const receiver = {}
      receiver.userType = ''
      receiver.company = ''
      receiver.mobile = ''
      const sender = {}
      sender.userType = ''
      sender.company = ''
      sender.mobile = ''

      var sql = `select user_type,distributer_user_master_id,mobile_id,company FROM ma_user_master where profileid=${retailerId}`
      const userDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'userdetailsqueryresponse', fields: { userDetails } })
      var receiverName
      // eslint-disable-next-line no-irregular-whitespace
      if (userDetails[0].user_type === 'DT') {
        receiverName = ' Distributor'
      } else if (userDetails[0].user_type === 'RT') {
        receiverName = ' Retailer'
      } else if (userDetails[0].user_type === 'SDT') {
        receiverName = ' SuperDistributor User'
      } else {
        receiverName = ' SuperDistributor'
      }
      if (retailerId === util.airpayUserId) {
        receiverName = ' SuperDistributor'
      }
      receiver.userType = userDetails[0].user_type
      receiver.company = userDetails[0].company
      receiver.mobile = userDetails[0].mobile_id

      // Check Retailer and Distributer logic for sendmoney
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'commissionType', fields: { commissionType } })
      if (commissionType === '3') {
        sql = `select user_type,mobile_id,company from ma_user_master where profileid=${distributorId}`
        const distributerDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'distributerdetailsqueryresponse', fields: { distributerDetails } })
        if (distributerDetails[0].user_type !== 'DT' && distributerDetails[0].user_type !== 'SDT') {
          return { status: 400, respcode: 1029, message: errorMsg.responseCode[1029] }
        }
        if (userDetails[0].user_type !== 'RT' && userDetails[0].distributer_user_master_id !== distributorId) {
          return { status: 400, respcode: 1030, message: errorMsg.responseCode[1030] }
        }
        sender.userType = distributerDetails[0].user_type
        sender.company = distributerDetails[0].company
        sender.mobile = distributerDetails[0].mobile_id

        /** Security Pin Validation for Send Money Only **/
        /** ** Verify Security Pin Tempory commented don't delete */
        // console.log('security_pin' ,security_pin)
        const securePinData = await securePinCtrl.verifySecurePin(null, {
          ma_user_id: distributorId,
          userid: userid,
          security_pin: security_pin,
          connection: connection
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'securitypinqueryresponse', fields: { securePinData } })

        if (securePinData.status === 400) {
          return securePinData
        }
      }

      const extraTextInDescription = commissionType === '6' ? ' for BBPS' : ''
      if (isSet) await mySQLWrapper.beginTransaction(connection)
      // Get commission details
      var commission = 0

      // Differentiate between sendMoney and others
      const commssionMerchantId = commissionType === 3 ? retailerId : distributorId

      // Get commission from commission master table
      const commissionDetails = await commissionController.getCommission(_, {
        ma_user_id: commssionMerchantId,
        ma_commission_type: commissionType,
        ma_deduction_type: 2,
        amount,
        connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'commissionDetails', fields: { commissionDetails } })
      // No commission will be applied while reversing the transaction
      if (commissionDetails.status === 400 || ma_status === 'R') {
        commission = 0
      } else {
        commission = commissionDetails.commissionVal
      }

      // Differentiate between sendMoney and others
      const debitAmount = commissionType === 3 ? amount : amount + commission
      const creditAmount = commissionType === 3 ? amount - commission : amount
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'debitAmount', fields: { debitAmount } })
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'creditAmount', fields: { creditAmount } })
      // Check points balance
      if (distributorId !== util.airpayUserId) {
        // getPointsBalance replaced here
        const availableBalance = await balanceController.getWalletBalancesDirect(_, { ma_user_id: distributorId, ma_status: 'ACTUAL', balance_flag: 'SUMMARY', connection })
        console.log('1. total points balance', availableBalance)
        if (availableBalance.amount < debitAmount) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
        }
      }

      var pointsDetailsEntries = {}
      if (distributorId !== util.airpayUserId) {
        // getWalletBalance replaced here
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
          ma_user_id: distributorId,
          amount: amount,
          transactionType: '1',
          connection
        })
        console.log('2. wallet points balance', pointsDetailsEntries)
        if (pointsDetailsEntries.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return pointsDetailsEntries
        }
      }
      // [START] Added by Majid for Limit assigned description changes
      let debitDesc = ''
      let creditDesc = ''
      if (commissionType === '3') {
        debitDesc = util.sendMoneyDistributorDescriptionNew
        debitDesc = debitDesc.replace('#userType#', receiver.userType)
        debitDesc = debitDesc.replace('#companyName#', receiver.company)

        creditDesc = util.sendMoneyRetailerDescriptionNew
        creditDesc = creditDesc.replace('#userType#', sender.userType)
        creditDesc = creditDesc.replace('#companyName#', sender.company)
      } else {
        // debitDesc = util.sendMoneyDistributorDescription + receiverName + extraTextInDescription
        // creditDesc = util.sendMoneyRetailerDescription + receiverName + extraTextInDescription
        const common = require('../../util/common')
        const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: commissionType }, connection)
        const descType = descObj.code_desc ? descObj.code_desc : ''
        let refundText = ''
        if (txnStatus == 'R') {
          refundText = ' - Refund'
        } else if (txnStatus == 'REV') {
          refundText = ' - Reverse'
        }
        debitDesc = 'Debit - ' + descType + refundText
        creditDesc = 'Credit - ' + descType + refundText
      }
      // [END] Added by Majid for Limit assigned description changes

      // Debit from Distributer
      const distributorLedgerId = await this.createEntry(_, {
        ma_user_id: distributorId,
        amount: debitAmount,
        mode: 'dr',
        transaction_type: commissionType,
        description: debitDesc,
        ma_status,
        orderid: orderid,
        userid,
        corresponding_id: retailerId,
        connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'distributorLedgerId', fields: { distributorLedgerId } })
      if (distributorLedgerId.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return distributorLedgerId
      }

      // Points Debit details for distibutor
      if (distributorId !== util.airpayUserId) {
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry(_, {
            ma_user_id: distributorId,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: distributorLedgerId.id,
            orderid,
            ma_status: 'S',
            transaction_status: ma_status,
            connection
          })
          log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'pointsdebitdistributor', fields: { entry } })
          if (entry.status === 400) {
            if (isSet) await mySQLWrapper.rollback(connection)
            return entry
          }
        }
      }

      // Credit commission
      if (commission > 0) {
        const commissionLedgerId = await this.createEntry(_, {
          ma_user_id: util.airpayCommissionId,
          amount: commission,
          mode: 'cr',
          transaction_type: 4,
          description: util.sendMoneyCommissionDescription,
          ma_status,
          userid,
          corresponding_id: retailerId,
          orderid: orderid,
          connection
        }
        )
        log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'creditcommission', fields: { commissionLedgerId } })
        if (commissionLedgerId.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return commissionLedgerId
        }
      }

      // Credit In Retailer after subtracting  commission
      const retailerLedgerId = await this.createEntry(_, {
        ma_user_id: retailerId,
        amount: creditAmount,
        mode: 'cr',
        transaction_type: commissionType,
        ma_status,
        userid,
        // description: util.sendMoneyRetailerDescription + receiverName + extraTextInDescription,
        description: creditDesc,
        orderid: orderid,
        corresponding_id: distributorId,
        connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'retailercreditcommission', fields: { retailerLedgerId } })
      if (retailerLedgerId.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return retailerLedgerId
      }

      if (isSet) await mySQLWrapper.commit(connection)

      // Send SMS
      if (commissionType === '3') {
        let creditSMS = util.communication.SENDMONEYCREDIT
        const company_name_sender = (sender.company.length > 30) ? sender.company.substring(0, 28) + '..' : sender.company
        creditSMS = creditSMS.replace('<amount>', creditAmount)
        creditSMS = creditSMS.replace('<credits>', 'credits')
        creditSMS = creditSMS.replace('<sendercompany>', company_name_sender)
        creditSMS = creditSMS.replace('<date>', common.getCurrentDate())
        creditSMS = creditSMS.replace('<orderid>', orderid)
        creditSMS = creditSMS.replace('<signature>', util.communication.Signature)

        let debitSMS = util.communication.SENDMONEYDEBIT
        const company_name = (receiver.company.length > 30) ? receiver.company.substring(0, 28) + '..' : receiver.company
        debitSMS = debitSMS.replace('<amount>', debitAmount)
        debitSMS = debitSMS.replace('<credits>', 'credits')
        debitSMS = debitSMS.replace('<receivercompany>', company_name)
        debitSMS = debitSMS.replace('<date>', common.getCurrentDate())
        debitSMS = debitSMS.replace('<orderid>', orderid)
        debitSMS = debitSMS.replace('<signature>', util.communication.Signature)

        await sms.sentSmsAsync(debitSMS, sender.mobile, util.templateid.SENDMONEYDEBIT)
        await sms.sentSmsAsync(creditSMS, receiver.mobile, util.templateid.SENDMONEYCREDIT)
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection after execution
      if (isSet) connection.release()
    }
  }

  /**
   * intraTransferSendMoney
   * @param {null} _
   * @param {{ senderId: number, receiverId: number, amount: number, commissionType: string, userid: number, transaction_charges: number, merchantData: { senderData: object, receiverData: object, connection: string } }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async intraTransferSendMoney ({ senderId, receiverId, amount, orderid, commissionType, userid, transaction_charges = 0, merchantData = {}, connection = null }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'intraTransferSendMoney', type: 'request', fields: { senderId, receiverId, amount, orderid, commissionType, userid, transaction_charges, merchantData } })
    try {
      // 1. Check Wallet Balance of Sender **********
      let pointsDetailsEntries = {}
      if (senderId !== util.airpayUserId) {
        // Actual Wallet Balance
        const availableBalance = await balanceController.getWalletBalancesDirect('_', { ma_user_id: senderId, ma_status: 'ACTUAL', balance_flag: 'SUMMARY', connection })
        log.logger({ pagename: 'pointsLedgerController.js', action: 'intraTransferSendMoney', type: 'total points balance', fields: availableBalance })
        console.log(`Check total points balance ${parseFloat(availableBalance.amount)} < (${parseFloat(amount)}+${parseFloat(transaction_charges)}) ======`, (parseFloat(availableBalance.amount) < (parseFloat(amount) + parseFloat(transaction_charges))))
        // availableBalance Sample Response = { status: 200, respcode: 1000, message: 'Success', amount: 926106.7958 }
        if (parseFloat(availableBalance.amount) < (parseFloat(amount) + parseFloat(transaction_charges))) return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }

        // Withdrawable Wallet Balance
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
          ma_user_id: senderId,
          amount: amount,
          transactionType: '2',
          connection
        })
        log.logger({ pagename: 'pointsLedgerController.js', action: 'intraTransferSendMoney', type: 'wallet points balance', fields: pointsDetailsEntries })
        // pointsDetailsEntries Sample Response = { status: 200, respcode: 1000,  message: 'Success', details: [ { wallet_type: '4', deductionAmount: 100 } ] }
        if (pointsDetailsEntries.status != 200) {
          if (merchantData.senderData.user_type != 'RT') {
            pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
              ma_user_id: senderId,
              amount: amount,
              transactionType: '1',
              connection
            })
            log.logger({ pagename: 'pointsLedgerController.js', action: 'intraTransferSendMoney', type: 'wallet pointsDetailsEntries 2', fields: pointsDetailsEntries })
            if (pointsDetailsEntries.status != 200) return pointsDetailsEntries
          } else {
            return pointsDetailsEntries
          }
        }
      }

      let debitDesc = ''
      let creditDesc = ''
      if ('receiverData' in merchantData) {
        debitDesc = util.sendMoneyDistributorDescriptionNew
        debitDesc = debitDesc.replace('#userType#', merchantData.receiverData.user_type)
        debitDesc = debitDesc.replace('#companyName#', merchantData.receiverData.company)
      }
      if ('senderData' in merchantData) {
        creditDesc = util.sendMoneyRetailerDescriptionNew
        creditDesc = creditDesc.replace('#userType#', merchantData.senderData.user_type)
        creditDesc = creditDesc.replace('#companyName#', merchantData.senderData.company)
      }

      // 2. Entries for Sneder **********
      // Debit from Sender
      const senderDrEntry = await this.createEntry('_', {
        ma_user_id: senderId,
        amount: amount,
        mode: 'dr',
        transaction_type: commissionType,
        description: debitDesc,
        ma_status: 'S',
        orderid,
        userid,
        corresponding_id: receiverId,
        connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'intraTransferSendMoney', type: 'intraTransferSendMoney', fields: senderDrEntry })
      if (senderDrEntry.status === 400) return senderDrEntry

      // Points Debit details for Sender
      if (senderId !== util.airpayUserId) {
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const senderPointDetailsEntry = await pointsDetailsController.createEntry('_', {
            ma_user_id: senderId,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: senderDrEntry.id,
            orderid,
            ma_status: 'S',
            transaction_status: 'S',
            connection
          })
          log.logger({ pagename: require('path').basename(__filename), action: 'intraTransferSendMoney', type: `senderPointDetailsEntry ${i + 1}`, fields: senderPointDetailsEntry })
          if (senderPointDetailsEntry.status === 400) return senderPointDetailsEntry
        }
      }

      // 3. Entries for Receiver **********
      const creditAmount = amount
      log.logger({ pagename: require('path').basename(__filename), action: 'intraTransferSendMoney', type: 'creditAmount', fields: creditAmount })

      // Credit In Receiver wallet after subtracting  commission
      const receiverLedgerId = await this.createEntry('_', {
        ma_user_id: receiverId,
        amount: creditAmount,
        mode: 'cr',
        transaction_type: commissionType,
        ma_status: 'S',
        userid,
        description: creditDesc,
        orderid: orderid,
        corresponding_id: senderId,
        connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'receiverLedgerId', fields: { receiverLedgerId } })
      if (receiverLedgerId.status === 400) return receiverLedgerId

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: 'pointsLedgerController.js', action: 'intraTransferSendMoney', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // Release the connection
    }
  }

  /**
     * Convert Points to Cash
     */
  // eslint-disable-next-line camelcase
  static async convertPointsToCash (_, { ma_user_id, amount, orderid, userid, type = '', connection = null, payment_mode = '' }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'convertPointsToCash', type: 'request', fields: { ma_user_id, amount, orderid, userid, type } })
    var isSet = false
    try {
      // Create connection if not passed
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      console.log('Connection status', isSet)
      var transaction_type = '7'
      if (type !== '') {
        transaction_type = '2'
      }
      console.log('transaction_type P to C', transaction_type)
      let executionResponse = {}
      let commissionAmount = 0
      let pointFactor = 1

      const pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
        ma_user_id: ma_user_id,
        amount: amount,
        transactionType: transaction_type === '2' ? '1' : '2',
        payment_mode,
        connection
      })

      if (pointsDetailsEntries.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return pointsDetailsEntries
      }

      // Mysql Transaction Begin
      if (isSet) await mySQLWrapper.beginTransaction(connection)

      // Get cash conversion rate
      const rateDetails = await pointsRate.getGlobalPointsRate(_, { connection: connection })
      if (rateDetails.status === 200) {
        pointFactor = rateDetails.points_value
      }

      // User type
      var merchantType = ''
      const userTypesql = `select user_type from ma_user_master where profileid=${ma_user_id}`
      const userDetails = await this.rawQuery(userTypesql, connection)
      if (userDetails[0].user_type === 'DT') {
        merchantType = 'Distributor'
      } else {
        merchantType = 'Retailer'
      }

      const descType = (type == '') ? 'Withdrawal' : type
      // For debit from merchant
      executionResponse = await this.createEntry(_, {
        ma_user_id,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        // description: util.pointsToCashMerchantDebitDescription + merchantType + type,
        description: util.pointsToCashMerchantDebitDescription + descType,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: connection
      })
      console.log('Fiest re', executionResponse)
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      // Create entry in ledger details table
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry(_, {
          ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: executionResponse.id,
          orderid,
          userid,
          ma_status: 'S',
          connection: connection
        })
        if (entry.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return entry
        }
      }

      /* --- Calculation for Points --- */
      // Get commission from commission master table
      const commissionData = await commissionController.getCommission(_, {
        ma_user_id: ma_user_id,
        ma_commission_type: transaction_type,
        ma_deduction_type: 2,
        amount,
        connection: connection
      })

      if ((typeof (commissionData.status) !== 'undefined' && commissionData.status === 400) || commissionData.length === 0) {
        commissionAmount = 0
      } else {
        commissionAmount = commissionData.commissionVal
        if (transaction_type === '7') amount = amount - commissionAmount
      }

      // For credit to airpay super user
      executionResponse = await this.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount: amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayCreditDescription + type,
        orderid: orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: connection
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      if (commissionAmount > 0 && transaction_type === '7') {
        // For credit to airpay commission user
        executionResponse = await this.createEntry(_, {
          ma_user_id: util.airpayCommissionId,
          amount: commissionAmount,
          mode: 'cr',
          transaction_type: transaction_type,
          description: util.pointsToCashCommissionCreditDescription + type,
          orderid,
          userid,
          ma_status: 'S',
          corresponding_id: ma_user_id,
          connection: connection
        })

        if (executionResponse.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return executionResponse
        }
      }

      /* Calculation for Cash */
      amount = amount / pointFactor

      const cashLedger = require('./cashLedgerController')
      // For debit from airpay
      executionResponse = await cashLedger.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayDebitDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: connection
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }
      // For credit to merchant
      executionResponse = await cashLedger.createEntry(_, {
        ma_user_id,
        amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashMerchantCreditDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: connection
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      // Transaction Commit
      if (isSet) await mySQLWrapper.commit(connection)

      return executionResponse
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'convertPointsToCash', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection after execution
      if (isSet) connection.release()
    }
  }

  static async cmsConvertPointsToCash (_, { ma_user_id, amount, orderid, userid, type = '', connection = null }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'request', fields: { ma_user_id, amount, orderid, userid, type } })
    var isSet = false
    try {
      // Create connection if not passed
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      console.log('Connection status', isSet)
      var transaction_type = '7'
      if (type !== '') {
        transaction_type = '2'
      }
      console.log('transaction_type P to C', transaction_type)
      let executionResponse = {}
      let commissionAmount = 0
      let pointFactor = 1

      // Mysql Transaction Begin
      if (isSet) await mySQLWrapper.beginTransaction(connection)

      // Get cash conversion rate
      const rateDetails = await pointsRate.getGlobalPointsRate(_, { connection: connection })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'rateDetails', fields: rateDetails })
      if (rateDetails.status === 200) {
        pointFactor = rateDetails.points_value
      }

      // User type
      var merchantType = ''
      const userTypesql = `select user_type from ma_user_master where profileid=${ma_user_id}`
      const userDetails = await this.rawQuery(userTypesql, connection)
      if (userDetails[0].user_type === 'DT') {
        merchantType = 'Distributor'
      } else {
        merchantType = 'Retailer'
      }

      const descType = (type == '') ? 'Withdrawal' : type
      // For debit from merchant
      executionResponse = await this.createEntry(_, {
        ma_user_id,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        // description: util.pointsToCashMerchantDebitDescription + merchantType + type,
        description: util.pointsToCashMerchantDebitDescription + descType,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: connection,
        cms_merchant: true
      })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'executionResponse for this.createEntry -  For debit from merchant', fields: executionResponse })
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      /* --- Calculation for Points --- */
      // Get commission from commission master table
      const commissionData = await commissionController.getCommission(_, {
        ma_user_id: ma_user_id,
        ma_commission_type: transaction_type,
        ma_deduction_type: 2,
        amount,
        connection: connection
      })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'commissionData', fields: commissionData })
      if ((typeof (commissionData.status) !== 'undefined' && commissionData.status === 400) || commissionData.length === 0) {
        commissionAmount = 0
      } else {
        commissionAmount = commissionData.commissionVal
        if (transaction_type === '7') amount = amount - commissionAmount
      }

      // For credit to airpay super user
      executionResponse = await this.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount: amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayCreditDescription + type,
        orderid: orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: connection
      })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'executionResponse for this.createEntry', fields: executionResponse })
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      if (commissionAmount > 0 && transaction_type === '7') {
        // For credit to airpay commission user
        executionResponse = await this.createEntry(_, {
          ma_user_id: util.airpayCommissionId,
          amount: commissionAmount,
          mode: 'cr',
          transaction_type: transaction_type,
          description: util.pointsToCashCommissionCreditDescription + type,
          orderid,
          userid,
          ma_status: 'S',
          corresponding_id: ma_user_id,
          connection: connection
        })
        log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'executionResponse for this.createEntry', fields: executionResponse })
        if (executionResponse.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return executionResponse
        }
      }

      /* Calculation for Cash */
      amount = amount / pointFactor

      const cashLedger = require('./cashLedgerController')
      // For debit from airpay
      executionResponse = await cashLedger.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayDebitDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: connection
      })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'executionResponse for debit cashLedger.createEntry', fields: executionResponse })
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }
      // For credit to merchant
      executionResponse = await cashLedger.createEntry(_, {
        ma_user_id,
        amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashMerchantCreditDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: connection
      })
      log.logger({ pagename: 'pointsLedgerController.js', action: 'cmsConvertPointsToCash', type: 'executionResponse for credit cashLedger.createEntry', fields: executionResponse })
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      // Transaction Commit
      if (isSet) await mySQLWrapper.commit(connection)

      return executionResponse
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'convertPointsToCash', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection after execution
      if (isSet) connection.release()
    }
  }

  // create function for fasttag ledgers

  static async PointsToCashLedger (_, { ma_user_id, amount, orderid, userid, type = '', connection = null }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'convertPointsToCash', type: 'request', fields: { ma_user_id, amount, orderid, userid, type } })
    var isSet = false
    try {
      // Create connection if not passed
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      console.log('Connection status', isSet)
      var transaction_type = ''
      var typedesc = ''
      if (type == 'FASTAG') {
        transaction_type = '31'
        typedesc = 'FASTAG'
      } else if (type == 'FASTAGRECHARGE') {
        transaction_type = '32'
        typedesc = 'FASTAG RECHARGE'
      } else if (type == 'GOLD') {
        transaction_type = '28'
        typedesc = 'GOLD'
      }

      console.log('transaction_type P to C', transaction_type)
      let executionResponse = {}
      let commissionAmount = 0
      let pointFactor = 1

      const pointsDetailsEntries = await balanceController.getWalletBalanceDirect(_, {
        ma_user_id: ma_user_id,
        amount: amount,
        transactionType: '1',
        connection
      })

      if (pointsDetailsEntries.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return pointsDetailsEntries
      }

      // Mysql Transaction Begin
      if (isSet) await mySQLWrapper.beginTransaction(connection)

      // Get cash conversion rate
      const rateDetails = await pointsRate.getGlobalPointsRate(_, { connection: connection })
      if (rateDetails.status === 200) {
        pointFactor = rateDetails.points_value
      }

      // User type
      var merchantType = ''
      const userTypesql = `select user_type from ma_user_master where profileid=${ma_user_id}`
      const userDetails = await this.rawQuery(userTypesql, connection)
      if (userDetails[0].user_type === 'DT') {
        merchantType = 'Distributor'
      } else {
        merchantType = 'Retailer'
      }

      const descType = typedesc
      // For debit from merchant
      executionResponse = await this.createEntry(_, {
        ma_user_id,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        // description: util.pointsToCashMerchantDebitDescription + merchantType + type,
        description: util.pointsToCashMerchantDebitDescription + ' ' + descType,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: connection
      })
      console.log('Fiest re', executionResponse)
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      // Create entry in ledger details table
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry(_, {
          ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: executionResponse.id,
          orderid,
          userid,
          ma_status: 'S',
          connection: connection
        })
        if (entry.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return entry
        }
      }

      /* --- Calculation for Points --- */
      // Get commission from commission master table
      const commissionData = await commissionController.getCommission(_, {
        ma_user_id: ma_user_id,
        ma_commission_type: transaction_type,
        ma_deduction_type: 2,
        amount,
        connection: connection
      })

      if ((typeof (commissionData.status) !== 'undefined' && commissionData.status === 400) || commissionData.length === 0) {
        commissionAmount = 0
      } else {
        commissionAmount = commissionData.commissionVal
        if (transaction_type === '7') amount = amount - commissionAmount
      }

      // For credit to airpay super user
      executionResponse = await this.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount: amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayCreditDescription + ' ' + typedesc,
        orderid: orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: connection
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      if (commissionAmount > 0 && transaction_type === '7') {
        // For credit to airpay commission user
        executionResponse = await this.createEntry(_, {
          ma_user_id: util.airpayCommissionId,
          amount: commissionAmount,
          mode: 'cr',
          transaction_type: transaction_type,
          description: util.pointsToCashCommissionCreditDescription + typedesc,
          orderid,
          userid,
          ma_status: 'S',
          corresponding_id: ma_user_id,
          connection: connection
        })

        if (executionResponse.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return executionResponse
        }
      }

      /* Calculation for Cash */
      amount = amount / pointFactor

      const cashLedger = require('./cashLedgerController')
      // For debit from airpay
      executionResponse = await cashLedger.createEntry(_, {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayDebitDescription + typedesc,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: connection
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }
      // For credit to merchant
      executionResponse = await cashLedger.createEntry(_, {
        ma_user_id,
        amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashMerchantCreditDescription + merchantType,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: connection
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return executionResponse
      }

      // Transaction Commit
      if (isSet) await mySQLWrapper.commit(connection)

      return executionResponse
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'convertPointsToCash', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection after execution
      if (isSet) connection.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:number,userid:number,amount:number, orderid:string,transaction_type:number,type?:string,connection?:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async fmtConvertPointsToCash ({ ma_user_id, userid, amount, orderid, transaction_type, type = '', connection = null }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'convertPointsToCash', type: 'request', fields: { ma_user_id, amount, orderid, userid, type, transaction_type } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      let executionResponse = {}
      let commissionAmount = 0
      let pointFactor = 1

      const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: ma_user_id,
        amount: amount,
        transactionType: transaction_type,
        connection: conn
      })

      if (pointsDetailsEntries.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return pointsDetailsEntries
      }

      // Mysql Transaction Begin
      if (isSet) await mySQLWrapper.beginTransaction(conn)

      // Get cash conversion rate
      const rateDetails = await pointsRate.getGlobalPointsRate('_', { connection: conn })
      if (rateDetails.status === 200) {
        pointFactor = rateDetails.points_value
      }

      const descType = (type == '') ? 'Withdrawal' : type
      // For debit from merchant
      executionResponse = await this.createEntry('_', {
        ma_user_id,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        description: 'Nepal Money Transfer  Transaction',
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: conn
      })
      console.log('Fiest re', executionResponse)
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }

      // Create entry in ledger details table
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: executionResponse.id,
          orderid,
          userid,
          ma_status: 'S',
          connection: conn
        })
        if (entry.status === 400) {
          if (isSet) await mySQLWrapper.rollback(conn)
          return entry
        }
      }

      /* --- Calculation for Points --- */
      // Get commission from commission master table
      const commissionData = await commissionController.getCommission('_', {
        ma_user_id: ma_user_id,
        ma_commission_type: transaction_type,
        ma_deduction_type: 2,
        amount,
        connection: conn
      })

      if ((typeof (commissionData.status) !== 'undefined' && commissionData.status === 400) || commissionData.length === 0) {
        commissionAmount = 0
      } else {
        commissionAmount = commissionData.commissionVal
      }

      // For credit to airpay super user
      executionResponse = await this.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: 'Nepal Money Transfer  Transaction',
        orderid: orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: conn
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }

      /* Calculation for Cash */
      amount = amount / pointFactor

      const cashLedger = require('./cashLedgerController')
      // For debit from airpay
      executionResponse = await cashLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'dr',
        transaction_type: transaction_type,
        description: util.pointsToCashAirpayDebitDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: conn
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }
      // For credit to merchant
      executionResponse = await cashLedger.createEntry('_', {
        ma_user_id,
        amount,
        mode: 'cr',
        transaction_type: transaction_type,
        description: util.pointsToCashMerchantCreditDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: conn
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }

      // Transaction Commit
      if (isSet) await mySQLWrapper.commit(conn)

      return executionResponse
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'convertPointsToCash', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection after execution
      if (isSet) connection.release()
    }
  }

  // DigiKhata Point to Cash
  static async digiKhataPointToCash (_, { ma_user_id, userid, amount, orderid, transaction_type, type = '', connection = null }) {
    log.logger({ pagename: 'pointsLedgerController.js', action: 'convertPointsToCash', type: 'request', fields: { ma_user_id, amount, orderid, userid, type, transaction_type } })
    // lokiLogger.info({ pagename: 'pointsLedgerController.js', action: 'convertPointsToCash', type: 'request', fields: { ma_user_id, amount, orderid, userid, type, transaction_type } })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      let executionResponse = {}
      let commissionAmount = 0
      let pointFactor = 1

      /* BALANCE CHECK */
      const balance = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id,
        amount,
        transactionType: transaction_type,
        connection: conn
      })
      if (balance.status === 400) {
        return balance
      }

      const rateDetails = await pointsRate.getGlobalPointsRate('_', { connection: conn })
      if (rateDetails.status === 200) {
        pointFactor = rateDetails.points_value
      }

      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '75' }, conn)
      const descType = descObj.code_desc ? descObj.code_desc : ''

      if (isSet) await mySQLWrapper.beginTransaction(conn)

      /* DEBIT FROM MERCHANT */
      executionResponse = await this.createEntry('_', {
        ma_user_id,
        amount,
        mode: 'dr',
        transaction_type: 75,
        description: `Debit - ${descType}`,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: conn
      })
      console.log('Fiest re', executionResponse)
      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }

      // Create entry in ledger details table
      for (var i = 0; i < balance.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id,
          amount: balance.details[i].deductionAmount,
          wallet_type: balance.details[i].wallet_type,
          ma_points_ledger_master_id: executionResponse.id,
          orderid,
          userid,
          ma_status: 'S',
          connection: conn
        })
        if (entry.status === 400) {
          if (isSet) await mySQLWrapper.rollback(conn)
          return entry
        }
      }

      /* --- Calculation for Points --- */
      // Get commission from commission master table
      const commissionData = await commissionController.getCommission('_', {
        ma_user_id: ma_user_id,
        ma_commission_type: 75,
        ma_deduction_type: 2,
        amount,
        connection: conn
      })

      if ((typeof (commissionData.status) !== 'undefined' && commissionData.status === 400) || commissionData.length === 0) {
        commissionAmount = 0
      } else {
        commissionAmount = commissionData.commissionVal
      }

      // For credit to airpay super user
      executionResponse = await this.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: amount,
        mode: 'cr',
        transaction_type: 75,
        description: `${util.pointsToCashAirpayCreditDescription} - ${descType}`,
        orderid: orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: conn
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }

      /* Calculation for Cash */
      amount = amount / pointFactor

      const cashLedger = require('./cashLedgerController')
      // For debit from airpay
      executionResponse = await cashLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'dr',
        transaction_type: 75,
        description: util.pointsToCashAirpayDebitDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: ma_user_id,
        connection: conn
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }
      // For credit to merchant
      executionResponse = await cashLedger.createEntry('_', {
        ma_user_id,
        amount,
        mode: 'cr',
        transaction_type: 75,
        description: util.pointsToCashMerchantCreditDescription + type,
        orderid,
        userid,
        ma_status: 'S',
        corresponding_id: util.airpayUserId,
        connection: conn
      })

      if (executionResponse.status === 400) {
        if (isSet) await mySQLWrapper.rollback(conn)
        return executionResponse
      }

      // Transaction Commit
      if (isSet) await mySQLWrapper.commit(conn)

      return executionResponse
    } catch (err) {
      if (isSet) await mySQLWrapper.rollback(conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'convertPointsToCash', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'convertPointsToCash', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }
}

module.exports = pointsLedger
