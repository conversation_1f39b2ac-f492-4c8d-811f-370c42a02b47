/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class cashAccount extends DAO {
  /**
       * Overrides TABLE_NAME with this class' backing table at MySQL
       */
  static get TABLE_NAME () {
    return 'ma_points_account'
  }

  static get PRIMARY_KEY () {
    return 'ma_points_account_id'
  }

  /**
     * Returns a cash account entry by its ID
     */
  static async getByID (_, { id }) {
    // eslint-disable-next-line no-return-await
    return await this.find(id)
  }

  /**
     * New entry in points account
     */
  static async createEntry (_, { ma_user_id, amount, transaction_type, ma_status, orderid, userid, corresponding_id, connection = null }) {
    log.logger({ pagename: 'pointsAccountController.js', action: 'createEntry', type: 'request', fields: { ma_user_id, amount, transaction_type, ma_status, orderid, userid, corresponding_id } })
    var isSet = false
    try {
      // Create seperate connection if not passed as an argument
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      // Get points factor
      const pointsRate = require('../pointsRate/pointsRateController')
      const cashToPointsRate = await pointsRate.getGlobalPointsRate(_, { connection })
      if (cashToPointsRate.status === 400) {
        return { status: 400, message: 'fail : Global rates missing' }
      }
      if (corresponding_id === null || corresponding_id === undefined) {
        corresponding_id = util.airpayUserId
      }
      if (transaction_type === 'CW' && amount > 0) {
        amount = -amount
      }

      var sql = ''
      var sales_id = 0

      if (ma_user_id != util.airpayCommissionId && ma_user_id != util.airpayUserId) {
        sql = `select ma_user_master_id,sales_id from ma_user_master where profileid=${ma_user_id}`
      } else if (corresponding_id != util.airpayCommissionId && corresponding_id != util.airpayUserId) {
        sql = `select ma_user_master_id,sales_id from ma_user_master where profileid=${corresponding_id}`
      }
      if (sql != '') {
        const userDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: 'pointsAccountController.js', action: 'createEntry', type: 'response-UserDetails', fields: userDetails })

        if (userDetails.length > 0) {
          sales_id = userDetails[0].sales_id
        }
      }

      // Condition for parentid
      let parent_id = ''
      if (orderid.includes('-')) {
        parent_id = (orderid).substr(orderid.lastIndexOf('-') + 1)
      } else {
        parent_id = orderid
      }
      const getTransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await getTransactionController.getTransactionDetailsByParentId(parent_id, connection)

      const _result = await this.insert(connection, {
        data: {
          ma_user_id, amount, transaction_type, points_factor: cashToPointsRate.points_value, ma_status, orderid, userid, corresponding_id, sales_id, parent_id, parent_transaction_master_id
        }
      })
      log.logger({ pagename: 'pointsAccountController.js', action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (isSet) connection.release()
    }
  }
}

module.exports = cashAccount
