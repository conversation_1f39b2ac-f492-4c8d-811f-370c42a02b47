const { default: Axios } = require('axios')
const moment = require('moment-timezone')

const DAO = require('../../lib/dao')
const log = require('../../util/log')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const common = require('../../util/common')
const util = require('../../util/util')
const { createHash } = require('../../util/common')
const { maTransactionStatus, txnEnumType } = require('../../model/commonEnum')

class RiskManagementController extends DAO {
  static get REQUEST_TYPE () {
    return 'transaction'
  }

  static get ZONE_TYPE () {
    return {
      1: 'North',
      2: 'South',
      3: 'East',
      4: 'West'
    }
  }

  static getStatus (statusCode) {
    return maTransactionStatus.getValues().filter(status => status.value == statusCode)[0].name
  }

  static getTxnType (txnType) {
    return txnEnumType.getValues().filter(status => status.value == txnType)[0].name
  }

  /**
   * UPDATE RISK ANALYSIS SCORE
   * @returns
   */
  static async updateRiskScore () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const isRiskAnalysisActive = await this.isRiskAnalysisActive({ connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'isRiskAnalysisActive', fields: isRiskAnalysisActive })
      if (isRiskAnalysisActive.status != 200) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      const maxRiskApiRetryCount = await common.getSystemCodes(this, util.max_risk_api_retry_count, connectionRead)
      const riskApiBatchSize = await common.getSystemCodes(this, util.risk_api_batch_size, connectionRead)
      /* FETCH ALL TRANSACTION */
      const fetchRequestQuery = `SELECT * FROM ma_risk_management_api_log WHERE cron_status = 'I' AND retry_count < ${maxRiskApiRetryCount}  LIMIT ${riskApiBatchSize}`
      const requestResult = await this.rawQuery(fetchRequestQuery, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'requestResult', fields: requestResult })
      if (requestResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }
      /* UPDATE STATUS TO 'P' and retry count AND BLOCK THE CURRENT PROGRESSING REQUEST */
      const ids = requestResult.map(request => request.ma_risk_management_api_log_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'requestResultIds', fields: ids })
      const updateRequestQuery = `UPDATE ma_risk_management_api_log SET cron_status='P',retry_count=retry_count+1 WHERE ma_risk_management_api_log_id IN (${ids.join(',')})`
      const updateResult = await this.rawQuery(updateRequestQuery, connection)
      if (updateResult.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }

      /* PROCESS REQUEST */
      /* comment old code - 30-05-2023
      const requestList = []
      for (let index = 0; index < requestResult.length; index++) {
        const request = requestResult[index]
        requestList.push(this.riskApiCall({ request, connectionRead, connection }))
      }
      const apiRequests = await Promise.allSettled(requestList)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'apiRequests', fields: apiRequests })
      */
      // Create batchwise execution for 20 records per batch. - 30-05-2023
      const AllbatchProcess = []
      let smallBatch = []
      const batchSize = 20
      /* CREATE BATCH PROCESS */
      for (let index = 0; index < requestResult.length; index++) {
        const request = requestResult[index]
        smallBatch.push(this.riskApiCall({ request, connectionRead, connection }))
        if (smallBatch.length == batchSize || index >= (requestResult.length - 1)) {
          /* clear */
          AllbatchProcess.push(smallBatch)
          smallBatch = []
        }
      }
      /* PROCESS THE REQUEST */
      for (let index = 0; index < AllbatchProcess.length; index++) {
        const batchProcess = AllbatchProcess[index]
        const apiRequests = await Promise.allSettled(batchProcess)
        log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'apiRequests', fields: apiRequests })
      }
      // const calculateRiskAnalysisRequest = apiParamsRequests
      //   .filter(request => request.status == 200)
      //   .map(payloadResp => this.riskApiCall({ request: payloadResp.request, payLoad: payloadResp.payLoad, connection }))

      // if (calculateRiskAnalysisRequest.length > 0) {
      //   const apiRequests = await Promise.all(calculateRiskAnalysisRequest)
      //   log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'apiRequests', fields: apiRequests })
      // }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  static async apiPayLoad ({ request, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiPayLoad', type: 'request', fields: request })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* GENERATE API REQUEST OBJECT */
      const apiParamsResp = await this.apiParams({ fields: request, connectionRead: connRead })
      if (apiParamsResp.status != 200) {
        /* REVERT THE STATUS INCASE OF INTERNAL ERROR */
        const updateRequestQuery = `UPDATE ma_risk_management_api_log SET cron_status='I',api_response='${JSON.stringify(apiParamsResp)}' WHERE ma_risk_management_api_log_id = ${request.ma_risk_management_api_log_id}`
        const updateResult = await this.rawQuery(updateRequestQuery, conn)
        if (updateResult.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        return apiParamsResp
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'apiPayLoad', type: 'apiParams', fields: apiParamsResp })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, payLoad: apiParamsResp.apiParams, request }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiPayLoad', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{request:Object,payLoad:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async riskApiCall ({ request, connectionRead, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'request', fields: { request } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const payLoadResp = await this.apiPayLoad({ request, connectionRead, connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'payLoadResp', fields: payLoadResp })
      if (payLoadResp.status != 200) return payLoadResp
      const payLoad = payLoadResp.payLoad
      const riskApiResponse = await this.callRiskScoreAPI({ payLoad })

      if (riskApiResponse.status == 400) return riskApiResponse

      /* UPDATE REQUEST */
      const updateRequestResp = await this.updateRequest({ fields: request, request: payLoad, response: riskApiResponse, connection: conn })
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'updateRequest', fields: updateRequestResp })
      if (updateRequestResp.status != 200) return updateRequestResp
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object, request:Object, response:AxiosResponse<any>,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async updateRequest ({ fields, request, response, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateRequest', type: 'request', fields: { fields, request, response } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const { data: responseData } = response
      let cronStatus = ''
      /* IF 200 UDPATE C , request , response in 'ma_risk_management_api_log'  */
      /* IF 200 then insert 'ma_transaction_risk_score_analysis' */
      if (responseData.status == 200) {
        cronStatus = 'C'
        await this.saveTransactionRiskAnalysis({ fields, request, response, connection: conn })
      }
      /* IF NOT 200 UDPATE I , request , response */
      if (responseData.status != 200) {
        cronStatus = 'I'
      }
      const updateRequestQuery = `UPDATE ma_risk_management_api_log SET cron_status='${cronStatus}',api_status='${responseData.status || 400}',api_request='${JSON.stringify(request)}',api_response='${JSON.stringify(responseData || {})}' WHERE ma_risk_management_api_log_id = ${fields.ma_risk_management_api_log_id}`
      const updateResult = await this.rawQuery(updateRequestQuery, conn)
      if (updateResult.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRequest', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object, request:Object, response:AxiosResponse<any>,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async saveTransactionRiskAnalysis ({ fields, request, response, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateRequest', type: 'request', fields: { fields, request, response } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const { data: responseData } = response
      const { request_id, score, weightage } = responseData.data
      this.TABLE_NAME = 'ma_transaction_risk_score_analysis'
      const data = {
        request_id,
        score,
        weightage,
        ma_user_id: fields.ma_user_id,
        aggregator_order_id: fields.aggregator_order_id,
        transaction_type: fields.transaction_type,
        request_status: responseData.status,
        request: JSON.stringify(request),
        response: JSON.stringify(responseData)
      }
      await this.insert(conn, { data })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRequest', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:Object,checkSumKey:string}} param0
   * @returns
   */
  static async calculateCheckSum ({ apiParams, checkSumKey }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateCheckSum', type: 'request', fields: { apiParams, checkSumKey } })
    try {
      let paramsString = ''
      for (const key in apiParams) {
        const element = apiParams[key]
        if (typeof element == 'string') {
          paramsString += element
        }

        if (typeof element == 'object') {
          for (const key in element) {
            paramsString += element[key]
          }
        }
      }

      const checksum = createHash(`${paramsString}${checkSumKey}`)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, checksum }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculateCheckSum', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiParams ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiParams', type: 'request', fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const defaultParamsResp = await this.defaultApiParams({ fields, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'apiParams', type: 'defaultParamsResp', fields: defaultParamsResp })
      if (defaultParamsResp.status != 200) return defaultParamsResp
      const { defaultParams } = defaultParamsResp
      /* NEW CHANGES */
      const fetchPreviousRequestQuery = `SELECT request FROM ma_transaction_risk_score_analysis WHERE aggregator_order_id = '${fields.aggregator_order_id}' ORDER BY added_on DESC LIMIT 1`
      const fetchPreviousRequestResult = await this.rawQuery(fetchPreviousRequestQuery, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchPreviousRequest', type: 'result', fields: fetchPreviousRequestResult })
      let previousRequest = {}
      if (fetchPreviousRequestResult.length > 0) {
        previousRequest = JSON.parse(fetchPreviousRequestResult[0].request)
        defaultParams.request_id = previousRequest.request_id
        defaultParams.device_id = previousRequest.device_id
        if (previousRequest.customer_details) defaultParams.customer_details = { ...defaultParams.customer_details, ...previousRequest.customer_details }
        if (previousRequest.service_details) defaultParams.service_details = { ...defaultParams.service_details, ...previousRequest.service_details }
      }
      /* NEW CHANGES END */
      const transactionType = fields.transaction_type
      switch (transactionType) {
        case '2':
          return this.apiDMTParams({ fields, params: defaultParams, connectionRead: connRead })
        case '21':
          return this.apiBeneValidationParams({ fields, params: defaultParams, previousRequest, connectionRead: connRead })
        case '28':
          return this.apiGoldParams({ fields, params: defaultParams, connectionRead: connRead })
        case '24':
          return this.apiCMSParams({ fields, params: defaultParams, connectionRead: connRead })
        case '34':
          return this.apiBazaarParams({ fields, params: defaultParams, connectionRead: connRead })
        case '44':
          return this.apiONDCParams({ fields, params: defaultParams, connectionRead: connRead })
        default:
          return this.apiDeafultParams({ fields, params: defaultParams, connectionRead: connRead })
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async defaultApiParams ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'defaultApiParams', type: 'fields', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const merchantApiParamsResp = await this.merchantApiParams({ fields, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'defaultApiParams', type: 'merchantApiParamsResp', fields: merchantApiParamsResp })
      if (merchantApiParamsResp.status != 200) return merchantApiParamsResp

      const defaultParams = {
        request_id: this.requestID(),
        request_type: this.REQUEST_TYPE,
        service_type: '',
        request_status: this.getStatus(fields.transaction_status),
        device_id: '',
        request_time: moment.tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss'),
        merchant_details: { ...merchantApiParamsResp.params },
        customer_details: {
          name: '',
          phone: '',
          email: '',
          city: '',
          state: '',
          customer_ip: ''
        },
        service_details: {
          domain: util.isProduction() ? 'vyaapaar.airpay.co.in' : 'vyaapaar.airpay.ninja',
          ip: '',
          amount: '',
          card_f6: '',
          card_e4: '',
          currency_code: '356',
          service_id: ''
        },
        beneficiary_details: {
          account_no: '',
          phone_no: '',
          name: '',
          bank_name: ''
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, defaultParams }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'defaultApiParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async merchantApiParams ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'merchantApiParams', type: 'request', fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchMerchantDetailQuery = `SELECT mu.profileid,mu.state,mu.city,mu.user_type,mu.company,mu.firstname,mu.lastname,mu.zone,DATE_FORMAT(mu.addedon,'%Y-%m-%d %H:%i:%s') as addedon,ct.name as city_master_name, mu.state as state_id, st.name as state_master_name, mu.mcc 
      FROM ma_user_master mu
      JOIN ma_cities_master ct on ct.id = mu.city
      JOIN ma_states_master st on st.id = mu.state 
      WHERE userid=${fields.userid} AND profileid=${fields.ma_user_id}  LIMIT 1`
      const merchantDetailResult = await this.rawQuery(fetchMerchantDetailQuery, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'merchantApiParams', type: 'merchantDetailResult', fields: merchantDetailResult })
      if (merchantDetailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1137], respcode: 1137 }
      const merchantDetail = merchantDetailResult[0]
      const params = {
        merchant_id: merchantDetail.profileid,
        merchant_state: merchantDetail.state_master_name,
        merchant_city: merchantDetail.city_master_name,
        activation_date: merchantDetail.addedon,
        merchant_type: merchantDetail.user_type,
        company_name: merchantDetail.company,
        owener_name: `${merchantDetail.firstname} ${merchantDetail.lastname}`,
        zone: this.ZONE_TYPE[merchantDetail.zone],
        mcc: merchantDetail.mcc
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'merchantApiParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiDMTParams ({ fields, params, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiDMTParams', type: 'request', fields: { fields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT 
      mtm.amount,
      mtm.mobile_number,
      mcd.remitter_name,
      mtmd.bank_name,
      mb.ben_mobile_number,
      mb.account_number,
      mb.beneficiary_name,
      mb.ma_bene_verification_id,
      mb.ifsc_code
      FROM ma_transaction_master mtm
      JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      JOIN ma_transfers mt ON mt.ma_transaction_master_id = mtm.ma_transaction_master_id
      JOIN ma_beneficiaries mb ON mb.ma_beneficiaries_id = mt.ma_beneficiaries_id
      JOIN ma_customer_details mcd ON mcd.mobile_number = mt.mobile_number
      WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]

      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.name = data.remitter_name || ''
      params.customer_details.phone = Number(data.mobile_number) ? data.mobile_number : ''
      params.service_details.amount = data.amount
      params.beneficiary_details.account_no = data.account_number || ''
      params.beneficiary_details.phone_no = Number(data.ben_mobile_number) ? data.ben_mobile_number : ''
      params.beneficiary_details.name = data.beneficiary_name || ''
      params.beneficiary_details.bank_name = data.bank_name || ''
      if (data.ma_bene_verification_id > 0) {
        const beneNameQuery = `SELECT bank_benename FROM ma_bene_verification WHERE ma_bene_verification_id = ${data.ma_bene_verification_id} AND bene_verify_status ='S';`
        const beneNameResult = await this.rawQuery(beneNameQuery, connRead)
        log.logger({ pagename: require('path').basename(__filename), action: 'beneName', type: 'result', fields: beneNameResult })
        if (beneNameResult.length > 0) params.beneficiary_details.name = beneNameResult[0].bank_benename
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiDMTParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,previousRequest:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiBeneValidationParams ({ fields, params, previousRequest, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiBeneValidationParams', type: 'request', fields: { fields, previousRequest } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT 
      mtm.amount,
      mtm.mobile_number,
      mtm.ma_transaction_master_id
      FROM ma_transaction_master mtm
      JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]
      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.phone = Number(data.mobile_number) ? data.mobile_number : ''
      params.service_details.amount = data.amount
      /* SET BENE DATA */
      params.customer_details.name = previousRequest.customer_details.name
      params.beneficiary_details.account_no = previousRequest.beneficiary_details.account_no || ''
      params.beneficiary_details.phone_no = Number(previousRequest.beneficiary_details.phone_no) ? previousRequest.beneficiary_details.phone_no : ''
      params.beneficiary_details.name = previousRequest.beneficiary_details.name || ''
      params.beneficiary_details.bank_name = previousRequest.beneficiary_details.bank_name || ''

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiBeneValidationParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiGoldParams ({ fields, params, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiGoldParams', type: 'request', fields: { fields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT 
      mtm.amount,
      mgcb.customer_name,
      mgcb.email,
      mtm.mobile_number
      FROM ma_transaction_master mtm
      JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      JOIN ma_gold_customer_on_boarding mgcb ON mgcb.mobile = mtm.mobile_number
      WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]
      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.name = data.customer_name || ''
      params.customer_details.email = data.email || ''
      params.customer_details.phone = Number(data.mobile_number) ? data.mobile_number : ''
      params.service_details.amount = data.amount || ''

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiGoldParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiCMSParams ({ fields, params, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiCMSParams', type: 'request', fields: { fields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT 
        mtm.amount,
        cms.merchant_name,
        cms.email,
        mtm.mobile_number
        FROM ma_transaction_master mtm
        JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
        JOIN ma_cms_merchant_on_boarding as cms on mtmd.cms_ma_user_id = cms.ma_user_id
        WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]
      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.name = data.merchant_name || ''
      params.customer_details.email = data.email || ''
      params.customer_details.phone = Number(data.mobile_number) ? data.mobile_number : ''
      params.service_details.amount = data.amount || ''

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiCMSParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiDeafultParams ({ fields, params, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiDeafultParams', type: 'request', fields: { fields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT 
      mtm.amount,
      mtmd.customer_name,
      mtmd.customer_mobile,
      mtmd.customer_email,
      mtm.mobile_number
      FROM ma_transaction_master mtm
      JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]
      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.name = data.customer_name || ''
      params.customer_details.email = data.customer_email || ''
      if (Number(data.customer_mobile)) { params.customer_details.phone = data.customer_mobile }
      if (Number(data.mobile_number)) { params.customer_details.phone = data.mobile_number }
      params.service_details.amount = data.amount

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiDeafultParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @returns string
   */
  static requestID () {
    const length = 10
    let result = ''
    const characters = '0123456789'
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
  }

  static async insertNewRequest ({ ma_user_id, userid, aggregator_order_id, transaction_type, transaction_status, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'insertNewRequest', type: 'request', fields: { transaction_status, ma_user_id, userid, aggregator_order_id, transaction_type } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const isRiskAnalysisActive = await this.isRiskAnalysisActive({ connectionRead: conn })
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'isRiskAnalysisActive', fields: isRiskAnalysisActive })
      if (isRiskAnalysisActive.status != 200) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      if (!['I', 'P', 'F', 'S'].includes(transaction_status)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      const insertQuery = `INSERT IGNORE INTO ma_risk_management_api_log ( ma_user_id,userid,aggregator_order_id,transaction_status,transaction_type) VALUES(${ma_user_id},${userid},'${aggregator_order_id}','${transaction_status}',${transaction_type})`
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewRequest', type: 'insertQuery', fields: insertQuery })
      const result = await this.rawQuery(insertQuery, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewRequest', type: 'insert result', fields: result })
      if (result.affectedRows == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewRequest', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiONDCParams ({ fields, params, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiONDCParams', type: 'request', fields: { fields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT
      mtm.amount,
      mtmd.customer_name,
      mtmd.customer_mobile,
      mtmd.customer_email,
      mocd.state,
      mocd.city
      FROM ma_transaction_master mtm
      JOIN ma_transaction_master_details mtmd ON mtm.ma_transaction_master_id = mtmd.ma_transaction_master_id
      JOIN ma_ondc_order_details mood ON mtm.aggregator_order_id = mood.aggregator_order_id
      JOIN ma_ondc_customer_details mocd ON mood.customer_mailing_id = mocd.ma_ondc_customer_details_id
      WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]
      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.name = data.customer_name || ''
      params.customer_details.email = data.customer_email || ''
      params.customer_details.phone = Number(data.customer_mobile) ? data.customer_mobile : ''
      params.customer_details.city = data.city || ''
      params.customer_details.state = data.state || ''
      params.service_details.amount = data.amount || ''

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiONDCParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,params:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async apiBazaarParams ({ fields, params, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'apiBazaarParams', type: 'request', fields: { fields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchDetailQuery = `SELECT
      mtm.amount,
      mssm.name,
      mssm.phone,
      mssm.email
      FROM ma_transaction_master mtm
      JOIN ma_shopping_shipping_master mssm ON mssm.orderid = mtm.aggregator_order_id
      WHERE mtm.aggregator_order_id = '${fields.aggregator_order_id}' LIMIT 1`
      const detailResult = await this.rawQuery(fetchDetailQuery, connRead)
      if (detailResult.length == 0) return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }

      /* UPDATE THE PARAMS */
      const data = detailResult[0]
      params.service_type = this.getTxnType(fields.transaction_type)
      params.customer_details.name = data.name || ''
      params.customer_details.email = data.email || ''
      params.customer_details.phone = Number(data.phone) ? data.phone : ''
      params.service_details.amount = data.amount || ''

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, apiParams: params }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'apiBazaarParams', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async isRiskAnalysisActive ({ connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isRiskAnalysisActive', type: 'request', fields: { } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isRiskAnalysisActive = await common.getSystemCodes(this, util.is_risk_analysis_active, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'isRiskAnalysisActive', type: 'value', fields: isRiskAnalysisActive })

      if (isRiskAnalysisActive == 'Y') return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isRiskAnalysisActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async isRiskSQSActive ({ connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isRiskSQSActive', type: 'request', fields: { } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isRiskSQSActive = await common.getSystemCodes(this, util.is_risk_sqs_active, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'isRiskSQSActive', type: 'value', fields: isRiskSQSActive })

      if (isRiskSQSActive == 'Y') return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isRiskSQSActive', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{payLoad:Object}} param0
   */
  static async callRiskScoreAPI ({ payLoad }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'callRiskScoreAPI', type: 'request', fields: { payLoad } })
    try {
      const env = util[process.env.NODE_ENV || 'development']
      const airpayKey = env.riskMangementAirpayKey
      const partnerKey = env.riskMangementPartnerKey
      const checkSumKey = env.riskCheckSumKey
      /* CALCULATE CHECKSUM */
      const checksumResp = await this.calculateCheckSum({ apiParams: payLoad, checkSumKey })
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'calculateCheckSum', fields: checksumResp })
      if (checksumResp.status != 200) return checksumResp
      /* CALL API */
      console.time('TIMER_MS_RISK_ANALYSIS')
      const requestStart = process.hrtime()
      const riskApiResponse = await Axios.post(env.riskManagementURL, payLoad, {
        headers: {
          'Content-Type': 'application/json',
          AIRPAYKEY: airpayKey,
          PARTNERKEY: partnerKey,
          CHECKSUM: checkSumKey
        },
        timeout: 5000 // 5 seconds
      })
      console.timeEnd('TIMER_MS_RISK_ANALYSIS')

      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'riskApiResponse', fields: riskApiResponse })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000
        console.log('API_MS_RISK_ANALYSIS_REQUEST_[' + JSON.stringify(payLoad) + ']_RESPONSE_[' + JSON.stringify(riskApiResponse.data) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      return riskApiResponse
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'callRiskScoreAPI', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   *
   * @param {{connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async isTransactionAboveRiskScoreThreshold ({ score, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionAboveRiskScoreThreshold', type: 'request', fields: { score } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isBlockTransactionApplicable = await common.getSystemCodes(this, util.risk_block_transaction, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'isBlockTransactionApplicable', type: 'value', fields: isBlockTransactionApplicable })

      if (isBlockTransactionApplicable != 'Y') return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      const riskScoreThresholdValue = await common.getSystemCodes(this, util.risk_score_threshold, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'riskScoreThresholdValue', type: 'value', fields: riskScoreThresholdValue })

      if (score > riskScoreThresholdValue) return { status: 400, message: `${errorMsg.responseCode[1028]} : transaction blocked`, respcode: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionAboveRiskScoreThreshold', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{requestParams:object,connection:Promise<mySQLWrapper.getConnectionFromReadReplica()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async checkTransactionRiskAnalysis ({ requestParams, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkTransactionRiskAnalysis', type: 'request', fields: requestParams })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* CHECK IF RISK ANALYSIS IS ACTIVE */
      const isRiskAnalysisActive = await this.isRiskAnalysisActive({ connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'updateRiskScore', type: 'isRiskAnalysisActive', fields: isRiskAnalysisActive })
      if (isRiskAnalysisActive.status != 200) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      /* DEFAULT PAYLOAD */
      const defaultParamsResp = await this.defaultApiParams({ fields: requestParams, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'apiParams', type: 'defaultParamsResp', fields: defaultParamsResp })
      if (defaultParamsResp.status != 200) return defaultParamsResp
      const { defaultParams } = defaultParamsResp

      const payLoad = { ...defaultParams }
      const deviceResp = await this.merchantDeviceId({ ma_user_id: requestParams.ma_user_id, userid: requestParams.userid, connectionRead: connRead })
      if (deviceResp.status == 200) payLoad.device_id = deviceResp.device_id
      /* UPDATE PARAMS */
      let coordinates = ''
      requestParams.latitude = requestParams.latitude || ''
      requestParams.longitude = requestParams.longitude || ''
      console.log('lat :', requestParams.latitude)
      console.log('long :', requestParams.longitude)
      if (requestParams.latitude != '' && requestParams.longitude != '') {
        coordinates = requestParams.latitude + ',' + requestParams.longitude
        console.log('coordinates', coordinates)
      }
      payLoad.service_type = this.getTxnType(requestParams.transaction_type)
      payLoad.customer_details.name = requestParams.customer_name || ''
      payLoad.customer_details.email = requestParams.customer_email || ''
      payLoad.customer_details.phone = Number(requestParams.customer_phone) ? requestParams.customer_phone : ''
      payLoad.customer_details.customer_ip = requestParams.ip_address // new ip changes
      payLoad.customer_details.coordinates = coordinates // new lat, long changes
      payLoad.service_details.amount = requestParams.amount
      payLoad.service_details.ip = requestParams.ip_address // new ip changes
      payLoad.beneficiary_details.account_no = requestParams.account_number || ''
      payLoad.beneficiary_details.phone_no = Number(requestParams.ben_mobile_number) ? requestParams.ben_mobile_number : ''
      payLoad.beneficiary_details.name = requestParams.beneficiary_name || ''
      payLoad.beneficiary_details.bank_name = requestParams.bank_name || ''

      // AEPS,MS Changes
      if (requestParams.aadhaar_last_4) payLoad.customer_details.aadhaar_last_4 = requestParams.aadhaar_last_4
      // IPN Changes
      if (requestParams.customer_vpa) payLoad.service_details.customer_vpa = requestParams.customer_vpa
      if (requestParams.card_f6) payLoad.service_details.card_f6 = requestParams.card_f6
      if (requestParams.card_e4) payLoad.service_details.card_e4 = requestParams.card_e4
      if (requestParams.terminal_id) payLoad.service_details.terminal_id = requestParams.terminal_id

      /* API CALL */
      const riskAPIResp = await this.callRiskScoreAPI({ payLoad })
      log.logger({ pagename: require('path').basename(__filename), action: 'riskAPIResp', type: 'response', fields: riskAPIResp })
      if (riskAPIResp.status == 400) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      if (!('data' in riskAPIResp) || (riskAPIResp.data && riskAPIResp.data.status == 400)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      /* SAVE TRANSACTION RISK RESPONSE */
      const { data: responseData } = riskAPIResp
      /* IF 200 UDPATE C , request , response in 'ma_risk_management_api_log'  */
      /* IF 200 then insert 'ma_transaction_risk_score_analysis' */
      if (responseData.status == 200) {
        await this.saveTransactionRiskAnalysis({ fields: requestParams, request: payLoad, response: riskAPIResp, connection: conn })
      }
      const { score } = responseData.data
      /* BLOCK TRANSACTION BASED UPON SCORE */
      const isTransactionAboveRiskScoreThreshold = await this.isTransactionAboveRiskScoreThreshold({ score, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'isTransactionAboveRiskScoreThreshold', type: 'value', fields: isTransactionAboveRiskScoreThreshold })
      /* UDPATE THE TRANSACTION STATUS */

      if (isTransactionAboveRiskScoreThreshold.status != 200) {
        /* const transactionController = require('../transaction/transactionController')
        await transactionController.updateWhereData(conn, {
          data: { transaction_status: 'F', transaction_reason: isTransactionAboveRiskScoreThreshold.message },
          id: requestParams.aggregator_order_id,
          where: 'aggregator_order_id'
        }) */
        return isTransactionAboveRiskScoreThreshold
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkTransactionRiskAnalysis', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{request:object,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async calculateRiskScore ({ request, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateRiskScore', type: 'request', fields: { request } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* RISK API CALL */
      const payLoadResp = await this.apiParams({ fields: request, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'payLoadResp', fields: payLoadResp })
      if (payLoadResp.status != 200) return payLoadResp
      const riskApiResponse = await this.callRiskScoreAPI({ payLoad: payLoadResp.apiParams })
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiResponse', type: 'response', fields: riskApiResponse })
      if (riskApiResponse.status == 400) {
        this.insertNewRequest({
          ma_user_id: request.ma_user_id,
          userid: request.userid,
          transaction_type: request.transaction_type,
          aggregator_order_id: request.aggregator_order_id,
          transaction_status: request.transaction_status,
          connection: conn
        })
        return riskApiResponse
      }
      /* UPDATE THE REQUEST :: SAVE TRANSACTION RISK RESPONSE */
      const { data: responseData } = riskApiResponse
      if (responseData.status == 200) {
        await this.saveTransactionRiskAnalysis({ fields: request, request: payLoadResp.apiParams, response: riskApiResponse, connection: conn })
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'riskApiCall', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{SQSDATA:Array<Object>,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async doSQSPost ({ SQSDATA, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doSQSPost', type: 'request', fields: { SQSDATA } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      if (SQSDATA.length > 0) {
        console.log(')this.SQSDATA>>', SQSDATA)
        console.log('SQS Calling....')
        const queueLib = require('../../util/sqs')
        console.time('TIMER_BULK_SQS_PROCESS')
        await queueLib.SinglePushToQueue(SQSDATA, 'risk_score_messages', conn)
        console.timeEnd('TIMER_BULK_SQS_PROCESS')
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doSQSPost', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{ma_user_id:Number,userid:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async merchantDeviceId ({ ma_user_id, userid, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doSQSPost', type: 'request', fields: { ma_user_id, userid } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      let device_id = ''
      const merchantDeviceIDQuery = `SELECT imei FROM ma_user_devices WHERE ma_user_id = ${ma_user_id} AND userid = ${userid}  AND verify_flag='V' ORDER BY updatedon DESC LIMIT 1`
      const merchantDeviceIDResult = await this.rawQuery(merchantDeviceIDQuery, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'merchantDeviceID', type: 'result', fields: merchantDeviceIDResult })
      if (merchantDeviceIDResult.length > 0) device_id = merchantDeviceIDResult[0].imei
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, device_id }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doSQSPost', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }
}

module.exports = RiskManagementController
