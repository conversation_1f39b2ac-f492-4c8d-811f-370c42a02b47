const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class Notice extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_notices'
  }

  static get PRIMARY_KEY () {
    return 'ma_notices_id'
  }

  static async getNotices (_, fields) {
    log.logger({ pagename: 'noticeController.js', action: 'getNotices', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      let mauseridcond = ''
      let timeCondition = ''
      if (typeof fields.ma_user_id !== 'undefined' && fields.ma_user_id !== null) {
        mauseridcond = ` OR (noticeType = 'MW' AND ma_user_id = ${fields.ma_user_id})`
      }

      if (fields.displaytype == '1' || fields.displaytype == '2') {
        timeCondition = ' AND CURRENT_TIMESTAMP BETWEEN start_date AND end_date'
      }

      var sql = `SELECT message AS messageText,notice_flag,DATE_FORMAT(addedon,'%d-%m-%Y') AS addedon, url, link_type, start_date, end_date 
                FROM ma_notices 
                WHERE  ((noticeType = 'GEN') ${mauseridcond}) 
                AND displayType = ${fields.displaytype} 
                AND notice_status = 'A'
                ${timeCondition}
                ORDER BY  start_date DESC`
      if (fields.displaytype == '1' && (typeof fields.offset != 'undefined' && typeof fields.limit != 'undefined')) {
        sql += `  LIMIT ${fields.offset},${fields.limit}`
      }

      if (fields.displaytype == '3') {
        sql += '  LIMIT 1'
      }

      console.log(sql)
      const result = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'noticeController.js', action: 'getNotices', type: 'response', fields: result })
      if (result.length > 0) {
        let notificationCount = 0

        if (fields.displaytype == '3') {
          return { status: 301, respcode: 1000, message: errorMsg.responseCode[1000], list: result, count: notificationCount, notice_flag: result[0].notice_flag, messageText: result[0].messageText, link_type: result[0].link_type }
        }

        if (fields.displaytype == '1') {
          var countsql = `SELECT COUNT(1) AS newCount
                      FROM ma_notices 
                      WHERE  ((noticeType = 'GEN') ${mauseridcond}) 
                      AND displayType = ${fields.displaytype} 
                      AND notice_status = 'A'
                      AND DATE(start_date) = CURDATE()
                      AND end_date > CURRENT_TIMESTAMP`
          console.log('---countsql---', countsql)
          const countresult = await this.rawQuery(countsql, connection)
          console.log('---countresult---', countresult)
          if (countresult.length > 0) {
            notificationCount = countresult[0].newCount
          }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], list: result, count: notificationCount, notice_flag: result[0].notice_flag, messageText: result[0].messageText, link_type: result[0].link_type }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002], list: [], count: result.length }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getNotices', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /* static async getNotices (_, fields) {
    log.logger({ pagename: 'noticeController.js', action: 'getNotices', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let mauseridcond = ''
      if (typeof fields.ma_user_id !== 'undefined' && fields.ma_user_id !== null) {
        mauseridcond = ` OR (noticeType = 'MW' AND ma_user_id = ${fields.ma_user_id})`
      }
      var sql = `SELECT message,notice_flag  FROM ma_notices WHERE  ((noticeType = 'GEN') ${mauseridcond}) AND displayType = ${fields.displaytype} ORDER BY  addedon DESC`
      if (fields.displaytype == '1') {
        sql += `  LIMIT ${fields.offset},${fields.limit}`
      }
      console.log(sql)
      const result = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'noticeController.js', action: 'getNotices', type: 'response', fields: result })
      return result
    } catch (err) {
      log.logger({ pagename: 'getNotices.js', action: 'getNotices', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + err }
    } finally {
      connection.release()
    }
} */
}

module.exports = Notice
