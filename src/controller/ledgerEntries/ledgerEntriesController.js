const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const balanceController = require('../balance/balanceController')
const common = require('../../util/common')
const collectMoneyDistributionController = require('../incentive/collectMoneyDistributionController')
const mircroAtmDistributionController = require('../incentive/matmDistributionController')
const cmsDistributionController = require('../incentive/cmsDistributionController')
const aepsDistributionController = require('../incentive/aepsDistributionController')
// const ipnController = require('../ipn/ipnController')

class ledgerEntries extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {

  }

  static get PRIMARY_KEY () {

  }

  /**
   * Distribution Configuration for AEPS CW Incentive
   * fields : amount, ma_user_id,state_master_id,connection
   */
  static async aepsLedgerEntries (fields, userid) {
    try {
      fields.userid = userid
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'aepsLedgerEntries', type: 'request', fields: fields })

      let tempObj = {}
      const {
        charge,
        gst,
        afterDeduct,
        bankCharge,
        txnAmount,
        minAmount,
        maxAmount,
        type,
        bankName,
        isGstInclusive
      } = await this.calculatePaymodeCharges({ amount: fields.amount, type: 'aeps', ma_user_id: fields.ma_user_id, userid: fields.userid, transaction_mode: fields.transaction_mode, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'aepsLedgerEntries', type: 'aepsCharges function call result', fields: { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } })

      if ((Number(fields.amount) >= Number(minAmount)) && (Number(fields.amount) <= Number(maxAmount))) {
        tempObj = { bankCharge, txnAmount, bankName, isGstInclusive, platformFee: isGstInclusive ? charge : charge + gst }

        if (fields.transaction_mode == '1') {
          tempObj.type = 'CW'
        } else if (fields.transaction_mode == '2') {
          tempObj.type = 'MS'
        } else if (fields.transaction_mode == '3') {
          tempObj.type = 'BE'
        } else {
          tempObj.type = type
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'aepsLedgerEntries', type: 'paymode charge process', fields: { charge: charge, GST: gst, otherDetails: tempObj } })
      }

      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '5' }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      const cashCredit = await cashAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (cashCredit.status === 400) {
        return cashCredit
      }
      const pointsCredit = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsCredit.status === 400) {
        return pointsCredit
      }

      const airpayPoints = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '5',
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (airpayPoints.status === 400) {
        return airpayPoints
      }

      const merchantPoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '5',
        // description: util.AEPSCreditAirpay + ' for AEPS',
        description: 'Credit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (merchantPoints.status === 400) {
        return merchantPoints
      }

      const chargesResponse = await this.chargesLedgerEntries({ ...fields, transaction_value: '5' }, charge, gst, tempObj)
      log.logger({ pagename: require('path').basename(__filename), action: 'aepsLedgerEntries', type: 'chargesResponse', fields: chargesResponse })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'aepsLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * Distribution Configuration for AEPS BE/MS Incentive
   * fields : amount, ma_user_id,state_master_id,connection
   */
  static async aepsNoAmountLedgerEntries (fields, userid) {
    try {
      fields.userid = userid
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'aepsNoAmountLedgerEntries', type: 'request', fields: fields })

      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.transaction_type }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''

      const airpayPoints = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: 0,
        mode: 'dr',
        transaction_type: fields.transaction_type,
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (airpayPoints.status === 400) {
        return airpayPoints
      }

      const merchantPoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: 0,
        mode: 'cr',
        transaction_type: fields.transaction_type,
        // description: util.AEPSCreditAirpay + ' for AEPS',
        description: 'Credit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (merchantPoints.status === 400) {
        return merchantPoints
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'aepsNoAmountLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async collectmoneyLedgerEntries (fields, userid) {
    const tempConnection = !fields.connection
    const connection = fields.connection || await mySQLWrapper.getConnectionFromPool()
    log.logger({ pagename: 'ledgerEntriesController.js', action: 'collectmoneyLedgerEntries', type: 'connection', fields: connection })

    try {
      fields.userid = userid
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'collectmoneyLedgerEntries', type: 'request', fields: fields })
      /**
      let isUPICC = false
      let upicccharge = 0
      let upiccgst = 0
      if ('chmod' in fields && fields.chmod == 'upi' && 'charge_type' in fields && fields.charge_type == 'CREDIT' && Number(fields.amount) >= util.upi_credit_above_amount) {
        isUPICC = true
        const { charge, gst, afterDeduct } = await this.calculateChargeUPICC({ amount: fields.amount })
        upicccharge = charge
        upiccgst = gst
        // fields.amount = fields.amount - (charge + gst)
      } else if ('chmod' in fields && fields.chmod == 'upi' && 'charge_type' in fields && fields.charge_type == 'PPIWALLET' && Number(fields.amount) >= util.upi_credit_above_amount) {
        // Using common variables - upicc, upicccharge, upiccgst for ppiwallet
        isUPICC = true
        const { charge, gst, afterDeduct } = await this.calculateChargePPIWallet({ amount: fields.amount })
        upicccharge = charge
        upiccgst = gst
        // fields.amount = fields.amount - (charge + gst)
        log.logger({ pagename: require('path').basename(__filename), action: 'collectmoneyLedgerEntries', type: 'PPIWALLET condition', fields: { ppiWalletcharge: upicccharge, ppiWalletGST: upiccgst } })
      }
      */

      let isUPIPaymode = false
      let tempObj = {}

      const selectedPaymodeSlab = await this.paymodeSlabSelector(fields)

      const { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } = await this.calculatePaymodeCharges({ amount: fields.amount, type: selectedPaymodeSlab, ma_user_id: fields.ma_user_id, userid: fields.userid, state_master_id: fields.state_master_id, connection: connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'collectmoneyLedgerEntries', type: 'calculatePaymodeCharges function call result', fields: { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } })

      if ((Number(fields.amount) >= Number(minAmount)) && (Number(fields.amount) <= Number(maxAmount))) {
        console.log('<< slab range passed >>')
        isUPIPaymode = true
        tempObj = { bankCharge, txnAmount, type, bankName, isGstInclusive }
        tempObj.platformFee = isGstInclusive ? charge : charge + gst
        log.logger({ pagename: require('path').basename(__filename), action: 'collectmoneyLedgerEntries', type: 'paymode charge process', fields: { charge: charge, GST: gst, otherDetails: tempObj } })
      }

      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '10' }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''

      const cashCredit = await cashAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        // amount: fields.amount - (upicccharge + upiccgst),
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (cashCredit.status === 400) {
        return cashCredit
      }
      const pointsCredit = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        // amount: fields.amount - (upicccharge + upiccgst),
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsCredit.status === 400) {
        return pointsCredit
      }

      const airpayPoints = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '10',
        // description: util.AEPSDebitAirpay + ' for Collect Money',
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (airpayPoints.status === 400) {
        return airpayPoints
      }

      const merchantPoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '10',
        // description: util.AEPSCreditAirpay + ' for Collect Money',
        description: 'Credit - ' + descType + (fields.CUSTOMERVPA ? ` (${fields.CUSTOMERVPA})` : ' (Customer)'),
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (merchantPoints.status === 400) {
        return merchantPoints
      }
      /**
      if (isUPICC) {
        // const BalanceController = require('../balance/balanceController')
        const upiDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '70' }, fields.connection)
        const upiChargeDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '71' }, fields.connection)
        const upiGSTDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '72' }, fields.connection)
        const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
          ma_user_id: fields.ma_user_id,
          amount: upicccharge + upiccgst,
          transactionType: '1',
          connection: fields.connection
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
        if (pointsDetailsEntries.status === 400) {
          return pointsDetailsEntries
        }
        const chargePoints = await pointsLedger.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: upicccharge + upiccgst,
          mode: 'dr',
          transaction_type: '70',
          // description: util.AEPSCreditAirpay + ' for Collect Money',
          description: upiDescObj.code_desc ? upiDescObj.code_desc : 'Platform Fee',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayCommissionId,
          ma_status: 'S',
          connection: fields.connection
        }
        )
        if (chargePoints.status === 400) {
          return chargePoints
        }
        // const PointsDetailsController = require('../creditDebit/pointsDetailsController')
        for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: chargePoints.id,
            orderid: fields.aggregator_order_id,
            ma_status: 'S',
            connection: fields.connection
          })
          log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'PointsDetailsEntries', fields: entry })
          if (entry.status === 400) {
            return entry
          }
        }

        const customerCharge = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayCommissionId,
          amount: upicccharge,
          mode: 'cr',
          transaction_type: '71',
          // description: util.AEPSCreditAirpay + ' for Collect Money',
          description: upiChargeDescObj.code_desc ? upiChargeDescObj.code_desc : 'Net Platform Fee',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayUserId,
          ma_status: 'S',
          connection: fields.connection
        }
        )
        if (customerCharge.status === 400) {
          return customerCharge
        }

        const gstCharge = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayCommissionId,
          amount: upiccgst,
          mode: 'cr',
          transaction_type: '72',
          // description: util.AEPSCreditAirpay + ' for Collect Money',
          description: upiGSTDescObj.code_desc ? upiGSTDescObj.code_desc : 'GST on Platform Fee',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayUserId,
          ma_status: 'S',
          connection: fields.connection
        }
        )
        if (gstCharge.status === 400) {
          return gstCharge
        }
      }
        */

      if (isUPIPaymode) {
        await this.chargesLedgerEntries({ ...fields, transaction_value: '10' }, charge, gst, tempObj)
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'collectmoneyLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async posLedgerEntries (fields, userid) {
    const tempConnection = !fields.connection
    const connection = fields.connection || await mySQLWrapper.getConnectionFromPool()
    log.logger({ pagename: 'ledgerEntriesController.js', action: 'posLedgerEntries', type: 'connection', fields: connection })

    try {
      fields.userid = userid
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'posLedgerEntries', type: 'request', fields: fields })

      fields.chmod = fields.txn_type
      let isPOSPaymode = false
      let tempObj = {}

      const selectedPaymodeSlab = await this.paymodeSlabSelector(fields)

      const { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } = await this.calculatePaymodeCharges({ amount: fields.amount, type: selectedPaymodeSlab, ma_user_id: fields.ma_user_id, userid: fields.userid, state_master_id: fields.state_master_id, connection: connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'posLedgerEntries', type: 'calculatePaymodeCharges function call result', fields: { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } })

      if ((Number(fields.amount) >= Number(minAmount)) && (Number(fields.amount) <= Number(maxAmount))) {
        console.log('<< slab range passed >>')
        isPOSPaymode = true
        tempObj = { bankCharge, txnAmount, type, bankName, isGstInclusive }
        tempObj.platformFee = isGstInclusive ? charge : charge + gst
        log.logger({ pagename: require('path').basename(__filename), action: 'posLedgerEntries', type: 'paymode charge process', fields: { charge, gst, otherDetails: tempObj } })
      }

      const cashCredit = await cashAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (cashCredit.status === 400) {
        return cashCredit
      }
      const pointsCredit = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsCredit.status === 400) {
        return pointsCredit
      }

      const airpayPoints = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '20',
        description: util.AEPSDebitAirpay + ' for POS',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (airpayPoints.status === 400) {
        return airpayPoints
      }

      const merchantPoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '20',
        description: util.AEPSCreditAirpay + ' for POS',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (merchantPoints.status === 400) {
        return merchantPoints
      }

      if (isPOSPaymode) {
        await this.chargesLedgerEntries(fields, charge, gst, tempObj)
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'posLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async mircroAtmLedgerEntries (fields, userid) {
    try {
      fields.userid = userid
      fields.amount = parseFloat(fields.amount)
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'mircroAtmLedgerEntries', type: 'request', fields: fields })
      const cashCredit = await cashAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (cashCredit.status === 400) {
        return cashCredit
      }
      const pointsCredit = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'S',
        transaction_type: 'CL',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsCredit.status === 400) {
        return pointsCredit
      }

      const airpayPoints = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '23',
        description: util.AEPSDebitAirpay + ' for Human ATM',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (airpayPoints.status === 400) {
        return airpayPoints
      }

      const merchantPoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '23',
        description: util.AEPSCreditAirpay + ' for Human ATM',
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (merchantPoints.status === 400) {
        return merchantPoints
      }

      const {
        charge,
        gst,
        afterDeduct,
        bankCharge,
        txnAmount,
        minAmount,
        maxAmount,
        isGstInclusive
      } = await this.calculatePaymodeCharges({ ...fields, type: 'hatm' })
      log.logger({ pagename: require('path').basename(__filename), action: 'BankChargeLedgerEntry', type: 'calculatePaymodeCharges function call result', fields: { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, isGstInclusive } })

      let tempObj = {}
      if ((Number(fields.amount) >= Number(minAmount)) && (Number(fields.amount) <= Number(maxAmount))) {
        tempObj = { bankCharge, txnAmount, type: 'HATM', bankName: fields.bank_name, isGstInclusive, platformFee: isGstInclusive ? charge : charge + gst }
        log.logger({ pagename: require('path').basename(__filename), action: 'BankChargeLedgerEntry', type: 'paymode charge process', fields: { charge: charge, GST: gst, otherDetails: tempObj } })
      }
      const paymodeCharges = await this.chargesLedgerEntries({ ...fields, transaction_value: '23' }, charge, gst, tempObj)
      return paymodeCharges
    } catch (err) {
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'mircroAtmLedgerEntries', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * This function is used to create ledger entries for CMS transaction
   * @param {*} fields
   * @param {*} con connection
   */
  static async cmsLedgerEntries (fields, con) {
    log.logger({ pagename: 'ledgerEntriesController.js', action: 'cmsLedgerEntries', type: 'request', fields: fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      // Get Transaction Name by Transaction Type
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '24' }, connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      let tempObj = {}
      const { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } = await this.calculatePaymodeCharges({ amount: fields.amount, type: 'cms', ma_user_id: fields.ma_user_id, userid: fields.userid, cms_ma_user_id: fields.cms_ma_user_id })

      log.logger({ pagename: require('path').basename(__filename), action: 'cmsLedgerEntries', type: 'calculatePaymodeCharges result', fields: { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } })

      // if ((Number(fields.amount) >= Number(minAmount)) && (Number(fields.amount) <= Number(maxAmount))) {
      tempObj = { bankCharge, txnAmount, type, bankName, isGstInclusive }
      tempObj.platformFee = charge + gst

      log.logger({ pagename: require('path').basename(__filename), action: 'cmsLedgerEntries', type: 'paymode charge process', fields: { charge: charge, GST: gst, otherDetails: tempObj } })
      // }

      var cms_merchant_dr = true
      var cms_merchant_cr = false
      if (fields.orderid.indexOf('REV-') == -1) {
        cms_merchant_dr = false
        cms_merchant_cr = true
      }
      var pointsDetailsEntries = {}
      if (fields.orderid.indexOf('REV-') == -1) {
        // getWalletBalance replaced here
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          transactionType: '1',
          connection
        })
        console.log('2. wallet points balance', pointsDetailsEntries)
        if (pointsDetailsEntries.status === 400) {
          // if (isSet) await mySQLWrapper.rollback(connection)
          return pointsDetailsEntries
        }
      }
      // Debit from Retailer
      const retailerPointsDebit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '24',
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.cms_ma_user_id,
        ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
        connection,
        cms_merchant: cms_merchant_dr
      }
      )
      if (retailerPointsDebit.status === 400) {
        return retailerPointsDebit
      }
      const ma_points_ledger_master_id = retailerPointsDebit.id

      if (fields.orderid.indexOf('REV-') == -1) {
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: ma_points_ledger_master_id,
            orderid: fields.orderid,
            ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
            connection
          })
          if (entry.status === 400) {
            return entry
          }
        }
      }

      // Credit to CMS Merchant
      // let creditAmount = fields.amount
      // if (fields.inclusive_flag == 'Y' && fields.orderid.indexOf('REV-') == -1) {
      //   creditAmount = fields.amount - fields.customer_charge
      // }
      const cmsMerchantPointsCredit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.cms_ma_user_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '24',
        description: 'Credit - ' + descType,
        orderid: fields.orderid,
        userid: fields.cms_userid,
        corresponding_id: fields.ma_user_id,
        ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
        connection,
        cms_merchant: cms_merchant_cr // TO skip wallet part for CMS merchant
      }
      )
      if (cmsMerchantPointsCredit.status === 400) {
        return cmsMerchantPointsCredit
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'cmsLedgerEntries', type: 'response', fields: { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] } })
      const newCharge = (charge > 0) ? charge : 0

      fields.transaction_value = '24'
      fields.connection = connection
      if (newCharge > 0) {
        const chargesResponse = await this.chargesLedgerEntries({ ...fields, transaction_value: '24' }, charge, gst, tempObj)

        log.logger({ pagename: require('path').basename(__filename), action: 'cmsLedgerEntries', type: 'chargesResponse', fields: chargesResponse })
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'cmsLedgerEntries', type: 'chargesResponse - 0 charge ', fields: fields })

        await this.paymodeChargesRecord(fields, tempObj)
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'cmsLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * This function is used to create ledger entries for CMS transaction
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, corresponding_id: number, corresponding_userid: number, transaction_type: number, orderid: string, amount: number }} fields
   * @param {*} con connection
   */
  static async shoppingLedgerEntries (fields, con) {
    log.logger({ pagename: 'ledgerEntriesController.js', action: 'shoppingLedgerEntries', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      // Get Transaction Name by Transaction Type
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.transaction_type }, connection)
      const descType = (descObj.code_desc ? descObj.code_desc : '').replace(/shopping cart/ig, 'BAAZAAR')

      // dr and cr flags
      let shopping_merchant_dr = true
      let shopping_merchant_cr = false

      if (fields.orderid.indexOf('REV-') == -1) {
        shopping_merchant_dr = false
        shopping_merchant_cr = true
      }

      let pointsDetailsEntries = {}

      if (fields.orderid.indexOf('REV-') == -1) {
        // getWalletBalance replaced here
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          transactionType: '1',
          connection
        })
        console.log('2. wallet points balance', pointsDetailsEntries)
        if (pointsDetailsEntries.status === 400) return pointsDetailsEntries
      }

      // Debit from Retailer
      const retailerPointsDebit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: fields.transaction_type,
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.corresponding_id,
        ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
        connection,
        cms_merchant: shopping_merchant_dr
      })

      if (retailerPointsDebit.status === 400) return retailerPointsDebit

      const ma_points_ledger_master_id = retailerPointsDebit.id

      if (fields.orderid.indexOf('REV-') == -1) {
        for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: ma_points_ledger_master_id,
            orderid: fields.orderid,
            ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
            connection
          })
          if (entry.status === 400) return entry
        }
      }

      // Credit to Airpay Merchant
      // let creditAmount = fields.amount
      // if (fields.inclusive_flag == 'Y' && fields.orderid.indexOf('REV-') == -1) {
      //   creditAmount = fields.amount - fields.customer_charge
      // }
      const shoppingMerchantPointsCredit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.corresponding_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: fields.transaction_type,
        description: 'Credit - ' + descType,
        orderid: fields.orderid,
        userid: fields.corresponding_userid,
        corresponding_id: fields.ma_user_id,
        ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
        connection,
        cms_merchant: shopping_merchant_cr // TO skip wallet part for CMS merchant
      })

      if (shoppingMerchantPointsCredit.status === 400) return shoppingMerchantPointsCredit

      log.logger({ pagename: require('path').basename(__filename), action: 'shoppingLedgerEntries', type: 'response', fields: { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] } })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'shoppingLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * This function is used to create ledger entries for Withdrawal Charges transaction
   * @param {*} fields
   * @param {*} userid
   */
  static async withDrawalLedgerEntries (fields) {
    try {
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'withDrawalLedgerEntries', type: 'request', fields: fields })

      // Get Transaction Name by Transaction Type
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '30' }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''

      var pointsDetailsEntries = {}
      // getWalletBalance replaced here
      pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        transactionType: '1',
        connection: fields.connection
      })
      console.log('2. wallet points balance', pointsDetailsEntries)
      if (pointsDetailsEntries.status === 400) {
        // if (isSet) await mySQLWrapper.rollback(connection)
        return pointsDetailsEntries
      }

      // Debit from Retailer
      const retailerPointsDebit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '30',
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      }
      )
      if (retailerPointsDebit.status === 400) {
        return retailerPointsDebit
      }
      const ma_points_ledger_master_id = retailerPointsDebit.id

      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: ma_points_ledger_master_id,
          orderid: fields.orderid,
          ma_status: 'S',
          connection: fields.connection
        })
        if (entry.status === 400) {
          return entry
        }
      }

      // doubt : its exclusive, so creditAmount should be amount + customer charge (1000 + 10)
      const cashWithdrawaPointsCredit = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '30',
        description: 'Credit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id, // airpayUserid should be here ??
        ma_status: 'S',
        connection: fields.connection
        // cms_merchant: true // TO skip wallet part for CMS merchant // Need to comment this or not?
      }
      )
      if (cashWithdrawaPointsCredit.status === 400) {
        return cashWithdrawaPointsCredit
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'withDrawalLedgerEntries', type: 'response', fields: { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] } })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'withDrawalLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * This function is used to create ledger entries for ONDC transaction
   * @param {*} fields
   * @param {*} con connection
   */
  static async ondcLedgerEntries (fields, con) {
    log.logger({ pagename: 'ledgerEntriesController.js', action: 'ondcLedgerEntries', type: 'request', fields: fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      // Get Transaction Name by Transaction Type
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '44' }, connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      let ondc_merchant_dr = true
      let ondc_merchant_cr = false
      if (fields.orderid.indexOf('REV-') == -1) {
        ondc_merchant_dr = false
        ondc_merchant_cr = true
      }

      let pointsDetailsEntries = {}
      if (fields.orderid.indexOf('REV-') == -1) {
        // getWalletBalance replaced here
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          transactionType: '1',
          connection
        })
        console.log('2. wallet points balance', pointsDetailsEntries)
        if (pointsDetailsEntries.status === 400) return pointsDetailsEntries
      }

      // Debit from Retailer
      const retailerPointsDebit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '44',
        description: 'Debit - ' + descType,
        orderid: fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.airpayMaUserId,
        ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
        connection,
        cms_merchant: ondc_merchant_dr
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'ondcLedgerEntries', type: 'retailerPointsDebit', fields: retailerPointsDebit })
      if (retailerPointsDebit.status === 400) return retailerPointsDebit

      const ma_points_ledger_master_id = retailerPointsDebit.id
      if (fields.orderid.indexOf('REV-') == -1) {
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: ma_points_ledger_master_id,
            orderid: fields.orderid,
            ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
            connection
          })
          log.logger({ pagename: require('path').basename(__filename), action: 'ondcLedgerEntries', type: 'entry', fields: entry })
          if (entry.status != 200) return entry
        }
      }

      // Credit to Airpay
      const cmsMerchantPointsCredit = await pointsLedger.createEntry('_', {
        ma_user_id: fields.airpayMaUserId,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '44',
        description: 'Credit - ' + descType,
        orderid: fields.orderid,
        userid: fields.airpayUserId,
        corresponding_id: fields.ma_user_id,
        ma_status: fields.orderid.indexOf('REV-') == -1 ? 'S' : 'REV',
        connection,
        cms_merchant: ondc_merchant_cr // TO skip wallet part for CMS merchant
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'ondcLedgerEntries', type: 'cmsMerchantPointsCredit', fields: cmsMerchantPointsCredit })
      if (cmsMerchantPointsCredit.status != 200) return cmsMerchantPointsCredit

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'ondcLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async chargeBackReversalLedgerEntries (fields, userid) {
    try {
      fields.userid = userid
      log.logger({ pagename: 'ledgerEntriesController.js', action: 'chargeBackReversalLedgerEntries', type: 'request', fields: fields })

      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.transaction_type }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      const cashCredit = await cashAccount.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        ma_status: 'REV', // DOUBT : should we keep status as S or REV
        transaction_type: 'CL',
        orderid: 'REV-' + fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        connection: fields.connection
      })
      if (cashCredit.status === 400) {
        return cashCredit
      }
      const pointsCredit = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: 'REV', // DOUBT : should we keep status as S or REV
        transaction_type: 'CL',
        orderid: 'REV-' + fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        connection: fields.connection
      })
      if (pointsCredit.status === 400) {
        return pointsCredit
      }

      const airpayPoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: fields.transaction_type,
        // description: util.AEPSDebitAirpay + ' for Collect Money',
        description: 'Debit - ' + descType,
        orderid: 'REV-' + fields.orderid,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'REV', // DOUBT : should we keep status as S or REV
        connection: fields.connection
      }
      )
      if (airpayPoints.status === 400) {
        return airpayPoints
      }

      const merchantPoints = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: fields.transaction_type,
        // description: util.AEPSCreditAirpay + ' for Collect Money',
        description: 'Credit - ' + descType + (fields.CUSTOMERVPA ? ` (${fields.CUSTOMERVPA})` : ' (Customer)'),
        orderid: 'REV-' + fields.orderid,
        userid: fields.userid,
        corresponding_id: fields.ma_user_id,
        ma_status: 'REV', // DOUBT : should we keep status as S or REV
        connection: fields.connection
      }
      )
      if (merchantPoints.status === 400) {
        return merchantPoints
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'chargeBackReversalLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async reverseHatmIncentiveEntries ({ ma_user_id, userid, aggregator_order_id, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentiveEntries', type: 'request', fields: { ma_user_id, userid, aggregator_order_id } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const isIncentivePresentQuery = `SELECT * FROM ma_points_ledger_master WHERE parent_id = '${aggregator_order_id}' AND transaction_type IN ('25',11) AND ma_status='S' AND mode='cr'`
      const isIncentiveEntriesPresent = await this.rawQuery(isIncentivePresentQuery, conn)

      if (isIncentiveEntriesPresent.length < 1) {
        return false
      }

      await mySQLWrapper.beginTransaction(conn)
      const sqlOrderWise = `SELECT * FROM ma_orderwise_taxes WHERE parent_id = '${aggregator_order_id}' AND ma_status='S' AND transaction_type = '25'`
      const sqlOrderWiseRes = await this.rawQuery(sqlOrderWise, conn)

      log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentiveEntries', type: 'chargesDistribution', fields: sqlOrderWiseRes })

      if (sqlOrderWiseRes.length > 0) {
        const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
        for (const valueOrderWise of sqlOrderWiseRes) {
          const orderWiseTaxAmount = parseFloat((valueOrderWise.amount).toFixed(4))
          const orderWiseGstAmount = parseFloat((valueOrderWise.gst_amount).toFixed(4))
          const orderWiseTdsAmount = parseFloat((valueOrderWise.tds_amount).toFixed(4))
          const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
            transaction_type: '25',
            orderid: 'R' + '-' + valueOrderWise.orderid,
            ma_user_id: valueOrderWise.ma_user_id,
            amount: (-1 * parseFloat(orderWiseTaxAmount)),
            gst_amount: (-1 * parseFloat(orderWiseGstAmount)),
            tds_amount: (-1 * parseFloat(orderWiseTdsAmount)),
            ma_status: 'R', // Refund/Reverse
            connection: conn
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentiveEntries', type: 'insertOrderwiseTaxes', fields: sqlOrderWiseRes })

          if (orderwiseEntry.status === 400) {
            await mySQLWrapper.rollback(conn)
            return orderwiseEntry
          }
        }
      }
      // [END] Make refund/reverse entry in ma_orderwise_taxes table

      // check for entry in monthly incentives
      const TransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await TransactionController.getTransactionDetailsByParentId(aggregator_order_id, conn)
      const monthlyIncentiveController = require('../incentive/monthlyIncentiveDistributionController')
      const sqlincmonth = `SELECT * FROM ma_monthly_incentives_process WHERE transaction_master_id = '${parent_transaction_master_id}' AND isRefund = 'FALSE'`
      const sqlincmonthRes = await this.rawQuery(sqlincmonth, conn)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      for (var i = 0; i < sqlincmonthRes.length; i++) {
        const monthlyIncCreateRev = await monthlyIncentiveController.createEntry('_', {
          ma_user_id: sqlincmonthRes[i].ma_user_id,
          amount: (parseFloat((-1 * sqlincmonthRes[i].amount).toFixed(4))),
          transaction_type: sqlincmonthRes[i].transaction_type,
          monthyear: thisMonth + ' ' + now.getFullYear(),
          summary_id: 0,
          transaction_master_id: parent_transaction_master_id,
          isRefund: 'TRUE'
        }, conn)
        if (monthlyIncCreateRev.status === 400) {
          await mySQLWrapper.rollback(conn)
          log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentiveEntries', type: 'monthlyIncCreateRev', fields: monthlyIncCreateRev })
          return monthlyIncCreateRev
        }
      }

      let counting = 1
      for (const value of isIncentiveEntriesPresent) {
        counting++
        const subId = value.ma_user_id
        const subAmount = parseFloat((value.amount).toFixed(4))
        var pointsDetailsEntries = {}
        if (util.airpaymerchantconfig.includes(subId) == false) {
          pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
            ma_user_id: subId,
            amount: subAmount,
            transactionType: '2',
            connection: conn
          })
          if (pointsDetailsEntries.status === 400) {
            await mySQLWrapper.rollback(conn)
            log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentive', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
            return pointsDetailsEntries
          }
        }

        const subInsert = await pointsLedger.createEntry('_', {
          ma_user_id: subId,
          amount: subAmount,
          mode: 'dr',
          transaction_type: value.transaction_type,
          description: 'Debit - Chargeback - ' + value.description + ' - Reversed',
          ma_status: 'R',
          orderid: 'R' + '-' + value.orderid,
          userid: userid,
          corresponding_id: value.corresponding_id,
          connection: conn
        })
        if (subInsert.status === 400) {
          await mySQLWrapper.rollback(conn)
          log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentive', type: 'subInsert', fields: subInsert })
          return subInsert
        }
        if (util.airpaymerchantconfig.includes(subId) == false) {
          for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
            const entry = await pointsDetailsController.createEntry('_', {
              ma_user_id: subId,
              amount: pointsDetailsEntries.details[i].deductionAmount,
              wallet_type: pointsDetailsEntries.details[i].wallet_type,
              ma_points_ledger_master_id: subInsert.id,
              orderid: 'R' + '-' + value.orderid,
              ma_status: 'R',
              transaction_status: 'R',
              connection: conn
            })
            if (entry.status === 400) {
              await mySQLWrapper.rollback(conn)
              log.logger({ pagename: require('path').basename(__filename), action: 'reverseIncentive', type: 'entry', fields: entry })
              return entry
            }
          }
        }
      }
      await mySQLWrapper.commit(conn)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async calculateChargeUPICC ({ amount, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateChargeUPICC', type: 'request', fields: { amount } })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'get_slabwise_charge', type: 'query_request', fields: { amount } })
      // const sql = `SELECT customer_charges , gst_charges from ma_slabwise_distribution_upi_cc where ${amount} BETWEEN min_amount AND max_amount`
      // const queryResponse = await this.rawQuery(sql, connection)
      // log.logger({ pagename: require('path').basename(__filename), action: 'get_slabwise_charge', type: 'query_response', fields: queryResponse[0] })
      // if (queryResponse.length > 0) {
      //   const charge = amount * queryResponse[0].customer_charges / 100
      //   const gst = charge * queryResponse[0].gst_charges / 100
      //   log.logger({ pagename: require('path').basename(__filename), action: 'calculateChargeUPICC', type: 'result', fields: { charge, gst } })
      //   return { charge, gst }
      // }
      const charge = Number(amount) * 2.2 / 100
      const gst = charge * 18 / 100
      const afterDeduct = Number(amount) - (charge + gst)
      log.logger({ pagename: require('path').basename(__filename), action: 'calculateChargeUPICC', type: 'result', fields: { charge, gst, afterDeduct } })
      return { charge, gst, afterDeduct }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculateChargeUPICC', type: 'error', fields: { amount } })
      return err
    }
  }

  static async calculateChargePPIWallet ({ amount }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateChargePPIWallet', type: 'request', fields: { amount } })
    const charge = Number(amount) * 2.2 / 100
    const gst = charge * 18 / 100
    const afterDeduct = Number(amount) - (charge + gst)
    log.logger({ pagename: require('path').basename(__filename), action: 'calculateChargePPIWallet', type: 'calculations', fields: { charge, gst, afterDeduct } })
    return { charge, gst, afterDeduct }
  }

  static async paymodeSlabSelector (fields) {
    let paymodeChargeSlab = ''
    try {
      if ('chmod' in fields) {
        const paymodeChmod = fields.chmod
        const paymodeChargeType = fields.charge_type || fields.cardtype

        log.logger({ pagename: require('path').basename(__filename), action: 'paymodeSlabSelector', type: 'paymode slab selection request', fields: { paymodeChmod, paymodeChargeType } })

        const chargeSlabCategory = {
          upi: {
            CREDIT: 'UPI_CC',
            PPIWALLET: 'PPIWALLET',
            SAVINGS: 'UPI'
          },
          pg: {
            Credit: 'credit_card',
            Debit: 'debit_card'
          }
          // pos: {
          //   C: 'POS',
          //   D: 'POS'
          // }
        }

        if (paymodeChmod == 'pos') {
          paymodeChargeSlab = 'POS'
        } else if (paymodeChmod == 'nb') {
          paymodeChargeSlab = 'net_banking'
        } else {
          paymodeChargeSlab = chargeSlabCategory[paymodeChmod][paymodeChargeType]
        }

        log.logger({ pagename: require('path').basename(__filename), action: 'paymodeSlabSelector', type: 'paymode slab selection type', fields: { paymodeChargeSlab } })
        return paymodeChargeSlab
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'paymodeSlabSelector', type: 'error', fields: { message: '"chmod" not found for Paymode charges slab selection' } })
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeSlabSelector', type: 'error', fields: { message: 'Paymode charges slab selection failed' } })
      return err
    }
  }

  static async calculatePaymodeCharges (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'request', fields: fields })

    try {
      let data = ''
      if (fields.type == 'hatm') {
        data = await mircroAtmDistributionController.getDistribution(fields)
      } else if (fields.type == 'cms') {
        data = await cmsDistributionController.getDistribution(fields)
      } else if (fields.type == 'dmt') {
        data = fields.paymodeData
      } else if (fields.type == 'aeps') {
        data = await aepsDistributionController.getDistribution(fields)
      } else {
        data = await collectMoneyDistributionController.getDistribution(fields)
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'collectMoney getDistribution data', fields: data })

      let charge = 0
      let bank_charge = 0
      let gst = 0
      let afterDeduct = 0
      let finalData = {}
      let isGstInclusive = false

      if (data.status == 200) {
        // percentage
        if (data[0].merchant_charges_applied_type == '2') {
          charge = fields.amount * (data[0].merchant_charges / 100)
          console.log('charge: ' + charge)
        } else {
          charge = data[0].merchant_charges // for merchant_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        if (data[0].bank_charges_applied_type == '2') {
          bank_charge = fields.amount * (data[0].bank_charges / 100)
          console.log('charge: ' + charge)
        } else {
          bank_charge = data[0].bank_charges // for bank_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        // gst calculation
        if (data[0].gst_applied_calculation_type == 'E') {
          gst = charge * (data[0].gst_percentage / 100)
        } else if (data[0].gst_applied_calculation_type == 'I') {
          isGstInclusive = true
          gst = charge - (charge * (100 / (100 + data[0].gst_percentage)))
          gst = gst * -1 // Negating for deduction
        }

        // round off gst to 2 decimals
        gst = Math.round(gst * 100) / 100
        console.log('gst: ' + Math.abs(gst))

        afterDeduct = Number(fields.amount) - (charge + gst)
        console.log('afterDeduct: ' + afterDeduct)

        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: data[0].min_amount,
          maxAmount: data[0].max_amount,
          type: data[0].type,
          bankName: data[0].bank_name,
          isGstInclusive: isGstInclusive
        }
      } else {
        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: 0,
          maxAmount: 0,
          type: '',
          bankName: '',
          isGstInclusive: isGstInclusive
        }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'calc result', fields: finalData })
      return finalData
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'error', fields: err })
      return err
    }
  }

  static async chargesLedgerEntries (fields, charge, gst, tempObj) {
    try {
      if (charge && charge + gst) {
        const BalanceController = require('../balance/balanceController')
        const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '70' }, fields.connection)
        const chargeDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '71' }, fields.connection)
        const gstDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '72' }, fields.connection)
        const pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
          ma_user_id: fields.ma_user_id,
          amount: tempObj.isGstInclusive ? charge : charge + gst,
          transactionType: '1',
          connection: fields.connection
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
        if (pointsDetailsEntries.status === 400) {
          return pointsDetailsEntries
        }
        const chargePoints = await pointsLedger.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: tempObj.isGstInclusive ? charge : charge + gst,
          mode: 'dr',
          transaction_type: '70',
          // description: util.AEPSCreditAirpay + ' for Collect Money',
          description: descObj.code_desc ? descObj.code_desc : 'Platform Fee',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayCommissionId,
          ma_status: 'S',
          connection: fields.connection
        }
        )
        if (chargePoints.status === 400) {
          return chargePoints
        }
        const PointsDetailsController = require('../creditDebit/pointsDetailsController')
        for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await PointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: chargePoints.id,
            orderid: fields.orderid,
            ma_status: 'S',
            connection: fields.connection
          })
          log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'PointsDetailsEntries', fields: entry })
          if (entry.status === 400) {
            return entry
          }
        }

        const customerCharge = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayCommissionId,
          amount: tempObj.isGstInclusive ? charge + gst : charge,
          mode: 'cr',
          transaction_type: '71',
          // description: util.AEPSCreditAirpay + ' for Collect Money',
          description: chargeDescObj.code_desc ? chargeDescObj.code_desc : 'Net Platform Fee',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayUserId,
          ma_status: 'S',
          connection: fields.connection
        }
        )
        if (customerCharge.status === 400) {
          return customerCharge
        }

        const gstCharge = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayCommissionId,
          amount: Math.abs(gst),
          mode: 'cr',
          transaction_type: '72',
          // description: util.AEPSCreditAirpay + ' for Collect Money',
          description: gstDescObj.code_desc ? gstDescObj.code_desc : 'GST on Platform Fee',
          orderid: fields.orderid,
          userid: fields.userid,
          corresponding_id: util.airpayUserId,
          ma_status: 'S',
          connection: fields.connection
        }
        )

        if (gstCharge.status === 400) {
          return gstCharge
        }
      }
      if (tempObj.platformFee !== undefined && tempObj.bankCharge !== undefined) {
        await this.paymodeChargesRecord(fields, tempObj)
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async paymodeChargesRecord (fields, tempObj) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'fields of paymodeChargesRecord', type: 'bank charges query result', fields: fields })
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: fields.transaction_value }, fields.connection)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      const platformFeeAmt = Math.abs(tempObj.platformFee)
      const bankName = tempObj.bankName ? tempObj.bankName : ''

      const checkCmsPartnerSql = `SELECT cms_parent_partner_id FROM ma_transaction_master_details WHERE transaction_id = '${fields.orderid}' LIMIT 1;`

      const checkCmsPartnerResult = await this.rawQuery(checkCmsPartnerSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'check cms Partner id', fields: checkCmsPartnerResult })

      let merchantName = ''

      if (checkCmsPartnerResult.length > 0 && checkCmsPartnerResult[0].cms_parent_partner_id != null) {
        const getMerchantLegalNameSql = `SELECT merchant_legal_name FROM ma_cms_merchant_on_boarding WHERE parent_partner_id = '${checkCmsPartnerResult[0].cms_parent_partner_id}' LIMIT 1;`
        const merchantResult = await this.rawQuery(getMerchantLegalNameSql, fields.connection)

        if (merchantResult && merchantResult.length > 0) {
          merchantName = merchantResult[0].merchant_legal_name || ''
        }
      }
      if (fields.transaction_value != '24') {
        merchantName = tempObj.type
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'merchant name', fields: merchantName })
      // insert bank_charges
      const bankChargesQuery = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges, status) VALUES (${fields.ma_user_id}, '${fields.orderid}', 'Bank charges', '${bankName}', '${descType}', '${merchantName}', ${tempObj.txnAmount}, ${tempObj.bankCharge}, "S")`
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'insert bank charges query', fields: bankChargesQuery })
      const bankChargesResult = await this.rawQuery(bankChargesQuery, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'bank charges query result', fields: bankChargesResult })

      // insert platform fee
      const platformFeeQuery = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges, status) VALUES (${fields.ma_user_id}, '${fields.orderid}', 'Platform Fee', '${bankName}', '${descType}', '${merchantName}', ${tempObj.txnAmount}, ${platformFeeAmt}, "S")`
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'insert bank charges query', fields: platformFeeQuery })
      const platformFeeResult = await this.rawQuery(platformFeeQuery, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'bank charges query result', fields: platformFeeResult })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async simcardActivationLedgerEntries (fields, conn) {
    const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '81' }, fields.connection)
    const descType = descObj.code_desc ? descObj.code_desc : 'Sim Card Renewal Charges'
    // const platformFeeAmt = Math.abs(tempObj.platformFee)
    // const bankName = tempObj.bankName ? tempObj.bankName : ''

    const pointsDetailsController = require('../creditDebit/pointsDetailsController')
    const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
      ma_user_id: fields.ma_user_id,
      amount: fields.amount,
      transactionType: '1',
      connection: conn
    })
    log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalanceDirect', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
    if (pointsDetailsEntries.status === 400) {
      return pointsDetailsEntries
    }

    const retailerDebitResult = await pointsLedger.createEntry('_', {
      ma_user_id: fields.ma_user_id,
      amount: fields.amount,
      mode: 'dr',
      transaction_type: '81',
      description: 'Debit - ' + descType,
      ma_status: 'S',
      orderid: fields.order_id,
      userid: fields.userid,
      corresponding_id: util.airpayCommissionId,
      connection: conn
    })
    log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'retailerDebitResult', fields: retailerDebitResult })
    if (retailerDebitResult.status != 200) {
      return retailerDebitResult
    }

    // const pointsDetailsController = require('../creditDebit/pointsDetailsController')
    for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
      const entryResult = await pointsDetailsController.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: pointsDetailsEntries.details[i].deductionAmount,
        wallet_type: pointsDetailsEntries.details[i].wallet_type,
        ma_points_ledger_master_id: retailerDebitResult.id,
        orderid: fields.order_id,
        ma_status: 'S',
        connection: conn
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'entryResult', fields: entryResult })
      if (entryResult.status === 400) {
        return entryResult
      }
    }

    // const creditAmount = parseFloat(fields.customer_charges) - parseFloat(gst_charges)
    const customerCharge = await pointsLedger.createEntry('_', {
      ma_user_id: util.airpayCommissionId,
      amount: fields.amount,
      mode: 'cr',
      transaction_type: '81',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: 'Credit - ' + descType,
      orderid: fields.order_id,
      userid: fields.userid,
      corresponding_id: util.airpayUserId,
      ma_status: 'S',
      connection: conn
    }
    )
    if (customerCharge.status === 400) {
      return customerCharge
    }

    console.log('customerCharge>>', customerCharge)

    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
  }


  static async reversePaymodeCharges(fields, connection) {
    try {
      const { aggregator_order_id, ma_status, modifiedOrderId } = fields
      let paymodeChargeLedgerQuery = `SELECT * from ma_points_ledger_master where parent_id  = '${aggregator_order_id}' and transaction_type in (70,71,72) and ma_status = "S" `
      if (ma_status == 'REV') {
        paymodeChargeLedgerQuery += 'ORDER BY ma_points_ledger_master_id desc '
      }
      paymodeChargeLedgerQuery += 'LIMIT 3'

      const paymodeChargeLedgerQueryResult = await this.rawQuery(paymodeChargeLedgerQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'paymodeChargeLedgerQueryResult', fields: paymodeChargeLedgerQueryResult })

      if (paymodeChargeLedgerQueryResult?.length) {
        for (let i = 0; i < paymodeChargeLedgerQueryResult.length; i++) {
          const mode = paymodeChargeLedgerQueryResult[i].mode == 'cr' ? 'dr' : 'cr'
          const amount = mode == 'cr' ? paymodeChargeLedgerQueryResult[i].amount * -1 : paymodeChargeLedgerQueryResult[i].amount
          const reverseTxnDetails = {
            ma_user_id: paymodeChargeLedgerQueryResult[i].ma_user_id,
            corresponding_id: paymodeChargeLedgerQueryResult[i].corresponding_id,
            amount,
            mode,
            userid: paymodeChargeLedgerQueryResult[i].userid,
            orderid: modifiedOrderId,
            transaction_type: paymodeChargeLedgerQueryResult[i].transaction_type,
            ma_status,
            description: paymodeChargeLedgerQueryResult[i].description + '- Reversed',
            connection: connection
          }

          const resppoint = await pointsLedger.createEntry(null, reverseTxnDetails)
          if (resppoint.status === 400) return resppoint
        }
      }

      const bankPaymodeChargesSql = `SELECT 
      ma_user_id,
      order_id,
      charges_type,
      bank_name, 
      transaction_type, 
      sub_transaction_type,
      transaction_amount,
      charges
      FROM ma_bank_paymode_charges 
      WHERE
       order_id = '${aggregator_order_id}' 
       AND status = "S" 
      ORDER BY ma_bank_paymode_charges_id 
      LIMIT 2`
      const bankPaymodeChargesSqlResult = await this.rawQuery(bankPaymodeChargesSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'bankPaymodeChargesSqlResult', fields: bankPaymodeChargesSqlResult })

      if (bankPaymodeChargesSqlResult?.length) {
        for (let i = 0; i < bankPaymodeChargesSqlResult.length; i++) {
          const sql = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
          const params = [bankPaymodeChargesSqlResult[i].ma_user_id, bankPaymodeChargesSqlResult[i].order_id, bankPaymodeChargesSqlResult[i].charges_type, bankPaymodeChargesSqlResult[i].bank_name, bankPaymodeChargesSqlResult[i].transaction_type, bankPaymodeChargesSqlResult[i].sub_transaction_type, bankPaymodeChargesSqlResult[i].transaction_amount, -bankPaymodeChargesSqlResult[i].charges, ma_status]
          await this.secureRawQuery(sql, { params, connection: connection })
        }
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }

    }
    catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}

module.exports = ledgerEntries
