/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const sms = require('../../util/sms')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const bankOnBoardDetails = require('../bankHandler/bankOnBoardingDetails')
const Payment = require('./../bankHandler/payment')

class Otp extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_otp_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_otp_master_id'
  }

  /*
    Fetch data for verify otp
  */
  static async getVerifyData (aggregator_order_id, otptype, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'SELECT * FROM ma_otp_master WHERE aggregator_order_id = "' + aggregator_order_id + '"  AND otp_type = "' + otptype + '" AND expiry > CURRENT_TIMESTAMP ORDER BY ma_otp_master_id DESC LIMIT 1'
      const queryResult = await this.rawQuery(sql, connection)
      console.log('Result', queryResult, sql)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getVerifyData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  /*
    Fetch data for resent otp
  */
  static async getResentData (mobile, aggregator_order_id, otp_type, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'SELECT otp,expiry,otp_template,IF(expiry > CURRENT_TIMESTAMP,TRUE,FALSE) as isExpired,retry_count,TIMESTAMPADD(MINUTE,' + util.expiry[otp_type] + ',CURRENT_TIMESTAMP()) as expiryTime,ma_bank_on_boarding_id,ma_user_id,userid,response FROM ma_otp_master WHERE aggregator_order_id = "' + aggregator_order_id + '" AND mobile = ' + mobile + ' AND otp_type = "' + otp_type + '" AND updatedon + INTERVAL 30 SECOND < NOW() ORDER BY ma_otp_master_id DESC LIMIT 1'
      // console.log(sql)
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getResentData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  /*
    Fetch data for resent otp
  */
  static async getProviderIdForSms (mobile, aggregator_order_id, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'request', fields: { mobile, aggregator_order_id } })
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      // fetch ma_sent_message_status table data (Msg sent for perticular Txn)
      const sql = `SELECT provider_id FROM ma_sent_message_status WHERE txn_order_id = "${aggregator_order_id}" AND mobile_no = ${mobile} ORDER BY id DESC`
      log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'sql', fields: sql })
      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'queryResult', fields: queryResult })

      if (queryResult.length > 0) {
        // check provide_id not null in queryResult and create new array of json
        var checkProviderNotNull = queryResult.filter(obj => {
          return obj.provider_id != 'null' && obj.provider_id != null
        })
        // fetch provider_id from checkProviderNotNull and create new array for NOT IN Sql query
        var getProviderIdArr = checkProviderNotNull.map(a => a.provider_id)
        if (getProviderIdArr.length <= 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, data: { provider_id: null } }

        // fetch ma_sms_provider_master table data
        const sqlForProvider = `SELECT provider_id FROM ma_sms_provider_master WHERE provider_id NOT IN (${getProviderIdArr.join(',')}) AND status = 'Available' LIMIT 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'sqlForProvider', fields: sqlForProvider })
        const sqlForProviderData = await this.rawQuery(sqlForProvider, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'sqlForProviderData', fields: sqlForProviderData })

        if (sqlForProviderData.length <= 0) {
          // fetch ma_sms_provider_master table data
          const sqlForProvider1 = 'SELECT provider_id FROM ma_sms_provider_master WHERE status = "Current" LIMIT 1'
          log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'sqlForProvider1', fields: sqlForProvider1 })
          const sqlForProviderData1 = await this.rawQuery(sqlForProvider1, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'sqlForProviderData1', fields: sqlForProviderData1 })

          if (sqlForProviderData1.length <= 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: sqlForProviderData1[0] }
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: sqlForProviderData[0] }
      }
      // if anything wrong send data: { provider_id: null }
      return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, data: { provider_id: null } }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getProviderIdForSms', type: 'catcherror', fields: err })
      // if anything wrong send data: { provider_id: null }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, data: { provider_id: null } }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
     * Creates a new  entry in otp master table
     */
  static async createEntry (_, fields, conn) {
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)
      console.log('createEntry')
      fields.expiry = await this.getSQLExpiry(connection, fields.otp_type)
      const _result = await this.insert(connection, {
        data: fields
      })
      await mySQLWrapper.commit(connection)
      return _result
    } catch (err) {
      console.log(err)
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  /**
     * Updates entry in otp master table
     */
  static async updateEntry (_, { aggregator_order_id, retry_count, flag, response = null, updateObject = null }, conn) {
    // console.log('arguments', arguments)
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      let updateData = {}
      if (response === null) {
        updateData = {
          retry_count,
          flag
        }
      } else {
        updateData = {
          response
        }

        if (updateObject !== null && Object.keys(updateObject).length > 0) {
          updateData = updateObject
        }
      }

      await mySQLWrapper.beginTransaction(connection)
      const _result = await this.updateWhere(connection, {
        id: aggregator_order_id,
        where: 'aggregator_order_id',
        data: updateData
      })
      await mySQLWrapper.commit(connection)
      if (_result.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else {
        return { status: 400, respcode: 1023, message: errorMsg.responseCode[1023] }
      }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (tmpConn) connection.release()
    }
  }

  static getOtpMsg (otp, otp_type, smsMessage = '') {
    let message = ''
    if (typeof (smsMessage) == 'undefined' || smsMessage == '' || smsMessage == null) {
      message = util.communication[otp_type]
    } else {
      message = smsMessage
    }
    message = message.replace('<Customer>', 'Customer')
    message = message.replace('<OTP>', otp)
    message = message.replace('<TIME>', util.expiryComm[otp_type])
    message = message.replace('<Salutation>', util.communication.Signature)
    return message
  }

  static async sentOtp (ma_user_id, userid, mobile_number, otp_type, other = {}, smsMessage = '', conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'request', fields: { ma_user_id: ma_user_id, userid: userid, mobile_number: mobile_number, otp_type: otp_type, other: other } })
    try {
      const otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
      const expiry = await this.getExpiry('expiry', otp_type)
      const random = await this.generateOTP(4)
      const timestamp = await this.getExpiry('')
      const aggregator_order_id = `MA${otp_type}${random}${timestamp}`
      let email_message = ''
      // Get OTP Message by OTP Type
      let message = this.getOtpMsg(otp, otp_type, smsMessage)
      if (otp_type == 'DR') {
        email_message = this.getOtpMsg(otp, 'DREMAIL', smsMessage)
      }
      // Replace any extra parameters provided in message
      if (Object.keys(other).length > 0) {
        const keys = Object.keys(other)
        for (const key of keys) {
          if (key === 'bankName') {
            message = message + util.bankNameCommunication[other[key]]
          } else {
            message = message.replace('<' + key + '>', other[key])
          }
        }
      }
      console.log(message)
      const _data = await this.createEntry('', {
        ma_user_id: ma_user_id,
        userid: userid,
        otp: otp,
        mobile: mobile_number,
        expiry: expiry,
        aggregator_order_id: aggregator_order_id,
        retry_count: 0,
        flag: '1',
        otp_type: otp_type,
        otp_template: message
      }, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: '_data', fields: _data })

      if (_data.affectedRows > 0) {
        // for CMS sendotp............
        if (validator.definedVal(other) && validator.definedVal(other.otpTypeForTemplateId)) {
          otp_type = other.otpTypeForTemplateId
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'otp_type', fields: otp_type })
        // for CMS sendotp close......

        // const message = util.communication[otp_type] + otp + '.' + util.communication.Signature
        // sms.sentSms(message, mobile_number)
        const otpResp = await sms.sentSmsAsync(message, mobile_number, util.templateid[otp_type], 'otp', conn, { aggregator_order_id })
        log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'otpResp', fields: otpResp })
        await this.updateEntry('', { aggregator_order_id: aggregator_order_id, retry_count: 0, flag: '0', response: otpResp.message }, conn)
        if (otpResp.status !== 200) {
          return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
        if (otp_type == 'DR') {
          return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: aggregator_order_id, template_text: email_message } }
        } else {
          return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: aggregator_order_id, template_text: message } }
        }
      } else {
        return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * External API : SEND OTP
   * @param {{ma_user_id:number, userid:number, mobile_number:string, otp_type:string, other:Object, smsMessage:string, connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async sentInternalOtp ({ ma_user_id, userid, mobile_number, otp_type, other = {}, smsMessage = '', connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'request', fields: { ma_user_id: ma_user_id, userid: userid, mobile_number: mobile_number, otp_type: otp_type, other: other } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
      const expiry = await this.getExpiry('expiry', otp_type)
      const random = await this.generateOTP(4)
      const timestamp = await this.getExpiry('')
      const aggregator_order_id = `MA${otp_type}${random}${timestamp}`
      // Get OTP Message by OTP Type
      let message = this.getOtpMsg(otp, otp_type, smsMessage)
      // Replace any extra parameters provided in message
      if (Object.keys(other).length > 0) {
        const keys = Object.keys(other)
        for (const key of keys) {
          if (key === 'bankName') {
            message = message + util.bankNameCommunication[other[key]]
          } else {
            message = message.replace('<' + key + '>', other[key])
          }
        }
      }
      console.log(message)
      const _data = await this.createEntry('', {
        ma_user_id: ma_user_id,
        userid: userid,
        otp: otp,
        mobile: mobile_number,
        expiry: expiry,
        aggregator_order_id: aggregator_order_id,
        retry_count: 0,
        flag: '1',
        otp_type: otp_type,
        otp_template: message
      }, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: '_data', fields: _data })

      if (_data.affectedRows > 0) {
        // for CMS sendotp............
        if (validator.definedVal(other) && validator.definedVal(other.otpTypeForTemplateId)) {
          otp_type = other.otpTypeForTemplateId
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'otp_type', fields: otp_type })
        // for CMS sendotp close......

        // const message = util.communication[otp_type] + otp + '.' + util.communication.Signature
        // sms.sentSms(message, mobile_number)
        const otpResp = await sms.sentSmsAsync(message, mobile_number, util.templateid[otp_type], 'otp', conn, { aggregator_order_id })
        log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'otpResp', fields: otpResp })
        const response = JSON.stringify({ response: otpResp.message, isCustomOTP: true })
        await this.updateEntry('', { aggregator_order_id: aggregator_order_id, retry_count: 0, flag: '0', response }, conn)
        if (otpResp.status !== 200) {
          return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
        return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: aggregator_order_id, template_text: message } }
      } else {
        return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async sentOtpWithoutMessage (ma_user_id, userid, mobile_number, otp_type, other = {}, smsMessage = '', conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'request', fields: { ma_user_id: ma_user_id, userid: userid, mobile_number: mobile_number, otp_type: otp_type, other: other } })
    try {
      const otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
      const expiry = await this.getExpiry('expiry', otp_type)
      const random = await this.generateOTP(4)
      const timestamp = await this.getExpiry('')
      const aggregator_order_id = `MA${otp_type}${random}${timestamp}`
      let email_message = ''
      // Get OTP Message by OTP Type
      let message = this.getOtpMsg(otp, otp_type, smsMessage)
      if (otp_type == 'DR') {
        email_message = this.getOtpMsg(otp, 'DREMAIL', smsMessage)
      }
      // Replace any extra parameters provided in message
      if (Object.keys(other).length > 0) {
        const keys = Object.keys(other)
        for (const key of keys) {
          if (key === 'bankName') {
            message = message + util.bankNameCommunication[other[key]]
          } else {
            message = message.replace('<' + key + '>', other[key])
          }
        }
      }
      console.log(message)
      let template_id_type = otp_type
      if (await validator.definedVal(other) && await validator.definedVal(other.otpTypeForTemplateId)) {
        template_id_type = other.otpTypeForTemplateId
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'template_id_type', fields: template_id_type })
      const _data = await this.createEntry('', {
        ma_user_id: ma_user_id,
        userid: userid,
        otp: otp,
        mobile: mobile_number,
        expiry: expiry,
        aggregator_order_id: aggregator_order_id,
        retry_count: 0,
        flag: '1',
        otp_type: otp_type,
        otp_template: message
        // template_id: util.templateid[template_id_type] || null
      }, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: '_data', fields: _data })

      return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, mobile_number, otp, aggregator_order_id }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async requestCreditSendOtp ({ ma_user_id, userid, mobile_number, otp_type, orderid = '', conn }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'requestCreditSendOtp - optController', type: 'request', fields: { ma_user_id, userid, mobile_number, otp_type, orderid } })
    try {
      const otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
      const expiry = await this.getExpiry('expiry', otp_type)
      const aggregator_order_id = orderid

      // Get OTP Message by OTP Type
      const message = await this.getOtpMsg(otp, otp_type)
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCreditSendOtp - optController', type: 'message', fields: message })

      const _data = await this.createEntry('', {
        ma_user_id: ma_user_id,
        userid: userid,
        otp: otp,
        mobile: mobile_number,
        expiry: expiry,
        aggregator_order_id: aggregator_order_id,
        retry_count: 0,
        flag: '1',
        otp_type: otp_type,
        otp_template: message
      }, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCreditSendOtp - optController', type: '_data', fields: _data })

      if (_data.affectedRows > 0) {
        // const message = util.communication[otp_type] + otp + '.' + util.communication.Signature
        // sms.sentSms(message, mobile_number)
        const otpResp = await sms.sentSmsAsync(message, mobile_number, util.templateid[otp_type], 'otp', conn, { aggregator_order_id })
        log.logger({ pagename: require('path').basename(__filename), action: 'requestCreditSendOtp - optController', type: 'otpResp', fields: otpResp })
        await this.updateEntry('', { aggregator_order_id: aggregator_order_id, retry_count: 0, flag: '0', response: otpResp.message }, conn)
        if (otpResp.status !== 200) {
          return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
        return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: aggregator_order_id, template_text: message } }
      } else {
        return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'requestCreditSendOtp - optController', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async resentOtp (mobile_number, orderid, otp_type, others = {}) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'request', fields: { mobile_number, orderid, otp_type, others } })
    try {
      if (orderid === null || orderid === undefined || orderid === 'null' || orderid === 'undefined') {
        return { status: false, respcode: 1104, message: errorMsg.responseCode[1104] }
      }

      let templateIdByOtptype = util.templateid[otp_type]
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'templateIdByOtptype - 1', fields: templateIdByOtptype })
      if (otp_type == 'CMS') {
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'type', fields: 'CMS' })
        let subOtpTemplateId = null
        if (Object.keys(others).length > 0) {
          if ('templateId' in others && validator.definedVal(others.templateId)) subOtpTemplateId = others.templateId
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'subOtpTemplateId', fields: subOtpTemplateId })
        templateIdByOtptype = subOtpTemplateId || util.templateid[otp_type]
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'templateIdByOtptype - 2', fields: templateIdByOtptype })
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'templateIdByOtptype - 3', fields: templateIdByOtptype })

      const _resent = await this.getResentData(mobile_number, orderid, otp_type)
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: '_resent', fields: _resent })
      const getProviderIdForSms = await this.getProviderIdForSms(mobile_number, orderid)
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      const getProviderId = getProviderIdForSms.data.provider_id || null

      console.log(_resent)
      if (_resent.length > 0) {
        const resentSmsData = _resent[0]

        if (resentSmsData.retry_count >= 3) {
          return { status: false, message: errorMsg.responseCode[1006], respcode: 1006 }
        }

        // const message = util.communication[otp_type] + _resent[0].otp + '.' + util.communication.Signature
        // const message = this.getOtpMsg(_resent[0].otp, otp_type)
        // sms.sentSms(_resent[0].otp_template, mobile_number)
        // await sms.sendOtpSms(_resent[0].otp_template, mobile_number)
        // const expiry = await this.getExpiry('expiry', otp_type)
        const expiry = await _resent[0].expiryTime
        console.log('expiry', expiry)
        const otpResp = await sms.sentSmsAsync(_resent[0].otp_template, mobile_number, templateIdByOtptype, 'otp', null, { aggregator_order_id: orderid, provider_id: getProviderId })
        const updateObject = {
          retry_count: resentSmsData.retry_count + 1,
          flag: '0',
          response: 'Resend : ' + otpResp.message,
          expiry: expiry
        }
        await this.updateEntry('', { aggregator_order_id: orderid, retry_count: resentSmsData.retry_count + 1, flag: '0', response: 'Resend : ' + otpResp.message, updateObject })
        if (otpResp.status !== 200) {
          return { status: false, message: errorMsg.responseCode[1001] + ' ' + otpResp.message, respcode: 1001 }
        }
        return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, template_text: _resent[0].otp_template }
      } else {
        return { status: false, message: "Sorry, please try 'Resend OTP' after 30sec.", respcode: 1002 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  // Update in otp table, dont send actual sms
  static async resentOtpWithoutMessage (mobile_number, orderid, otp_type, others = {}) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'request', fields: { mobile_number, orderid, otp_type, others } })
    try {
      if (orderid === null || orderid === undefined || orderid === 'null' || orderid === 'undefined') {
        return { status: false, respcode: 1104, message: errorMsg.responseCode[1104] }
      }

      let templateIdByOtptype = util.templateid[otp_type]
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'templateIdByOtptype - 1', fields: templateIdByOtptype })
      if (otp_type == 'CMS') {
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'type', fields: 'CMS' })
        let subOtpTemplateId = null
        if (Object.keys(others).length > 0) {
          if ('templateId' in others && validator.definedVal(others.templateId)) subOtpTemplateId = others.templateId
        }
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'subOtpTemplateId', fields: subOtpTemplateId })
        templateIdByOtptype = subOtpTemplateId || util.templateid[otp_type]
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'templateIdByOtptype - 2', fields: templateIdByOtptype })
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'templateIdByOtptype - 3', fields: templateIdByOtptype })

      const _resent = await this.getResentData(mobile_number, orderid, otp_type)
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: '_resent', fields: _resent })

      console.log(_resent)
      if (_resent.length > 0) {
        const resentSmsData = _resent[0]

        if (resentSmsData.retry_count >= 3) {
          return { status: false, message: errorMsg.responseCode[1006], respcode: 1006 }
        }

        // const message = util.communication[otp_type] + _resent[0].otp + '.' + util.communication.Signature
        // const message = this.getOtpMsg(_resent[0].otp, otp_type)
        // sms.sentSms(_resent[0].otp_template, mobile_number)
        // await sms.sendOtpSms(_resent[0].otp_template, mobile_number)
        // const expiry = await this.getExpiry('expiry', otp_type)
        const expiry = await _resent[0].expiryTime
        console.log('expiry', expiry)
        const updateObject = {
          retry_count: resentSmsData.retry_count + 1,
          flag: '0',
          response: 'Resend : OTP',
          expiry: expiry
        }
        await this.updateEntry('', { aggregator_order_id: orderid, retry_count: resentSmsData.retry_count + 1, flag: '0', response: 'Resend : OTP', updateObject })

        return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, template_text: _resent[0].otp_template, otp: _resent[0].otp }
      } else {
        return { status: false, message: "Sorry, please try 'Resend OTP' after 30sec.", respcode: 1002 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async verifyOtp (orderid, otp_type, otp, conn) {
    try {
      const verifyResp = await this.getVerifyData(orderid, otp_type, conn)
      if (verifyResp.length > 0) {
        if (verifyResp[0].otp === otp) {
          await this.updateEntry('', { aggregator_order_id: orderid, retry_count: verifyResp[0].retry_count + 1, flag: '2' })
          return { status: true, message: errorMsg.responseCode[1000], respcode: 1000 }
        } else {
          if (verifyResp[0].retry_count >= 3) {
            return { status: false, message: errorMsg.responseCode[1006], respcode: 1006 }
          } else {
            console.log('condition3')
            await this.updateEntry('', { aggregator_order_id: orderid, retry_count: verifyResp[0].retry_count + 1, flag: '3' })
            return { status: false, message: errorMsg.responseCode[1007], respcode: 1007 }
          }
        }
      } else {
        return { status: false, message: (verifyResp.message) ? verifyResp.message : errorMsg.responseCode[1008], respcode: 1008 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
     * Generate random number
     */
  static async generateOTP (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  /**
         * Calculate date
         */
  static async getExpiry (type, otp_type = null) {
    console.log(type)
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    if (type === 'expiry') {
      console.log('inside type')
      if (timezone !== -330) {
        today.setTime(today.getTime() + (util.expiry[otp_type] * 60 * 1000) + (30 * 60 * 1000) + (5 * 60 * 60 * 1000)) // time converted to ist from utc for testing purpose on kubeless. On live remove the 5:30 from date calculation.
      } else {
        today.setTime(today.getTime() + (util.expiry[otp_type] * 60 * 1000))
      }
      const min = today.getMinutes()
      const sec = today.getSeconds()
      const hh = today.getHours()
      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`
    } else {
      const timeStamp = Math.floor(Date.now() / 1000)
      return `${timeStamp}`
    }
  }

  static async getSQLExpiry (connection, otp_type = null) {
    const sqlExpiry = `SELECT TIMESTAMPADD(MINUTE,${util.expiry[otp_type]},CURRENT_TIMESTAMP()) as expiryTime`
    const resultExiry = await this.rawQuery(sqlExpiry, connection)
    console.log('resultExiry>>', resultExiry)
    return resultExiry[0].expiryTime
  }

  static async sentBankOtp (ma_user_id, userid, mobile_number, otp_type, { handler, BANK_OP, bankName, bankOtp, sessionRQ, senderDetails = false, remitterName }, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentBankOtp', type: 'request', fields: { ma_user_id, userid, mobile_number, otp_type, handler, bankName, BANK_OP } })
    // other =
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      const { BANK_NAME, BANK_TPYE, BANK_ON_BOARDING_ID } = handler

      let random = ''
      let otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
      let otp_template = this.getOtpMsg(otp, otp_type)
      if (bankOtp == true) {
        // random = BANK_NAME.length < 4 ? BANK_NAME : BANK_NAME.substring(0, 4).toUpperCase()
        random = await this.generateOTP(4)
      } else {
        random = await this.generateOTP(4)
        otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
        // Get OTP Message by OTP Type
        let message = this.getOtpMsg(otp, otp_type)
        message = message + util.bankNameCommunication[bankName]
        // Replace any extra parameters provided in message

        console.log('CO_MESSAGE_FINAL==>', message)
        otp_template = message
      }

      const expiry = await this.getExpiry('expiry', otp_type)

      const timestamp = await this.getExpiry('')
      const aggregator_order_id = `MA${otp_type}${random}${timestamp}`

      const _data = await this.createEntry('', {
        ma_user_id: ma_user_id,
        userid: userid,
        otp: otp,
        mobile: mobile_number,
        expiry: expiry,
        aggregator_order_id: aggregator_order_id,
        retry_count: 0,
        flag: '1',
        otp_type: otp_type,
        otp_template: otp_template,
        ma_bank_on_boarding_id: BANK_ON_BOARDING_ID
      }, conn)
      if (_data.affectedRows > 0) {
        // const otpResp = await sms.sentSmsAsync(otp_template, mobile_number, util.templateid[otp_type], 'otp')

        const extraPayLoad = {}
        if (senderDetails === true) {
          const senderData = await bankOnBoardDetails.getSenderAddressDetails(null, { userid: userid }, connection)

          if (senderData.status !== 200 && senderData.data.length == 0) {
            return senderData
          }

          const senderArray = senderData.data[0]

          const remitterNameArr = remitterName.split(' ')
          const remitterNameLength = remitterNameArr.length
          if (remitterNameLength == 1) {
            extraPayLoad.firstName = remitterNameArr[0]
            extraPayLoad.lastName = remitterNameArr[1]
          } else {
            extraPayLoad.lastName = remitterNameArr.pop()
            extraPayLoad.firstName = remitterNameArr.join(' ')
          }

          extraPayLoad.senderaddress1 = senderArray.address
          extraPayLoad.senderaddress2 = senderArray.cityname
          extraPayLoad.pincode = senderArray.pincode
          extraPayLoad.cityname = senderArray.cityname
          extraPayLoad.statename = senderArray.statename
          extraPayLoad.dob = '' // optinal,

          let country = 'INDIA'
          if (validator.definedVal(senderArray.country)) {
            country = senderArray.country
          }

          extraPayLoad.country = country
        }

        const apiAPIPayload = {
          sendermobilenumber: mobile_number,
          ma_user_id: ma_user_id,
          userid: userid,
          ma_bank_on_boarding_id: BANK_ON_BOARDING_ID,
          otp_template: otp_template,
          templateid: util.templateid[otp_type],
          sms_type: 'otp'
        }

        const payment = new Payment(BANK_NAME, BANK_TPYE, connection, BANK_ON_BOARDING_ID)

        console.time('TIMER_' + BANK_OP + '_' + BANK_NAME)
        extraPayLoad.aggregator_order_id = aggregator_order_id || null
        const resRemit = await payment.requestToBank(BANK_OP, { ...apiAPIPayload, ...extraPayLoad }, sessionRQ, connection)

        console.timeEnd('TIMER_' + BANK_OP + '_' + BANK_NAME)

        log.logger({ pagename: require('path').basename(__filename), action: 'sentBankOtp', type: 'sendOtpAddRemitterResponse', fields: resRemit })

        const response = { ...resRemit }
        delete response.status
        delete response.message
        delete response.respcode
        await this.updateEntry('', { aggregator_order_id: aggregator_order_id, retry_count: 0, flag: '0', response: JSON.stringify(response) })

        if (resRemit.status === 200) {
          return { status: true, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: aggregator_order_id } }
        } else {
          return { status: false, message: resRemit.message, respcode: 1001 }
        }
      } else {
        return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sentOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async resentBankOtp (handler, BANK_OP, mobile_number, orderid, otp_type, conn, sessionRQ, respCode = 1000) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentBankOtp', type: 'request', fields: { handler, mobile_number, orderid, otp_type, sessionRQ, respCode } })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      if (!validator.definedVal(orderid)) {
        return { status: false, respcode: 1104, message: errorMsg.responseCode[1104] }
      }
      const _resent = await this.getResentData(mobile_number, orderid, otp_type)
      console.log(_resent)
      if (_resent.length > 0) {
        const _resentData = _resent[0]
        if (_resentData.retry_count >= 3) {
          return { status: false, message: errorMsg.responseCode[1006], respcode: 1006 }
        }
        const apiAPIPayload = {
          sendermobilenumber: mobile_number,
          ma_user_id: _resentData.ma_user_id,
          userid: _resentData.userid,
          ma_bank_on_boarding_id: _resentData.ma_bank_on_boarding_id,
          otp_template: _resentData.otp_template,
          templateid: util.templateid[otp_type]
        }

        const extraPayLoad = JSON.parse(_resentData.response)

        const Payment = require('./../bankHandler/payment')
        const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)

        console.time(BANK_OP + '_' + handler.BANK_NAME)
        extraPayLoad.aggregator_order_id = orderid || null
        const resRemit = await payment.requestToBank(BANK_OP, { ...apiAPIPayload, ...extraPayLoad }, sessionRQ, connection)

        console.timeEnd(BANK_OP + '_' + handler.BANK_NAME)

        log.logger({ pagename: require('path').basename(__filename), action: 'resentBankOtp', type: 'resendOtpAddRemitterResponse', fields: resRemit })

        const response = { ...resRemit }
        delete response.status
        delete response.message
        delete response.respcode

        const otpExpiry = _resent[0].expiryTime

        const updateObject = {
          retry_count: _resentData.retry_count + 1,
          flag: '0',
          response: JSON.stringify(response),
          expiry: otpExpiry
        }

        await this.updateEntry('', { aggregator_order_id: orderid, retry_count: _resentData.retry_count + 1, flag: '1', response: JSON.stringify(response), updateObject })

        if (resRemit.status === 200) {
          return { status: true, message: errorMsg.responseCode[respCode], respcode: respCode, data: { aggregator_order_id: orderid } }
        } else {
          return { status: false, message: resRemit.message, respcode: 1001 }
        }
      } else {
        return { status: false, message: "Sorry, please try 'Resend OTP' after 30sec.", respcode: 1002 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentBankOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async verifyBankOtp (orderid, otp_type, otp, { handler, BANK_OP, bankName, bankOtp, fields, required_field }, conn, sessionRQ) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyBankOtp', type: 'request', fields: { orderid, otp_type, otp } })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      const { BANK_NAME, BANK_TPYE, BANK_ON_BOARDING_ID } = handler
      const verifyResp = await this.getVerifyData(orderid, otp_type, connection)
      let senderid = ''
      let remitter_name = ''
      if (verifyResp.length > 0) {
        const validOTPFlag = bankOtp ? validator.definedVal(otp) : verifyResp[0].otp === otp
        if (validOTPFlag) {
          let bankOTPRsponse = {}
          const verifyRespJson = verifyResp[0].response
          if (validator.validJson(verifyRespJson)) {
            bankOTPRsponse = JSON.parse(verifyRespJson)
          }

          const senderData = await bankOnBoardDetails.getSenderAddressDetails(null, { userid: fields.userid }, connection)

          // console.log('senderData', senderData)
          if (senderData.status === 200 && senderData.data.length > 0) {
            const senderArray = senderData.data[0]

            /** Parse CustomJSON Fields */
            fields.customerFieldJsonArr = []
            if (validator.definedVal(fields.customerFieldJson)) {
              try {
                const customerFieldJson = Buffer.from(fields.customerFieldJson, 'base64').toString('ascii')
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpV2', type: 'customerFieldJson', fields: customerFieldJson })
                fields.customerFieldJsonArr = JSON.parse(customerFieldJson)
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpV2', type: 'customerFieldJsonArr', fields: fields.customerFieldJsonArr })
              } catch (error) {
                log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpV2', type: 'catcherrorcustomerFieldJsonArr', fields: error })
                return { status: 400, respcode: 1083, message: errorMsg.responseCode[1083] }
              }
            }

            const customFields = {}
            if (validator.definedVal(required_field) && validator.definedVal(fields.customerFieldJson) !== '' && Object.keys(fields.customerFieldJsonArr).length > 0) {
              // Validate as per fields rule
              fields.customerFieldJsonArr.forEach(currentField => {
                for (const [key, value] of Object.entries(currentField)) {
                  console.log(`customerField ==> ${key}: ${value}`)
                  customFields[key] = value
                }
              })
            }

            let country = 'INDIA'
            if (validator.definedVal(senderArray.country)) {
              country = senderArray.country
            }

            const apiAPIPayload = {
              ma_user_id: fields.ma_user_id,
              ma_bank_on_boarding_id: BANK_ON_BOARDING_ID,
              sendermobilenumber: fields.mobile_number,
              senderaddress1: senderArray.address,
              sendername: 'Remitter Airpay',
              senderaddress2: senderArray.cityname, // to do need to asked
              pincode: senderArray.pincode,
              cityname: senderArray.cityname,
              statename: senderArray.statename,
              bcpartnerrefno: fields.uic,
              dob: '', // optinal,
              customFields: customFields,
              firstName: fields.firstName,
              lastName: fields.lastName,
              country: country,
              bankOTPRsponse: bankOTPRsponse,
              otp: fields.otp
            }

            apiAPIPayload.sendername = fields.remitter_name

            // Load Bank Modules

            const Payment = require('./../bankHandler/payment')
            const payment = new Payment(BANK_NAME, BANK_TPYE, connection, BANK_ON_BOARDING_ID)
            const resRemit = await payment.requestToBank(BANK_OP, apiAPIPayload, sessionRQ, connection)
            console.log('resRemit', resRemit)
            if (resRemit.status == 200) {
              senderid = resRemit.senderid
              remitter_name = resRemit.remitter_name
              return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: verifyResp[0].response, remitter_id: senderid, remitter_name: resRemit.remitter_name }
            } else {
              // Failed due to api or bank module issue
              return resRemit
            }
          } else {
            // Sender address details not found ma_user_master
            return senderData
          }
        } else {
          if (verifyResp[0].retry_count >= 3) {
            return { status: 400, message: errorMsg.responseCode[1006], respcode: 1006 }
          } else {
            console.log('condition3')
            await this.updateEntry('', { aggregator_order_id: orderid, retry_count: verifyResp[0].retry_count + 1, flag: '3' })
            return { status: 400, message: errorMsg.responseCode[1007], respcode: 1007 }
          }
        }
      } else {
        return { status: 400, message: (verifyResp.message) ? verifyResp.message : errorMsg.responseCode[1008], respcode: 1008 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBankOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async verifyOnlyBankOtp (orderid, otp_type, otp, conn, bankOtp) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOnlyBankOtp', type: 'request', fields: { orderid, otp_type, otp } })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      const verifyResp = await this.getVerifyData(orderid, otp_type, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOnlyBankOtp', type: 'verifyRespData', fields: verifyResp })
      if (verifyResp.length > 0) {
        const validOTPFlag = bankOtp ? validator.definedVal(otp) : verifyResp[0].otp === otp
        if (validOTPFlag) {
          let bankOTPRsponse = {}
          const verifyRespJson = verifyResp[0].response
          if (validator.validJson(verifyRespJson)) {
            bankOTPRsponse = JSON.parse(verifyRespJson)
          }

          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: bankOTPRsponse }
        } else {
          if (verifyResp[0].retry_count >= 3) {
            return { status: 400, message: errorMsg.responseCode[1006], respcode: 1006 }
          } else {
            console.log('condition3')
            await this.updateEntry('', { aggregator_order_id: orderid, retry_count: verifyResp[0].retry_count + 1, flag: '3' })
            return { status: 400, message: errorMsg.responseCode[1007], respcode: 1007 }
          }
        }
      } else {
        return { status: 400, message: (verifyResp.message) ? verifyResp.message : errorMsg.responseCode[1008], respcode: 1008 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOnlyBankOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async sentBeneficiaryOtp ({ fields, connection, handler, otp_type, bankOtp, BANK_OP }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentBeneficiaryOtp', type: 'request', fields: { fields, connection, handler, otp_type, bankOtp, BANK_OP } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const { BANK_NAME, BANK_TPYE, BANK_ON_BOARDING_ID } = handler
      const { ma_user_id, userid, mobile_number, sessionRQ } = fields
      const payment = new Payment(BANK_NAME, BANK_TPYE, connection, BANK_ON_BOARDING_ID)

      let random = ''
      let otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
      let otp_template = this.getOtpMsg(otp, otp_type)
      if (bankOtp == true) {
        random = await this.generateOTP(4)
      } else {
        random = await this.generateOTP(4)
        otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
        // Get OTP Message by OTP Type
        let message = this.getOtpMsg(otp, otp_type)
        message = message + util.bankNameCommunication[BANK_NAME]
        // Replace any extra parameters provided in message

        console.log('CO_MESSAGE_FINAL==>', message)
        otp_template = message
      }

      const expiry = await this.getExpiry('expiry', otp_type)

      const timestamp = await this.getExpiry('')
      const aggregator_order_id = fields.aggregator_order_id || `MA${otp_type}${random}${timestamp}`

      const _data = await this.createEntry('', {
        ma_user_id: ma_user_id,
        userid: userid,
        otp: otp,
        mobile: mobile_number,
        expiry: expiry,
        aggregator_order_id: aggregator_order_id,
        retry_count: 0,
        flag: '1',
        otp_type: otp_type,
        otp_template: otp_template,
        ma_bank_on_boarding_id: BANK_ON_BOARDING_ID
      }, conn)

      if (_data.affectedRows == 0) {
        return { status: false, message: errorMsg.responseCode[1001], respcode: 1001 }
      }

      const apiAPIPayload = {
        beneficiary_name: fields.beneficiary_name,
        account_number: fields.re_account_number,
        bank_name: fields.bank_name,
        ifsc_code: fields.ifsc_code,
        ben_mobile_number: fields.ben_mobile_number,
        isVerified: fields.isVerified,
        mobile_number: fields.mobile_number,
        ma_user_id: fields.ma_user_id,
        ma_bank_on_boarding_id: BANK_ON_BOARDING_ID

      }

      const result = await payment.requestToBank(BANK_OP, apiAPIPayload, sessionRQ, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'sentBeneficiaryOtp', type: 'bankResponse', fields: result })

      const response = { ...result }
      delete response.status
      delete response.message
      delete response.respcode

      await this.updateEntry('', { aggregator_order_id: aggregator_order_id, retry_count: 0, flag: '0', response: JSON.stringify(response) })

      if (result.status === 400) {
        return { status: 400, message: result.message, respcode: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: aggregator_order_id, response: response.response } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sentBeneficiaryOtp', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async resentBeneficiaryOtp ({ fields, connection, handler, otp_type, BANK_OP }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const { mobile_number, orderid, sessionRQ, ma_user_id } = fields
      const _resent = await this.getResentData(mobile_number, orderid, otp_type)

      if (_resent.length == 0) {
        return { status: false, message: "Sorry, please try 'Resend OTP' after 30sec.", respcode: 1002 }
      }
      const _resentData = _resent[0]

      if (_resentData.retry_count >= 3) {
        return { status: false, message: errorMsg.responseCode[1006], respcode: 1006 }
      }

      const { BANK_NAME, BANK_TPYE, BANK_ON_BOARDING_ID } = handler
      const payment = new Payment(BANK_NAME, BANK_TPYE, connection, BANK_ON_BOARDING_ID)

      const apiAPIPayload = {
        ...JSON.parse(_resentData.response).request,
        mobile_number: mobile_number,
        ma_bank_on_boarding_id: BANK_ON_BOARDING_ID,
        ma_user_id: ma_user_id
      }

      const result = await payment.requestToBank(BANK_OP, apiAPIPayload, sessionRQ, connection)

      const response = { ...result }
      delete response.status
      delete response.message
      delete response.respcode

      const otpExpiry = _resent[0].expiryTime

      const updateObject = {
        retry_count: _resentData.retry_count + 1,
        flag: '0',
        response: JSON.stringify(response),
        expiry: otpExpiry
      }

      await this.updateEntry('', { aggregator_order_id: orderid, retry_count: _resentData.retry_count + 1, flag: '1', response: JSON.stringify(response), updateObject })

      if (result.status === 400) {
        return { status: 400, message: result.message, respcode: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { aggregator_order_id: orderid } }
    } catch (error) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = Otp
