const nodemailer = require('nodemailer')
const mailconfig = require('../lib/mailConfig')

const expressErrorHandler = (error) => {
  const errorResponse = {}

  errorResponse.status = 500
  errorResponse.respcode = 500
  errorResponse.message = 'Something went wrong! please connect to customer care for support.'

  errorResponse.message += error.message
  errorResponse.locations = error.locations
  errorResponse.stack = error.stack ? error.stack.split('\n') : []
  errorResponse.path = error.path

  return errorResponse
}

const serverlessErrorHandler = async (request, response) => {
  const errorResponse = {}

  errorResponse.status = 500
  errorResponse.respcode = 500
  errorResponse.message = 'Something went wrong! please connect to customer care for support.'
  console.log('Response::', response)
  try {
    if ('errors' in response) {
      // const errRes = response.errors[0]
      // errorResponse.message += errorResponse.message
      if (process.env.STOP_ERROR_EMAIL === 'true') {
        console.log('GraphQLRequest::', request.data)
        console.log('GraphQLUser-Agent', (request.headers['User-Agent']) ? request.headers['User-Agent'] : '')
        console.log('GraphQLOrigin::', (request.headers.origin) ? request.headers.origin : '')
        console.log('GraphQLResponseError::', response.errors)
      } else {
        console.log('GraphQLEmailRequest::', request.data)
        if (request.headers) {
          console.log('GraphQLEmailUser-Agent', (request.headers['User-Agent']) ? request.headers['User-Agent'] : '')
          console.log('GraphQLEmailOrigin::', (request.headers.origin) ? request.headers.origin : '')
          console.log('GraphQLEmailResponseError::', response.errors)
        }
        const messageResponseObj = {
          message: response.errors[0].message + '<br/>',
          stack: response.errors[0].stack ? response.errors[0].stack.split('\n') : []
        }
        const messageResponse = `<b>Message</b><p>${messageResponseObj.message}</p><b>Stack</b><p>${messageResponseObj.stack}</p>`
        await notifyErrorByEmail(request, messageResponse)
      }

      return errorResponse
    }
    return response
  } catch (error) {
    console.log('serverlessErrorHandlerErr', error)
    errorResponse.message += ' Catch'
    return errorResponse
  }
}

const notifyErrorByEmail = async (request, response) => {
  const transporter = nodemailer.createTransport(mailconfig)

  const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'

  let userAgent = ''
  let headersOrigin = ''
  let requestdata = ''
  if (request.headers) {
    userAgent = (request.headers['User-Agent']) ? request.headers['User-Agent'] : ''
    headersOrigin = (request.headers.origin) ? request.headers.origin : ''
  }

  if (request.data) {
    requestdata = request.data
  } else {
    requestdata = request
  }

  const htmlMessage = `
    <p>Hi Team,</p>
    <p>We are facing some issue please check</p>
    <b>Enviroment:</b> ${env} - ${process.env._HANDLER} - ${process.env.AWS_LAMBDA_FUNCTION_NAME}
    <hr/>
    <b>Request:</b> 
    <p> ${JSON.stringify(requestdata)} </p>
    <hr/>
    <b>User agent:</b> 
    <p> ${userAgent} </p>
    <hr/>
    <b>Origin:</b> 
    <p> ${headersOrigin} </p>
    <hr/>
    <b>response:</b> 
    <p> ${response} </p>
  `
  let to = '<EMAIL>,<EMAIL>,<EMAIL>'

  if (env === 'production') {
    to = '<EMAIL>,<EMAIL>,<EMAIL>'
  }

  // to = '<EMAIL>'

  const info = await transporter.sendMail({
    from: '"GraphQL Error" <<EMAIL>>', // sender address
    to: to, // list of receivers
    subject: 'Graphql Error occured - [' + env + '] ', // Subject line
    html: htmlMessage // html body
  })

  console.log('Message sent: %s', info.messageId)
  if (info) {
    return { status: 200, message: 'successfully email sent' }
  } else {
    return { status: 400, message: 'failed to sent email' }
  }
}

const notifyCatchErrorEmail = async (request) => {
  const transporter = nodemailer.createTransport(mailconfig)

  const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'

  if (env == 'development') return 'done'

  let requestData = {}

  if ('connection' in request.data) {
    requestData = { ...request.data }
    requestData.connection = ''
  } else {
    requestData = request.data
  }

  const htmlMessage = `
    <p>Hi Team,</p>
    <p>We are facing some issue please check</p>
    <b>Enviroment:</b> ${env}
    <hr/>
    <b>Time:</b> 
    <p> ${new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }))} </p>
    <hr/>
    <b>Request:</b> 
    <p> ${JSON.stringify(requestData)} </p>
    <hr/>
    <b>Function:</b> 
    <p> ${JSON.stringify(request.function)} </p>
    <hr/>
    <b>Error:</b>
    <p>NAME: ${JSON.stringify(request.error.name)} </p>
    <hr/>
    <p>MESSAGE: ${JSON.stringify(request.error.message)} </p>
    <hr/>
    <p>STACK: ${JSON.stringify(request.error.stack)} </p>
    <hr/>
  `
  let to = '<EMAIL>,<EMAIL>,<EMAIL>'

  if (env === 'production') {
    to = '<EMAIL>,<EMAIL>,<EMAIL>'
  }

  // to = '<EMAIL>'

  const info = await transporter.sendMail({
    from: '"Catch Error" <<EMAIL>>', // sender address
    to: to, // list of receivers
    subject: 'Catch Error occured - [' + env + '] ', // Subject line
    html: htmlMessage // html body
  })

  console.log('Message sent: %s', info.messageId)
  if (info) {
    return { status: 200, message: 'successfully email sent' }
  } else {
    return { status: 400, message: 'failed to sent email' }
  }
}

module.exports = {
  expressErrorHandler,
  serverlessErrorHandler,
  notifyCatchErrorEmail
}
