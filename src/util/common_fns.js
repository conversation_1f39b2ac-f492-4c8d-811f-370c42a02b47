const calculate_age = (birth_day, birth_month, birth_year) => {
  const today_date = new Date()
  const today_year = today_date.getFullYear()
  const today_month = today_date.getMonth()
  const today_day = today_date.getDate()
  let age = today_year - birth_year

  if (today_month < (birth_month - 1)) {
    age--
  }
  if (((birth_month - 1) == today_month) && (today_day < birth_day)) {
    age--
  }
  return age
}

const maskOrRemoveString = (query, positions, remove, fullmasking, parenthis = false) => {
  let newQuery = query.substring(0, positions[0])
  if (!remove) {
    newQuery += query.substring(positions[0], positions[1])
    if (fullmasking) {
      newQuery += query.substring(positions[1], positions[2]).replace(/./g, '*')
    } else {
      newQuery += query.substring(positions[1], positions[2]).replace(/ /g, '').replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
    }
  }
  newQuery += query.substring(positions[2] + (!remove && !parenthis ? 0 : 1))
  return newQuery
}

const maskOrRemoveJson = (json, key, remove, fullmasking) => {
  // checks if defined
  if (json[key]) {
    // deletes if marked true else masks it
    if (remove || typeof json[key] != 'string') {
      delete json[key]
    } else {
      if (fullmasking) {
        json[key] = json[key].replace(/./g, '*')
      }
      json[key] = json[key].replace(/ /g, '').replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
    }
  }

  return json
}

/**
 * Returns a new json Object or event.data.query string with data masked or removed
 * @param {String|JSON|Array} content that you want to mask or remove value from
 * @param {String|String[]} param key of params that you want to mask or remove
 * @example 'password', ['password', 'current_password']
 * @param {boolean} remove if true removes the specified params else mask them
 * @param {boolean} fullmasking if true masks the specified params completely
 */
const maskValue = (
  content,
  param = ['password', 'new_password', 'new_confirm_password', 'current_password'],
  remove = false,
  fullmasking = false
) => {
  // set default values for params if any param is null
  remove = remove || false
  fullmasking = fullmasking || false
  param = param || ['password', 'new_password', 'new_confirm_password', 'current_password']

  // checks if param is an array if not then sets it as an array
  param = Array.isArray(param) ? param : [param]

  try {
    // if content is an ojbect removes or mask sepecified params within the object
    console.log(typeof content)
    if (typeof content == 'object') {
      // creates a new object to avoid unwanted data mutation
      let newContent = Array.isArray(content) ? [...content] : { ...content }

      // loops through each specified param
      for (const _param of param) {
        newContent = maskOrRemoveJson(newContent, _param, remove, fullmasking)
      }
      return newContent
    }

    // removes space from the event.data.query
    let query = (content || '').replace(/ /g, '')

    // const query = 'checkUserLogin(username:"M28479",password:"Test4545",gcm_id:"hjdgfhgdfhg1hjhhhhg"){status,respcode,message,orderid,deviceid,data}'

    for (const _param of param) {
      let position = query.indexOf(',' + _param)
      position = position > -1 ? position : query.indexOf('(' + _param)
      if (position > -1) {
        const positionOfColon = query.indexOf(':', position)
        const positionOfNextComma = query.indexOf(',', position + 1)
        const positionOfCloseParenthis = query.indexOf(')', position + 1)

        if (positionOfNextComma > -1 && positionOfCloseParenthis > positionOfNextComma) {
          query = maskOrRemoveString(query, [position + 1, positionOfColon + 1, positionOfNextComma], remove, fullmasking)
        } else {
          query = maskOrRemoveString(query, [position + 1, positionOfColon + 1, positionOfCloseParenthis], remove, fullmasking, true)
        }
      }
    }

    return query
  } catch (err) {
    console.log('Mask Value error ---------->', err)
    return content
  }
}

const asyncForEachPromised = async (array, callback) => {
  await Promise.all(array.map(async (items) => {
    await callback(items)
  }))
}

const getTryBankArray = (BankListData = [], skipBankId = 0) => {
  console.log('getTryBankArrayPARAMETERS', BankListData, 'skipBankId ==>', skipBankId)
  const tryWithOtherBankObject = {}
  try {
    const tryWithOtherBankTmp = BankListData.filter((bankItem) => !(skipBankId.indexOf(bankItem.ma_bank_on_boarding_id) > -1))

    console.log('tryWithOtherBankTmpFilterArray', tryWithOtherBankTmp)

    if (typeof (tryWithOtherBankTmp) != 'undefined' && Array.isArray(tryWithOtherBankTmp) && tryWithOtherBankTmp.length > 0) {
      for (let index = 0; index < tryWithOtherBankTmp.length; index++) {
        const bankItemTmp = tryWithOtherBankTmp[index]
        const tmp = {}

        tmp.ma_bank_on_boarding_id = bankItemTmp.ma_bank_on_boarding_id
        tmp.bank_name = bankItemTmp.bank_name
        tmp.bank_logo = bankItemTmp.bank_logo
        tmp.dailyLimit = bankItemTmp.max_amount
        tmp.monthlyLimit = bankItemTmp.max_amount
        tmp.remainingLimit = 0
        tmp.consumedLimit = 0
        tmp.transfer_mode = [{
          ma_bank_type_id: bankItemTmp.ma_bank_type_id,
          transaction_type: bankItemTmp.transaction_type,
          priority: bankItemTmp.priority
        }]

        if (!tryWithOtherBankObject[bankItemTmp.bank_name]) {
          tryWithOtherBankObject[bankItemTmp.bank_name] = tmp
        } else {
          tryWithOtherBankObject[bankItemTmp.bank_name].transfer_mode.push({
            ma_bank_type_id: bankItemTmp.ma_bank_type_id,
            transaction_type: bankItemTmp.transaction_type,
            priority: bankItemTmp.priority
          })
        }
      }
    }

    console.log('tryWithOtherBankObjectFinal', tryWithOtherBankObject)
    return Object.keys(tryWithOtherBankObject).length > 0 ? Object.values(tryWithOtherBankObject) : []
  } catch (error) {
    console.log('getTryBankArrayError', error)
    return []
  }
}

module.exports = {
  calculateAge: calculate_age,
  maskValue: maskValue,
  asyncForEachPromised: asyncForEachPromised,
  getTryBankArray: getTryBankArray
}
