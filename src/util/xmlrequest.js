
const axios = require('axios')
const j2xml = require('js2xmlparser')
const xml2js = require('fast-xml-parser')
const http = require('http')
const https = require('https')

const isHandlerEnabled = (config = {}) => {
  return !('handlerEnabled' in config && !config.handlerEnabled)
}

const requestHandler = (request) => {
  if (isHandlerEnabled(request)) {
    // Modify request here
    request.headers['Content-Type'] = 'text/xml;charset=utf-8'
    request.headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/69.0.3497.105 Mobile/15E148 Safari/605.1'
  }
  return request
}

const errorHandler = (error) => {
  if (isHandlerEnabled(error.config)) {
    // Handle errors
  }
  console.log('errorHandler::', error)
  return Promise.reject(error)
}

const successHandler = (response) => {
  if (isHandlerEnabled(response.config)) {
    // Handle responses
  }
  //  console.log('successHandler::', response)
  return response
}

// Init Axios
const xmlInstanceRequest = axios.create({
  // keepAlive pools and reuses TCP connections, so it's faster
  httpAgent: new http.Agent({ keepAlive: true }),
  httpsAgent: new https.Agent({ keepAlive: true }),
  transformRequest: [
    (data, headers) => {
      const xmlOption = {
        declaration: {
          include: true,
          encoding: 'UTF-16',
          standalone: 'no',
          version: '1.0'
        },
        format: {
          doubleQuotes: true,
          pretty: true
        },
        useSelfClosingTagIfEmpty: false
      }

      // console.log(data)
      const xmlData = j2xml.parse(data.wrapIn, data.payLoad, xmlOption)
      // masking pancard number
      console.log('API Request::', xmlData.replace(/<pannumber>([A-Z]{5}[0-9]{4}[A-Z]{1})<\/pannumber>/, '<pannumber>********</pannumber>'))
      return xmlData
    }
  ],
  transformResponse: [
    (data) => {
      try {
        console.log('API Response::', data)
        const result = xml2js.parse(data, { parseTrueNumberOnly: true }, true)
        return result
      } catch (error) {
        console.log('transformResponse::', error)
        return { status: 400, respcode: 1041, message: 'XmlParsingError' }
      }
    }
  ]
})

xmlInstanceRequest.interceptors.request.use(
  request => requestHandler(request)
)
xmlInstanceRequest.interceptors.response.use(
  response => successHandler(response),
  error => errorHandler(error)
)

module.exports = {
  apiXmlRequest: xmlInstanceRequest
}
