const externalapi = {
  params: {
  },
  lambdaTypes: {
    fasttagcustomer: {
      functionName: 'merchantapp-fasttagcustomer-DA1GR64BACOF',
      path: 'src/model/ma_ft_customer_master/type.js',
      addressDetail: ['address1', 'address2', 'address3'],
      customers: ['ma_user_id', 'userid', 'ma_ft_customer_id', 'ma_ft_customer_master_id', 'customer_type', 'first_name', 'middle_name', 'last_name', 'gender', 'dob', 'gst_number', 'status', 'mobile_number', 'phone_number', 'email', 'usr_consent', 'address_1', 'address_2', 'address_3', 'district', 'city', 'country', 'state', 'pincode'],
      VehicleDetails: ['entityId', 'tagId', 'vrn']
    },
    fasttagissue: {
      functionName: 'merchantapp-fastagissue-1STFWBI4QKB6D',
      path: 'src/model/ma_ft_vehicle_details/type.js'
    },
    fasttagproduct: {
      functionName: 'merchantapp-fasttagproduct-EUG1XO8TBNJ1',
      path: 'src/model/ma_ft_product_master/type.js',
      vehicleList: ['ma_ft_vehicle_master_id', 'vehicle_class', 'vehicle_name', 'cch'],
      productList: ['ma_ft_product_master_id', 'vehicle_class', 'product_id', 'product_name', 'cch'],
      categoryList: ['category_name']
    },
    cms: {
      functionName: 'stag-dmtapp-cms-1TGWHQ1J3BV3Q',
      path: 'src/model/ma_cms_merchant_on_boarding/type.js',
      MerchantList: ['ma_cms_merchant_on_boarding_id', 'merchant_name', 'merchant_legal_name', 'cms_ma_user_id', 'cms_userid', 'client_id', 'merchant_type']
    }
  }
}

module.exports = externalapi
