const curl = require('curl')
const util = require('./util')
const axios = require('axios')
const mySQLWrapper = require('../lib/mysqlWrapper')
const DAO = require('../lib/dao')
const log = require('./log')
const errorMsg = require('../util/error')

class Sms extends DAO {
  static sentSms (message, number) {
    console.log('SMS Gateway Mobile::', number)
    console.log('SMS Gateway Message::', message)
    const body = `phone_number=${number}&message=${encodeURI(message)}`
    const options = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        token: util.smsPostmanToken,
        requrl: 'core'
      }
    }

    // Fetch whatsapp flag from DB
    const connection = mySQLWrapper.getConnectionFromPool()
    const sql = 'SELECT code_val FROM system_codes WHERE code_name = \'send_whatsapp_notification\''
    const queryResponse = this.rawQuery(sql, connection)
    connection.release()
    // console.log('---------------CODE VAL-------------------')
    // console.log(queryResponse)
    let code_val = 0
    if (queryResponse.length > 0) {
      code_val = queryResponse[0].code_val
    }
    // Check Whatsapp flag to send notifcation else send Sms
    if (code_val == 1) {
      // sendWhatsapp notification
      const response = this.sendWhatsappPostman(number, message)
      if (response.status === 200) {
        return { status: 200, message: JSON.stringify(response.data) }
      }
      return { status: 400, message: 'Curl error' }
    } else {
      curl.post(util.smsPostmanUrl, body, options, function (err, response, body) {
        if (err) {
          console.log('SMS Gateway Error::', err)
        }
        // console.log('SMS Gateway Response::', response)
        console.log('SMS Gateway body:: ', body)
      })
    }

    /* const options = {}
    const data = 'aid=' + util.smsUserName + '&pin=' + util.smsPassword + '&mnumber=91' + number + '&signature=' + util.smsSignature + '&message=' + message
    const url = util.smsURL + data
    console.log('SMS Gateway URL:: ', url)
    curl.get(url, options, function (err, response, body) {
      if (err) {
        console.log('SMS Gateway Error::' + number + ' ', err)
      }
      // console.log('SMS Gateway Response::', response)
      console.log('SMS Gateway body:: ' + number + ' ', body)
    }) */
  }

  static async sentSmsAsync (message, number, template_id, sms_type = null, conn, other = {}) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentSmsAsync', type: 'request', fields: { message, number, template_id, sms_type, other } })
    console.log('OTP SMS Mobile:: ', number)
    console.log('OTP SMS Message:: ', message)

    // Fetch whatsapp flag from DB
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    const sql = 'SELECT code_val FROM system_codes WHERE code_name = \'send_whatsapp_notification\''
    const queryResponse = await this.rawQuery(sql, connection)
    console.log('queryResponse>> ', queryResponse)
    if (tmpConn) connection.release()
    // console.log('---------------CODE VAL-------------------')
    // console.log(queryResponse)
    let code_val = 0
    if (queryResponse.length > 0) {
      code_val = queryResponse[0].code_val
    }
    let msg_type = ''
    if (code_val == 0) {
      msg_type = 'sms'
    } else {
      msg_type = 'whatsapp'
    }

    // generate Order number
    const orderNo = await this.generateOrderNo()
    const msg_status = 'P'

    // Txn order_id for which we r sending this OTP
    let aggregator_order_id = null
    if ('aggregator_order_id' in other) aggregator_order_id = other.aggregator_order_id

    // for provider id
    let provider_id = null
    if ('provider_id' in other && other.provider_id != null && other.provider_id != 'null') {
      provider_id = other.provider_id
    } else {
      const maSmsMasterData = await this.fetchMaSmsMasterData()
      log.logger({ pagename: require('path').basename(__filename), action: 'sentSmsAsync', type: 'maSmsMasterData', fields: maSmsMasterData })
      provider_id = (maSmsMasterData.status == 200 && maSmsMasterData.data != null) ? maSmsMasterData.data.provider_id : provider_id
    }

    // insert data in ma_sent_message_status
    const insertData = { order_id: orderNo, message_val: message, mobile_no: number, message_type: msg_type, status: msg_status, template_id: template_id, sms_type: sms_type, txn_order_id: aggregator_order_id, provider_id }
    const saveSentMessageStatusResult = await this.saveSentMessageStatus(insertData)
    log.logger({ pagename: require('path').basename(__filename), action: 'sentSmsAsync', type: 'saveSentMessageStatusResult', fields: saveSentMessageStatusResult })
    log.logger({ pagename: require('path').basename(__filename), action: 'sentSmsAsync', type: 'code_val', fields: code_val })

    if (code_val == 1) {
      // message = 'Dear Airpay,Please find the link to your invoice 8585.url : https://www.airpay.co.in'
      const response = await this.sendWhatsappPostman(number, message, orderNo)
      log.logger({ pagename: require('path').basename(__filename), action: 'sentSmsAsync', type: 'sendWhatsappPostman', fields: response })
      if (response.status === 200) {
        return { status: 200, message: JSON.stringify(response.data) }
      }
      return { status: 400, message: 'Curl error' }
    } else {
      const response = await this.sendSmsPostman(number, message, orderNo, template_id, sms_type, provider_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'sentSmsAsync', type: 'sendSmsPostman', fields: response })
      if (response.status === 200) {
        return { status: 200, message: JSON.stringify(response.data) }
      }
      return { status: 400, message: 'Curl error' }
    }

    /* const data = 'aid=' + util.smsUserName + '&pin=' + util.smsPassword + '&mnumber=91' + number + '&signature=' + util.smsSignature + '&message=' + message
    const url = util.smsURL + data
    console.log('SMS Mobile:: ', number)
    console.log('SMS URL:: ', url)
    const smsResp = await axios.get(url)
    console.log('SMS Response Code:: ' + smsResp.status)
    console.log('SMS Response Data:: ' + smsResp.data)
    if (smsResp.status === 200) {
      const resp = smsResp.data
      if (resp.search('Rejected') > -1) {
        return { status: 400, message: resp }
      }
      return { status: 200, message: resp }
    } else {
      return { status: 400, message: 'Curl Error : Unable to connect' }
    } */
  }

  // OTP SMS Function
  static async sendOtpSms (message, number) {
    var isValidNumber = !(typeof (number) === 'undefined' || number === null || number === '' || number === 'undefined')
    var isValidMessage = !(typeof (message) === 'undefined' || message === null || message === '' || message === 'undefined')

    if (!isValidNumber) {
      return { status: 400, message: 'Invalid Mobile number provided' }
    }

    if (!isValidMessage) {
      return { status: 400, message: 'Invalid Message provided' }
    }
    console.log('OTP SMS Mobile:: ', number)
    console.log('OTP SMS Message:: ', message)
    const response = await this.sendSmsPostman(number, message)
    if (response.status === 200) {
      return { status: 200, message: JSON.stringify(response.data) }
    }
    return { status: 400, message: 'Curl error' }
    /*
    const data = 'aid=' + util.smsUserName + '&pin=' + util.smsPassword + '&mnumber=91' + number + '&signature=' + util.smsSignature + '&message=' + message
    const url = util.smsURL + data
    console.log('OTP SMS Mobile:: ', number)
    console.log('OTP SMS URL:: ', url)
    const smsResp = await axios.get(url)
    console.log('OTP SMS Response Code:: ' + smsResp.status)
    console.log('OTP SMS Response Data:: ' + smsResp.data)
    if (smsResp.status === 200) {
      const resp = smsResp.data
      if (resp.search('Rejected') > -1) {
        return { status: 400, message: resp }
      }
      return { status: 200, message: resp }
    } else {
      return { status: 400, message: 'Curl Error : Unable to connect' }
    }
    */
  }

  static async sendSmsPostman (phoneNumber, message, uniqueId, template_id, smstype = null, provider_id = null) {
    console.log('PARAMETETS sendSmsPostman', arguments)
    try {
      // Fetch whatsapp flag from DB
      const connection = await mySQLWrapper.getConnectionFromPool()
      const sql = 'SELECT code_val FROM system_codes WHERE code_name = \'send_sms_from_retail\''
      const queryResponse = await this.rawQuery(sql, connection)
      console.log('queryResponse[send_sms_from_retail]>> ', queryResponse)
      connection.release()
      // console.log('---------------CODE VAL-------------------')
      // console.log(queryResponse)
      let code_val = 0
      if (queryResponse.length > 0) {
        code_val = queryResponse[0].code_val
      }

      if (code_val == 1) {
        const { url, aid, pin, signature } = util.retail_sms
        const postData = `aid=${aid}&pin=${pin}&mnumber=91${phoneNumber}&signature=${signature}&message=${encodeURI(message)}`

        try {
          console.time('TIMER_RETAIL_SMS')
          const response = await axios({
            method: 'get',
            url: url + '?' + postData,
            data: null,
            timeout: 20000
          })
          console.timeEnd('TIMER_RETAIL_SMS')

          console.log('----DIRECT SMS RESPONSE----', response.data)
          return { status: 200, message: 'Success : ' + response.data, data: 'Success : ' + response.data }
        } catch (error) {
          console.log('DIRECT SMS RESPONSE CATCH ERROR', error)
          return { status: 400, message: 'Failure : ' + error.message }
        }
      } else {
        const responseUrl = util[process.env.NODE_ENV].sendSmsStatusUrl
        let postData = ''
        postData = `phone_number=${phoneNumber}&message=${encodeURI(message)}&response_url=${responseUrl}&unique_id=${uniqueId}&template_id=${template_id}`
        if (smstype != null && smstype == 'otp') {
          postData += '&smstype=otp'
        }

        if (provider_id != null) {
          postData += `&provider=${provider_id}`
        }
        console.log('sms req', postData)
        const response = await axios({
          method: 'post',
          url: util.smsPostmanUrl,
          data: postData,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            token: util.smsPostmanToken,
            requrl: 'retail'
          },
          timeout: 20000
        })
        console.log('----POSTMAN SMS RESPONSE----', response.data)
        console.log(response.data)
        return response
      }
    } catch (error) {
      console.log('sendSmsPostmanError >>', error)
      return { status: 400, message: error.message }
    }
  }

  static async sendWhatsappPostman (phoneNumber, message, uniqueId) {
    try {
      const responseUrl = util[process.env.NODE_ENV].sendSmsStatusUrl
      const postData = `phone_number=${phoneNumber}&message=${encodeURI(message)}&response_url=${responseUrl}&unique_id=${uniqueId}`
      const response = await axios({
        method: 'post',
        url: util.whatsappPostmanUrl,
        data: postData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          token: util.smsPostmanToken,
          requrl: 'retail'
        }
      })
      console.log('----POSTMAN WHATSAPP RESPONSE----', response.data)
      // console.log(response.data)
      return response
    } catch (error) {
      return { status: 400, message: error.message }
    }
  }

  static async generateOrderNo () {
    const random = await this.generateRandom(4)
    const timestamp = await this.getTimestamp('')
    const orderId = `MAMSG${random}${timestamp}`
    return orderId
  }

  static async generateRandom (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  static async getTimestamp () {
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    const timeStamp = Math.floor(Date.now() / 1000)
    return `${timeStamp}`
  }

  static async saveSentMessageStatus (req) {
    log.logger({ pagename: 'sms.js', action: 'saveSentMessageStatus', type: 'request', fields: req })
    try {
      const connection1 = await mySQLWrapper.getConnectionFromPool()
      const sql1 = `INSERT INTO ma_sent_message_status (order_id,message,mobile_no,message_type,message_status,template_id,otp_type,txn_order_id,provider_id) VALUES ('${req.order_id}','${req.message_val.replace(/\'/g, "\\'").replace(/\"/g, '\\"')}',${req.mobile_no},'${req.message_type}','${req.status}','${req.template_id}','${req.sms_type}','${req.txn_order_id}','${req.provider_id}')`
      log.logger({ pagename: require('path').basename(__filename), action: 'saveSentMessageStatus', type: 'insert query', fields: sql1 })
      const result = await this.rawQuery(sql1, connection1)
      log.logger({ pagename: require('path').basename(__filename), action: 'saveSentMessageStatus', type: 'result', fields: result })
      connection1.release()

      log.logger({ pagename: 'sms.js', action: 'saveSentMessageStatus', type: 'response', fields: result })
      if (result.insertId) {
        return { status: 200, message: 'success' }
      } else {
        return { status: 400, message: 'no record inserted' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'saveSentMessageStatus', type: 'catcherror', fields: err })
      return { status: 400, message: 'Something went wrong', respcode: 1001 }
    }
  }

  /*
    Fetch data for resent otp
  */
  static async fetchMaSmsMasterData (connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      // fetch fetchMaSmsMasterData table data
      const sql = 'SELECT provider_id FROM ma_sms_provider_master WHERE status = "Current" LIMIT 1'
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchMaSmsMasterData', type: 'sql', fields: sql })
      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchMaSmsMasterData', type: 'queryResult', fields: queryResult })

      if (queryResult.length <= 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002, data: null }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: queryResult[0] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchMaSmsMasterData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }
}

module.exports = Sms
