const util = require('./util')
const crypto = require('crypto')

const airpaychecksum = (data) => {
  // console.log("data is"+data)
  return crypto.createHash('sha256')
    .update(data)
    .digest('hex')
}

const createMSCheckSum = (data, key) => {
  // console.log("data is"+data)
  let rawStr = ''
  if (Object.keys(data).length > 0) {
    const valueArr = Object.values(data)
    valueArr.push(key)

    rawStr = valueArr.join('')
  }
  console.log('createMSCheckSumRowStr >> ', rawStr, ',', key)
  rawStr = airpaychecksum(rawStr)
  console.log('createMSCheckSumRowCheckSum >> ', rawStr)
  return rawStr
}

module.exports = {
  airpaychecksum,
  createMSCheckSum
}
