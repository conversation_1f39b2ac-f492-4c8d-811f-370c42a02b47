const errorMsg = require('./error')
const log = require('./log')
const common_fns = require('./common_fns')
const path = require('path')

var PhoneNumber = require('awesome-phonenumber')
const validateMobile = (mobile, countrycode) => {
  var pn = new PhoneNumber(mobile, countrycode)
  if (pn.isValid() === true && pn.isMobile() === true) {
    return { status: true, message: 'success', number: pn.getNumber('significant'), countrycode: pn.getCountryCode() }
  } else {
    return { status: false, message: 'Enter valid mobile number' }
  }
  // var phoneno = /^\d{10}$/
  // if (mobile.match(phoneno)) {
  //   if (mobile.charAt(0) == 7 || mobile.charAt(0) == 8 || mobile.charAt(0) == 9) {
  //     if (/^(.)\1+$/.test(mobile) == false) {
  //       return { status: true, message: 'success' }
  //     } else {
  //       return { status: false, message: 'All numbers cannot be repetitive' }
  //     }
  //   } else {
  //     return { status: false, message: 'number starts with 7,8,9' }
  //   }
  // } else {
  //   return { status: false, message: 'Enter 10 digit number' }
  // }
}

const validateAccountNo = (accountNo) => {
  var inValidIp = (typeof (accountNo) === 'undefined' || accountNo === null || accountNo === '')
  var accountNoRule = /^\d{9,18}$/
  return !inValidIp && accountNoRule.test(parseInt(accountNo))
}

const validateAccNoAlphaNumeric = (accountNo) => {
  var inValidIp = (typeof (accountNo) === 'undefined' || accountNo === null || accountNo === '')
  var accountNoRule = /^[a-zA-Z0-9]{9,18}$/
  return !inValidIp && accountNoRule.test(accountNo)
}

const validInput = (type, validData, extraData) => {
  // console.log('validInput', type, validData, extraData)
  var inValidIp = (typeof (validData) === 'undefined' || validData === null || validData === '')
  switch (type) {
    case 'required':
      return !inValidIp
    case 'numberonly':
      var numberonly = /^\d+$/
      return !inValidIp && numberonly.test(validData)
    case 'stringlength':
      var isEmpty = inValidIp
      if (!isEmpty) {
        return validData.length === extraData
      } else {
        return false
      }
    case 'indianmobile':
      var indianmobile = /^\d{10}$/
      return !inValidIp && indianmobile.test(validData)
    case 'minlength':
      var minlengthStr = !inValidIp ? validData.toString() : ''
      return minlengthStr.length >= extraData
    case 'maxlength':
      var maxlengthStr = !inValidIp ? validData.toString() : ''
      return maxlengthStr.length <= extraData
    case 'alphasinglespace':
      var alphasinglespace = /^[a-z]+([\s]{1}[a-z]+)*$/i
      return !inValidIp && alphasinglespace.test(validData)
    case 'alphanumeric':
      var alphanumericregex = /^[a-z0-9]+$/i
      return !inValidIp && alphanumericregex.test(validData)
    case 'alphanumericspacecomma':
      var alphanumericspacecomma = /^[a-z\d,\s]+$/i
      return !inValidIp && alphanumericspacecomma.test(validData)
    case 'alphanumericspace':
      var alphanumericspace = /^[a-z\d\s]+$/i
      return !inValidIp && alphanumericspace.test(validData)
    case 'alphanumericsinglespace':
      var alphanumericsinglespace = /^[a-z0-9]+([\s]{1}[a-z0-9]+)*$/i
      return !inValidIp && alphanumericsinglespace.test(validData)
    case 'alphanumericsinglespacedashunderscore':
      var alphanumericsinglespacedashunderscore = /^[a-z0-9]+([-_\s]{1}[a-z0-9]+)*$/i
      return !inValidIp && alphanumericsinglespacedashunderscore.test(validData)
    case 'email':
      var emailregex = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
      return !inValidIp && emailregex.test(validData)
    case 'repeatnobytwice':
      var repeatnobytwice = /(\d)\1{2}/
      return !inValidIp && repeatnobytwice.test(validData)
    case 'indianpincode':
      var indianpincode = /^[1-9][0-9]{5}$/
      return !inValidIp && indianpincode.test(validData)
    case 'name':
      var name = /^[a-zA-Z ]{2,50}$/
      return !inValidIp && name.test(validData)
    case 'passwordmin':
      var passwordmin = /[a-zA-Z0-9]{7,}$/
      return !inValidIp && passwordmin.test(validData)
    case 'passwordmax':
      var passwordmax = /[a-zA-Z0-9]{7,32}$/
      return !inValidIp && passwordmax.test(validData)
    case 'nospecialchar':
      var nospecialchar = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{7,32}$/
      return !inValidIp && nospecialchar.test(validData)
    case 'mobilecheck':
      var mobilecheck = /^[6-9][0-9]{9}$/
      return !inValidIp && mobilecheck.test(validData)
    case 'paytmname':
      var paytmname = /^[a-zA-Z. ']{1,100}$/
      return !inValidIp && paytmname.test(validData)
    case 'paytmaddress':
      var paytmaddress = /^[a-zA-Z0-9_ ,/.=-]{2,500}$/
      return !inValidIp && paytmaddress.test(validData)
    case 'indiabankifsc':
      var indiabankifsc = /^[A-Za-z]{4}0[A-Z0-9a-z]{6}$/ // https://en.wikipedia.org/wiki/Indian_Financial_System_Code
      return !inValidIp && indiabankifsc.test(validData) // https://stackoverflow.com/questions/********/regular-expression-for-ifsc-codefirst-four-alphabet-and-then-7-digit
    default:
      return true
  }
}

const validatePAN = (pan) => {
  var inValidIp = (typeof (pan) === 'undefined' || pan === null || pan === '')
  var panRule = /^([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}?$/
  return !inValidIp && panRule.test(pan)
}

const validateGST = (gst) => {
  if (!gst) {
    return false
  }

  var gstRule = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
  return gstRule.test(gst)
}

const definedVal = (value) => {
  try {
    var isValidIp = !(typeof (value) === 'undefined' || value === null || value === '' || value === 'undefined')
    return isValidIp
  } catch (error) {
    console.log('definedValError', error)
    return false
  }
}

const validatedate = (value) => {
  var dateformat = /^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/
  // Match the date format through regular expression
  if (value.match(dateformat)) {
    // Test which seperator is used '/' or '-'
    var opera1 = value.split('/')
    var opera2 = value.split('-')
    var lopera1 = opera1.length
    var lopera2 = opera2.length
    // Extract the string into month, date and year
    var pdate = ''
    if (lopera1 > 1) {
      pdate = value.split('/')
    } else if (lopera2 > 1) {
      pdate = value.split('-')
    } else {
      return false
    }
    var dd = parseInt(pdate[0])
    var mm = parseInt(pdate[1])
    var yy = parseInt(pdate[2])
    // Create list of days of a month [assume there is no leap year by default]
    var ListofDays = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    if (mm == 1 || mm > 2) {
      if (dd > ListofDays[mm - 1]) {
        console.log('Invalid date format!')
        // alert('Invalid date format!')
        return false
      }
    }
    if (mm == 2) {
      var lyear = false
      if ((!(yy % 4) && yy % 100) || !(yy % 400)) {
        lyear = true
      }
      if ((lyear == false) && (dd >= 29)) {
        // alert('Invalid date format!')
        console.log('Invalid date format!')
        return false
      }
      if ((lyear == true) && (dd > 29)) {
        // alert('Invalid date format!')
        console.log('Invalid date format!')
        return false
      }
    }
    return true
  } else {
    // alert('Invalid date format!')
    console.log('Invalid date format!')
    return false
  }
}

const validJson = (value) => {
  try {
    JSON.parse(value)
  } catch (e) {
    return false
  }
  return true
}

/**
 * Validates if the required element/property exists in fields json
 * @param {{}} fields
 * @param {String[]} mustHaveParams
 */
const validateFields = (fields, mustHaveParams) => {
  log.debug({ pagename: path.basename(__filename), action: 'validateFields', type: 'request', fields: { fields: common_fns.maskValue(fields, ['pan', 'aadhar_number'], false, true), mustHaveParams } })
  for (const mustHaveParam of mustHaveParams) {
    if (!validateField(fields[mustHaveParam]) || ((mustHaveParam == 'ma_user_id' || mustHaveParam == 'userid') && fields[mustHaveParam] <= 0)) {
      log.debug({ pagename: path.basename(__filename), action: 'validateFields', type: 'missing params', fields: { mustHaveParam } })
      return { status: 400, message: `${errorMsg.responseCode[1019]}, ${mustHaveParam} is missing`, respcode: 1019 }
    }

    switch (mustHaveParam) {
      case 'ma_user_id':
      case 'userid':
        if (fields[mustHaveParam] <= 0) return { status: 400, message: `${errorMsg.responseCode[1019]}, ${mustHaveParam} is missing`, respcode: 1019 }
        break
      case 'amount':
        if (isNaN(fields[mustHaveParam]) || fields[mustHaveParam] <= 0) return { status: 400, respcode: 1136, message: errorMsg.responseCode[1136] }
        break
    }
  }
  return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
}

/**
 * Validates if field exists
 * @param {{}} fields
 * @returns {boolean}
 */
const validateField = (field) => {
  try {
    if (typeof (field) === 'string') return field.trim() !== null && field.trim() !== '' && field.trim() !== 'undefined' && field.trim() !== 'null'
    return typeof (field) !== 'undefined' && field !== null && field !== '' && field !== 'undefined' && field !== 'null'
  } catch (err) {
    log.logger({ pagename: path.basename(__filename), action: 'validateField', type: 'catch error', fields: err })
    return false
  }
}

/**
 * Validates whether the field is a valid enum
 * <AUTHOR> Andrew
 * @param {string} field
 * @param {string[]} enums
 * @param {boolean} returnStatement
 * @returns {boolean}
 */
const validateEnum = (field, enums = [], returnStatement) => {
  if (returnStatement) {
    if (validateEnum(field, enums)) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    return { status: 400, respcode: 14002, message: errorMsg.responseCode[14002] }
  }
  return typeof field == 'string' && enums.indexOf(field) >= 0
}

module.exports = { validateMobile, validateAccountNo, validInput, validatePAN, validateGST, definedVal, validateAccNoAlphaNumeric, validatedate, validJson, validateFields, validateField, validateEnum }
