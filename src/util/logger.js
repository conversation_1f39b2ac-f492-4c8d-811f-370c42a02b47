const fs = require('fs')
const { createLogger, format, transports } = require('winston')
const { combine, timestamp, label, printf } = format

const writelog = (logData) => {
  const myFormat = printf(({ level, message, timestamp }) => {
    return `[${timestamp}] [${level}] [IP:${requestData.clientIp}] [Req-Query:${requestData.query}] [Message: ${message}]`
  })

  // logData.mer_id = (logData.mer_id !=='undefined' && logData.mer_id.trim() !='')?logData.mer_id:0
  const moduleDir = (requestData.module !== 'undefined' && requestData.module.trim() != '') ? requestData.module : 'unknown'
  const logDir = 'logs/' + moduleDir // directory path you want to set
  if (!fs.existsSync(logDir)) {
    // Create the directory if it does not exist
    fs.mkdirSync(logDir)
  }

  const date_ob = new Date()
  const date = date_ob.getFullYear() + ('0' + (date_ob.getMonth() + 1)).slice(-2) + ('0' + date_ob.getDate()).slice(-2)
  const logfile = '/' + date + '.log'
  console.log(logDir + logfile)

  const options = {
    file: {
      level: 'info',
      filename: logDir + logfile,
      handleExceptions: true,
      json: true,
      maxsize: 5242880, // 5MB
      maxFiles: 10,
      colorize: false
    },
    console: {
      level: 'debug',
      handleExceptions: true,
      json: true,
      colorize: false
    }
  }

  // instantiate a new Winston Logger with the settings defined above
  const logger = createLogger({
    format: combine(
      // label({ label: requestData.module }),
      timestamp(),
      myFormat
    ),
    transports: [
      new transports.File(options.file)
      // new winston.transports.Console(options.console)
    ],
    exitOnError: false // do not exit on handled exceptions
  })

  logger.log(logData)
}

module.exports = {
  writelog
}

/* call in controllers
logger.writelog({mer_id:ma_user_id,level: 'error',message: 'log the data'});
*/
