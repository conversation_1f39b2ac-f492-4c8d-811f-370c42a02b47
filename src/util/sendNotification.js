const gcm = require('node-gcm')
const util = require('./util')
const log = require('./log')

const sendNotifications = (notificationData) => {
  try {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendNotification', type: 'notificationData', fields: notificationData })
    const apiKey = util.gcmApiKey
    // Set up the sender with your GCM/FCM API key (declare this once for multiple messages)
    var sender = new gcm.Sender(apiKey)

    // Prepare a message to be sent
    const message = new gcm.Message({
      data: notificationData.message
    })

    log.logger({ pagename: require('path').basename(__filename), action: 'sendNotification', type: 'gcmKey', fields: message })
    // Specify which registration IDs to deliver the message to
    var regTokens = [notificationData.recipient]

    // Actually send the message
    sender.send(message, { registrationTokens: regTokens }, function (err, response) {
      if (err) console.error(err)
      else console.log(response)
    })
  } catch (error) {
    console.log(error)
  }
}

module.exports = {
  sendNotifications
}
