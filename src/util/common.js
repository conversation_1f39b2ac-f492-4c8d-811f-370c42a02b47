const path = require('path')
const logger = require('../util/logger')
const notify = require('../util/sendNotification')
const util = require('../util/util')
const crypto = require('crypto')
const notice = require('../controller/notice/noticeController')
// const { algorithm } = require('../util/util')
const axios = require('axios')
// const qs = require('qs')
const externalapi = require('./externalapi')
const error = require('../util/error')
const { rawQuery } = require('../controller/notice/noticeController')
const mySQLWrapper = require('../lib/mysqlWrapper')
const logs = require('../util/log')
const RepositoryError = require('./errors/RepositoryError')
const ValidationError = require('./errors/ValidationError')
const HelperError = require('./errors/HelperError')

function create_UUID () {
  var dt = new Date().getTime()
  var uuid = 'xxxxxx4xxxxyxxxy'.replace(/[xy]/g, function (c) {
    var r = (dt + Math.random() * 16) % 16 | 0
    dt = Math.floor(dt / 16)
    return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
  return uuid
}

async function encrypt (text, password) {
  const env = process.env.NODE_ENV
  console.log(env)
  console.log('node env', util[env].keyEnc)
  const iv = create_UUID()
  console.log('iv', iv)
  const cipher = crypto.createCipheriv(util.algorithm, util[env].keyEnc, iv)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  console.log('enc type of', typeof encrypted)
  return iv + encrypted
}

async function decrypt (text, password) {
  const env = process.env.NODE_ENV
  console.log(env)
  console.log('node env', util[env].keyDec)
  const iv = text.substr(0, 16)
  console.log(iv)
  const data = text.substring(16)
  const decipher = crypto.createDecipheriv(util.algorithm, util[env].keyDec, iv)
  let decrypted = decipher.update(data, 'base64')
  decrypted += decipher.final()
  return decrypted
}

/**
 *
 * @param {{text:string,encKey:string}} param0
 * @returns string
 */
function encryptExternalApiResponse ({ text, encKey }) {
  const IV = create_UUID()
  const algorithm = util[process.env.NODE_ENV || 'development'].externalApi.encryptionAlgorithm
  const cipher = crypto.createCipheriv(algorithm, encKey, IV)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  return IV + encrypted
}

function encryptMerchantLocatorApiResponse ({ text, encKey }) {
  const IV = create_UUID()
  const algorithm = 'aes-256-cbc'
  const cipher = crypto.createCipheriv(algorithm, encKey, IV)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  return IV + encrypted
}

/**
 *
 * @param {{encryptedTxt:string, decKey:string}} param0
 * @returns string
 */
function decryptMerchantLocatorApiRequest ({ encryptedTxt, decKey }) {
  console.log('decryptExternalApiRequest => encryptedTxt, decKey =====', { encryptedTxt, decKey })
  const algorithm = 'aes-256-cbc'
  /* GET REQUEST SPACE ISSUE FIX : REPLACE WITH '+' */
  if (encryptedTxt.match(/\s/)) {
    encryptedTxt = encryptedTxt.replace(/\s/g, '+')
  }
  const IV = encryptedTxt.substr(0, 16)
  const encrypted = encryptedTxt.substr(16)
  // console.log({ IV, encrypted, encryptedTxt })
  const decipher = crypto.createDecipheriv(algorithm, decKey, IV)
  let decrypted = decipher.update(encrypted, 'base64')
  decrypted += decipher.final()
  return decrypted
}

/**
 *
 * @param {{encryptedTxt:string, decKey:string}} param0
 * @returns string
 */
function decryptExternalApiRequest ({ encryptedTxt, decKey }) {
  console.log('decryptExternalApiRequest => encryptedTxt, decKey =====', { encryptedTxt, decKey })
  const algorithm = util[process.env.NODE_ENV || 'development'].externalApi.encryptionAlgorithm
  /* GET REQUEST SPACE ISSUE FIX : REPLACE WITH '+' */
  if (encryptedTxt.match(/\s/)) {
    encryptedTxt = encryptedTxt.replace(/\s/g, '+')
  }
  const IV = encryptedTxt.substr(0, 16)
  const encrypted = encryptedTxt.substr(16)
  // console.log({ IV, encrypted, encryptedTxt })
  const decipher = crypto.createDecipheriv(algorithm, decKey, IV)
  let decrypted = decipher.update(encrypted, 'base64')
  decrypted += decipher.final()
  return decrypted
}

async function encryptRandomWithKey (text, existingKey = null) {
  const env = process.env.NODE_ENV
  console.log(env)
  // console.log('node env', util[env].keyEnc)
  const iv = create_UUID()
  console.log('iv', iv)

  let userKey = ''
  let fullKey = ''
  if (existingKey === null) {
    userKey = crypto.randomBytes(8).toString('hex')
    fullKey = userKey + util[env].partial
  } else {
    const keyiv = existingKey.substr(0, 16)
    console.log('keyiv', keyiv)
    const keydata = existingKey.substring(16)
    console.log('existingKey', keydata)

    const keydecipher = crypto.createDecipheriv(util.algorithm, util.pinnmk, keyiv)
    let keydecrypted = keydecipher.update(keydata, 'base64')
    keydecrypted += keydecipher.final()

    console.log('keydecrypted', keydecrypted)
    fullKey = keydecrypted + util[env].partial
  }

  const cipher = crypto.createCipheriv(util.algorithm, fullKey, iv)
  // console.log('cipher', cipher)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  console.log('enc type of', typeof encrypted)

  if (existingKey === null) {
    /** * Key encryption */
    const keyCipher = crypto.createCipheriv(util.algorithm, util.pinnmk, iv)
    // console.log('keycipher', keyCipher)
    let keyEncrypted = keyCipher.update(userKey, 'utf8', 'base64')
    keyEncrypted += keyCipher.final('base64')
    console.log('key enc type of', typeof keyEncrypted)
    return {
      encryptData: iv + encrypted,
      encryptKey: iv + keyEncrypted
    }
  }

  return {
    encryptData: iv + encrypted
  }
}

async function decryptRandomWithKey (text, dynamicKey) {
  const env = process.env.NODE_ENV
  console.log(env)

  const keyiv = dynamicKey.substr(0, 16)
  console.log('keyiv', keyiv)
  const keydata = dynamicKey.substring(16)
  console.log('dynamicKey', keydata)

  const keydecipher = crypto.createDecipheriv(util.algorithm, util.pinnmk, keyiv)
  let keydecrypted = keydecipher.update(keydata, 'base64')
  keydecrypted += keydecipher.final()

  console.log('keydecrypted', keydecrypted)

  const iv = text.substr(0, 16)
  console.log(iv)
  const data = text.substring(16)
  console.log('dynamicKey', keydecrypted)

  const decipher = crypto.createDecipheriv(util.algorithm, keydecrypted + util[env].partial, iv)
  let decrypted = decipher.update(data, 'base64')
  decrypted += decipher.final()
  return decrypted
}

async function tokenValidation (token, userid) {
  const login = require('../controller/login/loginController')
  console.log('tokenValidation >>', token)
  // console.trace()
  // console.log(login)
  return await login.tokenValidation(token, userid)
}

async function logout (token, userid) {
  const login = require('../controller/login/loginController')
  return await login.logout(token, userid)
}

async function downTime () {
  const noticeRes = await notice.getNotices('', { displaytype: '3' })
  console.log(noticeRes)
  const downtimeresp = []
  if (noticeRes.status == 200) {
    const noticeObj = {
      notice_flag: noticeRes.notice_flag || 'N',
      message: noticeRes.messageText
    }
    downtimeresp.push(noticeObj)
  } else {
    const noticeObj = {
      notice_flag: 'N',
      message: noticeRes.message
    }
    downtimeresp.push(noticeObj)
  }

  return downtimeresp
}

async function getSystemCodes (that, codeObject, connection) {
  const sql = `SELECT code_val FROM system_codes WHERE code_id = ${codeObject.code}`
  const queryResponse = await that.rawQuery(sql, connection)
  if (queryResponse.length > 0) {
    const codeVal = queryResponse[0].code_val
    if (codeVal === '' || codeVal === null) {
      return codeObject.fallBackVal
    }
    return codeVal
  } else {
    return codeObject.fallBackVal
  }
}

const incentiveOrderid = (data) => {
  const text = 'MAINM'
  const unix_seconds = Math.floor(Date.now() / 1000)
  return text + data + unix_seconds
}

const createHash = (text, algorithm = 256) => {
  // base64:
  // return crypto.createHash('sha256').update(text).digest('base64')
  // hex:
  return crypto.createHash('sha256').update(text).digest('hex')
}

const getCurrentDate = () => {
  const d = new Date()
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()
  if (month.length < 2) {
    month = '0' + month
  }
  if (day.length < 2) {
    day = '0' + day
  }
  return [year, month, day].join('-')
}

const getCurrentDateCMS = () => {
  const d = new Date()
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()
  if (month.length < 2) {
    month = '0' + month
  }
  if (day.length < 2) {
    day = '0' + day
  }
  return [day, month, year].join('-')
}

const getCurrentDate1 = () => {
  const d = new Date()
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()
  let hours = '' + d.getHours()
  let mins = '' + d.getMinutes()
  let sec = '' + d.getSeconds()
  if (month.length < 2) {
    month = '0' + month
  }
  if (day.length < 2) {
    day = '0' + day
  }
  if (hours.length < 2) {
    hours = '0' + hours
  }
  if (mins.length < 2) {
    mins = '0' + mins
  }
  if (sec.length < 2) {
    sec = '0' + sec
  }
  var time = [day, month, year].join('/')
  time += ' '
  time += [hours, mins, sec].join(':')
  return time
}

async function getSystemCodesByVal (that, codeObject, connection) {
  const sql = `SELECT * FROM system_codes WHERE code_name = '${codeObject.codeName}' AND code_val = '${codeObject.codeVal}'`
  const queryResponse = await that.rawQuery(sql, connection)
  if (queryResponse.length > 0) {
    return queryResponse[0]
  } else {
    return {}
  }
}

/**
 * This function is used to send notification based on GCM key
 * <AUTHOR> N.
 */
async function sendNotification ({ txnId, orderId, amount, txnStatus, txnTime, userId, instance, connection }) {
  console.log('---SEND NOTIFICATION REQUEST---')
  console.log({ txnId, orderId, amount, txnStatus, txnTime, userId })
  try {
    let message = ''
    const NOTIFICATION_API_ACCESS_KEY = 'AAAAXcea8tY:APA91bHN8pi3HoTuVwYDORguUN-5ScYUAjaIgYM7_JmdGSQBkqd11blu0Bs9NApTrWJCHNNSHA0xkGnVgQuYWQh_5XUhBnuGnK4srHpUgLUIGxainqhSw23I6NqV8ENY8jHqhRNBWHEg'

    // Get GCM key
    const gcmKey = await getGcmKey(userId, instance, connection)
    // const gcmKey = 'dwAFL4gvT9CyXprrgHlw88:APA91bFJFesMXGTXvUVfWyJb1o5KMUT4gdQqV0dTHKkctDnYaT01jzS-r0vHI9uLvfoAjog0PdagEJyodHpw0VVdnVk-OxJNpdlKxNBSX4fMb7jPsRBUKgPBkUhGqFOQV8qQ29urDnHY'
    console.log('---GCM KEY LENGTH---' + gcmKey.length)
    if (typeof gcmKey == 'undefined' || gcmKey.length == 0) {
      return { status: 400, message: 'GCM Key not found' }
    }

    // Get notification message
    if (txnStatus == 'S') {
      message = `Hi, Your transaction of Rs. ${amount} done at ${txnTime} against order # ${orderId} is success. Transaction reference number is ${txnId}`
    } else {
      message = `Hi, Your transaction of Rs. ${amount} done at ${txnTime} against order # ${orderId} is failed. Transaction reference number is ${txnId}`
    }
    console.log('---SEND NOTIFICATION MESSAGE---', message)

    // Send notification
    const messageParam = {
      notification_id: txnId,
      body: message,
      title: 'Airpay Transaction',
      icon: 'ic_launcher',
      sound: 'default',
      priority: 'high',
      badge: '1',
      image: '',
      action: '1',
      'action-url': ''
    }
    const headers = { Authorization: 'key=' + NOTIFICATION_API_ACCESS_KEY, 'Content-Type': 'application/json' }
    const postData = {
      registration_ids: [gcmKey],
      data: messageParam
    }

    console.log('---SEND NOTIFICATION DATA---', postData)
    const notificationResp = await axios({
      method: 'post',
      url: 'https://fcm.googleapis.com/fcm/send',
      data: postData,
      headers: headers
    })
    console.log('---SEND NOTIFICATION RESPONSE---')
    console.log(notificationResp.data)
  } catch (error) {
    console.log('---SEND NOTIFICATION ERROR---')
    console.log(error)
    return { status: 400, message: error.message }
  }
}

async function getGcmKey (userId, instance, connection) {
  try {
    const sql = `SELECT gcmid FROM ma_login_details WHERE userid = '${userId}' ORDER BY apploginid DESC LIMIT 1`
    const queryResponse = await instance.rawQuery(sql, connection)
    if (queryResponse.length > 0) {
      return queryResponse[0].gcmid
    } else {
      return ''
    }
  } catch (error) {
    console.log(error)
    return ''
  }
}

async function encryptEmitra (plainText, secret) {
  var key = crypto.createHash('sha256').update(secret, 'utf8').digest().slice(0, 16)
  var iv = key.slice(0, 16)
  const cipher = crypto.createCipheriv('aes-128-cbc', key, iv)
  let encrypted = cipher.update(plainText, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  return encrypted
}

async function decryptEmitra (messagebase64, secret) {
  var key = crypto.createHash('sha256').update(secret, 'utf8').digest().slice(0, 16)
  var iv = key.slice(0, 16)
  const decipher = crypto.createDecipheriv('aes-128-cbc', key, iv)
  let decrypted = decipher.update(messagebase64, 'base64')
  decrypted += decipher.final()
  return decrypted
}

async function encryptDynamic (text, key) {
  const iv = create_UUID()
  console.log('iv', iv)
  const cipher = crypto.createCipheriv(util.algorithm, key.keyEnc, iv)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  console.log('enc type of', typeof encrypted)
  return iv + encrypted
}

async function decryptDynamic (text, key) {
  const iv = text.substr(0, 16)
  console.log(iv)
  const data = text.substring(16)
  const decipher = crypto.createDecipheriv(util.algorithm, key.keyDec, iv)
  let decrypted = decipher.update(data, 'base64')
  decrypted += decipher.final()
  return decrypted
}

async function sendDynamicNotification ({ ma_transaction_master_id, instance, connection }) {
  // console.log('---SEND NOTIFICATION REQUEST---')
  // console.log({ txnId, orderId, amount, txnStatus, txnTime, userId })
  try {
    let message = ''
    let message_web = ''
    // const NOTIFICATION_API_ACCESS_KEY = 'AAAAXcea8tY:APA91bHN8pi3HoTuVwYDORguUN-5ScYUAjaIgYM7_JmdGSQBkqd11blu0Bs9NApTrWJCHNNSHA0xkGnVgQuYWQh_5XUhBnuGnK4srHpUgLUIGxainqhSw23I6NqV8ENY8jHqhRNBWHEg'

    const sqldata = `SELECT ma_user_id,userid,aggregator_txn_id,aggregator_order_id,(amount + IF(commission_amount IS NULL, 0.0,commission_amount)) as amount,transaction_status,transaction_type,DATE_FORMAT(addedon,'%a %b %d %Y %H:%i:%s') as addedon FROM ma_transaction_master WHERE ma_transaction_master_id = '${ma_transaction_master_id}'`
    const queryResponse = await instance.rawQuery(sqldata, connection)
    if (queryResponse.length == 0) {
      return ''
    }
    if (queryResponse[0].transaction_status == 'P') {
      console.log('pending status')
      return ''
    }
    // Get GCM key
    const gcmKey = await getGcmKey(queryResponse[0].userid, instance, connection)
    // const gcmKey = 'dwAFL4gvT9CyXprrgHlw88:APA91bFJFesMXGTXvUVfWyJb1o5KMUT4gdQqV0dTHKkctDnYaT01jzS-r0vHI9uLvfoAjog0PdagEJyodHpw0VVdnVk-OxJNpdlKxNBSX4fMb7jPsRBUKgPBkUhGqFOQV8qQ29urDnHY'
    console.log('---GCM KEY LENGTH---' + gcmKey.length)
    if (typeof gcmKey == 'undefined' || gcmKey.length == 0) {
      return { status: 400, message: 'GCM Key not found' }
    }

    // Get notification message
    if (queryResponse[0].transaction_status == 'S') {
      message = `Hi, ${util.transaction_comm_mapping[queryResponse[0].transaction_type]} of Rs. ${queryResponse[0].amount} done at ${queryResponse[0].addedon} against order # ${queryResponse[0].aggregator_order_id} is Successful.`
      message_web = `Hi, ${util.transaction_comm_mapping[queryResponse[0].transaction_type]} of Rs. ${queryResponse[0].amount} done at ${queryResponse[0].addedon} against order # ${queryResponse[0].aggregator_order_id} is <span class="notisuc">Successful</span>.`
    } else if (queryResponse[0].transaction_status == 'F') {
      message = `Hi, ${util.transaction_comm_mapping[queryResponse[0].transaction_type]} of Rs. ${queryResponse[0].amount} done at ${queryResponse[0].addedon} against order # ${queryResponse[0].aggregator_order_id} has failed.`
      message_web = `Hi, ${util.transaction_comm_mapping[queryResponse[0].transaction_type]} of Rs. ${queryResponse[0].amount} done at ${queryResponse[0].addedon} against order # ${queryResponse[0].aggregator_order_id} has <span class="notifail">failed</span>.`
    } else if (queryResponse[0].transaction_status == 'PS') {
      const partialsql = `SELECT  SUM(IF(transfer_status='S',transfer_amount,0)) as success_transfer_amount FROM ma_transfers WHERE ma_transaction_master_id = '${ma_transaction_master_id}'`
      const partialResponse = await instance.rawQuery(partialsql, connection)
      message = `Hi, ${util.transaction_comm_mapping[queryResponse[0].transaction_type]} of Rs. ${queryResponse[0].amount} done at ${queryResponse[0].addedon} against order # ${queryResponse[0].aggregator_order_id} is partially Successful for Rs. ${partialResponse[0].success_transfer_amount}.`
      message_web = `Hi, ${util.transaction_comm_mapping[queryResponse[0].transaction_type]} of Rs. ${queryResponse[0].amount} done at ${queryResponse[0].addedon} against order # ${queryResponse[0].aggregator_order_id} is <span class="notipartial">partially Successful</span> for Rs. ${partialResponse[0].success_transfer_amount}.`
    }
    /* let message = {
      ma_transaction_master_id: ma_transaction_master_id,
      ma_user_id: queryResponse[0].ma_user_id,
      userid:queryResponse[0].userid,
      aggregator_order_id:queryResponse[0].aggregator_order_id,
      amount:queryResponse[0].amount,
      datetime:queryResponse[0].addedon,
      transaction_status:queryResponse[0].transaction_status,
      transaction_type:queryResponse[0].transaction_type
    } */
    console.log('---SEND NOTIFICATION MESSAGE---', message)

    // Send notification
    const messageParam = {
      notification_id: ma_transaction_master_id,
      body: message,
      title: 'Airpay Transaction',
      icon: 'ic_launcher',
      sound: 'default',
      priority: 'high',
      badge: '1',
      image: '',
      action: '1',
      'action-url': '',
      ma_transaction_master_id: ma_transaction_master_id,
      transaction_type: util.transaction_type_mapping[queryResponse[0].transaction_type],
      transaction_status: util.transaction_status_mapping[queryResponse[0].transaction_status],
      ma_user_id: queryResponse[0].ma_user_id,
      body_web: message_web,
      aggregator_order_id: queryResponse[0].aggregator_order_id
    }
    const headers = { Authorization: 'key=' + util.NOTIFICATION_API_ACCESS_KEY, 'Content-Type': 'application/json' }
    const postData = {
      registration_ids: [gcmKey],
      data: messageParam
    }

    console.log('---SEND NOTIFICATION DATA---', postData)
    const notificationResp = await axios({
      method: 'post',
      url: 'https://fcm.googleapis.com/fcm/send',
      data: postData,
      headers: headers
    })
    console.log('---SEND NOTIFICATION RESPONSE---')
    console.log(notificationResp.data)
  } catch (error) {
    console.log('---SEND NOTIFICATION ERROR---')
    console.log(error)
    return { status: 400, message: error.message }
  }
}
/**
 * Returns GraphQl Query
 * @param {JSON|Object} event Restful format
 * @param {Number} event.merchant_id
 * @param {Number} event.client_id
 * @param {String} event.purpose
 * @param {String} event.core_function
 */
async function graphqlQueryBuilder (event) {
  console.time('graphqlQueryBuilder')

  const connection = await mySQLWrapper.getConnectionFromPool()
  try {
  // set basic required params
  let params = `(ma_user_id:${event.ma_user_id}, userid: ${event.user_id}`

  // replace param name if required and add to params for query
  console.log('graphqlQueryBuilder++++++++++++++++ ', event, event.data)
  Object.keys(event.data).map(e => {
    if (e !== 'functionname' && e !== 'format') {
      const newVarname = e.startsWith('ext_') ? e.substring(4) : e
      params += ', ' + (typeof event.data[e] == 'string' ? ` ${newVarname}: "${event.data[e]}"` : (typeof event.data[e] == 'object' && typeof event.data[e].enum != 'undefined' ? ` ${newVarname}: ${event.data[e].enum}` : ` ${newVarname}: ${event.data[e]}`))
    }
  })

  params += ')'

  // getting required api for query building
  const requiredApi = { ...externalapi.lambdaTypes[event.core_function] }
  if (typeof requiredApi.path == 'undefined') {
    return { ext_status: 404, ext_message: error.responseCode[1194], ext_respcode: 1194 }
  }

  const apiUserSql = 'SELECT api_allowed FROM ma_api_user_master where purpose = "' + event.purpose + '" limit 1'
    const apiUser = await rawQuery(apiUserSql, connection)

  console.log('+++++++++ api user purpose', apiUser)
  if (apiUser.length == 0) {
    console.log('+++++++++ invalid purpose', event.purpose)
    return { ext_status: 402, ext_message: error.responseCode[1195], ext_respcode: 1195 }
  } else if (apiUser.length == 0 || typeof apiUser[0].api_allowed == 'undefined' || (JSON.parse(apiUser[0].api_allowed)[event.core_function] || []).filter(e => e == event.data.functionname).length == 0) {
    console.log('+++++++++ invalid purpose', apiUser)
    return { ext_status: 401, ext_message: error.responseCode[1196], ext_respcode: 1196 }
  }
  const types = { ...(await (require('../../' + requiredApi.path)).getFields()) }

  // setting response params to get all available params
  const responseParams = []
  let respParams = []
  if (typeof event.responseParams == 'object' && Array.isArray(event.responseParams)) {
    for (let param of event.responseParams) {
      param = param.startsWith('ext_') ? param.substring(4) : param
      if (typeof types[param] != 'undefined' && typeof types[param].type != 'undefined') {
        const type = types[param].type.toString()
        if (typeof type && requiredApi[type.substring(1, type.length - 1)]) {
          responseParams.push(param, ...requiredApi[type.substring(1, type.length - 1)])
          respParams.push(`${param}{ ${requiredApi[type.substring(1, type.length - 1)].join(', ')} }`)
        } else {
          responseParams.push(param)
          respParams.push(param)
        }
      }
    }
  } else {
    respParams = Object.keys(types).map(e => {
      const type = types[e].type.toString()
      if (typeof type && requiredApi[type.substring(1, type.length - 1)]) {
        responseParams.push(e, ...requiredApi[type.substring(1, type.length - 1)])
        return `${e}{ ${requiredApi[type.substring(1, type.length - 1)].join(', ')} }`
      }
      responseParams.push(e)
      return e
    })
  }

  const appVersionSql = "SELECT code_val FROM system_codes WHERE code_name = 'appversion'"
    const appVersion = await rawQuery(appVersionSql, connection)

  const app_version = appVersion[0].code_val.split(':::')[0]

  // building query
  const query = `${((event.data.format == 'mutation' && 'mutation') || '')}{${event.data.functionname + params}{ ${respParams.join(', ')} }}`

  console.timeEnd('graphqlQueryBuilder')
  return { ext_status: 200, message: error.responseCode[1000], respcode: 1000, query, functionName: requiredApi.functionName, responseParams, app_version }
  } catch (err) {
    console.log('ERROR:::', err)
  } finally {
    connection.release()
  }
}

async function lambdaEncrypt (text) {
  const env = process.env.NODE_ENV
  console.log(env)
  console.log('node env', util[env].keyDec)
  const iv = create_UUID()
  console.log('iv', iv)
  const cipher = crypto.createCipheriv(util.algorithm, util[env].keyDec, iv)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  console.log('enc type of', typeof encrypted)
  return iv + encrypted
}

async function lambdaDecrypt (text) {
  const env = process.env.NODE_ENV
  console.log(env)
  console.log('node env', util[env].keyEnc)
  const iv = text.substr(0, 16)
  console.log(iv)
  const data = text.substring(16)
  const decipher = crypto.createDecipheriv(util.algorithm, util[env].keyEnc, iv)
  let decrypted = decipher.update(data, 'base64')
  decrypted += decipher.final()
  return decrypted
}

async function changeVariableName (text, params) {
  for (const param of params) {
    const reg = new RegExp(`"${param}":`, 'g')
    // console.log('renaming: ', text.match(/"[a-z_]+":/g))
    text = text.replace(reg, `"ext_${param}":`)
  }
  console.log('text ', text)
  return text
}

/**
* parseJsonParam description - Parse json
* @param {{ param: object, defaultParam: object }}
* @returns {{ Object }}
*/
function parseJsonParam (param, defaultParam) {
  try {
    logs.logger({ pagename: path.basename(__filename), action: 'parseJsonParam', type: 'request', fields: param })
    const params = JSON.parse(param)
    if (params.some(p => p.postkey == 'email')) {
      params.push({
        label: 'Email',
        value: '',
        type: 'String',
        isEditable: true,
        isVisible: true,
        validation: {
          max: 50,
          min: 10,
          regex: '^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$'
        },
        postKey: 'email',
        isRequired: true,
        subLabel: ''
      })
    }
    if (params.some(p => p.postkey == 'mobile_number')) {
      params.push({
        label: 'Mobile Number',
        value: '',
        type: 'Number',
        isEditable: true,
        isVisible: true,
        validation: {
          max: 10,
          min: 10,
          regex: '^[0-9]{1,10}$'
        },
        postKey: 'mobile_number',
        isRequired: true,
        subLabel: ''
      })
    }
    return params
  } catch (error) {
    logs.logger({ pagename: path.basename(__filename), action: 'parseJsonParam', type: 'err', fields: error })
    return defaultParam
  }
}

/**
* mergeObject description - merge object
* @param {{ param: object, defaultParam: object }}
* @returns {{ Object }}
*/
function mergeObject (defaultParam, dynamicParam) {
  return Object.assign({}, defaultParam, dynamicParam)
}

const camelToSnakeCase = str => str.replace(/[A-Z]/g, letter => `${letter.toLowerCase()}`).replace(' ', '_')

/**
 *
 * @param {string} remitter_name
 * @return {{ firstName: string,lastName: string }}
 */
function splitRemitterNameWithSpace (remitter_name) {
  if (typeof remitter_name != 'string') {
    return false
  }

  if (remitter_name == '') {
    return false
  }
  let firstName, lastName
  const splitBySpace = remitter_name.trim(' ').split(' ')
  const splitBySpacelen = splitBySpace.length
  if (splitBySpacelen == 2) {
    firstName = splitBySpace[0]
    lastName = splitBySpace[1]
  }
  if (splitBySpacelen > 2) {
    let sliceEndIndex = splitBySpacelen - 1
    if (sliceEndIndex > util.DmtRemitterMaxSpaceAllowedInFirstName) {
      sliceEndIndex = util.DmtRemitterMaxSpaceAllowedInFirstName
    }
    firstName = splitBySpace.slice(0, sliceEndIndex).join(' ')
    lastName = splitBySpace.slice(sliceEndIndex, sliceEndIndex + util.DmtRemitterMaxSpaceAllowedInLastName).join(' ')
  }
  return {
    firstName,
    lastName
  }
}

/**
 * Returns boolean values based on error type.
 * @param {Error} err
 * @returns {boolean}
 */
const isCustomError = (err) => err instanceof RepositoryError || err instanceof ValidationError || err instanceof HelperError

/**
 * Parses error object and returns basic error response
 * @param {Error} err
 * @returns { status: number, message: string, respcode: number, action_code: 1000|1001 }
 */
const getErrorResponse = (err) => ({ status: err.status, message: err.message, respcode: err.respcode, action_code: err.action_code })

/**
 * MA SOUNDBOX API Encryption
 * @param {{plainText:string,key:string}} param0
 * @returns
 */
function soundBoxEncrypt ({ plainText, key }) {
  try {
    const secretkey = crypto.createHash('md5').update(key).digest('hex')
    const iv = crypto.randomBytes(8).toString('hex')
    const cipher = crypto.createCipheriv('aes-256-cbc', secretkey, iv)
    let encrypted = cipher.update(plainText, 'utf8', 'base64')
    encrypted += cipher.final('base64')
    const data = iv + encrypted
    return data
  } catch (error) {
    console.error('Error occurred during decryption:', error)
    return null
  }
}

/**
 * MA SOUNDBOX API Decryption
 * @param {{encryptedText:string,key:string}} param0
 * @returns
 */
function soundBoxDecrypt ({ encryptedText, key }) {
  try {
    const secretkey = crypto.createHash('md5').update(key).digest('hex')
    const iv = encryptedText.substring(0, 16)
    const data = encryptedText.substring(16)
    const decipher = crypto.createDecipheriv('aes-256-cbc', secretkey, iv)
    let decrypted = decipher.update(Buffer.from(data, 'base64'), 'binary', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted || encryptedText
  } catch (error) {
    console.error('Error occurred during decryption:', error)
    return null
  }
}
/**
 *
 * @param {{paramArr:Array,key:string}} param0
 * @returns
 */
function soundBoxChecksum ({ paramArr, key }) {
  try {
    let param = ''
    if (!Array.isArray(paramArr)) return crypto.createHash('sha256').update(param + key).digest('hex')

    for (const paramValue of paramArr) {
      if (Array.isArray(paramValue)) {
        for (const finalValue of paramValue) {
          param += finalValue
        }
      } else {
        param += paramValue
      }
    }
    return crypto.createHash('sha256').update(param + key).digest('hex')
  } catch (error) {
    console.error('Error occurred during checksum generate:', error)
    return null
  }
}

function encryptIPNRequest ({ encKey, text }) {
  const IV = crypto.randomBytes(8).toString('hex').substr(0, 16)
  const algorithm = 'aes-256-cbc'
  const cipher = crypto.createCipheriv(algorithm, encKey, IV)
  let encrypted = cipher.update(text, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  return IV + encrypted
}

function queryObject (request) {
  try {
    var result = {}
    var pairs = request.split('&')
    pairs.forEach(function (pair) {
      pair = pair.split('=')
      result[pair[0]] = decodeURIComponent(pair[1] || '')
    })
    return JSON.parse(JSON.stringify(result))
  } catch (error) {
    return {}
  }
}

/**
 * Generate random number
 */
function generateOTP (length) {
  var digits = '0123456789'
  let otp = ''
  for (let i = 0; i < length; i++) {
    otp += digits[Math.floor(Math.random() * 10)]
  }
  return otp
}

/**
 * Calculate date
 */
function getExpiry (type, otp_type = null) {
  console.log(type)
  const today = new Date()
  const timezone = today.getTimezoneOffset()
  console.log('timezone', timezone)
  console.log('timezone type', typeof timezone)
  let dd = today.getDate()
  let mm = today.getMonth() + 1
  const yyyy = today.getFullYear()
  if (dd < 10) {
    dd = `0${dd}`
  }
  if (mm < 10) {
    mm = `0${mm}`
  }
  if (type === 'expiry') {
    console.log('inside type')
    if (timezone !== -330) {
      today.setTime(today.getTime() + (util.expiry[otp_type] * 60 * 1000) + (30 * 60 * 1000) + (5 * 60 * 60 * 1000)) // time converted to ist from utc for testing purpose on kubeless. On live remove the 5:30 from date calculation.
    } else {
      today.setTime(today.getTime() + (util.expiry[otp_type] * 60 * 1000))
    }
    const min = today.getMinutes()
    const sec = today.getSeconds()
    const hh = today.getHours()
    return `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`
  } else {
    const timeStamp = Math.floor(Date.now() / 1000)
    return `${timeStamp}`
  }
}

const addRupeeSymbol = (amount) => {
  return '₹'.concat(' ', amount)
}

module.exports = {
  writelog: logger.writelog,
  sendNotifications: notify.sendNotifications,
  encrypt,
  decrypt,
  tokenValidation,
  logout,
  encryptRandomWithKey,
  decryptRandomWithKey,
  downTime,
  getSystemCodes,
  incentiveOrderid,
  createHash,
  getCurrentDate,
  getCurrentDate1,
  getSystemCodesByVal,
  sendNotification,
  encryptEmitra,
  decryptEmitra,
  encryptDynamic,
  decryptDynamic,
  sendDynamicNotification,
  graphqlQueryBuilder,
  lambdaEncrypt,
  lambdaDecrypt,
  changeVariableName,
  getCurrentDateCMS,
  parseJsonParam,
  mergeObject,
  getGcmKey,
  camelToSnakeCase,
  splitRemitterNameWithSpace,
  isCustomError,
  getErrorResponse,
  encryptExternalApiResponse,
  decryptExternalApiRequest,
  encryptMerchantLocatorApiResponse,
  decryptMerchantLocatorApiRequest,
  /* SOUNDBOX CHANGES */
  soundBoxEncrypt,
  soundBoxDecrypt,
  soundBoxChecksum,
  queryObject,
  /* IPN MERCHANT CHANGES */
  encryptIPNRequest,
  generateOTP,
  getExpiry,
  addRupeeSymbol
}
