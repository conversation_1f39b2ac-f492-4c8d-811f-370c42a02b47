const jwt = require('jsonwebtoken')
const util = require('./util')
const moment = require('moment')
const momentTimezone = require('moment-timezone')

class Token {
  static async signToken (publickey) {
    return jwt.sign({ publickey }, util.jwtKey, {
      algorithm: 'HS256',
      expiresIn: util.jwtExpirySeconds
    })
  }

  static async signTokenAuth (authentication) {
    return jwt.sign({ authentication }, util.jwtKey, {
      algorithm: 'HS256',
      expiresIn: util.jwtExpirySeconds
    })
  }

  static async verifyToken (token) {
    return jwt.verify(token, util.jwtKey)
  }

  static async signJwtToken ({ publickey, profileid, login_ip, user_agent }) {
    const currentTime = moment().tz('Asia/Kolkata')
    const endDayTime = moment().tz('Asia/Kolkata').hours(23).minutes(59).seconds(50)
    const timeDiff = endDayTime.diff(currentTime, 'seconds')
    return jwt.sign({ publickey, profileid, login_ip, user_agent }, util.jwtKey, {
      algorithm: 'HS256',
      expiresIn: timeDiff
    })
  }

  /**
   * Affiliate JWT Token
   * @param {Object|String} payload
   * @param {Object|String} key
   * @returns
   */
  static async generateTokenWithDynamicKey (payload, key, expiresIn = '10m') {
    return jwt.sign(payload, key, { algorithm: 'HS256', expiresIn })
  }

  /**
   *
   * @param {string} token
   * @returns
   */
  static async verifyTokenWithDynamicKey (token, key) {
    console.log('verifyTokenWithDynamicKey = token and key =====', token, key)
    return jwt.verify(token, key)
  }
}

module.exports = Token
