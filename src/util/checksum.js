const util = require('./util')
const crypto = require('crypto')
const md5 = require('md5')
const errorMsg = require('./error')
const log = require('./log')
const moment = require('moment')
const { basename } = require('path')
const mySQLWrapper = require('../lib/mysqlWrapper')
const common_fns = require('./common_fns')

const checksum = (data) => {
  return crypto.createHmac('sha256', util.jwtKey)
    .update(data)
    .digest('hex')
}

const md5checksum = (data) => {
  return md5(data)
}

/**
 * Get Checksum for Generate Order API
 * <AUTHOR> Hassan
 * @param {*} that
 * @param {*} fields
 * @returns postData
 */
async function generateOrderchecksum (that, fields) {
  log.logger({ pagename: basename(__filename), action: 'generateOrderchecksum', type: 'request', fields: { ...fields, cardnumberORUID: common_fns.maskValue(fields.cardnumberORUID, 'adhaarNumber') } })
  const tempConnection = !(that.connection || fields.connection)
  const connection = that.connection || fields.connection || await mySQLWrapper.getConnectionFromPool()

  try {
  // Get User password
  const mercid = fields.mid ? fields.mid : fields.mercid
    log.logger({ pagename: basename(__filename), action: 'generateOrderchecksum', type: 'mid/mercid', fields: mercid })
  const userSql = `SELECT username, password FROM ma_user_master WHERE profileid = ${mercid} AND userid = ${fields.userid} LIMIT 1`
  log.debug({ pagename: basename(__filename), action: 'userSql', type: 'request-userSql', fields: userSql })
    const userResponse = await that.rawQuery(userSql, connection)
  log.debug({ pagename: basename(__filename), action: 'userResponse', type: 'request-userResponse', fields: userResponse })
  if (userResponse.length <= 0) {
      log.logger({ pagename: basename(__filename), action: 'userResponse', type: 'request', fields: userResponse })
    return { status: 400, respcode: 12361, message: errorMsg.responseCode[12361] }
  }
  const password = userResponse[0].password
  const username = userResponse[0].username
  // Input data to base64 encoding
  const encryptedData = Buffer.from(JSON.stringify(fields)).toString('base64')

  const checksumVal = mainChecksum({ username, password, ...fields })

  const postData = {
    enc: encryptedData,
    checksum: checksumVal
  }
    log.logger({ pagename: basename(__filename), action: 'generateOrderchecksum', type: 'response', fields: postData })
  return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: postData }
  } catch (e) {
    log.logger({ pagename: basename(__filename), action: 'generateOrderchecksum', type: 'catch error', fields: e })
    throw (e)
  } finally {
    if (tempConnection) connection.release()
  }
}


/**
 * Get Checksum for Pay index API
 * <AUTHOR> Hassan
 * @param {*} that
 * @param {*} fields
 * @returns postData
 */
const payIndexchecksum = (fields) => {
  log.logger({ pagename: basename(__filename), action: 'payIndexchecksum', type: 'request', fields: fields })

  const password = fields.merchantDet.password
  const username = fields.merchantDet.username
  delete fields.merchantDet

  const checksumVal = mainChecksum({ username, password, ...fields })
  log.logger({ pagename: basename(__filename), action: 'payIndexchecksum', type: 'response', fields: mainChecksum })
  return checksumVal
  // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: mainChecksum }
}

function mainChecksum (fields) {
  log.logger({ pagename: basename(__filename), action: 'mainChecksum', type: 'request', fields: { ...fields, cardnumberORUID: common_fns.maskValue(fields.cardnumberORUID, 'adhaarNumber') } })
  // First hash using Merchant id and Password
  const checksum = crypto.createHash('sha256')
    .update(fields.username + '~:~' + fields.password)
    .digest('hex')

  delete fields.username
  delete fields.password
  delete fields.ma_user_id

  const concatenated = joinObjectValues(fields)
  log.logger({ pagename: basename(__filename), action: 'mainChecksum', type: 'concatenated', fields: concatenated })
  const date = moment().format('YYYY-MM-DD')

  log.logger({ pagename: basename(__filename), action: 'mainChecksum', type: 'checksum before', fields: checksum + '@' + concatenated + date })
  // Final hash
  const mainChecksum = crypto.createHash('sha256')
    .update(checksum + '@' + concatenated + date)
    .digest('hex')

  log.logger({ pagename: basename(__filename), action: 'payIndexchecksum', type: 'response', fields: mainChecksum })
  return mainChecksum
}

/**
 * Get private key of user
 */
function getPrivateKey (username, password, secret) {
  log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'request', fields: { username, password, secret } })
  try {
    const privateKey = crypto.createHash('sha256')
      .update(secret + '@' + username + ':|:' + password)
      .digest('hex')
    log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'response', fields: privateKey })
    return privateKey
  } catch (err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPrivateKey', type: 'catcherror', fields: err })
    return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
  }
}

// Checksum logic for Index.php
async function indexChecksum (that, fields) {
  log.logger({ pagename: basename(__filename), action: 'indexChecksum', type: 'request', fields: fields })
  const tempConnection = !(that.connection || fields.connection)
  const connection = that.connection || fields.connection || await mySQLWrapper.getConnectionFromPool()

  try {
    if (!(fields.username && fields.password && fields.secret)) {
      const userSql = `SELECT username, password, apikey FROM ma_user_master WHERE profileid = ${fields.ma_user_id} AND mer_user  = 'mer'`
      const userResponse = await that.rawQuery(userSql, connection)
      if (userResponse.length <= 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'userResponse', type: 'request', fields: userResponse })
        return { status: 400, respcode: 12361, message: errorMsg.responseCode[12361] }
      }
      fields.password = userResponse[0].password
      fields.username = userResponse[0].username
      fields.secret = userResponse[0].apikey
    }
    delete fields.userid
    const privateKey = getPrivateKey(fields.username, fields.password, fields.secret)
    delete fields.secret

    const checksum = mainChecksum(fields)
    log.logger({ pagename: require('path').basename(__filename), action: 'indexChecksum', type: 'response', fields: checksum })
    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], checksum: checksum, private_key: privateKey }
  } catch (err) {
    log.logger({ pagename: basename(__filename), action: 'checkOtherConfiguration', type: 'catcherror', fields: err })
    return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
  } finally {
    if (tempConnection) connection.release()
  }
}

function joinObjectValues (obj, delimiter = '') {
  return Object.values(obj)
    .map(val => {
      // convert any objects recursively
      if (typeof val === 'object') {
        return joinObjectValues(val, delimiter)
      }

      return val
    })
    .join(delimiter)
}

// For Airtel CMS , getbankdetails handler
const checksum512 = (data) => {
  return crypto.createHash('sha512')
    .update(data)
    .digest('hex')
}

const ondcRequestEncrypt = ({ data = {}, secrete_key, iv }) => {
  console.log('ondcRequestEncrypt request =====', { data, secrete_key, iv })
  var encryptor = crypto.createCipheriv('aes-256-cbc', secrete_key, iv)
  return encryptor.update(data, 'utf8', 'base64') + encryptor.final('base64')
}

const checksum256 = (data) => {
  return crypto.createHash('sha256')
    .update(data)
    .digest('hex')
}

// CMS Cashpick module
function AES256EncryptRequest (plainText, key) {
  const IV_LENGTH = 16
  const iv = crypto.randomBytes(IV_LENGTH) // Generate a 16-byte IV
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'utf8'), iv)

  let encrypted = cipher.update(plainText, 'utf8', 'base64')
  encrypted += cipher.final('base64')

  // Prepend IV to the encrypted data and return Base64 encoded string
  const encryptedData = Buffer.concat([iv, Buffer.from(encrypted, 'base64')]).toString('base64')

  return encryptedData
}

// CMS Cashpick module
function AES256DecryptResponse (encryptedText, key) {
  const IV_LENGTH = 16
  const encryptedData = Buffer.from(encryptedText, 'base64') // Decode Base64
  const iv = encryptedData.slice(0, IV_LENGTH) // Extract first 16 bytes as IV
  const encrypted = encryptedData.slice(IV_LENGTH) // Remaining bytes as encrypted content

  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'utf8'), iv)

  let decrypted = decipher.update(encrypted, 'base64', 'utf8')
  decrypted += decipher.final('utf8')
  console.log('AES256DecryptResponse typeof =>', typeof decrypted)

  return typeof decrypted === 'string' ? JSON.parse(decrypted) : decrypted
}

module.exports = {
  checksum,
  md5checksum,
  generateOrderchecksum,
  checksum512,
  payIndexchecksum,
  indexChecksum,
  ondcRequestEncrypt,
  checksum256,
  AES256EncryptRequest,
  AES256DecryptResponse
}
