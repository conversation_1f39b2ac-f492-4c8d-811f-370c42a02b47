const util = require('../util/util')
const redis = require('redis')
const bluebird = require('bluebird')
const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
const redisHost = util[env].redisHostIp
const client = redis.createClient({ port: 6379, host: redisHost })

/**
 * Get data from Redis using key
 * @param {*} key
 * @returns res
 */
const getKey = async (key) => {
  if (client.connected) {
    console.log('client connected')
    bluebird.promisifyAll(redis.RedisClient.prototype)
    bluebird.promisifyAll(redis.Multi.prototype)
    return new Promise((resolve, reject) => {
      return client.getAsync(key).then(function (res) {
        if (res == null) {
          resolve(res)
        } else {
          resolve(res)
        }
      }).catch(function (err) {
        reject(err)
      })
    })
  } else {
    return null
  }
}

module.exports = {
  getKey
}
