
const axios = require('axios')
const http = require('http')
const https = require('https')

const isHandlerEnabled = (config = {}) => {
  return !('handlerEnabled' in config && !config.handlerEnabled)
}

const requestHandler = (request) => {
  if (isHandlerEnabled(request)) {
    // Modify request here
    request.headers['Content-Type'] = 'application/json'
    request.headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/69.0.3497.105 Mobile/15E148 Safari/605.1'
    // request.headers.Authorization = 'application/json'
  }
  return request
}

const errorHandler = (error) => {
  if (isHandlerEnabled(error.config)) {
    // Handle errors
  }
  // console.log('errorHandler::', error)
  return Promise.reject(error)
}

const successHandler = (response) => {
  if (isHandlerEnabled(response.config)) {
    // Handle responses
  }
  // console.log('successHandler::', response)
  return response
}

// Init Axios
const jsonInstanceRequest = axios.create({
  // keepAlive pools and reuses TCP connections, so it's faster
  httpAgent: new http.Agent({ keepAlive: true }),
  httpsAgent: new https.Agent({ keepAlive: true })
})

jsonInstanceRequest.interceptors.request.use(
  request => requestHandler(request)
)
jsonInstanceRequest.interceptors.response.use(
  response => successHandler(response),
  error => errorHandler(error)
)

module.exports = {
  apiJsonRequest: jsonInstanceRequest
}
