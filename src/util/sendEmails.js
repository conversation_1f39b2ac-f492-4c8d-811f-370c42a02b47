const fs = require('fs')
const nodemailer = require('nodemailer')
const ejs = require('ejs')
const mailconfig = require('../lib/mailConfig')
const validator = require('validator')

module.exports = async function (mailData) {
  try {
    const path = 'src/templates/' + mailData.template
    // console.log('path : ' + path)

    if (fs.existsSync(path)) {
      if (typeof mailData.content === 'undefined') {
        mailData.content = {}
      }
      const transporter = nodemailer.createTransport(mailconfig)

      const contentData = await ejs.renderFile(path, mailData.content).catch((err) => {
        console.log(err)
        return false
      })

      if (contentData) {
        const mainOptions = {}
        const invalidEmails = []
        mainOptions.from = (typeof mailData.from !== 'undefined' && mailData.from.trim() !== '') ? mailData.from.trim() : '"Airpay" <<EMAIL>>' // sender address

        if (typeof mailData.to === 'undefined' || mailData.to.length < 1) {
          return { status: 400, message: 'invalid to receivers email address' }
        } else {
          mainOptions.to = mailData.to // list of to receivers

          mainOptions.to.forEach(emailIds => {
            if (!validator.isEmail(emailIds)) {
              invalidEmails.push(emailIds)
            }
          })
        }

        if (typeof mailData.cc !== 'undefined' && mailData.cc.length > 0) {
          mainOptions.cc = mailData.cc // list of cc receivers
          mainOptions.cc.forEach(emailIds => {
            if (!validator.isEmail(emailIds)) {
              invalidEmails.push(emailIds)
            }
          })
        }

        if (typeof mailData.bcc !== 'undefined' && mailData.bcc.length > 0) {
          mainOptions.bcc = mailData.bcc // list of bcc receivers
          mainOptions.bcc.forEach(emailIds => {
            if (!validator.isEmail(emailIds)) {
              invalidEmails.push(emailIds)
            }
          })
        }

        if (invalidEmails.length > 0) {
          return { status: 400, message: 'invalid emailids ' + JSON.stringify(invalidEmails) }
        }

        if (typeof mailData.subject !== 'undefined' && mailData.subject.trim() !== '') {
          mainOptions.subject = mailData.subject // Subject line
        }

        if (typeof mailData.attachments !== 'undefined' && mailData.attachments.length > 0) {
          mainOptions.attachments = mailData.attachments // Subject line
        }

        if (typeof mailData.replyTo !== 'undefined' && mailData.replyTo.length > 0) {
          mainOptions.replyTo = mailData.replyTo
        }

        mainOptions.html = contentData

        // console.log("html data ======================>", JSON.stringify(mainOptions));

        const sendemail = await transporter.sendMail(mainOptions).catch((err) => {
          console.log(err)
          return false
        })

        if (sendemail) {
          return { status: 200, message: 'successfully email sent' }
        } else {
          return { status: 400, message: 'failed to sent email' }
        }
      } else {
        return { status: 400, message: 'templete content data invalid' }
      }
    } else {
      return { status: 400, message: 'template not found' }
    }
  } catch (err) {
    return { status: 400, message: 'something went wrong !!!' + err }
  }
}
