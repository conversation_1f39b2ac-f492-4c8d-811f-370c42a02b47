module.exports = {
  isProduction: () => process.env.NODE_ENV === 'production',
  isStaging: () => process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'beta',
  isProduction1: () => true,
  debug: true,
  jwtKey: 'TV6wDRZacz1CK27WGGjZxzAlUoeIlSVCeKAonebDcYntNrIjVqli',
  airpayKey: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
  secretkey: 'b0B1c#dsaf45ADSF5645adf0215',
  affkey: 'UkVUQUlMQVBQ',
  jwtExpirySeconds: 86400,
  jwtApiExpirySeconds: 3000,
  encryptionFlag: true,
  gcmApiKey: 'AAAAXcea8tY:APA91bHN8pi3HoTuVwYDORguUN-5ScYUAjaIgYM7_JmdGSQBkqd11blu0Bs9NApTrWJCHNNSHA0xkGnVgQuYWQh_5XUhBnuGnK4srHpUgLUIGxainqhSw23I6NqV8ENY8jHqhRNBWHEg',
  // keyEnc: 'Ob3nY7PgUeJ5ovyvn8uUWyFjQbBvn6J2',
  // keyDec: 'AAb6YKF6VvkFuAM1lKuKJTIkLcr5ZCrv',
  reCaptchaVerifyUrl: 'https://www.google.com/recaptcha/api/siteverify',
  riskCovryTNC: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/InsuranceDMT/terms_and_conditions.pdf',
  riskCovryLogoUrl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/InsuranceDMT/Riskcovry%20Logo-02.png',
  iciciLombardLogoUrl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/insurance/logos/icici-lombard-insurance-logo.png',
  aepsAuthenticationBiometricUrl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/aeps_authentication/Biometric.svg',
  aepsAuthenticationIrisUrl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/aeps_authentication/Iris+Authentication.svg',
  aepsAuthenticationFaceUrl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/aeps_authentication/Face+Authentication.svg',
  staging: {
    keyEnc: 'MX6zFWMETRyCbucugKxD22f9R4Il7zvP',
    keyDec: '5PbpCqeioHQ3MWj6dJVeluXDE7UttN8P',
    partial: '35c31d5e1aa63843',
    airpayKey: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
    secretkey: 'b0B1c#dsaf45ADSF5645adf0215',
    affkey: 'UkVUQUlMQVBQ',
    createorderins: 'http://insurance.airpay.ninja/api/ins/create_order',
    verifyorderins: 'https://insurance.airpay.ninja/api/ins/verify_order',
    encryptionKey: 'c024f2066cdd0a6ac397adcf5bbaad',
    secretKey: 'd7Kf9dZxQ8jJ3sWbJ4cL2q7vT6uQ0rD4',
    secondaryEncrptionKey: 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7',
    queue: true,
    queueManager: 'queuemanagermessages.fifo',
    queuebaseurl: 'https://sqs.ap-south-1.amazonaws.com/554846686217/',
    queueurls: {
      transaction_message: 'transactionmessages.fifo',
      ledgers_messages: 'ledgersmessages.fifo',
      incentives_messages: 'incentivesmessages.fifo',
      amlthreshold_messages: 'amlthresholdmessages.fifo',
      create_remitter: 'createremittermessages.fifo',
      requery_messages: 'requerymessage.fifo',
      transfer_messages: 'transfermessage.fifo',
      refund_process: 'refundprocess.fifo',
      risk_score_messages: 'riskscoremessage.fifo',
      fmt_transfer_verified_messages: 'fmtverifiedtransfers.fifo',
      fmt_transfer_pending_messages: 'fmtpendingtransfers.fifo',
      merchant_ipn_messages: 'merchantipnmessages.fifo'
    },
    sendSmsStatusUrl: 'https://retaila.airpay.ninja/api/saveMsgStatus',
    bucketurl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/',
    bucketname: 'retailappdocs',
    airpayApiKey: {
      jry4WpwDkok6lfr1: {
        keyEnc: 'JnheWbF7dWvcHHFhozRixE8zSqcnbmpD',
        keyDec: '6AFP8L3Ct3GAufl8nI6sHiTcgiSbDhqk',
        checksumkey: 'Airpay@2020',
        purpose: 'EMITRAINT'
      },
      jry4WpwDkok6lfr2: {
        keyEnc: 'JnheWbF7dWvcHHFhozRixE8zSqcnbmpD',
        keyDec: '6AFP8L3Ct3GAufl8nI6sHiTcgiSbDhqk',
        checksumkey: 'Airpay@2020',
        purpose: 'MERCHANTUR'
      },
      t3j3JaHIXfkzcWDE: {
        keyEnc: '9f3Knkz2rCI5Ju5ldc9I808PKVRGhnM3',
        keyDec: 'TruAQF5W6J60jfqPbDXgrfWnT9supH7c',
        checksumkey: 'Airpay@2020',
        purpose: 'ACQUIRINGUR'
      }
    },
    channelListApiUrl: 'https://payments.airpay.ninja/api/getMerchantChannelList.php',
    redisHostIp: process.env.REDIS_HOST || '***********',
    shighrapay_privatekey: 1,
    merchant_details: {
      mercid: '19378',
      secret: '5q9M2W1uKe67B3Ab',
      USER_ID: '5',
      password: 'A3IEpPKn',
      username: '5610027',
      privatekey: '4510be7376e77d6ea35b630858837659d3a262bc28f902c09ec05a8564c22f0f'
    },
    cron_details: {
      ma_user_id: '44554',
      userid: '20086'
    },
    beneficiaryVertification: {
      ma_user_id: '28641',
      userid: '1249',
      salt: 'YHKBGAsadsjliLLHNL788'
    },
    nsdlKycUrl: 'https://hqvt6tbkl5.execute-api.ap-south-1.amazonaws.com/stag/nsdlkycstatus',
    reCaptchaV3SecretKey: '6Lcn2XkiAAAAAHZqgiQ4euwMywrVB8LD95zWWJ3V',
    reCaptchaV2SecretKey: '6LeL5o0iAAAAAETjSQ7ckTeLWdQIVOqZdQfJgK5C',
    /* RISK MANAGEMENT Changes */
    riskMangementAirpayKey: 'AL0011AL1658921466',
    riskMangementPartnerKey: 'AF0060AF1658921607',
    riskCheckSumKey: 'b0B1c#dsaf45ADSF5645adf0215',
    riskManagementURL: 'https://sentry.airpay.ninja/node/api/sentry/search',
    /* SOUNDBOX CHANGES */
    soundBoxAffiliate: 'AF0059AF1658123547',
    soundBoxAirpaykey: 'AL0014AL1689601820',
    soundBoxBaseURL: 'https://soundbox.airpay.ninja',
    soundBoxVerifyVPAURL: 'https://secure.airpay.ninja',
    soundBoxendpoint: {
      DEVICE: 'node/api/device-management',
      NOTIFY: 'node/api/transaction',
      VERIFY_VPA: 'upi/vpa/icici/merchant_vpa_verify.php'
    },
    icicLombardPDFURL: 'https://retaila.airpay.ninja/api/icici-policy-certificate',
    startHealthPDFURL: 'https://retaila.airpay.ninja/api/star-health-policy-certificate'
  },
  beta: {
    keyEnc: 'MX6zFWMETRyCbucugKxD22f9R4Il7zvP',
    keyDec: '5PbpCqeioHQ3MWj6dJVeluXDE7UttN8P',
    partial: '35c31d5e1aa63843',
    airpayKey: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
    secretkey: 'b0B1c#dsaf45ADSF5645adf0215',
    affkey: 'UkVUQUlMQVBQ',
    createorderins: 'http://insurance.airpay.ninja/api/ins/create_order',
    verifyorderins: 'https://insurance.airpay.ninja/api/ins/verify_order',
    encryptionKey: 'c024f2066cdd0a6ac397adcf5bbaad',
    secretKey: 'd7Kf9dZxQ8jJ3sWbJ4cL2q7vT6uQ0rD4',
    secondaryEncrptionKey: 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7',
    queue: true,
    queueManager: 'betaqueuemanagermessages.fifo',
    queuebaseurl: 'https://sqs.ap-south-1.amazonaws.com/554846686217/',
    queueurls: {
      transaction_message: 'betatransactionmessages.fifo',
      ledgers_messages: 'betaledgersmessages.fifo',
      incentives_messages: 'betaincentivesmessages.fifo',
      amlthreshold_messages: 'betaamlthresholdmessages.fifo',
      create_remitter: 'betacreateremittermessages.fifo',
      requery_messages: 'betarequerymessage.fifo',
      transfer_messages: 'betatransfermessage.fifo',
      refund_process: 'betarefundprocess.fifo',
      risk_score_messages: 'betariskscoremessage.fifo',
      fmt_transfer_verified_messages: 'betafmtverifiedtransfers.fifo',
      fmt_transfer_pending_messages: 'betafmtpendingtransfers.fifo',
      merchant_ipn_messages: 'betamerchantipnmessages.fifo'
    },
    sendSmsStatusUrl: 'https://retaila.airpay.ninja/api/saveMsgStatus',
    bucketurl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/',
    bucketname: 'retailappdocs',
    airpayApiKey: {
      jry4WpwDkok6lfr1: {
        keyEnc: 'JnheWbF7dWvcHHFhozRixE8zSqcnbmpD',
        keyDec: '6AFP8L3Ct3GAufl8nI6sHiTcgiSbDhqk',
        checksumkey: 'Airpay@2020',
        purpose: 'EMITRAINT'
      },
      jry4WpwDkok6lfr2: {
        keyEnc: 'JnheWbF7dWvcHHFhozRixE8zSqcnbmpD',
        keyDec: '6AFP8L3Ct3GAufl8nI6sHiTcgiSbDhqk',
        checksumkey: 'Airpay@2020',
        purpose: 'MERCHANTUR'
      },
      t3j3JaHIXfkzcWDE: {
        keyEnc: '9f3Knkz2rCI5Ju5ldc9I808PKVRGhnM3',
        keyDec: 'TruAQF5W6J60jfqPbDXgrfWnT9supH7c',
        checksumkey: 'Airpay@2020',
        purpose: 'ACQUIRINGUR'
      }
    },
    channelListApiUrl: 'https://payments.airpay.ninja/api/getMerchantChannelList.php',
    redisHostIp: process.env.REDIS_HOST || '***********',
    shighrapay_privatekey: 1,
    merchant_details: {
      mercid: '19378',
      secret: '5q9M2W1uKe67B3Ab',
      USER_ID: '5',
      password: 'A3IEpPKn',
      username: '5610027',
      privatekey: '4510be7376e77d6ea35b630858837659d3a262bc28f902c09ec05a8564c22f0f'
    },
    cron_details: {
      ma_user_id: '44554',
      userid: '20086'
    },
    beneficiaryVertification: {
      ma_user_id: '28641',
      userid: '1249',
      salt: 'YHKBGAsadsjliLLHNL788'
    },
    nsdlKycUrl: 'https://cy2xo14im4.execute-api.ap-south-1.amazonaws.com/dev/nsdlkycstatus',
    reCaptchaV3SecretKey: '6Lcn2XkiAAAAAHZqgiQ4euwMywrVB8LD95zWWJ3V',
    reCaptchaV2SecretKey: '6LeL5o0iAAAAAETjSQ7ckTeLWdQIVOqZdQfJgK5C',
    /* RISK MANAGEMENT Changes */
    riskMangementAirpayKey: 'AL0011AL1658921466',
    riskMangementPartnerKey: 'AF0060AF1658921607',
    riskCheckSumKey: 'b0B1c#dsaf45ADSF5645adf0215',
    riskManagementURL: 'https://sentry.airpay.ninja/node/api/sentry/search',
    /* SOUNDBOX CHANGES */
    soundBoxAffiliate: 'AF0059AF1658123547',
    soundBoxAirpaykey: 'AL0014AL1689601820',
    soundBoxBaseURL: 'https://soundbox.airpay.ninja',
    soundBoxVerifyVPAURL: 'https://secure.airpay.ninja',
    soundBoxendpoint: {
      DEVICE: 'node/api/device-management',
      NOTIFY: 'node/api/transaction',
      VERIFY_VPA: 'upi/vpa/icici/merchant_vpa_verify.php'
    },
    icicLombardPDFURL: 'https://retaila.airpay.ninja/api/icici-policy-certificate',
    startHealthPDFURL: 'https://retaila.airpay.ninja/api/star-health-policy-certificate'

  },
  production: {
    keyEnc: 'Nn9TGhXxliYsLO5Ky8CrX83OQuAzSmet',
    keyDec: 'E90f8k5Y0aj93ZSPIfhqvd08asNwM7Xh',
    partial: '971b0c2188a10b3c',
    airpayKey: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
    secretkey: 'b0B1c#dsaf45ADSF5645adf0215',
    affkey: 'UkVUQUlMQVBQ',
    createorderins: 'https://insurance.airpay.co.in/api/ins/create_order',
    verifyorderins: 'https://insurance.airpay.co.in/api/ins/verify_order',
    secondaryEncrptionKey: 'UkXp2s5v8y$B@EiHnMbQeThVmYq3t6w9',
    queue: true,
    queueManager: 'prodqueuemanagermessages.fifo',
    queuebaseurl: 'https://sqs.ap-south-1.amazonaws.com/554846686217/',
    encryptionKey: '1784faf7afb6f1e6f855e5a9cd1c0a',
    secretKey: 'd7Kf9dZxQ8jJ3sWbJ4cL2q7vT6uQ0rD4',
    queueurls: {
      transaction_message: 'prodtransactionmessages.fifo',
      ledgers_messages: 'prodledgersmessages.fifo',
      incentives_messages: 'prodincentivesmessages.fifo',
      amlthreshold_messages: 'prodamlthresholdmessages.fifo',
      create_remitter: 'prodcreateremittermessages.fifo',
      requery_messages: 'prodrequerymessage.fifo',
      transfer_messages: 'prodtransfermessage.fifo',
      refund_process: 'prodrefundprocess.fifo',
      risk_score_messages: 'prodriskscoremessage.fifo',
      fmt_transfer_verified_messages: 'prodfmtverifiedtransfers.fifo',
      fmt_transfer_pending_messages: 'prodfmtpendingtransfers.fifo',
      merchant_ipn_messages: 'prodmerchantipnmessages.fifo'
    },
    sendSmsStatusUrl: 'https://retaila.airpay.co.in/api/saveMsgStatus',
    triggerLowGoldFloatBalanceUrl: 'https://retaila.airpay.co.in/goldfloatdepositmail',
    getGoldConsentDocUploadUrl: 'https://retaila.airpay.co.in/api/gold/html-pdf',
    bucketurl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/',
    bucketname: 'retailappdocs',
    airpayApiKey: {
      FSx1n6AFlru7SRey: {
        keyEnc: 'o67tCi0j1SGX3B1twHPUJGI03tRX51pA',
        keyDec: 'C6mrkz14TAqIkwrmaHEgJ84lDubSq4Hq',
        checksumkey: 'Airpay@2020',
        purpose: 'EMITRAINT'
      },
      jry4WpwDkok6lfr2: {
        keyEnc: 'JnheWbF7dWvcHHFhozRixE8zSqcnbmpD',
        keyDec: '6AFP8L3Ct3GAufl8nI6sHiTcgiSbDhqk',
        checksumkey: 'Airpay@2020',
        purpose: 'MERCHANTUR'
      },
      t3j3JaHIXfkzcWDE: {
        keyEnc: '9f3Knkz2rCI5Ju5ldc9I808PKVRGhnM3',
        keyDec: 'TruAQF5W6J60jfqPbDXgrfWnT9supH7c',
        checksumkey: 'Airpay@2020',
        purpose: 'ACQUIRINGUR'
      }
    },
    channelListApiUrl: 'https://payments.airpay.co.in/api/getMerchantChannelList.php',
    redisHostIp: '*********',
    merchant_details: {
      mercid: '29457',
      secret: '5QeBpJr6Pf86AC4G',
      password: '8xkxQwpm',
      username: '9402845'
    },
    cron_details: {
      ma_user_id: '44554',
      userid: '20086'
    },
    beneficiaryVertification: {
      ma_user_id: '271312',
      userid: '339403',
      salt: 'YHKBGAsadsjliLLHNL788PROD'
    },
    nsdlKycUrl: 'https://flerken.airpay.co.in/nsdlkycstatus',
    reCaptchaV3SecretKey: '6Lcn2XkiAAAAAHZqgiQ4euwMywrVB8LD95zWWJ3V',
    reCaptchaV2SecretKey: '6LeL5o0iAAAAAETjSQ7ckTeLWdQIVOqZdQfJgK5C',
    /* RISK MANAGEMENT Changes */
    riskMangementAirpayKey: 'AL0012AL1671437301',
    riskMangementPartnerKey: '9BONRHY',
    riskCheckSumKey: 'b0B1c#dsaf45ADSF5645adf0215',
    riskManagementURL: 'https://sentry.airpay.co.in/node/api/sentry/search',
    /* SOUNDBOX CHANGES */
    soundBoxAffiliate: 'AF0040AF1694684333',
    soundBoxAirpaykey: 'AL0016AL1693837532',
    soundBoxBaseURL: 'https://soundbox.airpay.co.in',
    soundBoxVerifyVPAURL: 'https://secure.airpay.co.in',
    soundBoxendpoint: {
      DEVICE: 'node/api/device-management',
      NOTIFY: 'node/api/transaction',
      VERIFY_VPA: 'upi/vpa/icici/merchant_vpa_verify.php'
    },
    icicLombardPDFURL: 'https://retaila.airpay.co.in/api/icici-policy-certificate',
    startHealthPDFURL: 'https://retaila.airpay.ninja/api/star-health-policy-certificate'
  },
  development: {
    keyEnc: 'Ob3nY7PgUeJ5ovyvn8uUWyFjQbBvn6J2',
    keyDec: 'AAb6YKF6VvkFuAM1lKuKJTIkLcr5ZCrv',
    partial: 'f5bb77da0258658f',
    airpayKey: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
    secretkey: 'b0B1c#dsaf45ADSF5645adf0215',
    secondaryEncrptionKey: 'UkXp2s5v8y$B@EiHnMbQeThVmYq3t6w9',
    affkey: 'UkVUQUlMQVBQ',
    createorderins: 'http://insurance.airpay.ninja/api/ins/create_order',
    verifyorderins: 'https://insurance.airpay.ninja/api/ins/verify_order',
    secretKey: 'd7Kf9dZxQ8jJ3sWbJ4cL2q7vT6uQ0rD4',
    bucketurl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/',
    bucketname: 'retailappdocs',
    queue: true,
    queueManager: 'queuemanagermessages.fifo',
    queuebaseurl: 'http://localhost:4566/000000000000/',
    sendSmsStatusUrl: 'https://retaila.airpay.ninja/api/saveMsgStatus',
    queueurls: {
      transaction_message: 'transactionmessages.fifo',
      ledgers_messages: 'ledgersmessages.fifo',
      incentives_messages: 'incentivesmessages.fifo',
      amlthreshold_messages: 'amlthresholdmessages.fifo',
      create_remitter: 'createremittermessages.fifo',
      requery_messages: 'requerymessage.fifo',
      transfer_messages: 'transfermessage.fifo',
      risk_score_messages: 'riskscoremessage.fifo',
      fmt_transfer_verified_messages: 'fmtverifiedtransfers.fifo',
      fmt_transfer_pending_messages: 'fmtpendingtransfers.fifo',
      merchant_ipn_messages: 'merchantipnmessages.fifo'
    },
    merchant_details: {
      mercid: '19378',
      secret: '5q9M2W1uKe67B3Ab',
      USER_ID: '5',
      password: 'A3IEpPKn',
      username: '5610027',
      privatekey: '4510be7376e77d6ea35b630858837659d3a262bc28f902c09ec05a8564c22f0f'
    },
    cron_details: {
      ma_user_id: '44554',
      userid: '20086'
    },
    beneficiaryVertification: {
      ma_user_id: '28641',
      userid: '1249',
      salt: 'YHKBGAsadsjliLLHNL788'
    },
    nsdlKycUrl: 'http://127.0.0.1:3000/dev/nsdlkycstatus',
    reCaptchaV3SecretKey: '6Lcn2XkiAAAAAHZqgiQ4euwMywrVB8LD95zWWJ3V',
    reCaptchaV2SecretKey: '6LeL5o0iAAAAAETjSQ7ckTeLWdQIVOqZdQfJgK5C',
    /* RISK MANAGEMENT Changes */
    riskMangementAirpayKey: 'AL0011AL1658921466',
    riskMangementPartnerKey: 'AF0060AF1658921607',
    riskCheckSumKey: 'b0B1c#dsaf45ADSF5645adf0215',
    riskManagementURL: 'https://sentry.airpay.ninja/node/api/sentry/search',
    /* SOUNDBOX CHANGES */
    soundBoxAffiliate: 'AF0059AF1658123547',
    soundBoxAirpaykey: 'AL0014AL1689601820',
    soundBoxBaseURL: 'https://soundbox.airpay.ninja',
    soundBoxVerifyVPAURL: 'https://secure.airpay.ninja',
    soundBoxendpoint: {
      DEVICE: 'node/api/device-management',
      NOTIFY: 'node/api/transaction',
      VERIFY_VPA: 'upi/vpa/icici/merchant_vpa_verify.php'
    },
    icicLombardPDFURL: 'https://retaila.airpay.ninja/api/icici-policy-certificate',
    startHealthPDFURL: 'https://retaila.airpay.ninja/api/star-health-policy-certificate'
  },
  // android app_version
  android_app_version: '273',
  channels_key_value_array: {
    bankservices: 'AEPS',
    moneytransfer: 'Transfer Money',
    collectmoney: 'Collect Money',
    insurance: 'Insurance',
    withdrawal: 'WITHDRAWAL',
    cashinonline: 'CashIn - Online',
    cashinevalue: 'CashIn - Evalue',
    cms: 'CMS',
    gold: 'Gold',
    khata: 'Khata Book',
    fastag: 'FASTag',
    upi: 'UPI',
    upiqr: 'UPI QR',
    trnshistory: 'Transaction History',
    billpayment: 'Recharge & Billpay',
    featured: 'Featured Services',
    nonchannel: 'Non Channels'
  },
  airpayCustomerSupport: {
    supportnumber: '***********',
    supportemail: '<EMAIL>',
    supporttime: '9am to 9pm'
  },
  airpayUserId: process.env.NODE_ENV === 'production' ? 30057 : 18999,
  airpayId: process.env.NODE_ENV === 'production' ? 99998 : 18999,
  shoppingAirpayId: 18999,
  shoppingAirpayuserId: 3,
  airpayCommissionId: 99998,
  airpayHoldingId: 99999,
  defaultGstPercent: 0,
  algorithm: 'aes-256-cbc',
  iv: 'b88698fceb5c7ddc', // crypto.randomBytes(16),
  encryptionkey: 'c4b302788e8adeacdfac48b1a2b6cbc6',
  channelApiKey: 'HJ4CAjt8kgkcWGUfYLJfV9K8LnteyDEM',
  // channel_data_code: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
  channel_data_code: { cms: 'cms', dmt: 'moneytransfer', aeps: 'bankservices', bbps: 'billpayment', collectmoney: 'collectmoney', insurance: 'insurance', withdrawal: 'withdrawal', cashinonline: 'cashinonline', cashinevalue: 'cashinevalue', gold: 'gold', khata: 'khata', fastag: 'fastag', upi: 'upi', recharge: 'recharge', upiqr: 'upiqr', shopping: 'baazaar', requestcredit: 'requestcredit', test: 'test', posactivation: 'posactivation', certificates: 'certificates', btqr: 'btqr', requestscredits: 'requestscredits', intra_transfer: 'intra_transfer', business_dashboard: 'business_dashboard', ondc: 'ondc', bajaj_emi_card: 'bajaj_emi_card', ppiwallet: 'ppiwallet', pan_application: 'pan_application', advance_credit: 'advance_credit', subscription: 'subscription', form16_A: 'form16_A', commissions_report: 'commissions_report', fino_suvidha_dmt: 'fino_suvidha_dmt' },
  additional_channel_code: 'trnshistory',
  additional_channel_code_v2: ['transaction_history', 'agreement', 'certificates', 'credit_debit_others', 'credit_ledger_history', 'btqr', 'lead_loan_gen', 'intra_transfer', 'pos activation'],
  password_expiry_days: 90,
  lockdown_user_minutes: 30,
  user_block_count: 5,
  max_days_without_login_send_otp: 30,
  keySize: 256,
  iterations: 100,
  encryptpassword: 'airpay',
  transferBunch: 5000,
  AEPSMinAmount: 100,
  sendMoneyDistributorDescription: 'Credits to', // distributor/retailer',
  sendMoneyDistributorDescriptionNew: 'Debit - Limits assigned - #userType# #companyName#', // distributor/retailer',
  sendMoneyCommissionDescription: 'Commission Credits to super Distributor',
  sendMoneyRetailerDescription: 'Credits received by', // distributor/retailer'
  sendMoneyRetailerDescriptionNew: 'Credit - Limits assigned - #userType# #companyName#', // distributor/retailer'
  // pointsToCashMerchantDebitDescription: 'Credits by ',
  pointsToCashMerchantDebitDescription: 'Debit - ',
  // pointsToCashAirpayCreditDescription: 'Credits to SuperDistributor',
  pointsToCashAirpayCreditDescription: 'Credit - Points to cash',
  pointsToCashMerchantCreditDescription: 'Cash to Distributor',
  pointsToCashAirpayDebitDescription: 'Cash by super Distributor',
  pointsToCashCommissionCreditDescription: 'Commission earned in conversion',
  incentivePointsDebitDescription: 'Credits debited for Incentive ',
  incentivePointsDebitDescriptionNew: 'Debit - #type# Incentive',
  surchargePointsDebitDescription: 'Debit - #type# Surcharge ',
  insurancePointsDebitDescription: 'Debit - Insurance ',
  insurancePointsCreditsDescription: 'Credit - Insurance ',
  incentivePointsCreditDescription: 'Credits for Incentive',
  AEPSDebitAirpay: 'Credits by SuperDistributor',
  AEPSDebitAirpayNew: 'Debit - ',
  AEPSCreditAirpay: 'Credits to Retailer',
  AEPSCreditAirpayNew: 'Credit - ',
  IncentiveCommissionDescription: 'TDS on Incentive for ',
  IncentiveCommissionDescriptionNew: 'TDS - #type# incentive',
  IncentiveEarnedMerchant: 'Credits for Incentive for ',
  IncentiveEarnedMerchantNew: 'Credit - #type# Incentive',
  smsUserName: '633609',
  smsPassword: 'welcome12',
  smsSignature: 'AIRPAY',
  smsURL: 'http://luna.a2wi.co.in:7501/failsafe/HttpLink?',
  smsPostmanUrl: 'https://postman.airpay.co.in/sms/index.php',
  whatsappPostmanUrl: 'https://postman.airpay.co.in/whatsapp/index.php',
  smsPostmanToken: 'aff8ff6cdd77a969826f18d85ebb52d3',
  topupCashDebit: 'Top Up Cash by ',
  topupCashCredit: 'Top Up Cash to ',
  // topupPointsDebit: 'Top Up Credits by ',
  topupPointsDebitNew: 'Debit - Cashin',
  topupPointsCredit: 'Top Up Credits to ',
  topupPointsCreditNew: 'Credit - Cashin',
  neftFileFormat: 'AIRPAY~REFPAY~NEFT~<NEFT DATE DASH> <AIRPAYID>~<NEFT DATE SLASH>~~**********~<NEFT AMOUNT>~M~~<MERCHANT NAME>~~<IFSC CODE>~<ACCOUNT NUMBER>~~~~~~~~~~~AIRPAY PAYMENT SERVICES PVT LTD_PG~AIRPAY PAYMENT SERVICES PVT LTD~',
  neftFilePath: '',
  communication: {
    // CO: 'Dear <Customer>, your OTP for registering on airpay is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar', // new template
    CO: 'Dear <Customer>, your OTP for registering on airpay vyaapaar is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar', // new template
    KYC: 'Dear <Customer>, your OTP to complete KYC is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar',
    MN: 'Dear <Customer>, your OTP for changing registered mobile number is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar',
    BBPS: 'Dear <Customer>, your OTP to perform transaction is <OTP>. This OTP will be valid for <TIME>.Team airpay vyaapaar',
    BE: 'Dear <Customer>, <OTP> is your OTP for authorizing the addition of new beneficiary (<account_number>). This OTP will be valid for <TIME>. Agent Name: <agent_name>.Team airpay vyaapaar',
    BT: 'Dear Customer,The OTP for Money Transfer is: ',
    REF: 'Dear Customer, <OTP> is your OTP for authorizing the refund of <currency> <amount>.This OTP will be valid for <TIME>.Agent Name: <agent_name>.Team airpay vyaapaar',
    CW: 'Dear <Customer>, your OTP to complete cash withdrawal is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar',
    BA: 'Hello <Customer>, the OTP to add your bank details for withdrawal is: <OTP>. This OTP is valid for <TIME>. Team airpay vyaapaar',
    DR: 'Dear <Customer>, your OTP for device registration is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar',
    DREMAIL: 'Your OTP for device registration is <OTP>. This OTP will be valid for <TIME>.',
    TRANSFER: 'Dear <Customer>, your transfer of <currency> <amount> to <benf_name> acct no. ending with <account_number> is successful! Your reference number is <airpay_id>,<Customer> charge <currency> <charge_rs>. <Salutation>',
    PPITRANSFER: 'Dear <Customer>, your transfer of <currency> <amount> to <benf_name> acct no. ending with <account_number> is successful! Your reference number is <airpay_id>,<Customer> charge <currency> <charge_rs>. airpay vyaapaar',
    TRANSFERPENDING: 'Dear <Customer>, your transfer of <currency> <amount> to <benf_name> acct no. ending with <account_number> is in process! The status will be updated soon. Your reference number is <airpay_id>,<Customer> charge <currency> <charge_rs>. <Salutation>',
    TRANSFERFAILURE: 'Dear <Customer>, your transfer of <currency> <amount> to Beneficiary Account <account_number> could not be completed successfully. Your reference number is <airpay_id>. Please visit your nearest agent for assistance. Team airpay vyaapaar',
    TRANSFERFAILUREKYC: 'Dear <Customer>, your transfer of <currency> <amount> to Beneficiary Account <account_number> could not be completed due to KYC Limits. Your reference number is <airpay_id>. Please visit your nearest agent for assistance. Team airpay vyaapaar', // new template
    TRANSFERFAILUREPAN: 'Dear <Customer>, your transfer of <currency> <amount> to Beneficiary Account <account_number> could not be completed successfully. Your reference number is <airpay_id>. Please visit your nearest agent for assistance. Team airpay vyaapaar',
    REFUND: 'Dear <Customer>, your Full Refund of <currency> <amount> was successful! Your reference number is <airpay_id>. Team Airpay Vyaapaar',
    REMITTERADD: 'Dear <Customer>, you have been successfully registered as a remitter. Transfer Fund now seamlessly. Team airpay vyaapaar',
    KYCVERIFY: 'Dear <Customer>, your KYC has been verified successfully. You can transfer funds now seamlessly - <Salutation>',
    KYCFAIL: 'Dear <Customer>, your KYC verification has failed. Please visit your nearest agent for assistance - <Salutation>',
    BENFSUCCESS: 'Dear <Customer>, you have successfully added beneficiary <benf_name> account number ending with <account_number>, IFSC code: <ifsc>.Team airpay vyaapaar',
    BALALERT: 'Alert! your credit balance has fallen below <threshold limit>. Click this link and cash-in immediately to enjoy our services without any interruptions: <Link>. <Salutation>',
    BALBILLPAYALERT: 'Alert! Account balance has fallen below <threshold limit> for <app_name> of user <client_id>. Please add fund this account . <Salutation>',
    // Signature: 'Team Airpay. ',
    Signature: 'Team Airpay Vyaapaar. ',
    SP: 'Dear <Customer>, your OTP for changing security pin is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team airpay vyaapaar',
    SPSUCCESS: 'Dear <Customer>, you have successfully changed security pin. Transfer Fund now seamlessly! Team airpay vyaapaar',
    // SENDMONEYCREDIT: 'UPDATE: Your airpay a/c has been credited with <amount> <credits> by <sendercompany> on <date>. Ref: <orderid> .Team airpay vyaapaar', // new template
    SENDMONEYCREDIT: 'UPDATE: Your airpay vyaapaar a/c has been credited with <amount> <credits> by <sendercompany> on <date>. Ref: <orderid> .Team airpay vyaapaar', // new template
    // SENDMONEYDEBIT: 'UPDATE: <amount> <credits> debited from your airpay a/c and credited to a/c of <receivercompany> on <date>. Ref: <orderid> .Team airpay vyaapaar', // new template
    SENDMONEYDEBIT: 'UPDATE: <amount> <credits> debited from your airpay vyaapaar a/c and credited to a/c of <receivercompany> on <date>. Ref: <orderid> .Team airpay vyaapaar', // new template
    // TOPUP: 'UPDATE: Your airpay a/c has been credited with <amount> on <date>. Ref: <orderid>. Current Balance: <balance>.Team airpay vyaapaar',
    TOPUP: 'UPDATE: Your airpay vyaapaar a/c has been credited with <amount> on <date>. Ref: <orderid>. Current Balance: <balance>.Team airpay vyaapaar',
    // WITHDRAWAL: 'UPDATE: <amount> <credits> debited from your airpay a/c on <date>. Ref: <orderid> .Team airpay vyaapaar', // new template
    WITHDRAWAL: 'UPDATE: <amount> <credits> debited from your airpay vyaapaar a/c on <date>. Ref: <orderid> .Team airpay vyaapaar', // new template
    TRANSACTIONADPAYSUCCESS: 'Dear Customer, your transaction <orderid> of <amount> was successfully processed via Aadhaar Pay on <date>. Team airpay vyaapaar',
    TRANSACTIONADPAYFAILURE: 'Dear Customer, your transaction <orderid> of <amount> was declined via Aadhaar Pay on <date>. Team airpay vyaapaar',
    TRANSACTIONAEPSSUCCESS: 'Aadhaar Cash Withdrawal of Rs.<amount> was successfully completed on <date>. Ref: <orderid> Team Airpay Vyaapaar',
    TRANSACTIONAEPSMSSUCCESS: 'Aadhaar Mini Statement was generated successfully on <date>. Ref: <orderid> Team Airpay Vyaapaar',
    TRANSACTIONAEPSBESUCCESS: 'Aadhaar Balance Enquiry was successfully completed on <date>. Ref: <orderid> Team Airpay Vyaapaar',
    // TRANSACTIONAEPSFAILURE: 'Aadhaar Cash Withdrawal of Rs.<amount> could not be successfully completed on <date>. Ref: <orderid> Team airpay vyaapaar',
    // TRANSACTIONAEPSMSFAILURE: 'Aadhaar Mini Statement generation could not be successfully completed on <date>. Ref: <orderid> Team Airpay Vyaapaar',
    // TRANSACTIONAEPSBEFAILURE: 'Aadhaar Balance Enquiry could not be successfully completed on <date>. Ref: <orderid> Team Airpay Vyaapaar',
    TRANSACTIONAEPSFAILURE: 'Dear Customer, your Aadhaar Cash Withdrawal transaction of <amount> failed on <date>. Ref: <orderid>. Team airpay vyaapaar',
    TRANSACTIONAEPSMSFAILURE: 'Dear Customer, your Aadhaar Mini Statement request failed on <date>. Ref: <orderid>. Team airpay vyaapaar',
    TRANSACTIONAEPSBEFAILURE: 'Dear Customer, your Aadhaar Balance Enquiry failed on <date>. Ref: <orderid>. Team airpay vyaapaar',
    BILLPAYSUCCESS: '<Provider name> transaction of Rs <amount> on <date time> is successful. Ref: <orderid>.<signature>',
    BILLPAYFAILURE: '<Provider name> transaction of <amount> on <date time> has failed. Ref: <orderid>.Team airpay vyaapaar', // new template
    RFD: 'Dear <Customer>, your OTP to perform refund is <OTP>. This OTP will be valid for <TIME>. <Salutation>',
    CMSCUSTSUCCESS: 'Dear <Customer>, we have received <amount> for <Biller name> on <date time>. Transaction ID: <orderid>.<signature>',
    CMSSSFBCUSTSUCCESS: 'Dear <Customer>, we have received Rs. <amount> for Txn with Emp ID <Employee_Code>, Txn ID <orderid> from Branch <Branch_Name> at <Agent_Name> on <date time>. Airpay Vyaapaar',
    CMSCUSTFAILURE: 'Dear <Customer>, <Biller name> Transaction with Amount <amount> has failed on <date time>. Transaction ID: <orderid>.<signature>',
    CMS: 'Dear <Customer>, Your One Time Password for transaction with Amount <Amount> Deposited is <OTP>.Thank You! Airpay Vyaapaar',
    SSFB: 'Dear <Customer>,Your OTP for txn at <Agent_Name> with Emp ID <Employee_Code> from Branch <Branch_Name> and Amount Rs. <Amount> is <OTP>. Airpay Vyaapaar',
    PASSWORDCHANGESUCCESSFULLY: 'Greetings from  Airpay Vyaapaar! The password for <Username> has been reset. <Salutation>',
    TAGSUCCESS: 'Congratulations! Your order for FasTag with Order Id <orderid> was successful. Thank You! Airpay Vyaapaar',
    TAGFAILURE: 'Dear Customer, Your order for FAStag with Order Id <orderid> has failed. Please try again. Thank You! Airpay Vyaapaar',
    TAGACTIVE: 'Dear Customer, Your FAStag A/c for Vehicle <vehicleregistrationno> is now active. Balance amount in the A/c is <amt>. Thank You! Airpay Vyaapaar',
    TAGRECHARGE: 'Dear Customer, Your FASTag A/c for Vehicle <vrn> is credited with <amount>. Balance amount in the A/c is <balance>. Thank You! <Salutation>',
    GOLD: 'Dear <Customer>, your OTP for Gold registration is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! Team Airpay Vyaapaar',
    TRANSACTIONGOLDSUCCESS: 'Gold <transaction_type> of <amount> was successfully completed on <date>. Ref: <orderid> Team Airpay Vyaapaar',
    TRANSACTIONGOLDFAILURE: 'Gold <transaction_type> of <amount> failed on <date>. Ref: <orderid>. Team Airpay Vyaapaar',
    GOLDCUSTOMERACCOUNTNO: 'Dear Customer, Your airpay Digital Gold account is active. Your registered bank account ends in <account_number>. Team Airpay Vyaapaar',
    DBE: 'Dear <Customer>, your OTP for deleting beneficiary is <OTP>. This OTP will be valid for <TIME>. Look forward to serving you! <Salutation>',
    GOLDCUSTOMERUPIADDRESS: 'Dear Customer, Your airpay Digital Gold account is active. Your registered UPI ID is <account_number>. Team Airpay Vyaapaar',
    SignatureNew: 'Team Airpay Vyaapaar',
    POSACTIVATIONSUCCESS: 'Dear Vyaapaari, your <POS_TYPE> terminal, serial # <POS_S#>, is ACTIVATED as of <date_time>. Team Airpay Vyaapaar',
    POSACTIVATIONFAILURE: 'Dear Vyaapaari, your activation request, POS serial <POS_S#>, was UNSUCCESSFUL due to <Reason>. Team Airpay Vyaapaar',
    CMSBAJAJ: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSMITRATA: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSPRAYAS: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSANANYA: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSANNAPURNA: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSINFOSYSTEMS: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSIIFLSAMASTA: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSVBAZAAR: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSPAHALFINANCE: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSSAVEMICROFINANCE: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSBAJAJCUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSSHRIRAMCUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSSHRIRAM: 'Dear <Customer>, your OTP for transaction with <Partner_Name> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    // CMSSHRIRAMCUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for SFL account no <AccountNo> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    // CMSSHRIRAM: 'Dear <Customer>, your OTP for transaction for SFL account no <AccountNo> for the amount of Rs. <Amount> deposited is <OTP>. This OTP will be valid for <minutes> minutes. Thank You! Team airpay vyaapaar.',
    CMSMITRATACUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSPRAYASCUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSSAVEMICROFINANCECUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSANANYACUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSANNAPURNACUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSINFOSYSTEMSCUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSIIFLSAMASTACUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSVBAZAARCUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    CMSPAHALFINANCECUSTSUCCESS: 'Dear <Customer>, we have received a deposit of Rs. <amount> for <Partner_Name> on <date time>. Transaction ID: <orderid>. Thank You! Team airpay vyaapaar.',
    SHOPPINGSUCCESS: 'Dear Customer, <Seller Name> has received your order (Order ID: <value>) successfully. Enjoy your purchase! Team airpay vyaapaar',
    SHOPPINGFAILURE: 'Dear Customer, your purchase of <product name> was unsuccessful. Kindly place another order. Team airpay vyaapaar.',
    REQUESTCREDIT: 'Alert! <Requester_Name> has REQUESTED CREDITS. Login on your dashboard to review this request. Team airpay vyaapaar',
    REQUESTAPPROVE: 'Success! Credits requested from <Role> <Firstname> <Lastname> are approved and will reflect in your account by <Date_Time> hours. Team airpay vyaapaar',
    REQUESTREJECT: 'Alert! Your credit request has been rejected by the <Role> <Firstname> <Lastname>. Please try again. Team airpay vyaapaar',
    CASHBACKPROMO: 'Click <URL> to claim cashback for your recent <transaction_type> transaction! Link valid for 24 hours. TnC apply. Team airpay vyaapaar.',
    SHOPPINGFASTAGSUCCESS: 'Dear Customer, Purchase of <quantity> Fastags, Amount <amount> was successful on <DD-MM>. Order ID <ORDERID>. Team airpay vyaapaar',
    SHOPPINGGOGAMESSUCCESS: 'Success! Play Tamboola through Game Pass ticket(s), purchased on <DATE>, here: <Product Name> airpay vyaapaar',
    SHOPPINGGOGAMESFAILED: 'Alert! Your purchase of <Product Name> Tamboola ticket(s) has failed on <DATE>. Please retry. Team airpay vyaapaar',
    UPISUCCESSTRANSACTIONSMS: 'Success! INR <Value> was received through UPI from <customer UPI ID>, on <HH:MM, DD:MM>. <Salutation>',
    UPISUCCESSTRANSACTIONAPP: 'Success! INR <Value> was received through UPI from <customer UPI ID>, on <HH:MM, DD:MM>. Please check your Transaction History for more details.',
    CR: 'Alert! <OTP> is the OTP for authorizing REVERSAL of <reversal_amount> credits, given on <Disbursal_DD-MM-YY> by <dist_name>. Team Airpay Vyaapaar',
    QR: 'Congratulations! Your QR has been activated. Get benefit of Instant Settlement by doing transactions now. airpay vyaapaar',
    // AIRPAYOFFER: "Thank you! Click here <offer_url> to redeem your reward for using airpay's <transaction_type> service. Team airpay vyaapaar.", // Used only for CMS Airpay Offers
    AIRPAYOFFER: "Thank you! Click here <offer_url> to redeem your reward for using airpay vyaapaar's <transaction_type> service. Team airpay vyaapaar.", // Used only for CMS Airpay Offers
    CMSMIDLANDSUCCESS: 'Dear Team, we have received a deposit of Rs. <amount> from Emp ID <Employee_Code>, Txn ID <orderid> from branch <Branch_Name>, <Branch_Code> at <merchant_company> on <transaction_time>. airpay vyaapaar.',
    ONDCSUCCESS: 'Dear Customer, <ondc_txn_id> is the order number for your purchase. Thank you for placing order on airpay vyaapaar. Team airpay vyaapaar.', // ,
    ONDC: 'Dear Customer, <OTP> is the OTP to place your order on airpay vyaapaar. OTPs are secret. DO NOT disclose it to anyone. Team airpay vyaapaar',
    // ONDCC: 'Dear Customer, <OTP> is the OTP to cancel your order on vyaapaar. OTPs are secret. DO NOT disclose it to anyone. Team airpay vyaapaar', // ONDC Cancellation OTP
    ONDCC: 'Dear Customer, <OTP> is the OTP to cancel your order on airpay vyaapaar. OTPs are secret. DO NOT disclose it to anyone. Team airpay vyaapaar', // ONDC Cancellation OTP
    ONDCCANCELREFUND: 'Dear Customer, your refund for order <ORDER_ID> is successful. Please collect your refund <AMOUNT> from vyaapaari. Team airpay vyaapaar',
    ONDCORDERCANCEL: 'Dear Vyaapaari, your refund against order cancellation of <ORDER_ID> amount <AMOUNT> is credited to your trade balance. Team airpay vyaapaar', // SMS - Order cancelled (Refund credited to merchants trade balance) => TO MERCHANT
    ONDCORDERRETURN: 'Dear Customer, your request for returning order <ORDER_ID>. Refund will be processed within 7 working days. Team airpay vyaapaar', // SMS - Order Return (Refund awaited) => TO CUSTOMER
    ONDCORDERHISTORYOTP: 'Dear Customer, <OTP> is the OTP to view shopping history on vyaapaar. OTPs are secret. DO NOT disclose it to anyone. Team airpay vyaapaar', // SMS - Order history send otp => TO CUSTOMER
    ONDCORDERRETURNOTP: 'Dear Customer, <OTP> is the OTP to return your order on vyaapaar. OTPs are secret. DO NOT disclose it to anyone. Team airpay vyaapaar', // SMS - Order history send otp => TO CUSTOMER
    BAJAJEMICARDPROMO: 'Dear Customer, Thank you for your interest in Bajaj Insta EMI Card. Register with link <Bitly link>. Team airpay Vyaapaar.',
    BGJR: 'Dear Customer, your OTP is <OTP> for BrainGymJr Plan <Planname> for amount INR <Amount>.Team airpay vyaapaar.',
    MONBOARDING: 'Your service request <Request No> is generated on <time>, Team airpay vyaapaar.',
    FMTCO: 'Dear Customer, your OTP is <OTP>.Team airpay vyaapaar.',
    FMTBE: 'Dear Customer, your OTP is <OTP>.Team airpay vyaapaar.',
    FMT: 'Dear Customer, your OTP is <OTP>.Team airpay vyaapaar.',
    LOMBARDCUSTOMERSUCCESS: 'Dear customer,  your transaction <transaction id> for personal accident insurance policy was successfully completed on <Date & Time>. To view the policy details, click here : <Bitly Link>, Team airpay vyaapaar.',
    LOMBARDCUSTOMERSMS: 'Dear customer, your insurance registration is incomplete. Please contact your airpay vyaapaari to complete the process. Best wishes, Team airpay vyaapaar',
    LOMBARDCUSTOMERVDSUCCESS: 'Dear customer, your transaction <transaction id> for <vector borne> <disease insurance> Policy was successfully completed on <Date & Time>. To view the policy details, click here : <Bitly Link>, Team airpay vyaapaar',
    FGTPSWD: 'Dear vyaapaari, Please use <OTP> as your OTP to authenticate password reset. DO NOT SHARE your OTP with anyone. It is valid for next <TIME>. Team airpay vyaapaar.',
    LGN: 'Dear vyaapaari, please use <OTP> as your login OTP. DO NOT SHARE your OTP with anyone. It is valid for the next <TIME>. Team airpay vyaapaar.',
    FMTREFUND: 'Dear Customer, your OTP is <OTP>.Team airpay vyaapaar.',
    FMTME: 'Dear Customer, your OTP is <OTP>.Team airpay vyaapaar.',
    FMTMERCHANTBOARDING: 'Dear Vyaapaari, Nepal Money Transfer service is now active for you on Vyaapaar. airpay vyaapaar',
    SIMACTIVATIONSUCCESS: 'Dear Vyaapaari, Soundbox SIM plan renewal is successful! Your SIM will be active and valid for one year. Team airpay vyaapaar'
  },
  templateid: {
    // CO: '1107169485710812492', // new template id
    CO: '1107174177697942751', // new template id
    KYC: '1107165417605387384',
    MN: '1107165417611734792',
    BBPS: '1107165417587223344',
    BE: '1107165417602543130',
    BT: '',
    REF: '1107165417577075250',
    CW: '1107165417608536824',
    CMS: '1107160863821771832',
    BA: '1107165417596118813',
    DR: '1107165417593728038',
    TRANSFER: '1107165607232645261',
    PPITRANSFER: '1107173890898027249',
    TRANSFERPENDING: '1107160706549459785',
    TRANSFERFAILURE: '1107165841863639079',
    TRANSFERFAILUREKYC: '1107169485756919679', // new template id
    TRANSFERFAILUREPAN: '1107165841863639079',
    REFUND: '1107165900124266457',
    REMITTERADD: '1107165607189588129',
    KYCVERIFY: '1107163758884158765',
    KYCFAIL: '1107163758889580874',
    BENFSUCCESS: '1107165607174529482',
    BALALERT: '1107160135679242975',
    BALBILLPAYALERT: '',
    SP: '1107165417590410993',
    SPSUCCESS: '1107165822265294866',
    // SENDMONEYCREDIT: '1107169485720780000', // new template id
    SENDMONEYCREDIT: '1107174177708780734', // new template id
    // SENDMONEYDEBIT: '1107169485734795059', // new template id
    SENDMONEYDEBIT: '1107174177718421811', // new template id
    // TOPUP: '1107165538447401333',
    TOPUP: '1107174177725078998',
    // WITHDRAWAL: '1107169485742799903', // new template id
    WITHDRAWAL: '1107174184962274929', // new template id
    TRANSACTIONADPAYSUCCESS: '1107167578157631990',
    TRANSACTIONADPAYFAILURE: '1107167578152653595',
    TRANSACTIONAEPSMSSUCCESS: '1107166478920671005',
    TRANSACTIONAEPSBESUCCESS: '1107166478930611598',
    TRANSACTIONAEPSSUCCESS: '1107160230429523206',
    // TRANSACTIONAEPSMSFAILURE: '1107166478925416290',
    // TRANSACTIONAEPSBEFAILURE: '1107166478936948212',
    // TRANSACTIONAEPSFAILURE: '1107165591514122618',
    TRANSACTIONAEPSMSFAILURE: '1107174904109321077',
    TRANSACTIONAEPSBEFAILURE: '1107174904113746288',
    TRANSACTIONAEPSFAILURE: '1107174904101443167',
    BILLPAYSUCCESS: '1107160230419242542',
    BILLPAYFAILURE: '11*****************', // new template id
    CMSCUSTSUCCESS: '1107160863814695878',
    CMSCUSTFAILURE: '1107160863817385474',
    CMSSSFBCUSTSUCCESS: '1107161985030169274',
    SSFB: '1107161985023263820',
    TAGSUCCESS: '1107161372873165027',
    TAGFAILURE: '1107161372883197548',
    TAGACTIVE: '1107161372890297599',
    TAGRECHARGE: '1107161431831462189',
    GOLD: '1107161759727243368',
    TRANSACTIONGOLDSUCCESS: '1107161759720636865',
    TRANSACTIONGOLDFAILURE: '1107161759723717653',
    GOLDCUSTOMERACCOUNTNO: '1107161854984328082',
    GOLDCUSTOMERUPIADDRESS: '1107161854989829953',
    DBE: '1107161777306187412',
    POSACTIVATIONSUCCESS: '1107162244986583457',
    POSACTIVATIONFAILURE: '1107162245003578868',
    CMSBAJAJ: '1107162642048884081',
    CMSBAJAJCUSTSUCCESS: '1107162642056081202',
    CMSSHRIRAM: '1107162642048884081',
    CMSSHRIRAMCUSTSUCCESS: '1107162642056081202',
    CMSMITRATA: '1107162642048884081',
    CMSMITRATACUSTSUCCESS: '1107162642056081202',
    CMSPRAYAS: '1107162642048884081',
    CMSPRAYASCUSTSUCCESS: '1107162642056081202',
    CMSANANYA: '1107162642048884081',
    CMSANNAPURNA: '1107162642048884081',
    CMSINFOSYSTEMS: '1107162642048884081',
    CMSIIFLSAMASTA: '1107162642048884081',
    CMSVBAZAAR: '1107162642048884081',
    CMSPAHALFINANCE: '1107162642048884081',
    CMSANANYACUSTSUCCESS: '1107162642056081202',
    CMSANNAPURNACUSTSUCCESS: '1107162642056081202',
    CMSINFOSYSTEMSCUSTSUCCESS: '1107162642056081202',
    CMSIIFLSAMASTACUSTSUCCESS: '1107162642056081202',
    CMSVBAZAARCUSTSUCCESS: '1107162642056081202',
    CMSPAHALFINANCECUSTSUCCESS: '1107162642056081202',
    CMSSAVEMICROFINANCE: '1107162642048884081',
    CMSSAVEMICROFINANCECUSTSUCCESS: '1107162642056081202',
    REQUESTCREDIT: '1107162815979518588',
    REQUESTAPPROVE: '1107165841879833194',
    REQUESTREJECT: '1107165841874818170',
    SHOPPINGSUCCESS: '1107163093056562155',
    SHOPPINGFAILURE: '1107163093061225623',
    CASHBACKPROMO: '1107165874565595996',
    SHOPPINGFASTAGSUCCESS: '1107163179599331835',
    SHOPPINGGOGAMESSUCCESS: '1107163421883074627',
    SHOPPINGGOGAMESFAILED: '1107163179593565418',
    UPISUCCESSTRANSACTIONSMS: '1107163775473963492',
    CR: '1107163956190376573',
    QR: '1107164300455800753',
    // AIRPAYOFFER: '1107165822242302541',
    AIRPAYOFFER: '1107174177737275358',
    ONDCSUCCESS: '1107166376842283922',
    ONDC: '1107166376836059302',
    // ONDCC: '1107166806391590705',
    ONDCC: '1107174177742786896',
    ONDCCANCELREFUND: '1107166806391590705',
    ONDCORDERCANCEL: '1107167384476194534',
    ONDCORDERRETURN: '1107167384480740010',
    ONDCORDERHISTORYOTP: '1107167455901238883',
    ONDCORDERRETURNOTP: '1107167455904970442',
    BAJAJEMICARDPROMO: '1107169278835546685',
    BGJR: '1107169198928107607',
    MONBOARDING: '1107169217193396478',
    FMTCO: '',
    FMTBE: '',
    FMT: '',
    LOMBARDCUSTOMERSUCCESS: '1107171040286075391',
    LOMBARDCUSTOMERSMS: '1107172060871237090',
    LOMBARDCUSTOMERVDSUCCESS: '1107172658317001190',
    FGTPSWD: '1107171801642788622',
    LGN: '1107171803290212412',
    FMTREFUND: '1107169198928107607',
    FMTME: '',
    FMTMERCHANTBOARDING: '1107169356040152986',
    SIMACTIVATIONSUCCESS: '1107174522525248730'
  },
  expiry: {
    CO: 3,
    KYC: 3,
    MN: 3,
    BBPS: 3,
    BE: 3,
    BT: 3,
    REF: 3,
    CW: 3,
    CMS: 3,
    BA: 3,
    DR: 3,
    SP: 3,
    GOLD: 3,
    DBE: 3,
    CR: 5,
    ONDC: 3,
    ONDCC: 3,
    ONDCORDERHISTORYOTP: 3,
    ONDCORDERRETURNOTP: 3,
    BGJR: 3,
    FMTCO: 10,
    FMTBE: 10,
    FMT: 10,
    FGTPSWD: 3,
    LGN: 3,
    FMTREFUND: 10,
    FMTME: 10,
    FMTMERCHANTBOARDING: 10
  },
  expiryComm: {
    CO: '3 minute',
    KYC: '3 minutes',
    MN: '3 minutes',
    BBPS: '3 minutes',
    BE: '3 minute',
    BT: '3 minutes',
    REF: '3 minutes',
    CW: '3 minutes',
    CMS: '3 minutes',
    BA: '3 minutes',
    DR: '3 minutes',
    DREMAIL: '3 minutes',
    SP: '3 minute',
    GOLD: '3 minutes',
    DBE: '3 minute',
    CR: '5 minutes',
    ONDC: '3 minutes',
    ONDCC: '3 minutes',
    ONDCORDERHISTORYOTP: '3 minutes',
    ONDCORDERRETURNOTP: '3 minutes',
    BGJR: '3 minutes',
    FMTCO: '10 minutes',
    FMTBE: '10 minutes',
    FMT: '10 minutes',
    FGTPSWD: '3 minutes',
    LGN: '3 minutes',
    FMTREFUND: '10 minutes',
    FMTME: '10 minutes',
    FMTMERCHANTBOARDING: '10 minutes'
  },
  bankNameCommunication: {
    NSDL: '',
    PAYTM: '',
    AIRTEL: '',
    FINO: ''
  },
  DmtRemitterMaxSpaceAllowedInFirstName: 2,
  DmtRemitterMaxSpaceAllowedInLastName: 2,
  BeneValidationCharges: 'Beneficiary Validation - Charges by Customer',
  KYCVerificationCharges: 'KYC Verification  - Charges by Customer',
  CommissionEarnedBeneValidation: 'Beneficiary Validation - Commission earned on transaction',
  CommissionEarnedKYCVerification: 'KYC Verification - Commission earned on transaction',
  TransferCharges: 'Debit - DMT - Transfer Charges by Customer',
  GSTAP: 'GST on DMT transfer charges to Airpay',
  BankCharges: 'Bank Charges to Airpay',
  MerchantGST: 'GST credit to ',
  RevMerchantGST: 'GST debited',
  TDSonCommission: 'TDS on commission to Airpay from ',
  CommissionEarnedDMT: 'Commission earned on transaction',
  // ReversedCreditTRF: 'Transfer Amount reversed or refunded',
  ReversedCreditTRF: '#type# - DMT - Transfer Amount Reversed ',
  // ReverseBankCharges: 'Customer Charges reversed - DMT',
  ReverseBankCharges: 'Credit - DMT - Transfer charges to customer - Reversed',
  ticketsTemplate: {
    1: 'Dispute for mobile number change has been raised.',
    2: 'Ticket for Account change raised successfully.'
  },
  migrateNumberCommunication: 'Dear <Customer>, you have initiated a request to change your mobile number registered with airpay. If you have not initiated this request, click here: <url>. <Salutation>',
  migrateNumberSuccess: 'Dear <Customer>, your request to change your registered mobile number has been approved. Look forward to serving you! <Salutation>',
  migrateNumberReject: 'Dear <Customer>, your request to change your registered mobile number has been denied. Please visit the nearest agent to learn more. <Salutation>',
  migrateNumberCommunicationTid: '1107160135653494684',
  migrateNumberSuccessTid: '1107160135658703515',
  migrateNumberRejectTid: '1107160135662096242',
  appUrl: 'http://localhost:8080/tickets?',
  maUrl: 'https://devel-new-ma.airpayme.com/',
  paymentsUrlDev: 'https://payments.airpay.ninja/',
  paymentsUrl: process.env.NODE_ENV === 'production' ? 'https://payments.airpay.co.in/' : 'https://payments.airpay.ninja/',
  makepaymentsUrl: process.env.NODE_ENV === 'production' ? 'https://partnerpay.co.in/' : 'https://partnerpay.airpay.ninja/',
  upiAddressValidationUrl: process.env.NODE_ENV === 'production' ? 'https://payments.airpay.co.in/api/verifyvpa.php' : 'https://payments.airpay.co.in/api/verifyvpa.php',
  paymentsUrlProd: 'https://payments.airpay.co.in/',
  nsdlRequestNoPrefix: process.env.NSDL_PREFIX ? process.env.NSDL_PREFIX : 'T',
  defaultOtp: 123456,
  transferMode: ['NEFT', 'IMPS', 'RTGS'],
  retryBeneficiaryCount: 100002,
  supportTicketEmailSignature: 'Team Airpay Vyaapaar',
  minbalamount: 1000,
  panGoldMinAmount: 50000,
  goldMinAmount: 500,
  goldBankName: 'Dvara Smart Gold',
  minGoldWeight: 0.15,
  arpyVer: 3,
  goldConsentDocDownloadUrl: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/CUSTOMER_CONSENT_DOC.pdf',
  pinnmk: '100ee3f0fd677ce17c73ad5d8fc2b75e', // crypto.randomBytes(16).toString('hex')
  secretQsConfig: {
    secretQsRequiredNew: 3,
    secretQsRequiredChange: 1,
    secretPinMaxAttempt: 5,
    secretPinLength: 4,
    accoutLockTimeInMin: 15, // Minutes
    secretPinExpiryType: process.env.NODE_ENV === 'production' ? 'DAY' : 'DAY',
    secretPinExpiryVal: process.env.NODE_ENV === 'production' ? '90' : '90',
    secretPinExpiryTypeExt: process.env.NODE_ENV === 'production' ? 'YEAR' : 'YEAR',
    secretPinExpiryValExt: process.env.NODE_ENV === 'production' ? '15' : '15'
  },
  topupurl: 'https://vyaapaar.airpay.co.in',
  bankdefaultcred: {
    code: 100015,
    fallBackVal: 'Kunal::31801f9726b7b5edf15fa71c9c187b1cdf05ab43::'
  },
  billpay: {
    informToOPsBySms: {
      code: 100010,
      fallBackVal: '**********'
    },
    informToOPsByEmail: {
      code: 100011,
      fallBackVal: '<EMAIL>'
    },
    threshold_limit: {
      code: 100012,
      fallBackVal: 1000
    },
    minimum_recharge: {
      code: 100013,
      fallBackVal: 10
    },
    maximum_recharge: {
      code: 100014,
      fallBackVal: 5000
    }
  },
  defaultTrasferLimit: 25000, // This will be use in case of bank doesn't return limit
  aepsLtsTime: 60, // Time in minutes for aeps lts button flag,
  posDevConfigMerchd: {
    mercid: '243',
    apikey: 'd895Q31YW6247',
    username: '4083701',
    password: 'S3TVzjln',
    userid: 1115,
    devUrl: 'https://devel-payments.airpayme.com/'
  },
  monthsconfig: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
  emitrapass: 'E-m!tr@2016',
  airpaymerchantconfig: process.env.NODE_ENV === 'production' ? [99999, 99998, 30057, *********] : [99999, 99998, 18999, 28478],
  bbpsReceiptParams: {
    'Customer Name': ['customer_name'],
    'Bill ID': ['billid'],
    'Bill Date': ['billdate'],
    'Bill Due Date': ['billduedate'],
    'Bill Period': ['billperiod'],
    'Bill Number': ['billnumber'],
    // 'Bill Status': ['billstatus'],
    'Bill Amount': ['billamount'],
    'Early Bill Due Date': ['early_billduedate'],
    'Early Bill Discount': ['early_billdiscount'],
    'Early Bill Amount': ['early_billamount'],
    'Late Payment Charges': ['late_payment_charges'],
    'Late Payment Amount': ['late_payment_amount'],
    'Biller ID': ['billerid'],
    'Biller Name': ['biller_name'],
    'Biller Category': ['biller_category']
  },
  bbpsReceiptExclude: ['net_billamount', 'net_Amount', 'objectid', 'billstatus', 'billamount', 'validationid', 'validation_date', 'valid_until', 'additional_details'],
  bbpscallbackurl: process.env.NODE_ENV === 'production' ? 'https://flerken.airpay.co.in/rechargestatus' : 'https://hqvt6tbkl5.execute-api.ap-south-1.amazonaws.com/stag/rechargestatus',
  retailDevEmails: ['<EMAIL>', '<EMAIL>'],
  mer_domain: 'https://apmerchantapp.nowpay.co.in',
  INVOICEPAYCOMM: 'Dear <Sir/Madam>, Your payment of <RS AMOUNT> to <RETAILER BUSINESS NAME> is due. Click Here: <PAYMENT LINK> to make the payment. Airpay Vyaapaar',
  INVOICEPAYCOMMTID: '1107160809587104946',
  kbsignature: 'Airpay Vyaapaar',
  integratedIncentive: {
    EMITRA: process.env.NODE_ENV === 'production' ? 34221 : 28707
  },
  aeps_emitra_reg_service: 7534,
  emitraChecksumKey: 'Airpay@2020',
  retail_sms: {
    url: 'https://japi.instaalerts.zone/failsafe/HttpLink',
    aid: 633609,
    pin: 'welcome12',
    signature: 'AIRPAY'
  },
  ftCategoryList: [
    {
      category_name: 'C'
    },
    {
      category_name: 'NC'
    }
  ],
  // Color Codes for Transactions- Merchant dashboard
  transactionColorCodes: { 5: '#fdc93f', 6: '#ff715b', 24: '#6557e0', 10: '#f26d7d', 2: '#45bee7', 31: '#a764a7', 28: '#ddd140', 15: '#48cfae', 17: '#ffa15b', 34: '#f07cb0', 23: '#a29fa8', 20: '#4ace56' },

  colorCodeForCashInRT: { Cash: '#e9dcf8', bankTransfer: '#a5d6db', merchantTransfer: '#81aeef' },

  colorCodeForCashInDT: { Cash: '#929cfe', bankTransfer: '#404fe6', merchantTransfer: '#3fb8f7' },

  previousMerchantBaseColorCode: '#046daf',
  newMerchantsThisMonthColorCode: '#00c6f7',
  DMTMinAmount: 100,
  sqsenable: {
    code: 1100001,
    fallBackVal: false
  },
  transaction_type_mapping: {
    2: 'TRANSFERS',
    75: 'Digikhata'
  },
  NOTIFICATION_API_ACCESS_KEY: 'AAAAXcea8tY:APA91bHN8pi3HoTuVwYDORguUN-5ScYUAjaIgYM7_JmdGSQBkqd11blu0Bs9NApTrWJCHNNSHA0xkGnVgQuYWQh_5XUhBnuGnK4srHpUgLUIGxainqhSw23I6NqV8ENY8jHqhRNBWHEg',
  transaction_status_mapping: {
    S: 'SUCCESS',
    F: 'FAILED',
    R: 'REFUNDED',
    P: 'PENDING',
    PROC: 'PROCESSING',
    PS: 'PARTIALSUCCESS',
    REV: 'REVERSE'
  },
  transfer_retry_time: 15, // minutes
  aepsrefundurl: process.env.NODE_ENV === 'production' ? 'https://payments.airpay.co.in/api/refundtxn.php' : 'https://payments.airpay.ninja/api/refundtxn.php',
  transaction_comm_mapping: {
    2: 'Your Money Transfer',
    75: 'Your Money Transfer'
  },
  btqr_distributor: *********,
  default_bank_for_aeps_kiosk: {
    code: 1100003,
    fallBackVal: 5 // nsdl default
  },
  zeroSettlementThreshold: 100000,
  IpValidationexpression: /((^\s*((([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]))\s*$)|(^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$))/,
  action_types: ['QRDOWNLOAD', 'BTQRDOWNLOAD'],
  upi_qr_settlement_realtime: {
    code: 1100004
  },
  getMasterLoginUserNameId: {
    code: 1100005
  },
  emitraDistributorUserMasterId: process.env.NODE_ENV === 'production' ? ********* : *********,
  sms_provider_switch: {
    staging: { code: 1100007 },
    production: { code: 1100007 }
  },
  showBankListingCount: {
    code: 1100008
  },
  withdrawalReqAfterSettlementAllowedTxnType: ['btqr', 'upi'],
  getBankDetailsCredentialForChecksum: {
    username: 'airpay_bank_details_user',
    password: 'airpay_bank_details_user_password',
    salt: 'jhfksdhfkjasdasbasdjha21872b'
  },
  getBomQrEndpointCredentialForChecksum: {
    username: 'airpay_bom_qr_user',
    password: 'airpay_bom_qr_password',
    salt: 'jhfks43fkja67asbasdjha21872b'
  },
  maxWithdrawalAmount: {
    code: 1100009
  },
  orderVerifyTimeoutValue: {
    code: 1100010
  },
  BOM_MERCHANT_ID: process.env.NODE_ENV === 'production' ? ********* : *********,
  noOfCronAttemptValue: {
    code: 1100012
  },
  transactionTypeMerchantDashboard: {
    code: 1100015
  },
  selectedTransactionTypeForDashboard: {
    code: 1100016
  },
  transactionTypeForIncentive: {
    code: 1100017
  },
  channel_data_code_dashboard: { cms: 24, moneytransfer: 2, bankservices: 5, billpayment: 6, collectmoney: 10, insurance: 15, recharge: 17, pos: 20, hatm: 23, gold: 28, fastag: 31, baazaar: 34 },
  intraTransfersVerifications: {
    BALANCE_TRANSFER: 'PIN',
    REQUEST_CREDIT: 'PIN',
    WITHDRAWAL: 'PIN'
  },
  marketing_carousel_change_time: {
    code: 1100021
  },
  aeps_emitra_start_timing: {
    code: 50001,
    fallBackVal: '06:00' // default start time
  },
  aeps_emitra_close_timing: {
    code: 50002,
    fallBackVal: '19:30' // default close time
  },
  collect_money_link_expiry_time: {
    code: 1100022
  },
  umang_system_code_id: {
    code: 1100023
  },
  mcc_global_code: {
    code: 1100045,
    fallBackVal: 12345
  },
  // ADHAAR PAY : COLLECT MONEY
  adpay_min_limit: {
    code: 1100026
  },
  adpay_max_limit: {
    code: 1100027
  },
  loginMerchantId: [{ ma_user_id: 255805, userid: 318608, username: 'M255805' }, { ma_user_id: 44554, userid: 20086, username: 'M44554' }],
  // EMITRA DMT BUNCH AMOUNT
  emitraDMTBunchAmount: 5000,
  /* RISK MANAGEMENT Changes */
  max_risk_api_retry_count: {
    code: 1100099,
    fallBackVal: 2
  },
  risk_api_batch_size: {
    code: 1100036,
    fallBackVal: 300
  },
  is_risk_analysis_active: {
    code: 1100037,
    fallBackVal: 'N'
  },
  risk_block_transaction: {
    code: 1100038,
    fallBackVal: 'N'
  },
  risk_score_threshold: {
    code: 1100039,
    fallBackVal: '70'
  },
  merchant_onboarding_value: {
    code: 2001212,
    fallBackVal: 3
  },
  is_risk_sqs_active: {
    code: 1100040,
    fallBackVal: 'N'
  },
  ondcCredentialForChecksum: {
    MS: {
      username: 'ondc_details_user',
      password: 'ondc_details_user_password',
      salt: 'gffksgjfkjasdas245sdjha21872b'
    },
    ADMIN: {
      username: 'ondc_admin_user',
      password: 'e69a3d510c283f2c8b15d0c52b37b74',
      salt: '2af6f9a7ca1b5c5e0a16cea8e92b4'
    }
  },
  // Threshold value set for bank failure rate
  bank_failure_threshold: {
    code: 1100028,
    fallBackVal: 5
  },
  fidypay_distributor_id_list: [*********],
  aepsTwoFactorAuthCheck: {
    code: 1100029 // Flag to check AEPS two factor authenticastion
  },
  aepsTwoFactorAuthCheckHours: {
    code: 1100030 // get expiry hours in AEPS 2 factor authentication
  },
  aepsMerchantLocationValidation: {
    code: 1100034 // Y - Yes, N - No - get value from system_codes
  },
  aepsMidLocValidateRadius: {
    code: 1100041 // Kilometers, check AEPS merchant is inside within the juridiction
  },
  aepsMidLocDeviationPer: {
    code: 1100042 // deviation % allowed for merchant
  },
  banklistaepscheck: {
    code: 200123,
    fallBackVal: 'N'
  },
  // Providers to be excluded and run seperately when bbpsproviders cron is run
  bbpsProvidersExclude: [{ value: 'Education', key: '15' }],
  is_fmt_sqs_active: {
    code: 3000001,
    fallBackVal: 'N'
  },
  mock_api_flag: {
    code: 3000002,
    fallBackVal: 'Y'
  },
  pmt_gst_rate: {
    code: 3000003,
    fallBackVal: 'N'
  },
  iciciLombardGracePeriod: {
    code: 456002,
    fallBackVal: 7
  },
  /* IPN MERCHANT CHANGES */
  is_ipn_merchant_active: {
    code: ********,
    fallBackVal: 'Y'
  },
  is_ipn_merchant_sqs_active: {
    code: ********,
    fallBackVal: 'Y'
  },
  pmt_otp_consent_url_path: {
    code: ********,
    fallBackVal: 'https://uiduat.rblbank.com/PrepaidCustomerLogin/PPIAgentEkyc.aspx?ref=YRZyRX9c9Kk0GwOMgSlXWg=='
  },
  upi_credit_above_amount: 2000,
  dmt_kyc_charges_debit_flag: {
    code: ********
  },
  campaign_flag: {
    code: ********
  },
  noOfAadhaarCronAttemptValue: {
    code: ********
  },
  migration_campaign_flag: {
    code: ********
  },
  ppi_kyc_charges_debit_flag: {
    code: ********
  },
  getGeoLocationTime: {
    code: ********,
    fallBackVal: 4
  },
  subscriptionExpiryTime: {
    code: ********,
    fallBackVal: 10
  },
  resendDmtOtpLimit: 3,
  call_dmt_runtime_sqs: {
    code: ********,
    fallBackVal: false
  }
}
