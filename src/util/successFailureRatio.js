const DAO = require('../lib/dao')
const mySQLWrapper = require('../lib/mysqlWrapper')

const updateCounter = async (req, connection = null) => {
  try {
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    if (typeof req.ma_bank_type_id === 'undefined' || req.ma_bank_type_id === '') {
      return { status: 400, message: 'bank type id invalid' }
    }
    if (typeof req.ma_beneficiary_bank_id === 'undefined' || req.ma_beneficiary_bank_id === '') {
      return { status: 400, message: 'beneficiary bank id invalid' }
    }
    if (typeof req.total_transactions === 'undefined' || req.total_transactions === '') {
      return { status: 400, message: 'total txn count invalid' }
    }
    if (typeof req.failed_transactions === 'undefined' || req.failed_transactions === '') {
      return { status: 400, message: 'fail txn count invalid' }
    }
    if (typeof req.success_transactions === 'undefined' || req.success_transactions === '') {
      return { status: 400, message: 'success txn count invalid' }
    }
    const getData = `SELECT ma_bank_type_id FROM ma_daily_transaction_success_rate WHERE analysis_date = '${req.analysis_date}' AND ma_bank_type_id=${req.ma_bank_type_id} AND ma_beneficiary_bank_id = ${req.ma_beneficiary_bank_id} `
    const result = await DAO.rawQuery(getData, connection)
    if (result.length > 0) {
      const upd = `UPDATE ma_daily_transaction_success_rate SET total_transactions = total_transactions + ${req.total_transactions}, failed_transactions = failed_transactions+ ${req.failed_transactions}, success_transactions = success_transactions+ ${req.success_transactions} WHERE analysis_date = '${req.analysis_date}' AND ma_bank_type_id=${req.ma_bank_type_id} AND ma_beneficiary_bank_id = ${req.ma_beneficiary_bank_id}`
      const result = await DAO.rawQuery(upd, connection)
      if (result.affectedRows > 0) {
        return { status: 200, message: 'Update txn_success_rate success' }
      } else {
        return { status: 400, message: 'Update txn_success_rate fail' }
      }
    } else {
      const Ins = `INSERT INTO ma_daily_transaction_success_rate (ma_bank_type_id,ma_beneficiary_bank_id,analysis_date,total_transactions,failed_transactions,success_transactions) VALUES (${req.ma_bank_type_id},${req.ma_beneficiary_bank_id},'${req.analysis_date}',${req.total_transactions},${req.failed_transactions},${req.success_transactions})`
      const result = await DAO.rawQuery(Ins, connection)
      if (result.insertId) {
        return { status: 200, message: 'Insert txn_success_rate success' }
      } else {
        return { status: 400, message: 'Insert txn_success_rate fail' }
      }
    }
  } catch (err) {
    return { status: 400, message: 'Update Txn Success Rate ' + err.sqlMessage }
  } finally {
    if (isSet) connection.release()
  }
}

module.exports = {
  updateCounter
}
