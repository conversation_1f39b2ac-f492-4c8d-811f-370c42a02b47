const FCM = require('fcm-node')
const util = require('./util')

const sendNotifications = (notificationData) => {
  try {
    const apiKey = util.gcmApiKey
    // Set up the sender with your GCM/FCM API key (declare this once for multiple messages)
    // var sender = new gcm.Sender(apiKey)
    var sender = new FCM(apiKey)
    var message = {
      to: notificationData.recipient,
      notification: {
        title: 'MerchantApp notification',
        body: notificationData.message
      }
    }
    // Actually send the message
    sender.send(message, function (err, response) {
      if (err) {
        console.log('Something has gone wrong!', err)
      } else {
        console.log('Successfully sent with response: ', response)
      }
    })
  } catch (error) {
    console.log(error)
  }
}

module.exports = {
  sendNotifications
}
