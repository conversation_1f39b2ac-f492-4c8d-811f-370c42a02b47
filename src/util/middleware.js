const jwt = require('jsonwebtoken')
const util = require('../util/util')
const validator = require('../util/validator')
const url = require('url')
const MySQLWrapper = require('../lib/mysqlWrapper')
const log = require('./log')
const DAO = require('../lib/dao')

const JwtMiddleware = (req, res, next) => {
  try {
    if (!req.headers.authorization) {
      res.send({ status: 401, message: 'missing authorization header' })
    }
    const token = req.headers.authorization
    const payload = jwt.verify(token, util.jwtKey)
    if (payload.profileid) { next() }
  } catch (err) {
    res.send({ status: 401, message: err.message })
  }
}

// this middleware is used to restrict large unwanted queries to the system
const sizeLimiting = (req, res, next) => {
  const query = req.query.query || req.body.query || ''
  try {
    if (query.length > 10000) {
      res.send({ status: 401, message: 'Query too large' })
    }
    next()
  } catch (err) {
    res.send({ status: 401, message: err.message })
  }
}

const sizeLimit = (event) => {
  try {
    if (('data' in event) && ('query' in event.data) && typeof (event.data.query) === 'string') {
      const QUERY_LIMIT = process.env.QUERY_LIMIT ? process.env.QUERY_LIMIT : 10000
      if (event.data.query.length > parseInt(QUERY_LIMIT)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  } catch (err) {
    return false
  }
}

// this middleware is used to restrict large unwanted queries to the system
const secureServerless = async (event) => {
  const connection = await MySQLWrapper.getConnectionFromReadReplica()

  try {
    const errorResponse = {}

    errorResponse.status = 500
    errorResponse.respcode = 500
    errorResponse.message = 'Something went wrong! Request query too large.'

    const successResponse = {}

    successResponse.status = 200
    successResponse.respcode = 200
    successResponse.message = 'Success'

    const isInValidSize = sizeLimit(event)

    if (isInValidSize === true) {
      return errorResponse
    }

    // Check for introspection queries
    if (event.data.query.includes('__schema') || event.data.query.includes('__type')) {
      errorResponse.status = 500
      errorResponse.respcode = 500
      errorResponse.message = 'Introspection queries are disabled'
      return errorResponse
    }

    const checkInjection = hasSQLInjection(event.data.query)
    if (checkInjection?.matched) {
      // console.warn("SQL Injection detected:");
      console.log("Matched Segment:", checkInjection.matchedValue);
      console.log("Reason:", checkInjection.reason);
    } else {
      console.log(" Input is clean");
    }
    if (checkInjection?.matched) {
      errorResponse.status = 500
      errorResponse.respcode = 500
      errorResponse.message = 'Fail: Please Enter Valid Mutation / Schema. '
      return errorResponse
    }

    const app_version_check = await DAO.rawQuery('Select code_val, code_name from system_codes where code_name IN("appversioncheck", "appversion")', connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'app_version_check', fields: { app_version_check } })

    if (app_version_check.length <= 0 || app_version_check.length <= 1) {
      errorResponse.message = 'Fail: App version configuration missing!'
      return errorResponse
    }

    const versionToCheckRes = app_version_check.find((code) => code.code_name == 'appversioncheck').code_val || ''
    log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'versionToCheck', fields: { versionToCheckRes } })
    const app_version_str = app_version_check.find((code) => code.code_name == 'appversion').code_val || ''
    log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'versionToCheck', fields: { app_version_str } })
    const app_version_arr = app_version_str.split(':::')
    let app_version = app_version_arr[0]
    const force_update = app_version_arr[1]

    let versionToCheck = false
    if (validator.definedVal(versionToCheckRes) && versionToCheckRes == 'true') {
      versionToCheck = true
    }

    if (versionToCheck) {
      log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'versionToCheck', fields: { versionToCheck } })
      // let app_version = await DAO.rawQuery('Select code_val from system_codes where code_name = \'appversion\'', connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'appversion', fields: { app_version } })
      app_version = Number.parseFloat(app_version)
      const user_app_version = Number.parseFloat(event.headers.app_version || '')
      log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'appversion', fields: { app_version, user_app_version } })

      if (isNaN(user_app_version)) {
        errorResponse.message = 'Fail:  App Version is required, Please upgrade!'
        log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'response_user_app_version', fields: errorResponse })
        return errorResponse
      } else if (!isNaN(app_version) && (!isNaN(user_app_version) && user_app_version < app_version)) {
        errorResponse.respcode = 501
        errorResponse.message = 'Fail:  App Version lower than the required version, Please upgrade!'
        // errorResponse.force_update = ((await DAO.rawQuery('Select code_val from system_codes where code_name = \'forceupdate\'', connection) || [{ }])[0].code_val || false) == 'true'
        errorResponse.force_update = (force_update == 'true')
        log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'response_app_version', fields: errorResponse })
        return errorResponse
      }
    }
    log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'response', fields: successResponse })
    return successResponse
  } catch (err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'secureServerless', type: 'catcherror', fields: err })
    const errorResponse = {}

    errorResponse.status = 500
    errorResponse.respcode = 500
    errorResponse.message = 'Something went wrong in ~[MID]!'

    return errorResponse
  } finally {
    connection.release()
  }
}

// Example SQL injection checker
function hasSQLInjection(value) {

  if (typeof value !== 'string') return false;
 
  // Whitelist safe text

  if (/^[\w\s\-:.@',()&]+$/i.test(value)) return false;
 
  const patterns = [

    {
      pattern: /\b(OR|AND)\b\s*['"]?\s*[\w.+:-]+\s*['"]?\s*=\s*['"]?\s*[\w.+:-]+/i,
      reason: 'Logical comparison injection (e.g., OR 1=1 / OR \'1\'=\'1\')'
    },
 
    {
 
      pattern: /['"]\s*(OR|AND|XOR)\b/i,
 
      reason: 'Dangling quote followed by boolean operator'
 
    },

    {

      pattern: /\bSLEEP\s*\(\s*\d+\s*\)/i,

      reason: 'Time delay injection (SLEEP)'

    },

    {

      pattern: /\bWAITFOR\s+DELAY\b/i,

      reason: 'Time delay injection (WAITFOR DELAY)'

    },

    {

      pattern: /\bXOR\b(?=\s+\w+)/i,

      reason: 'Obfuscated operator (XOR)'

    },

    {

      pattern: /\bx0r\b(?=\s+\w+)/i,

      reason: 'Encoded obfuscated operator (x0r)'

    },

    {

      pattern: /\b(IF|CASE|CONVERT|CAST|CHAR)\s*\(/i,

      reason: 'Obfuscated SQL logic or type cast'

    },

    {

      pattern: /(--\s|#\s|\/\*|\*\/)/,

      reason: 'SQL comment pattern'

    },

    {

      pattern: /;\s*\b(SELECT|INSERT|DELETE|DROP|UPDATE)\b/i,

      reason: 'Stacked query using semicolon'

    },

    {

      pattern: /\b(INFORMATION_SCHEMA|TABLE_SCHEMA|XP_CMDSHELL|sysobjects|pg_catalog)\b/i,

      reason: 'System/internal schema access'

    }

  ];
 
  for (const { pattern, reason } of patterns) {

    const match = value.match(pattern);

    if (match) {

      return { matched: true, reason, matchedValue: match[0] };

    }

  }
 
  return false;

}

// set logRequest data
const logRequest = (req, res, next) => {
  const query = req.query.query || req.body.query || ''
  const myurl = url.parse(req.url).pathname.split('/')
  const modulepath = myurl[1]
  let moduleName = ''
  if (req.routeEndpoints.includes(modulepath)) {
    moduleName = modulepath
  } else {
    moduleName = 'unknown'
  }
  const requestData = {
    clientIp: req.ip,
    query: JSON.stringify(query),
    module: moduleName
  }

  const fs = require('fs')
  const { createLogger, format, transports } = require('winston')
  const { combine, timestamp, label, printf } = format

  const myFormat = printf(({ level, message, timestamp }) => {
    return `[${timestamp}] [${level}] [IP:${requestData.clientIp}] [Req-Query:${requestData.query}] [Message: ${message}]`
  })

  const moduleDir = (requestData.module !== 'undefined' && requestData.module.trim() != '') ? requestData.module : 'unknown'
  const logDir = 'logs/' + moduleDir // directory path you want to set
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir)
  }

  const date_ob = new Date()
  const date = date_ob.getFullYear() + ('0' + (date_ob.getMonth() + 1)).slice(-2) + ('0' + date_ob.getDate()).slice(-2)
  const logfile = '/' + date + '.log'
  console.log(logDir + logfile)

  const options = {
    file: {
      level: 'info',
      filename: logDir + logfile,
      handleExceptions: true,
      json: true,
      maxsize: 5242880, // 5MB
      maxFiles: 10,
      colorize: false
    },
    console: {
      level: 'debug',
      handleExceptions: true,
      json: true,
      colorize: false
    }
  }

  // instantiate a new Winston Logger with the settings defined above
  global.logger = createLogger({
    format: combine(
      // label({ label: requestData.module }),
      timestamp(),
      myFormat
    ),
    transports: [
      new transports.File(options.file)
      // new winston.transports.Console(options.console)
    ],
    exitOnError: false // do not exit on handled exceptions
  })

  next()
}

const logs = (req, res, next) => {
  global.referer = req.headers.referer + url.parse(req.url).pathname.split('/')[1]
  next()
}

const validateTokenSchema = (event, payload) => {
  const errorResponse = {}
  const successResponse = {}
  let finalResponse = {}
  const { parse } = require('graphql')
  try {
    const parsedQuery = parse(event.data.query)
    // console.log('After parsedQuery event.data', parsedQuery)
    const firstOperationDefinition = (ast) => ast.definitions[0]
    const firstFieldValueNameFromOperation = (operationDefinition) =>
      operationDefinition.selectionSet.selections[0].arguments

    const larguments = firstFieldValueNameFromOperation(firstOperationDefinition(parsedQuery))
    const collectData = ['userid', 'ma_user_id'] // get multiple args
    const collectedData = {}
    for (let index = 0; index < larguments.length; index++) {
      const fieldData = larguments[index]
      if (collectData.indexOf(fieldData.name.value) > -1) {
        collectedData[fieldData.name.value] = fieldData.value.value
        // break
      }
    }

    if (Object.keys(collectedData).length > 0 && ('userid' in collectedData) && ('publickey' in payload) && ('profileid' in payload)) {
      // if (event.headers['X-Forwarded-For'] !== payload.login_ip || event.headers['User-Agent'] !== payload.user_agent) {
      //   successResponse.status = 401
      //   successResponse.respcode = 1009
      //   successResponse.message = 'Fail: Invalid Token'
      //   successResponse.operation_name = operationName
      // } else
      if (collectedData.userid == payload.publickey && collectedData.ma_user_id == payload.profileid) {
        successResponse.status = 200
        successResponse.respcode = 200
        successResponse.message = 'Success'
      } else {
        successResponse.status = 400
        successResponse.respcode = 400
        successResponse.message = 'Unauthenticated Access'
      }
    } else {
      successResponse.status = 400
      successResponse.respcode = 400
      successResponse.message = 'User not found'
    }
    finalResponse = successResponse
  } catch (error) {
    console.log('MiddlewareGetSchemaDetailsError', error)
    errorResponse.status = 500
    errorResponse.respcode = 500
    errorResponse.message = 'Something went wrong! At token Mismatch.'
    finalResponse = errorResponse
  }

  return finalResponse
}

const getOperationName = (event, payload) => {
  const errorResponse = {}
  const successResponse = {}
  let finalResponse = {}
  const { parse } = require('graphql')
  try {
    const parsedQuery = parse(event.data.query)
    // console.log('After parsedQuery event.data', parsedQuery)
    const firstOperationDefinition = (ast) => ast.definitions[0]
    const firstFieldValueNameFromOperation = (operationDefinition) =>
      operationDefinition.selectionSet.selections[0].arguments

    const operationTypeName = (operationDefinition) =>
      operationDefinition.selectionSet.selections[0].name.value

    const operationName = operationTypeName(firstOperationDefinition(parsedQuery))

    successResponse.status = 200
    successResponse.respcode = 200
    successResponse.message = 'Success'
    successResponse.operation_name = operationName
    return successResponse
  } catch (error) {
    console.log('MiddlewareGetSchemaDetailsError', error)
    errorResponse.status = 500
    errorResponse.respcode = 500
    errorResponse.message = 'Something went wrong! At token Mismatch.'
    finalResponse = errorResponse
  }

  return finalResponse
}

module.exports = {
  JwtMiddleware: JwtMiddleware,
  sizeLimiting: sizeLimiting,
  logRequest: logRequest,
  logs: logs,
  secureServerless: secureServerless,
  validateTokenSchema: validateTokenSchema,
  getOperationName: getOperationName
}
