const responseCode = {
  1000: 'Success',
  1001: 'Fail: Something Went Wrong.',
 // 1001: 'We are unable to proceed. Feel free to hit refresh or come back in a few moments.',
  1002: 'Fail: No Records',
  1003: 'Fail: User does not exist',
  //1004: 'Fail: Checksum mismatch',
  1004: 'We are unable to proceed, please try again.',
  //1005: 'Fail: Insufficient balance',
  1005: 'Fail: Insufficient balance, hence cannot proceed further.',
  1006: 'Fail: OTP attempts exceeded',
 // 1007: 'Fail: Wrong OTP',
  1007: 'Wrong OTP entered, please check once again.',
  1008: 'Fail: OTP has expired',
  1009: 'Fail: Invalid Token',
  // 1010: 'Fail: Missing Authorization Header',
  1010: 'We are unable to proceed, please try again.',
 // 1011: 'Fail: Insufficient Wallet Balance',
  1011: 'Insufficient balance, hence cannot proceed further.',
  1012: 'Fail: Transaction not found',
  1013: 'Fail: Transaction already updated',
  1014: 'Fail: Transaction already refunded',
  1015: 'Transaction amount mismatch. Please check the amount and try again',
  // 1015: 'Fail: Amount mismatch',
  //1016: 'Fail: Recon of Transaction is pending ',
  1016: 'Internal Error Message. At the time of  Transaction status updation, calculation of Commission or Incentives via Cron.',
  //1017: 'Fail: Settlement processed already',
  1017: 'Fail: Settlement already processed',
  1018: 'Wrong reference/transaction id provided , please check once again.',
  //1018: 'Fail: Wrong reference/transaction id provided',
  1019: 'Fail: Invalid request',
  1020: 'Fail: Transaction not initiated',
  1021: 'Internal Error Message. Whenever MA API is called, if we do not get Airpay ID in it. This message is thrown.',
  //1021: 'Fail: airpay transaction id is a required field',
  1022: 'Fail: Database error ', // Append sql message after this
  1023: 'Fail: No rows affected',
  1024: 'The provided date is not valid. Please double-check the date to ensure it follows the correct format and falls within the range of 1 to 31.',
  //1024: 'Fail: Invalid Date',
  //1025: 'Fail: Invalid Year',
  1025: 'Please enter valid year',
  1026: 'Please enter valid month. ',
  //1026: 'Fail: Invalid Month',
  //1027: 'Fail: Invalid Day',
  1027: 'Please enter valid day',
  1028: 'Fail', // Append error msg here
  // 1029: 'Fail: Not a distibutor',
  1029: 'This transaction can be done only by Distributor or Super Distributor',
  //1030: 'Fail: Receiver should be mapped retailer',
  1030: 'Selected receiver is not mapped to you. We are unable to proceed with the request.',
  // 1071: 'Fail: Bank Handler not found for further processing',
  1071: 'We are unable to proceed with your request. Please contact customer support.',
  2001: 'Success: Remitter logged in successfully',
  2002: 'Success: OTP Sent to customer',
  2004: 'Success: Remitter registered successfully',
  //1031: 'Fail: Bank/Branch is not available',
  1031: 'Bank/Branch is not available. Please provide correct details.',
  1032: 'Fail: Beneficiary already exists',
  1033: 'Mobile number not valid. Please check and try again.', // Exact issue will append here
  //1033: 'Fail: Mobile number not valid ',
  1034: 'Fail: Account number entered do not match.',
  // 1035: 'Fail: Invalid Bank',
  1035: 'The Bank is not operational. Try with another bank.',
  1036: 'Fail: Blacklisted values',
  1037: 'Please enter correct PAN Number (Format : **********)',
  //1037: 'Fail: Invalid PAN Number',
  // 1038: 'Fail: Invalid Aadhaar Number',
  1038: 'Please enter Valid Aadhar Number (Total - 12 digits)',
  1039: 'We are unable to proceed with your request. Please contact customer support.',
 // 1039: 'Fail: Invalid Document type',
  1040: 'Fail: Invalid file type',
  1041: 'Fail: Consumer details not found',
  //1042: 'Fail: Beneficiary addition Failed',
  1042: 'We are unable to add beneficiary. Please try again.',
  1050: 'Success: PAN needed',
  // 1051: 'Fail: UIC not provided',
  1051: 'We are unable to proceed with your request. Please try again.',
  // 1052: 'Fail: Invalid UIC',
  1052: 'We are unable to proceed with your request. Please try again.',
  // 1053: 'Fail: UIC Locked/Offline/Deleted',
  1053: 'We are unable to proceed with your request. Please contact customer support.',
  1054: 'Fail: Exhuasted limits',
  //1055: 'Fail: PAN number required',
  1055: 'Please provide PAN number to proceed.',
  1043: 'Fail: Invalid KYC Status',
  1044: 'Fail: KYC Details not found',
  1060: 'Fail: Mobile number already exists',
  1061: 'Fail: KYC details does not match',
  1062: 'Fail: KYC already completed.',
  1063: 'We are unable to proceed with your request. Please try again.',
 // 1063: 'Fail: No OTP Handler defined',
  // 1064: 'Fail: Invalid file ',
  1064: 'Please upload file in valid format',
  1065: 'Document is already uploaded',
  //1065: 'Fail: Duplicate document type',
  1066: 'Unable to proceed. Please contact customer support for further information. ',
  //1066: 'Fail: Blocked due to blacklisted ',
  1067: 'Fail: Mobile number is not linked with logged in user',
  //1068: 'Fail: Invalid Credentials',
  1068: 'Please enter valid credentials',
  // 1069: 'Fail: KYC master document details not found',
  1069: 'KYC Document is unavailable. Please upload',
  1070: 'Please upload all the mandatory documents',
  // 1070: 'Fail: Require document ',
  1072: 'Fail: No records updated. ',
  // 1073: 'Fail: Customer has transfers in pending state.',
  1073: 'Your transaction is in pending status. Please check the status in 5 mins.',
  1074: 'Fail: Ticket already added. ',
  // 1075: 'Fail: Parent user not found',
  1075: 'Your Distributor/ Super Distributor setup is pending. Please contact ASM or customer support.',
  1076: 'Fail: Profile user not found',
  2003: 'Success: Bank side Otp verification',
  // 1077: 'Fail: Bank on-boarding details not found',
  1077: 'We are in process to enable this service for you. It usually takes 24 hours. More than 24 hours,  Please customer support. ',
  1078: 'Fail: Agent details not found',
  1079: 'Fail: Sender not added',
  // 1080: 'Fail: NSDL remitter-boarding details not found',
  1080: 'We are unable to proceed with your request. Please contact customer support.',
  1081: 'Fail: Agent Details not found',
  1082: 'Fail: NSDL session issue',
  1083: 'Fail: Invalid data provided',
  // 1084: 'Fail: Beneficiary addition Failed',
  1084: 'We are unable to add beneficiary. Please try again.',
  // 1085: 'Fail: Money Transfer failed',
  1085: 'We are unable to transfer money. Please try again.',
  // 1086: 'Fail: Bank on-boarding module not found',
  1086: 'We are in process to enable this service for you. It usually takes 24 hours. If it is taking more than 24 hours,  Please customer support. ',
  1087: 'Success: Beneficiary added successfully',
  1088: 'Fail: Invalid transfer mode',
  1089: 'The account number you provided is invalid. Please verify the account number and ensure it matches the correct account. ',
  // 1089: 'Fail: Invalid account number provided ',
  // 1090: 'Fail: Refund not initiated',
  1090: 'Refund not initiated for the transaction id',
  1091: 'Fail: Deletion of beneficiary failed',
  2005: 'Success: Beneficiary deleted successfully',
  2006: 'Success: Retailer/Distributor logged in successfully',
  2007: 'Success: OTP sent successfully',
  2008: 'Success: Retailer/Distributor registered successfully',
  // 1092: 'Fail: Security question list empty ',
  1092: 'Security question list not available. Please try again.',
  1093: 'Fail: Security pin is already set ',
  1094: 'Fail: Security pin is not set ',
  2009: 'Success: Security pin is set successfully ',
  1095: 'Fail: Invalid data provided for secretQsAns',
  1096: 'Fail: Invalid Question id or answer provided',
  // 1097: 'Fail: Secret Question criteria not match',
  1097: 'Provided answer does not match the answer to security question.',
  // 1098: 'Fail: Mismatch in newPin and confirmNewPin number',
  1098: 'Mismatch in newPin and confirmNewPin number. Please check and try again.',
  1099: 'Fail: Invalid secret answer provided',
  // 1100: 'Fail: Encrypt key details not found',
  1100: 'Please contact the customer support for further assistance.',
  1101: 'Please enter valid remitter name ',
  //1101: 'Fail: Invalid remitter name provided',
  1102: 'Fail: Wrong pin entered ',
  1103: 'Fail: PIN locked, Please try after 15 mins',
  // 1104: 'Fail: Invalid order id',
  1104: 'Invalid order id. Please check and try again.',
  1105: 'Fail: Invalid Pin length ',
  // 1106: 'Fail: Username and mobile combination does not match. ',
  1106: 'Mobile number does not match with the username. Please check the number and try again.',
  //1107: 'Fail: Security pin required ',
  1107: 'Please provide the security PIN to proceed',
  1108: 'Fail: Please create strong security pin',
  // 1109: 'Fail: Cannot add more than 5 accounts',
  1109: 'Only one bank account is allowed for the withdrawal.',
  1110: 'Fail: Bank account already exists',
  // 1111: 'Fail: Invalid Beneficiary name provided',
  1111: 'The beneficiary name you provided is not valid. Please review and make sure it matches the beneficiary actual name.',
  // 1112: 'Fail: Charges not found!',
  1112: 'We are unable to proceed with your request. Please try again.',
  // 1113: 'Fail: Invalid user.',
  1113: 'Invalid user details. Please check and try again.',
  //1114: 'Fail: Your account has been locked',
  1114: 'Account locked! Incorrect credentials entered more than 4 times. Please wait for 15 Mins',
  1115: 'Invalid remitter details. Please check and try again.',
  //1115: 'Fail: Invalid customer',
  //1116: 'Fail: Remitter id not mapped',
  1116: 'Existing remitter is not mapped with the new bank. Please try again.',
  // 1117: 'Fail: Parent details not found. Transaction logged',
  1117: 'We are unable to proceed with your request. Please try again.',
  1118: 'Fail : Transaction already exists.',
  1119: 'Fail : No threshold limits found.',
  1120: 'Fail : Transaction denied by Risk - Amount Exceeded - 1120-<period>', // One2Many
  1121: 'Fail : Transaction denied by Risk - Amount Exceeded - 1121-<period>', // Many2One
  1123: 'Fail : Transaction denied by Risk - Amount Exceeded - 1123-<period>', // One2One
  1124: 'Fail : Transaction denied by Risk - Amount Exceeded - 1124', // Spike Amount threshold
  1125: 'Fail : Transaction denied by Risk - Count Exceeded - 1125', // Spike bene threshold
  1126: 'Fail : Transaction denied by Risk',
  1127: 'Fail: Your PIN is expired, You must change your PIN.',
  1128: 'Fail: Old Pin can\'t be used.Please use different PIN',
  // 1129: 'Fail: Invalid make payment param request',
  1129: 'We are unable to proceed with your request. Please try again.',
  //1130: 'Transaction in process',
  1130: 'Your transaction is currently in progress. Please be patient while we complete your request. ',
  // 1131: 'Fail: Transaction failed',
  1131: 'Transaction failed. Please try again.',
  // 1132: 'Fail: Transaction response Unknown',
  1132: 'We are unable to fetch transaction status. Please try again.',
  // 1133: 'Fail: Previous transaction already in process',
  1133: 'Previous transaction already in process. Please wait for some time.',
  // 1134: 'Fail: Bill pay transaction not found',
  1134: 'We are unable to fetch transaction details. Please check and try again.',
  1135: 'Fail: Bill pay transaction already success',
  1136: 'Fail: Invalid Amount',
  // 1137: 'Fail: Invalid Merchant Id',
  1137: 'Please enter valid Merchant Id',
  1138: 'Fail: Maximun 1000 records allowed per page',
  1139: 'Fail: Credit Received Exceeds Credit Given',
  1142: 'Fail: Business Name Already Exist',
  1146: 'Fail: Minimum 3 and maximum 50 characters allowed for business name',
  // 1143: 'Fail: Beneficiary Verification In Progress',
  1143: 'Beneficiary Verification In progress. Please wait.',
  // 1144: 'Fail: API call failed',
  1144: 'We are unable to proceed, please try again.',
  // 1145: 'Fail: API timed out',
  1145: 'We are unable to proceed, please try again.',
 // 1147: 'Fail: Service Not Available',
  1147: 'We are unable to proceed, please try again.',
  // 1148: 'Fail: Unique id mismatch',
  1148: 'We are unable to proceed, please try again.',
  1149: 'Fail: Only alphanumeric characters allow!',
  1150: 'Fail: The minimum length of The New Password should be :7',
  1151: 'Fail: The maximum length of The New Password should be :32',
  1152: 'Fail: The new password format is invalid, please check password hint',
  // 1153: 'Success: Password change successfully',
  1153: 'Success: Password changed successfully',
  // 1154: 'Fail: New Password and Confirm Password not match!',
  1154: 'New Password and Confirm Password does not match! Please check and retry.',
  // 1155: 'Fail: Current password not match!',
  1155: 'Current password does not match!',
  1156: 'Fail: Your new password can not be same as any of your recent passwords. Please choose a new password.',
  1158: 'Reminder sent successfully',
  1159: 'Due date set successfully',
  1170: 'Pending',
  // 1181: 'Fail: Invalid barcode / tag id',
  1181: 'Please enter Valid Barcode/tag id and try again.',
  1182: 'Customer already exists',
  //1183: 'Tag Exist, Please Recharge',
  1183: 'Fastag is already activated, please recharge',
  1184: 'Fail: Invalid Tag[Blacklisted]',
  // 1185: 'Fail: Tag is not activated for recharge',
  1185: 'Please active the Fastag to recharge',
  1186: 'Fail: Tag not issued against VRN',
  1187: 'Fail: Invalid Amount[Min: 100, Max: 10000]',
  // 1160: 'Fail: Session Issue',
  1160: 'We are unable to proceed, please try again.',
  // 1161: 'Fail: Invalid session Id',
  1161: 'We are unable to proceed, please try again.',
  1162: 'Customer account already Verified',
  1163: 'Fail: Customer #mobile mobile not registered with any bank',
  // 1164: 'Fail: Priories bank list empty!',
  1164: 'We are unable to proceed with your request. Please try again.',
  1165: 'Fail: Customer #mobile mobile not registered with #any bank',
  1166: 'Fail: Beneficiary not registered with #any bank',
  1167: 'Fail: Transaction charges not matched',
  // 1168: 'Fail: Invalid beneficiary for customer',
  1168: 'We are unable to find the beneficiary with given details. Please check and try again.',
  1169: 'Invalid #name provided. Please check the name and try again.',
 // 1169: 'Fail: Invalid #name provided!',
  1191: 'Fail: Transaction not allowed!',
  // 1192: 'Fail: Something Went wrong~[BNK-EMTY]',
  1192: 'We are unable to proceed. Feel free to hit refresh or come back in a few moments.',
  1193: 'Fail: Same onboarding bank not allowed!',
  // 1194: 'Fail: API NOT FOUND',
  1194: 'We are unable to proceed, please try again.',
  // 1195: 'Fail: INVALID PURPOSE',
  1195: 'We are unable to proceed, please try again.',
  1196: 'Fail: UNAUTHORIZIED ACCESS',
  1197: 'Fail: Transaction retry time expired!, Please initiate new transaction',
  // 1198: 'Fail: Bank Inactive',
  1198: 'We are unable to proceed with your request. Please contact customer support.',
  // 1199: 'Fail: Orderid Not Found',
  1199: 'Order id not found. Please check and try again.',
  1200: 'Fail: Complete Addhar pan Card eKYC First',
  1201: 'Error: eKYC already done',
  // 1202: 'Fail: Aadhaar Number does not match',
  1202: 'The provided Aadhaar Number does not match our records. Please double-check the number and try again.',
  1203: 'Fail: Complete Addhar pan Card eKYC First',
  // 1204: 'Error: Something Went Wrong [API - ETID]',
  1204: 'We are unable to proceed. Feel free to hit refresh or come back in a few moments.',
  // 1234: 'Success: PreCustomLogin not required',
  1234: 'We are unable to proceed with your request. Please try again.',
  // 1235: 'Success: PreCustomLogin Required',
  1235: 'We are unable to proceed with your request. Please try again.',
  1236: 'Success: Customer Already Registered',
  13001: 'Fail: Unable to verify beneficiary. Please try after sometime',
  // 1205: 'Fail: Invalid Address Detail',
  1205: 'The address details provided are invalid or incomplete. Please check.',
  1206: 'Fail: Product Out of Stock',
  1207: 'Fail: Coupon Code Not Available. Try After Sometime',
  // 1208: 'Fail: Promo txn ID expired',
  1208: 'Cashback promotion code expired',
  1209: 'Error: Billpay Billers Not Found',
  2010: 'Fail: Request Timeout! Please try again after sometime',
  // 12346: 'Fail: KYC not available',
  12346: 'We are unable to proceed with your request. Please contact customer support.',
  12348: 'Success: Kyc Registered required',
  // 12349: 'KYC already done start transacting',
  12349: 'KYC already done. Start transacting',
  // 12350: 'KYC Completed Successfully Start transacting',
  12350: 'KYC Completed Successfully. Start transacting',
  12351: 'KYC Verification Failed Please Try Again',
  12352: 'Success: KYC needed',
  12353: 'Fail: KYC needed',
  12354: 'This particular VPA is already Mapped to different MID',
  12355: 'This VPA is disabled',
  12356: 'This VPA details is not present',
  12357: 'VPA activation already done for this user',
  12358: 'This VPA is already active',
  12359: 'Fail: Order id required for this transaction type',
  12360: 'Fail: Transaction master id required for this transaction type',
  // 12361: 'Fail: User data mismatch',
  12361: 'We are unable to find user details. Please check and try again.',
  12362: 'Try again after sometime',
  12363: 'Another VPA already exist for this merchant',
  3001: 'Fail: Invalid IFSC code provided',
  // 3002: 'Fail: Invalid ma_bank_verification_id provided',
  3002: 'We are unable to proceed, please try again.',
  12364: 'Transaction Failed. Reasons: Fraud suspected by bank OR Rejection as per NPCI guideline - One withdrawal transaction per customer within 30 minutes.  Please try again later.',
  12365: 'Transaction Failed. As per NPCI guideline - One withdrawal transaction per customer within 30 minutes.  Please try again later.',
  // 14000: 'Error: Retreiving provider details Failed',
  14000: 'We are unable to fetch the provider details. Please try again.',
  // 14001: 'Fail: provider response unknown!',
  14001: 'We are unable to proceed with your request. Please try again.',
  14002: 'We are unable to proceed, please try again.',
  //14002: 'Error: Invalid Enum',
  14003: 'Error: Unable to fetch bill amount please retry!',
  // 14004: 'Error: Please enter valid ',
  14004: 'Please enter the correct data',
  14005: 'Fail: No Records for payment request',
  14006: 'Fail: No Records found for order ',
  // 14007: 'Fail: Order Confirmation Status ',
  14007: 'We are unable to fetch the order status. Please try again. ',
  14008: 'Alert! Withdrawals through NEFT will not be processed between 7 P.M. to 1 A.M. and on bank holidays.',
  120004: 'Fail: Transaction slab is not configured. Please contact support team.',
  14009: 'Fail: Not valid for Retailers',
  14010: 'Fail: You cannot transfer funds to yourself.',
  14011: 'Fail: The request can not be initiated with sub-users phone number, please use primary merchants phone number.',
  14012: 'Fail: You are not authorized to perform this action. Please contact the account owner.',
  14013: 'Fail: Wrong tranaction type',
  14014: 'Fail: No customer charges found',
  50001: 'Cut off time for AEPS transaction with eMitra is $endTime. Please try after $startTime',
  130002: 'Logout Failed. Please Try Again.',
  130003: 'User logged out successfully',
  130004: 'Policy still active',
  130005: 'Insurance details updated successfully! Reference #:',
  50001: 'Cut off time for AEPS transaction with eMitra is $endTime. Please try after $startTime',
  // 14013: 'Fail: Wrong tranaction type',
  14013: 'Wrong Transaction  Type',
  14014: 'Fail: No customer charges found',
  140001: 'No Products are available for cancellation/return at this moment.',
  140002: 'Invalid Products, Please check the product details entered and try again later.',
  140003: 'Product Cancellation/Return Failed. Please try again later.',
  140004: 'Something Went Wrong, Please contact the customer care or try again after sometime.',
  140005: 'Fail: Return window has expired.',
  140006: 'Sorry, the searched product is not available.',
  140007: 'Sorry, the searched product is not available at the store.',
  140008: 'Sorry, the searched product is not accepted by the store. Please try with the different store.',
  140009: 'Sorry, the product is not currently trackable. Please try again after sometime.',
  1210: 'Fail: Sender details not found',
  1211: 'Fail: Session timed out, please retry your transaction.',
  1233: 'Merchant account number verified',
  12677: 'Merchant name does not match the registered name or company name. Please verify and try again.',
  12687: 'Maximum retries exceeded.'


}
module.exports = { responseCode }
