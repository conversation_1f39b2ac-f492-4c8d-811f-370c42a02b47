const axios = require('axios')

class bitly {
  // Create bitly url
  static async create (url) {
    // console.log(url)
    const longUrl = encodeURIComponent(url)
    // console.log(longUrl)
    const apiToken = '83209e8f9626a8f3c0b565ea113c39bc991f4874'
    const apiUrl = 'https://www.arpy.in/api?api=' + apiToken + '&type=1&url=' + longUrl
    // console.log(apiUrl)
    const result = await axios.get(apiUrl)
    // console.log(result.data)
    if (result.status === 200) {
      if (result.data.status === 'success') {
        return { status: 200, message: 'Success', url: result.data.shortenedUrl }
      }
      return { status: 400, message: 'Error in creating bitly url' }
    }
    return { status: 400, message: 'Error in creating bitly url' }
  }
}

module.exports = bitly
