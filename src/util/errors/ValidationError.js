module.exports = class ValidationError extends Error {
  constructor (...params) {
    // Pass remaining arguments (including vendor specific ones) to parent constructor
    super(...params)
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError)
    }

    this.name = 'ValidationError'
    this.status = 400
    this.respcode = 1019
    this.message = this.message.replace('ValidationError', 'Error[' + this.respcode + ']')
    this.action_code = 1001
    console.log('ValidationError params =====', params, this.message, this.respcode, this.message.replace('ValidationError', 'Error[' + this.respcode + ']'))
  }
}
