module.exports = class IntegrationError extends Error {
  /**
     * Creates a custom error for Integration
     * @param {{status: 200|400, message: string, action_code: 1000|1001 }} errorObject
     */
  constructor (errorObject) {
    super(errorObject.message)
    console.log(errorObject)
    this.name = 'IntegrationError'
    this.status = errorObject.status
    this.respcode = 3000
    this.message = errorObject.message.replace(':', '[3000]:')
    this.action_code = errorObject.action_code
  }
}
