const ApplicationError = require('./ApplicationErrorFile')

class ApiError extends ApplicationError {
  /**
     *
     * @param {Number} code
     * @param {String} apiName
     * @param  {...any} params
     */
  constructor (code, apiName, ...params) {
    super(code, ...params)
    this.name = 'Api Error'
    this.apiName = apiName
    this.message = `Fail: ${this.apiName} Error : ${this.message}`
  }
}
module.exports = ApiError
