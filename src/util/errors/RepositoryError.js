module.exports = class RepositoryError extends Error {
/**
 * Creates a custom error for repository
 * @param {{status: 200|400, respcode: number, message: string, action_code: 1000|1001 }} errorObject
 */
  constructor (errorObject) {
    super(errorObject.message)
    console.log(errorObject)
    this.name = 'RepositoryError'
    this.status = errorObject.status
    this.respcode = errorObject.respcode
    this.message = errorObject.message
    this.action_code = errorObject.action_code
  }
}