/**
 * Console logs data for ease
 * @param {{ pagename: string, action: string, type: string, fields: JSON }} logData
 */
module.exports.logger = (logData) => {
  if (process.env.LOGGER == 'OFF') return
  const today = new Date()
  var dd = today.getDate()
  var mm = today.getMonth() + 1
  const yyyy = today.getFullYear()
  if (dd < 10) {
    dd = `0${dd}`
  }
  if (mm < 10) {
    mm = `0${mm}`
  }
  const min = today.getMinutes()
  const sec = today.getSeconds()
  const hh = today.getHours()
  const currDate = `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`

  try {
    if (logData && logData.fields && (logData.fields.connection === null || logData.fields.connection === undefined)) {
      console.log(`[${currDate}] || Page: ${logData.pagename} ||  Action: ${logData.action} || Type: ${logData.type} || Data:`, logData.fields)
    } else {
      var requestFields = {}
      requestFields = (({ connection, ...o }) => o)(logData.fields)
      requestFields.connectionThreadId = logData.fields.connection.threadId
      console.log(`[${currDate}] || Page: ${logData.pagename} ||  Action: ${logData.action} || Type: ${logData.type} || Data:`, requestFields)
    }
  } catch (errolog) {
    console.log('LoggerError>>', errolog)
    console.log('LoggerLogs>>', logData)
  }
  // let fieldsToPrint = logData.fields
  // if (('fields' in logData) && logData.fields !== null) {
  //   if (typeof (logData.fields) === 'object') {
  //     fieldsToPrint = Object.create(logData.fields)
  //     if ('connection' in fieldsToPrint) {
  //       fieldsToPrint.connection = null
  //     }
  //   } else {
  //     fieldsToPrint = logData.fields
  //   }
  // }
  // console.log(`[${currDate}] || Page: ${logData.pagename} ||  Action: ${logData.action} || Type: ${logData.type} || Data:`, fieldsToPrint)
}

module.exports.memoryLog = () => {
  if (process.env.LOGGER == 'OFF') return
  const used = process.memoryUsage()
  const arr = []
  for (const key in used) {
    arr.push(`${key} ${Math.round(used[key] / 1024 / 1024 * 100) / 100} MB`)
  }
  console.log('<<<MEMORY LOG>>>>\n', arr.join('\n'), '\n<<<MEMORY LOG>>>>')
}

/**
 * Console logs data for debugging
 * @param {{ pagename: string, action: string, type: string, fields: JSON }} logData
 */
module.exports.debug = (logData) => {
  if (process.env.LOGGER == 'OFF' || process.env.DEBUGGER == 'OFF') return
  const today = new Date()
  var dd = today.getDate()
  var mm = today.getMonth() + 1
  const yyyy = today.getFullYear()
  if (dd < 10) {
    dd = `0${dd}`
  }
  if (mm < 10) {
    mm = `0${mm}`
  }
  const min = today.getMinutes()
  const sec = today.getSeconds()
  const hh = today.getHours()
  const currDate = `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`

  try {
    if (logData && logData.fields && (logData.fields.connection === null || logData.fields.connection === undefined)) {
      console.log(`[${currDate}] || Page: ${logData.pagename} ||  Action: ${logData.action} || Type: ${logData.type} || Data:`, logData.fields)
    } else {
      var requestFields = {}
      requestFields = (({ connection, ...o }) => o)(logData.fields)
      requestFields.connectionThreadId = logData.fields.connection.threadId
      console.log(`[${currDate}] || Page: ${logData.pagename} ||  Action: ${logData.action} || Type: ${logData.type} || Data:`, requestFields)
    }
  } catch (errolog) {
    console.log('LoggerError>>', errolog)
    console.log('LoggerLogs>>', logData)
  }
}
