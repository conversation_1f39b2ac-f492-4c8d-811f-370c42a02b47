const axios = require('axios')
const http = require('http')
const https = require('https')
const querystring = require('querystring')

const isHandlerEnabled = (config = {}) => {
  return !('handlerEnabled' in config && !config.handlerEnabled)
}

const requestHandler = (request) => {
  if (isHandlerEnabled(request)) {
    // Modify request here
    request.headers['Content-Type'] = 'application/x-www-form-urlencoded'
    request.headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/69.0.3497.105 Mobile/15E148 Safari/605.1'
  }
  return request
}

const errorHandler = (error) => {
  if (isHandlerEnabled(error.config)) {
    // Handle errors
  }
  console.log('errorHandler::', error)
  return Promise.reject(error)
}

const isJsonString = (str) => {
  try {
    JSON.parse(str)
  } catch (e) {
    return false
  }
  return true
}

const successHandler = (response) => {
  if (isHandlerEnabled(response.config)) {
    // Handle responses
  }
  //  console.log('successHandler::', response)
  return response
}

// Init Axios
const formInstanceRequest = axios.create({
  // keepAlive pools and reuses TCP connections, so it's faster
  httpAgent: new http.Agent({ keepAlive: true }),
  httpsAgent: new https.Agent({ keepAlive: true }),
  transformRequest: [
    (data, headers) => {
      const formString = querystring.stringify(data)
      console.log('Form API Request::', formString)
      return formString
    }
  ],
  transformResponse: [
    (data) => {
      console.log('JSON API Response::', data)
      if (isJsonString(data)) {
        return JSON.parse(data)
      } else {
        return {
          status: 400,
          message: 'Response is not json',
          response: data
        }
      }
    }
  ]
})

formInstanceRequest.interceptors.request.use(
  request => requestHandler(request)
)
formInstanceRequest.interceptors.response.use(
  response => successHandler(response),
  error => errorHandler(error)
)

module.exports = {
  apiFormSumbitRequest: formInstanceRequest
}
