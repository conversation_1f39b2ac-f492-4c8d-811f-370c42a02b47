'use strict'
const AWS = require('aws-sdk')
const util = require('./util')
const mySQLWrapper = require('../lib/mysqlWrapper')
const mailconfig = require('../lib/mailConfig')
const nodemailer = require('nodemailer')
// Configure the region
AWS.config.update({ region: 'ap-south-1' })

// Create an SQS service object
const sqs = new AWS.SQS({ apiVersion: '2012-11-05' })
const queueManagerCtrl = require('../controller/queuemanager/queueManagerController')

const pushToQueue = async (queueType, Message, MessageAttributes = null) => {
  try {
    const MessagePayLoad = {
      QueueUrl: util[process.env.NODE_ENV].queueurls[queueType],
      MessageBody: Message,
      DelaySeconds: 0,
      MessageAttributes: {
        apiCall: {
          StringValue: '1',
          DataType: 'String'
        }
      }
    }

    if (MessageAttributes != null && Object.keys(MessageAttributes).length > 0) {
      for (const currentAttribute in MessageAttributes) {
        MessagePayLoad.MessageAttributes = {
          ...MessagePayLoad.MessageAttributes,
          [currentAttribute]: {
            StringValue: MessageAttributes[currentAttribute],
            DataType: 'String'
          }
        }
      }
    }
    console.log('MessagePayLoad >>', MessagePayLoad)
    console.time('TIMER_SEND_MESSAGE')
    let sqsPushResponse = {}
    if (process.env.IS_OFFLINE === 'true') {
      sqsPushResponse = { ...MessagePayLoad, ResponseMetadata: 'success' }
    } else {
      sqsPushResponse = await sqs.sendMessage(MessagePayLoad, function (err, data) {
        if (err) console.log('', err, err.stack) // an error occurred
        else console.log('', data) // successful response
      }).promise()
    }

    console.timeEnd('TIMER_SEND_MESSAGE')
    if (sqsPushResponse.ResponseMetadata) {
      return {
        message: 'Success :SMS push successfully to Queue',
        respcode: 1000,
        status: 200,
        data: sqsPushResponse
      }
    } else {
      return {
        message: 'Failed: To push SMS to Queue',
        respcode: 1001,
        status: 400,
        data: sqsPushResponse
      }
    }
  } catch (error) {
    console.log('pushToQueueError >> ', error)
    return {
      message: 'Failed: To push SMS to Queue' + error.message,
      respcode: 1001,
      status: 400,
      data: {}
    }
  }
}

const formatQueue = (ChunkMessages = []) => {
  const entries = []

  for (let index = 0; index < ChunkMessages.length; index++) {
    const chunkItem = ChunkMessages[index]
    entries.push({
      Id: 'id' + chunkItem.queue_id.toString() + parseInt(Math.random() * 1000000),
      MessageBody: JSON.stringify(chunkItem.queue_payload),
      // DelaySeconds: 0,
      MessageAttributes: {
        queueId: {
          StringValue: chunkItem.queue_id.toString(),
          DataType: 'String'
        },
        queueName: {
          StringValue: chunkItem.queue_name,
          DataType: 'String'
        }
      },
      MessageGroupId: chunkItem.queue_msg_grp_id
    })
  }
  return entries
}

const BulkPushToQueueManager = async (Messages = [], connection = null) => {
  let isSet = false
  try {
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    const errorEmail = require('./errorHandler')
    console.time('TIMER_QUEUE_INSERT')

    for (let index = 0; index < Messages.length; index++) {
      const queueMessage = Messages[index]
      const payload = { ...queueMessage.queue_payload }
      if ('connection' in payload) {
        delete payload.connection
      }
      if (('fields' in payload) && ('connection' in payload.fields)) {
        delete payload.fields.connection
      }
      console.log('queueMessage', queueMessage)
      console.log('queueMessagePayload', payload)
      console.log('queueMessageJson', JSON.stringify(payload))
      const _result = await queueManagerCtrl.insertData(connection, {
        data: {
          queue_name: queueMessage.queue_name,
          queue_payload: JSON.stringify(payload),
          queue_status: 'I'
        }
      })
      Messages[index].queue_id = _result.insertId
    }
    console.timeEnd('TIMER_QUEUE_INSERT')

    console.log('Messages To Push ', JSON.stringify(Messages))
    console.time('TIMER_SEND_MESSAGE_BATCH')
    const queueManager = util[process.env.NODE_ENV].queueManager
    const queueUrl = util[process.env.NODE_ENV].queuebaseurl + queueManager
    console.log('queueUrl >>', queueUrl)
    let i; let j; const chunkSize = 10
    const responseArr = []
    for (i = 0, j = Messages.length; i < j; i += chunkSize) {
      const temparray = Messages.slice(i, i + chunkSize)
      try {
        const temparray2 = formatQueue(temparray)
        console.log('formattedQueue>>', JSON.stringify(temparray2))
        console.time('TIMER_SEND_MESSAGE_BATCH_' + i)
        let sqsPushResponse = []
        if (process.env.IS_OFFLINE == 'true') {
          sqsPushResponse = { Entries: temparray2, QueueUrl: queueUrl }
        } else {
          sqsPushResponse = await sqs.sendMessageBatch({ Entries: temparray2, QueueUrl: queueUrl }).promise()
        }

        console.timeEnd('TIMER_SEND_MESSAGE_BATCH_' + i)
        console.log('BATCH_' + i + '_RESULT', sqsPushResponse)
        responseArr.push(sqsPushResponse)
      } catch (error) {
        console.log('sendMessageBatchError>>', error) // mails
        for (let index = 0; index < temparray.length; index++) {
          const queueData = temparray[index]
          const data = { queue_status: 'E', queue_insert_response: JSON.stringify({ error: error.message }) }
          const updateQueue = await queueManagerCtrl.updateWhereData(connection, { data, id: queueData.queue_id, where: 'queue_id' })
        }
        if (process.env.IS_OFFLINE != 'true') {
          errorEmail.notifyCatchErrorEmail({
            function: 'BulkPushToQueueManager',
            data: temparray,
            error: error
          })
        }
      }
    }

    console.timeEnd('TIMER_SEND_MESSAGE_BATCH')
    return {
      message: 'Bulk SMS push successfully to Queue',
      status: 200,
      data: responseArr
    }
  } catch (error) {
    console.log('BulkPushToQueueManager >> ', error)
    return {
      message: 'Failed: Bulk push to Queue ' + error.message,
      respcode: 1001,
      status: 400,
      data: {}
    }
  } finally {
    if (isSet) {
      connection.release()
    }
  }
}

const SinglePushToQueue = async (Messages = [], queueName, connection = null) => {
  let isSet = false
  try {
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    const errorEmail = require('./errorHandler')
    console.time('TIMER_QUEUE_INSERT')

    for (let index = 0; index < Messages.length; index++) {
      const queueMessage = Messages[index]
      const payload = { ...queueMessage.queue_payload }
      if ('connection' in payload) {
        delete payload.connection
      }
      if (('fields' in payload) && ('connection' in payload.fields)) {
        delete payload.fields.connection
      }
      console.log('queueMessage', queueMessage)
      console.log('queueMessagePayload', payload)
      console.log('queueMessageJson', JSON.stringify(payload))
      Messages[index].queue_id = Math.floor(Math.random() * 100000)
    }
    console.timeEnd('TIMER_QUEUE_INSERT')

    console.log('Messages To Push ', JSON.stringify(Messages))
    console.time('TIMER_SEND_MESSAGE_BATCH')
    const queueManager = util[process.env.NODE_ENV].queueurls[queueName]
    const queueUrl = util[process.env.NODE_ENV].queuebaseurl + queueManager
    console.log('queueUrl >>', queueUrl)
    let i; let j; const chunkSize = 10
    const responseArr = []
    for (i = 0, j = Messages.length; i < j; i += chunkSize) {
      const temparray = Messages.slice(i, i + chunkSize)
      try {
        const temparray2 = formatQueue(temparray)
        console.log('formattedQueue>>', JSON.stringify(temparray2))
        console.time('TIMER_SEND_MESSAGE_BATCH_' + i)
        let sqsPushResponse = []
        if (process.env.IS_OFFLINE == 'true') {
          sqsPushResponse = { Entries: temparray2, QueueUrl: queueUrl }
        } else {
          sqsPushResponse = await sqs.sendMessageBatch({ Entries: temparray2, QueueUrl: queueUrl }).promise()
        }

        console.timeEnd('TIMER_SEND_MESSAGE_BATCH_' + i)
        console.log('BATCH_' + i + '_RESULT', sqsPushResponse)
        responseArr.push(sqsPushResponse)
      } catch (error) {
        console.log('sendMessageBatchError>>', error) // mails
        if (process.env.IS_OFFLINE != 'true') {
          errorEmail.notifyCatchErrorEmail({
            function: 'SinglePushToQueueManager',
            data: temparray,
            error: error
          })
        }
      }
    }

    console.timeEnd('TIMER_SEND_MESSAGE_BATCH')
    return {
      message: 'SinglePushToQueue push successfully to Queue',
      status: 200,
      data: responseArr
    }
  } catch (error) {
    console.log('SinglePushToQueue >> ', error)
    return {
      message: 'Failed: Single Push To Queue ' + error.message,
      respcode: 1001,
      status: 400,
      data: {}
    }
  } finally {
    if (isSet) {
      connection.release()
    }
  }
}

module.exports = {
  pushToQueue,
  BulkPushToQueueManager,
  SinglePushToQueue
}
