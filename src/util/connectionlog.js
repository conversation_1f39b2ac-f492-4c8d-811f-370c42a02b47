const { basename } = require('path')
const moment = require('moment')

const DAO = require('../lib/dao')
const MySQLWrapper = require('../lib/mysqlWrapper')
const logs = require('./log')

module.exports = class ConnectionLog extends DAO {
  get TABLE_NAME () {
    return 'ma_connection_logs'
  }

  get PRIMARY_KEY () {
    return 'ma_connection_logs_id'
  }

  /**
   * Creates a new Connection if connection passed in params is undefined.
   * @param {mysqlConnection} con
   * @returns
   */
  static async getConnection (con) {
    const temp = !con
    const connection = con || await MySQLWrapper.getConnectionFromPool()
    const logid = temp && await this.create({ connection, connection_type: 'mysql' })
    return [temp, connection, logid]
  }

  /**
   * @param {{ connection_type: 'mysql'|'mysqlread'|'mariadb', connection: *}} param
   */
  static async create ({ connection_type, connection: con }) {
    return true
    /* logs.logger({ pagename: basename(__filename), action: 'create', type: 'request', fields: { connection_type } })

    const [isTempConnection, connection, logid] = await this.getConnection(con)

    try {
      const stack = (new Error()).stack.substring(6).split('\n')
      let index = 1
      console.log({ stack, __process: stack[1].indexOf('at process') })
      if (stack[1].indexOf('at process') >= 0) index = 2
      const function_name = stack[index].substring(stack[index].indexOf('at') + 3, stack[index].indexOf('(') - 1)
      const file_name = stack[index].substring(stack[index].lastIndexOf('\\') + 1).substring(0, stack[index].substring(stack[index].lastIndexOf('\\') + 1).indexOf(':'))
      const newStack = [...stack]
      const thread_id = !isTempConnection && connection.threadId
      newStack.shift()
      console.log({ stacktrace: newStack.join('\n').trim(), function_name, file_name, connection_type })

      const insertQuery = 'INSERT INTO ma_connection_logs (stacktrace, function_name, file_name, connection_type, thread_id) VALUES ? '
      const result = await this.secureRawQuery(insertQuery, { connection, params: [[[newStack.join('\n').trim(), function_name, file_name, connection_type, thread_id]]] })
      return result.insertId
    } catch (err) {
      logs.logger({ pagename: basename(__filename), action: 'create', type: 'error', fields: err })
    } finally {
      if (isTempConnection) {
        await this.release({ logid, connection })
        connection.release()
      }
    } */
  }

  /**
   * @param {{ logid: number, connection: * }} param
   */
  static async release ({ logid, connection: con }) {
    return true
    /* logs.logger({ pagename: basename(__filename), action: 'release', type: 'request', fields: { logid } })

    if (isNaN(+logid)) return

    const [isTempConnection, connection, _logid] = await this.getConnection(con)

    try {
      const insertQuery = 'UPDATE ma_connection_logs SET released_on = ? WHERE ma_connection_logs_id = ?'
      const result = await this.secureRawQuery(insertQuery, { connection, params: [moment().format('YYYY-MM-DD HH:mm:ss'), logid] })
      return result.id
    } catch (err) {
      logs.logger({ pagename: basename(__filename), action: 'release', type: 'error', fields: err })
    } finally {
      if (isTempConnection) {
        await this.release({ logid: _logid, connection })
        connection.release()
      }
    } */
  }
}
