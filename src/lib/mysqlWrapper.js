const mySQLConnector = require('./mysqlConnector')

module.exports = class MySQLWrapper {
  /**
     *
     *
     * Queries the database
     * @param {String} query - The query itself
     * @param {Array} params - The parameters to be passed to MySQL
     * @returns {Promise} - A promise to a query result
     *
     */
  static createQuery ({ query, params }) {
    console.log('createQuery', query, params)
    return new Promise((resolve, reject) => {
      mySQLConnector.pool.getConnection((err, connection) => {
        // If an error was passed getting a connection, fails the promise sending it to the caller
        if (err) {
          console.log('createQueryConnectionErr', err)
          return reject(err)
        }

        // Runs the query
        connection.query(query, params, (err, rows) => {
          // Releases the connection
          connection.release()

          // If an error was passed running the query, fails the promise sending it to the caller
          if (err) {
            console.log('createQueryErr', err)
            return reject(err)
          }

          console.log('createQueryRow', rows)
          // Fulfills the promise
          return resolve(rows)
        })
      })
    })
  }

  /**
     *
     *
     * Runs a transactional query
     * @param {MySQL.Connection} connection - The connection whose transaction will be used
     * @param {String} query - The query itself
     * @param {Array} params - The parameters to be passed to MySQL
     * @returns {Promise} - A promise to a query result
     *
     */
  static createTransactionalQuery ({ query, params, connection }) {
    return new Promise((resolve, reject) => {
      connection.query(query, params, (err, rows) => {
        // If an error was passed running the query, fails the promise sending it to the caller
        if (err) {
          console.log('createTransactionalQueryError', err)
          return reject(err)
        }

        // Fulfills the promise
        return resolve(rows)
      })
    })
  }

  /**
     *
     *
     * Rollbacks a transaction
     * @param {MySQL.Connection} connection - The connection whose transaction will be rollbacked
     * @returns {Promise} - A promise to the rollback
     *
     */
  static rollback (connection) {
    return new Promise((resolve, reject) => {
      try {
        connection.rollback(() => resolve())
      } catch (e) {
        return reject(e)
      }
    })
  }

  /**
     *
     *
     * Commits a transaction
     * @param {MySQL.Connection} connection - The connection whose transaction will be commited
     * @returns {Promise} - A promise to the commit
     *
     */
  static commit (connection) {
    return new Promise((resolve, reject) => {
      try {
        connection.commit(err => {
          if (err) {
            return connection.rollback(connection, err)
          }

          return resolve()
        })
      } catch (e) {
        return reject(e)
      }
    })
  }

  /**
     *
     *
     * Retrieves a connection from the pool to be used in transactions
     * @param {MySQL.Connection} connection - A connection from the pool
     *
     */
  static getConnectionFromPool () {
    return new Promise((resolve, reject) => {
      mySQLConnector.pool.getConnection((err, connection) => {
        // Fails the promise if a connection cannot be retrieved
        if (err) {
          return reject(err)
        }

        console.log('[PoolConnectionAdded]_[' + process.env.AWS_LAMBDA_FUNCTION_NAME + ']_[' + connection.threadId + ']')
        // Returns a conncetion
        return resolve(connection)
      })
    })
  }

  // Maria db connection Setup
  static getConnectionFromMariaDb () {
    return new Promise((resolve, reject) => {
      console.log('<== getConnectionFromMariaDb CALLED ==>')
      const sql = require('./mariadbConnector')
      try {
        const conn = sql.connect()
        return resolve(conn)
      } catch (error) {
        console.log('getConnectionFromMariaDbError>>', error)
        return reject(error)
      }
    })
  }

  /**
     *
     *
     * Begins a new transaction in a connection
     * @param {MySQL.Connection} connection - A connection from the pool
     *
     */
  static beginTransaction (connection) {
    return new Promise((resolve, reject) => {
      connection.beginTransaction(err => {
        // Fails the promise if the transaction cannot be opened
        if (err) {
          return reject(err)
        }

        // Fulfills the promise
        return resolve(connection)
      })
    })
  }

  static getConnectionFromReadReplica () {
    return new Promise((resolve, reject) => {
      console.log('<== getConnectionFromReadReplica CALLED ==>')
      const sql = require('./db')
      try {
        const conn = sql.connect()
        return resolve(conn)
      } catch (error) {
        console.log('getConnectionFromReadReplicaError>>', error)
        return reject(error)
      }
    })
  }
}
