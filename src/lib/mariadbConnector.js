const mysql = require('mysql')

// Create a connection to the database
const connectionPro = {
  connect () {
    return new Promise((resolve, reject) => {
      const connection = mysql.createConnection({
        host: process.env.DB_TSM_HOST || '***********',
        user: process.env.DB_TSM_USERNAME || 'noderetail-dw',
        password: process.env.DB_TSM_PASSWORD || 'retN0de*24',
        port: process.env.DB_TSM_PORT || 4000,
        database: process.env.DB_TSM_DATABASE || 'airpayretail',
        debug: process.env.MYSQL_DEBUG_R || false
        // timeout: 600000
      })

      connection.release = connection.end
      connection.on('error', (err) => console.log(`SINGLE CONNECT ERROR #${connection.threadId}`, err))
      connection.on('end', () => console.log(`SINGLE CONNECT #${connection.threadId} END`))

      connection.connect(error => {
        if (error) {
          console.log(`SINGLE ON CONNECT ERROR #${connection.threadId} ERROR`, error)
          return reject(error)
        }
        console.log(`SINGLE ON CONNECT #${connection.threadId} CONNECTED `)
        resolve(connection)
      })
    })
  }
}
module.exports = connectionPro
