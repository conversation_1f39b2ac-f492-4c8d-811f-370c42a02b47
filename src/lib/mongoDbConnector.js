const { MongoClient } = require('mongodb')

class MongoDbConnector {
  static get MONGODB_DB_USER () { return process.env.MONGODB_DB_USER || 'retail_admin' }
  static get MONGODB_DB_NAME () { return process.env.MONGODB_DB_NAME || 'retail_admin' }
  static get MONGODB_DB_PASSWORD () { return process.env.MONGODB_DB_PASSWORD || 'GH2{QPS_#PihAf' }
  static get MONGODB_DB_ADDRESS () { return process.env.MONGODB_DB_ADDRESS || '**********' }
  static get MONGODB_DB_PORT () { return process.env.MONGODB_DB_PORT || 27017 }
  static get MONGODB_DB_POOL_SIZE () { return process.env.MONGODB_DB_POOL_SIZE || 100 }

  /**
   *
   * @returns Promise<MongoClient>
   */
  static async _client () {
    /**
     * MongoParseError: Password contains unescaped characters
     */
    const url = `mongodb://${encodeURIComponent(this.MONGODB_DB_USER)}:${encodeURIComponent(this.MONGODB_DB_PASSWORD)}@${this.MONGODB_DB_ADDRESS}:${this.MONGODB_DB_PORT}/?maxPoolSize=${this.MONGODB_DB_POOL_SIZE}`
    // const url = `mongodb://${this.MONGODB_DB_ADDRESS}:${this.MONGODB_DB_PORT}/?maxPoolSize=${this.MONGODB_DB_POOL_SIZE}` // without username and password uri
    const client = new MongoClient(url)
    await client.connect()
    return client
  }

  /**
   *
   * @returns Promise<MongoClient>
   */
  static async connection () {
    try {
      const client = await this._client()
      const database = client.db(this.MONGODB_DB_NAME)
      return database
    } catch (error) {
      return false
    }
  }

  static async close () {
    const client = await this._client()
    client.close()
  }
}

module.exports = MongoDbConnector
