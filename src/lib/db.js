const mysql = require('mysql')

// Create a connection to the database
const connectionPro = {
  connect () {
    return new Promise((resolve, reject) => {
      const connection = mysql.createConnection({
        host: process.env.MYSQL_DB_ADDRESS_R || 'retailadminstage.ce2w7wvtnruc.ap-south-1.rds.amazonaws.com',
        user: process.env.MYSQL_DB_USER_R || 'retailstage',
        password: process.env.MYSQL_DB_PASSWORD_R || 'Mdw62k^83n',
        port: process.env.MYSQL_PORT_R || 3306,
        database: process.env.MYSQL_DB_NAME_R || 'merchantappuat',
        debug: process.env.MYSQL_DEBUG_R || false
        // timeout: 600000

      })

      connection.release = connection.end
      connection.on('error', (err) => console.log(`SINGLE CONNECT ERROR #${connection.threadId}`, err))
      connection.on('end', () => console.log(`SINGLE CONNECT #${connection.threadId} END`))

      connection.connect(error => {
        if (error) {
          console.log(`SINGLE ON CONNECT ERROR #${connection.threadId} ERROR`, error)
          return reject(error)
        }
        console.log(`SINGLE ON CONNECT #${connection.threadId} CONNECTED `)
        resolve(connection)
      })
    })
  }
}

module.exports = connectionPro
