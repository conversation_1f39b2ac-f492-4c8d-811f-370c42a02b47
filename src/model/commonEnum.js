const { GraphQLEnumType } = require('graphql')

// Used in ledgers for credit and debit
const modeEnumType = new GraphQLEnumType({
  name: 'modeEnumType',
  values: {
    CREDIT: {
      value: 'cr'
    },
    DEBIT: {
      value: 'dr'
    }
  }
})

// Used in accounts for cashload and cash withdraw
const mode = new GraphQLEnumType({
  name: 'TransactionType',
  values: {
    CASHWITHDRAWAL: {
      value: 'CW'
    },
    CASHLOADER: {
      value: 'CL'
    },
    INCENTIVECREDIT: {
      value: 'IN'
    },
    INCENTIVEDEBIT: {
      value: 'RIN'
    }

  }
})

// Status of transaction Failed, Pendind,Success etc
const maStatus = new GraphQLEnumType({
  name: 'Status',
  values: {
    INITIATED: {
      value: 'I'
    },
    SUCCESS: {
      value: 'S'
    },
    REFUNDED: {
      value: 'R'
    },
    FAILED: {
      value: 'F'
    },
    AWAITING_SETTLEMENT: {
      value: 'P'
    },
    REVERSE: {
      value: 'REV'
    },
    ACTUAL: {
      value: 'ACTUAL'
    },
    AUTORETRY: {
      value: 'AP'
    },
    PARTIALSUCCESS: {
      value: 'PS'
    },
    PENDING: {
      value: 'PND'
    },
    VOID: {
      value: 'V'
    },
    PROCESSING: {
      value: 'PROC'
    }
  }
})

// Transaction type TOPUP, SENDMONEY etc
const txnEnumType = new GraphQLEnumType({
  name: 'txnEnumType',
  values: {
    CREDITS: {
      value: '1'
    },
    TRANSFERS: {
      value: '2'
    },
    LIMITS_ASSIGNED: {
      value: '3'
    },
    COMMISSION_EARNED: {
      value: '4'
    },
    AEPS: {
      value: '5'
    },
    BBPS: {
      value: '6'
    },
    CASHLOAD: {
      value: 'CL'
    },
    CASHWITHDRAWAL: {
      value: 'CW'
    },
    WITHDRAWAL: {
      value: '7'
    },
    AEPS_INCENTIVE: {
      value: '8'
    },
    BBPS_INCENTIVE: {
      value: '9'
    },
    COLLECTMONEY: {
      value: '10'
    },
    INCENTIVE: {
      value: 'IN'
    },
    REVERSE_INCENTIVE: {
      value: 'RIN'
    },
    TDS: {
      value: '11'
    },
    GST: {
      value: '12'
    },
    CUSTOMER_CHARGES: {
      value: '13'
    },
    BBPS_SURCHARGE: {
      value: '14'
    },
    INSURANCE: {
      value: '15'
    },
    CREDITS_INCENTIVE: {
      value: '16'
    },
    RECHARGE: {
      value: '17'
    },
    RECHARGE_INCENTIVE: {
      value: '18'
    },
    INSURANCE_INCENTIVE: {
      value: '19'
    },
    POS: {
      value: '20'
    },
    BENEFICIARY_VALIDATION: {
      value: '21'
    },
    BENEFICIARY_VALIDATION_INCENTIVE: {
      value: '22'
    },
    HUMAN_ATM: {
      value: '23'
    },
    CMS: {
      value: '24'
    },
    HUMAN_ATM_INCENTIVE: {
      value: '25'
    },
    CMS_INCENTIVE: {
      value: '26'
    },
    ADHOC: {
      value: '27'
    },
    GOLD: {
      value: '28'
    },
    GOLD_INCENTIVE: {
      value: '29'
    },
    WITHDRAWAL_CHARGES: {
      value: '30'
    },
    FASTAG: {
      value: '31'
    },
    FASTAG_INCENTIVE: {
      value: '33'
    },
    FASTAG_RECHARGE: {
      value: '32'
    },
    BAAZAAR: {
      value: '34'
    },
    BAAZAAR_INCENTIVE: {
      value: '35'
    },
    BAAZAAR_DELIVERY_CHARGES: {
      value: '36'
    },
    CASHBACK_PROMOTION: {
      value: '37'
    },
    DMT_KYC_CHARGES: {
      value: '38'
    },
    DMT_KYC_CHARGES_INCENTIVE: {
      value: '39'
    },
    AEPS_STMT: {
      value: '40'
    },
    AEPS_STMT_INCENTIVE: {
      value: '41'
    },
    AEPS_BAL_ENQ: {
      value: '42'
    },
    AEPS_BAL_INCENTIVE: {
      value: '43'
    },
    ONDC: {
      value: '44'
    },
    ONDC_INCENTIVE: {
      value: '45'
    },
    GOVERNMENT_SERVICES: {
      value: '46'
    },
    PROCESSING_CHARGES: {
      value: '47'
    },
    COLLECTMONEY_INCENTIVE: {
      value: '48'
    },
    COLLECTMONEY_SURCHARGE: {
      value: '49'
    },
    /* FMT CHANGES */
    FMT: {
      value: '50'
    },
    FMT_INCENTIVE: {
      value: '51'
    },
    FMT_CUSTOMER_CHARGES: {
      value: '52'
    },
    FMT_BENEFICIARY_VALIDATION: {
      value: '53'
    },
    FMT_BENE_VALIDATION_INCENTIVE: {
      value: '54'
    },
    FMT_KYC_CHARGES: {
      value: '55'
    },
    FMT_KYC_INCENTIVE: {
      value: '56'
    },
    FMT_GST_CHARGES: {
      value: '57'
    },
    FMT_BANK_CHARGES: {
      value: '58'
    },
    // 2FA
    AEPS_2FA_CALENDAR_DAY_CHARGE: {
      value: '64'
    },
    AEPS_2FA_GST_CHARGES: {
      value: '65'
    },
    AEPS_2FA_CW_CHARGES: {
      value: '66'
    },
    CHARGEBACK: {
      value: '67'
    },
    CHARGEBACK_REVERSAL: {
      value: '68'
    },
    REFUNDED: {
      value: '69'
    },
    PLATFORM_FEE: {
      value: '70'
    },
    NET_PLATFORM_FEE: {
      value: '71'
    },
    GST_ON_PLATFORM_FEE: {
      value: '72'
    },
    DMT_KYC_GST_CHARGES: {
      value: '73'
    },
    DIGI_KHATA_DMT_PPI: {
      value: '75'
    },
    DIGI_KHATA_DMT_PPI_CUSTOMER_CHARGES: {
      value: '76'
    },
    DIGI_KHATA_BENEFICIARY_VALIDATION: {
      value: '77'
    },
    DIGI_KHATA_BENEFICIARY_VALIDATION_INCENTIVE: {
      value: '78'
    },
    PPI_CUSTOMER_KYC_CHARGES: {
      value: '79'
    },
    PPI_CUSTOMER_KYC_GST_CHARGES: {
      value: '80'
    },
    // SOUNDBOX_ACTIVATION: {
    //   value: '81'
    // }
    SIM_RENEWAL_CHARGES: {
      value: '81'
    },
    CASH_WITHDRAWAL_FEE: {
      value: '83'
    },
    NET_CASH_WITHDRAWAL_FEE: {
      value: '84'
    },
    GST_ON_CASH_WITHDRAWAL_FEE: {
      value: '85'
    }
  }
})

// User type Distributer, Retailer etc
const userType = new GraphQLEnumType({
  name: 'UserType',
  values: {
    SUPERDISTRIBUTOR: {
      value: 'SD'
    },
    DISTRIBUTOR: {
      value: 'DT'
    },
    RETAILER: {
      value: 'RT'
    },
    USER: {
      value: 'UR'
    },
    RESELLER: {
      value: 'RS'
    },
    SUPERDISTRIBUTORUSER: {
      value: 'SDT'
    }
  }
})

// User status as active, locked etc
const userStatus = new GraphQLEnumType({
  name: 'UserStatus',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    },
    LOCKED: {
      value: 'L'
    },
    OFFLINE: {
      value: 'O'
    },
    DELETED: {
      value: 'D'
    }
  }
})

// Wallet type as topup, bbps, aeps,collect money
const walletType = new GraphQLEnumType({
  name: 'Wallet',
  values: {
    TOPUP: {
      value: '1'
    },
    COLLECTMONEY: {
      value: '2'
    },
    AEPS: {
      value: '3'
    },
    BBPS: {
      value: '4'
    }
  }
})

// Account type as saving or current
const accountType = new GraphQLEnumType({
  name: 'AccountType',
  values: {
    SAVINGS: {
      value: 'S'
    },
    CURRENT: {
      value: 'C'
    }
  }
})

// Bank status as active or inactive
const bankStatus = new GraphQLEnumType({
  name: 'BankStatus',
  values: {
    ACTIVE: {
      value: 'Y'
    },
    INACTIVE: {
      value: 'N'
    }
  }
})

// Wallet balances will check with this category as Points to cash has limitation over wallet
const category = new GraphQLEnumType({
  name: 'Withdrawal_or_Transaction',
  values: {
    TRANSACTION: {
      value: '1'
    },
    WITHDRAW: {
      value: '2'
    }
  }
})

// Recon status of transaction as YES and NO
const reconStatus = new GraphQLEnumType({
  name: 'ReconStatus',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    }
  }
})

// Commission type such as TDS or general commission
const deductionType = new GraphQLEnumType({
  name: 'deductionType',
  values: {
    TDS: {
      value: '1'
    },
    GENERAL: {
      value: '2'
    }
  }
})

// Commission applied type such as fixed, percentage or both
const appliedTypeEnum = new GraphQLEnumType({
  name: 'appliedTypeEnum',
  values: {
    FIXED: {
      value: '1'
    },
    PERCENTAGE: {
      value: '2'
    },
    BOTH: {
      value: '3'
    }
  }
})

const orderType = new GraphQLEnumType({
  name: 'orderTypeEnum',
  values: {
    ALL: {
      value: 'ALL'
    },
    CASH: {
      value: 'CASH'
    },
    POINTS: {
      value: 'POINTS'
    }

  }
})

const customerStatus = new GraphQLEnumType({
  name: 'customerStatus',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    },
    LOCKED: {
      value: 'L'
    },
    OFFLINE: {
      value: 'O'
    },
    DELETED: {
      value: 'D'
    }
  }
})

const beneStatus = new GraphQLEnumType({
  name: 'beneStatus',
  values: {
    REGISTERED: {
      value: 'Y'
    },
    NOT_REGISTERED: {
      value: 'N'
    },
    ALREADY_REGISTERED: {
      value: 'P'
    }
  }
})

const migrationStatus = new GraphQLEnumType({
  name: 'migrationStatus',
  values: {
    DONE: {
      value: 'Y'
    },
    NOT_DONE: {
      value: 'N'
    },
    ERROR: {
      value: 'E'
    },
    PENDING: {
      value: 'P'
    }
  }
})

const kycStatus = new GraphQLEnumType({
  name: 'kycStatus',
  values: {
    APPROVED: {
      value: 'V'
    },
    PENDING: {
      value: 'NV'
    },
    REJECTED: {
      value: 'R'
    },
    APPROVALPENDING: {
      value: 'I'
    },
    PARTIALAPPROVED: {
      value: 'PA'
    },
    PARTIALREJECTED: {
      value: 'PR'
    }
  }
})

// KYC Approval Flag Enum
const kycApprovalEnum = new GraphQLEnumType({
  name: 'kycApprovalEnum',
  values: {
    INITIATED: {
      value: 'I'
    },
    APPROVED: {
      value: 'A'
    },
    REJECT: {
      value: 'R'
    },
    HOLD: {
      value: 'H'
    }
  }
})

const benListFlag = new GraphQLEnumType({
  name: 'benListFlag',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    }
  }
})

const searchType = new GraphQLEnumType({
  name: 'searchType',
  values: {
    NAME: {
      value: 'name'
    },
    ACCOUNTNO: {
      value: 'acc_no'
    },
    MOBILENO: {
      value: 'mob_no'
    }
  }
})

// KYC Document type
const kycDocumentEnum = new GraphQLEnumType({
  name: 'kycDocumentEnum',
  values: {
    PAN: {
      value: 1
    },
    AADHAR: {
      value: 2
    },
    PHOTO: {
      value: 3
    }
  }
})

// KYC OTP HANDLER
const kycOtpEnum = new GraphQLEnumType({
  name: 'kycOtpHandlerEnum',
  values: {
    SEND: {
      value: 1
    },
    RESEND: {
      value: 2
    },
    VERIFY: {
      value: 3
    },
    PAYMENTMODE: {
      value: 4
    }
  }
})

// KYC OTP TYPE
const kycOtpTypeEnum = new GraphQLEnumType({
  name: 'kycOtpTypeEnum',
  values: {
    MIGRATE: {
      value: 'MN'
    },
    KYC: {
      value: 'KYC'
    }
  }
})

const ticketTypeEnum = new GraphQLEnumType({
  name: 'ticketTypeEnum',
  values: {
    MIGRATENUMBER: {
      value: 1
    },
    ACCOUNTCHANGE: {
      value: 2
    }
  }
})

const ticketStatusEnum = new GraphQLEnumType({
  name: 'ticketStatusEnum',
  values: {
    RAISED: {
      value: 1
    },
    INPROCESS: {
      value: 2
    },
    COMPLETED: {
      value: 3
    },
    REJECTED: {
      value: 4
    },
    ASSIGNED: {
      value: 5
    }
  }
})

const supportTicketStatusEnum = new GraphQLEnumType({
  name: 'supportticketStatusEnum',
  values: {
    RAISED: {
      value: 'R'
    },
    PROGRESS: {
      value: 'P'
    },
    COMPLETED: {
      value: 'C'
    }
  }
})

// Transfer Mode for transaction In IMPS, NEFT, RTGS
const transferMode = new GraphQLEnumType({
  name: 'TransferMode',
  values: {
    NEFT: {
      value: 'NEFT'
    },
    IMPS: {
      value: 'IMPS'
    }
  }
})

// Commission Report Type
const commissionReportType = new GraphQLEnumType({
  name: 'CommissionReportType',
  values: {
    GLOBAL: {
      value: '1'
    },
    GENERAL: {
      value: '2'
    }
  }
})

const noticeType = new GraphQLEnumType({
  name: 'NoticeType',
  values: {
    GENERAL: {
      value: 'GEN'
    },
    MERCHANTWISE: {
      value: 'MW'
    }
  }
})

const displayType = new GraphQLEnumType({
  name: 'DisplayType',
  values: {
    NOTICE: {
      value: '1'
    },
    MARQUEE: {
      value: '2'
    },
    DOWNTIME: {
      value: '3'
    }
  }
})

const noticeFlag = new GraphQLEnumType({
  name: 'NoticeFlag',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    }
  }
})

const operatorCategory = new GraphQLEnumType({
  name: 'OperatorCategory',
  values: {
    PREPAID: {
      value: '1'
    },
    POSTPAID: {
      value: '2'
    }
  }
})

const providerStatus = new GraphQLEnumType({
  name: 'ProviderStatus',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    }
  }
})

const beneVerifyStatus = new GraphQLEnumType({
  name: 'beneVerifyStatus',
  values: {
    UNVERIFIED: {
      value: 'U'
    },
    VERIFIED: {
      value: 'S'
    },
    FAILED: {
      value: 'F'
    },
    PENDING: {
      value: 'P'
    },
    INITIATED: {
      value: 'I'
    }
  }
})

const AEPSbankStatus = new GraphQLEnumType({
  name: 'AEPSbankStatus',
  values: {
    ACTIVE: {
      value: 'ACTIVE'
    },
    INACTIVE: {
      value: 'INACTIVE'
    }
  }
})

const remitterStatus = new GraphQLEnumType({
  name: 'remitterStatus',
  values: {
    ACTIVE: {
      value: 'ACTIVE'
    },
    INACTIVE: {
      value: 'INACTIVE'
    }
  }
})

const khataFilterType = new GraphQLEnumType({
  name: 'khataFilterType',
  values: {
    MERCHANTWISE: {
      value: 'MERCHANTWISE'
    },
    USERWISE: {
      value: 'USERWISE'
    }
  }
})

const khataCreditType = new GraphQLEnumType({
  name: 'khataCreditType',
  values: {
    CREDIT_GIVEN: {
      value: 'CG'
    },
    CREDIT_RECEIVED: {
      value: 'CR'
    },
    ZERO_CREDIT: {
      value: 'ZC'
    }
  }
})

const cmsMerchantList = new GraphQLEnumType({
  name: 'cmsMerchantList',
  values: {
    DIRECT: {
      value: 'D'
    },
    SUB: {
      value: 'S'
    },
    PARENT: {
      value: 'P'
    }
  }
})

const barcodeFilterType = new GraphQLEnumType({
  name: 'barcodeFilterType',
  values: {
    BARCODE: {
      value: 'BARCODE'
    },
    TAG: {
      value: 'TAG'
    }
  }
})

const ftCategory = new GraphQLEnumType({
  name: 'ftCategory',
  values: {
    COMMERCIAL: {
      value: 'C'
    },
    NON_COMMERCIAL: {
      value: 'NC'
    }
  }
})

const goldGenderType = new GraphQLEnumType({
  name: 'goldGenderType',
  values: {
    MALE: {
      value: 'm'
    },
    FEMALE: {
      value: 'f'
    },
    OTHER: {
      value: 'o'
    }
  }
})

const goldDocumentType = new GraphQLEnumType({
  name: 'goldDocumentType',
  values: {
    AADHAR: {
      value: 'maskedAadhaar'
    },
    PANCARD: {
      value: 'pan'
    },
    PASSPORT: {
      value: 'passport'
    },
    DRIVINGLICENSE: {
      value: 'drivingLicense'
    },
    VOTERCARD: {
      value: 'voterCard'
    },
    NREGACARD: {
      value: 'nregaCard'
    }
  }
})

const goldRateType = new GraphQLEnumType({
  name: 'goldRateType',
  values: {
    BUY: {
      value: 'buy'
    },
    SELL: {
      value: 'sell'
    }
  }
})

const goldBuyType = new GraphQLEnumType({
  name: 'goldBuyType',
  values: {
    AMOUNT: {
      value: 'FixedAmount'
    },
    WEIGHT: {
      value: 'FixedWeight'
    }
  }
})

const beneBoardingStatus = new GraphQLEnumType({
  name: 'beneBoardingStatus',
  values: {
    ERROR: {
      value: 'E'
    },
    REGISTERED: {
      value: 'S'
    },
    FAILED: {
      value: 'F'
    },
    PENDING: {
      value: 'P'
    },
    INITIATED: {
      value: 'I'
    },
    DELETED: {
      value: 'D'
    },
    API_ERROR: {
      value: 'AE'
    },
    UNREGISTERED: {
      value: 'U'
    }
  }
})

const remitterBoardingStatus = new GraphQLEnumType({
  name: 'remitterBoardingStatus',
  values: {
    ERROR: {
      value: 'E'
    },
    REGISTERED: {
      value: 'S'
    },
    FAILED: {
      value: 'F'
    },
    PENDING: {
      value: 'P'
    },
    INITIATED: {
      value: 'I'
    },
    DELETED: {
      value: 'D'
    },
    API_ERROR: {
      value: 'AE'
    },
    UNREGISTERED: {
      value: 'U'
    }
  }
})

const bankOTPRequired = new GraphQLEnumType({
  name: 'bankOTPRequired',
  values: {
    YES: {
      value: 'YES'
    },
    NO: {
      value: 'NO'
    }
  }
})

const biometricKycType = new GraphQLEnumType({
  name: 'biometricKycType',
  values: {
    YES: {
      value: 'Y'
    },
    NO: {
      value: 'N'
    }
  }
})

const ekycStatus = new GraphQLEnumType({
  name: 'ekycStatus',
  values: {
    INITIATED: {
      value: 'I'
    },
    PENDING: {
      value: 'P'
    },
    SUCCESS: {
      value: 'S'
    },
    FAILURE: {
      value: 'F'
    },
    COMPLETED: {
      value: 'C'
    }
  }
})

const requestCreditStatus = new GraphQLEnumType({
  name: 'requestCreditStatus',
  values: {
    PENDING: {
      value: 'P'
    },
    APPROVED: {
      value: 'A'
    },
    REJECTED: {
      value: 'R'
    },
    EXPIRED: {
      value: 'E'
    }
  }
})

const shoppingCurrentStatus = new GraphQLEnumType({
  name: 'shoppingCurrentStatus',
  values: {
    INITIATED: {
      value: 'I'
    },
    PENDING: {
      value: 'P'
    },
    SUCCESS: {
      value: 'S'
    },
    FAILED: {
      value: 'F'
    },
    REVERSE: {
      value: 'REV'
    },
    FASTAG_ASSIGNED: {
      value: 'A'
    },
    SHIPPED: {
      value: 'SP'
    }
  }
})

const shoppingShippingStatus = new GraphQLEnumType({
  name: 'shoppingShippingStatus',
  values: {
    INITIATED: {
      value: 'I'
    },
    CONFIRMED: {
      value: 'C'
    },
    CANCELLED: {
      value: 'CAN'
    },
    DELIVERED: {
      value: 'D'
    }
  }
})

const requestWithdrawalStatus = new GraphQLEnumType({
  name: 'requestWithdrawalStatus',
  values: {
    COMPLETED: {
      value: 'C'
    },
    PENDING: {
      value: 'P'
    },
    REJECTED: {
      value: 'R'
    },
    INITIATED: {
      value: 'I'
    },
    APPROVED: {
      value: 'A'
    },
    UNDERPROCESSING: {
      value: 'U'
    },
    ALL: {
      value: 'ALL'
    }
  }
})

const goldAgentDeviceType = new GraphQLEnumType({
  name: 'agentDeviceType',
  values: {
    APP: {
      value: 'App'
    },
    WEB: {
      value: 'Web'
    }
  }
})

const kycRegistration = new GraphQLEnumType({
  name: 'kycRegistrationType',
  values: {
    PAN: {
      value: 'PAN'
    },
    BIO: {
      value: 'BIO'
    }
  }
})

const transactionTypes = new GraphQLEnumType({
  name: 'transactionType',
  values: {
    TRANSACTION_HISTORY: {
      value: 'TRANSACTION_HISTORY'
    },
    LEDGER_HISTORY: {
      value: 'LEDGER_HISTORY'
    }
  }
})

const deviceType = new GraphQLEnumType({
  name: 'deviceType',
  values: {
    APP: {
      value: 'APP'
    },
    WEB: {
      value: 'WEB'
    },
    BOTH: {
      value: 'BOTH'
    }
  }
})

const intraTransfersSourceEnum = new GraphQLEnumType({
  name: 'intraTransfersSourceEnum',
  values: {
    BALANCE_TRANSFER: {
      value: 'BALANCE_TRANSFER'
    },
    REQUEST_CREDIT: {
      value: 'REQUEST_CREDIT'
    },
    WITHDRAWAL: {
      value: 'WITHDRAWAL'
    }
  }
})

// MERCHANT DASHBOARD
const merchantDashboardEnum = new GraphQLEnumType({
  name: 'merchantDashboardEnum',
  values: {
    DAILY: {
      value: 'DAILY'
    },
    MONTHLY: {
      value: 'MONTHLY'
    },
    WEEKLY: {
      value: 'WEEKLY'
    }
  }
})

// Status of transaction Failed, Pendind,Success
const transacitonHistoryStatus = new GraphQLEnumType({
  name: 'HistoryStatus',
  values: {
    SUCCESS: {
      value: 'S'
    },
    FAILED: {
      value: 'F'
    },
    PENDING: {
      value: 'P'
    },
    INITIATED: {
      value: 'I'
    },
    PARTIALSUCCESS: {
      value: 'PS'
    }
  }
})

// ADHAAR PAY: COLLECT MONEY
const collectMoneyType = new GraphQLEnumType({
  name: 'CollectMoneyType',
  values: {
    UPI: {
      value: 'UPI'
    },
    ADHAAR_PAY: {
      value: 'AP'
    }
  }
})

// Status of transaction Failed, Pendind,Success etc
const maTransactionStatus = new GraphQLEnumType({
  name: 'Status',
  values: {
    INITIATED: {
      value: 'I'
    },
    SUCCESS: {
      value: 'S'
    },
    REFUNDED: {
      value: 'R'
    },
    FAILED: {
      value: 'F'
    },
    PENDING: {
      value: 'P'
    },
    REVERSE: {
      value: 'REV'
    },
    PARTIALSUCCESS: {
      value: 'PS'
    },
    VOID: {
      value: 'V'
    }
  }
})

// PMT Static Data
const pmtStaticData = new GraphQLEnumType({
  name: 'PMTStaticData',
  values: {
    Gender: { value: 'Gender' },
    IDType: { value: 'IDType' },
    Nationality: { value: 'Nationality' },
    IncomeSource: { value: 'IncomeSource' },
    Relationship: { value: 'Relationship' },
    PaymentMode: { value: 'PaymentMode' },
    RemittanceReason: { value: 'RemittanceReason' },
    AcPayBankBranchList: { value: 'AcPayBankBranchList' }
  }
})

// ONDC Product Search Type
const ondcSearchType = new GraphQLEnumType({
  name: 'SearchType',
  values: {
    PRODUCT: {
      value: 'PRODUCT'
    },
    CATEGORY: {
      value: 'CATEGORY'
    },
    PROVIDER: {
      value: 'PROVIDER'
    },
    SELLER: {
      value: 'SELLER'
    },
    SUB_CATEGORY_SELLER: {
      value: 'SUB_CATEGORY_SELLER'
    },
    SUB_CATEGORY_PRODUCT: {
      value: 'SUB_CATEGORY_PRODUCT'
    },
    SUB_CATEGORY: {
      value: 'SUB_CATEGORY'
    }
  }
})

// ONDC SORTING TYPES
const ondcSortType = new GraphQLEnumType({
  name: 'SortType',
  values: {
    PRICE_LOW_TO_HIGH: {
      value: 'price-low-to-high'
    },
    PRICE_HIGH_TO_LOW: {
      value: 'price-high-to-low'
    },
    NAME_ASC: {
      value: 'name-asc'
    },
    NAME_DESC: {
      value: 'name-desc'
    }
  }
})

const LinkTypeEnum = new GraphQLEnumType({
  name: 'LinkTypeEnum',
  values: {
    HYPERLINK: { value: 'H' },
    CHANNEL: { value: 'C' }
  }
})

const YesNoEnum = new GraphQLEnumType({
  name: 'YesNoEnum',
  values: {
    Y: { value: 'Y' },
    N: { value: 'N' }
  }
})
module.exports = { modeEnumType, mode, maStatus, txnEnumType, userType, userStatus, walletType, bankStatus, accountType, category, reconStatus, deductionType, appliedTypeEnum, orderType, customerStatus, kycStatus, benListFlag, searchType, kycApprovalEnum, kycDocumentEnum, kycOtpEnum, kycOtpTypeEnum, ticketTypeEnum, ticketStatusEnum, supportTicketStatusEnum, transferMode, commissionReportType, noticeType, displayType, noticeFlag, operatorCategory, providerStatus, beneVerifyStatus, AEPSbankStatus, beneStatus, migrationStatus, khataFilterType, khataCreditType, remitterStatus, cmsMerchantList, barcodeFilterType, ftCategory, goldGenderType, goldDocumentType, goldRateType, goldBuyType, beneBoardingStatus, remitterBoardingStatus, bankOTPRequired, biometricKycType, ekycStatus, requestCreditStatus, shoppingCurrentStatus, shoppingShippingStatus, goldAgentDeviceType, transactionTypes, requestWithdrawalStatus, deviceType, transacitonHistoryStatus, kycRegistration, intraTransfersSourceEnum, merchantDashboardEnum, collectMoneyType, maTransactionStatus, pmtStaticData, ondcSearchType, ondcSortType, LinkTypeEnum, YesNoEnum }
