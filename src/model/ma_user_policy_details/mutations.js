const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLInputObjectType,
  GraphQLList,
  GraphQLBoolean,
  GraphQLEnumType
} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const commonScalar = require('../commonScalar')
const termInsuranceCtrl = require('../../controller/insurance/termInsuranceController')
const { onlyDateScalar } = require('../commonScalar')

const genderEnum = new GraphQLEnumType({
  name: 'GenderEnumSelf',
  values: {
    MALE: {
      value: 'M'
    },
    FEMALE: {
      value: 'F'
    },
    OTHER: {
      value: 'O'
    }
  }
})

const bulkArgsNomineeList = new GraphQLInputObjectType({
  name: 'BulkArgsNomineeList',
  description: 'BulkArgs Nominee list for bulk upload',
  fields: {
    nominee_first_name: { type: new GraphQLNonNull(GraphQLString) },
    nominee_last_name: { type: new GraphQLNonNull(GraphQLString) },
    nominee_dob: { type: new GraphQLNonNull(onlyDateScalar) },
    nominee_gender: { type: new GraphQLNonNull(genderEnum) },
    nominee_relationship: { type: new GraphQLNonNull(GraphQLString) }
  }
})


module.exports = {
  calculatePremium: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_first_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_last_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_gender: {
        type: new GraphQLNonNull(genderEnum)
      },
      customer_dob: {
        type: new GraphQLNonNull(onlyDateScalar)
      },
      policy_code: {
        type: new GraphQLNonNull(GraphQLString)
      },
      state_code: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: termInsuranceCtrl.calculatePremium.bind(termInsuranceCtrl)
  },
  doPolicyTransaction: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_first_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_last_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_gender: {
        type: new GraphQLNonNull(genderEnum)
      },
      customer_dob: {
        type: new GraphQLNonNull(onlyDateScalar)
      },
      policy_code: {
        type: new GraphQLNonNull(GraphQLString)
      },
      state_code: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_email_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      address_one: {
        type: new GraphQLNonNull(GraphQLString)
      },
      address_two: {
        type: new GraphQLNonNull(GraphQLString)
      },
      pincode: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_user_policy_detail_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      nominee_list: {
        type: new GraphQLNonNull(new GraphQLList(new GraphQLNonNull(bulkArgsNomineeList)))
      },
      security_pin: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: termInsuranceCtrl.doPolicyTransaction.bind(termInsuranceCtrl)
  },
  verifyKotakOtp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_policy_detail_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      otp: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: termInsuranceCtrl.verifyKotakOtp.bind(termInsuranceCtrl)
  },
  resentSuccessSMS: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_policy_detail_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: termInsuranceCtrl.resentSuccessSMS.bind(termInsuranceCtrl)
  },
  resentKotakOtp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_policy_detail_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: termInsuranceCtrl.resentKotakOtp.bind(termInsuranceCtrl)
  }
}
