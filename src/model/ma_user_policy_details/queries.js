
const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const termInsuranceCtrl = require('../../controller/insurance/termInsuranceController')

// Defines the queries
module.exports = {
  checkActivePolicy: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      policy_code: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: termInsuranceCtrl.checkActivePolicy.bind(termInsuranceCtrl)
  },
  getPolicyPlans: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: termInsuranceCtrl.getPolicyPlans.bind(termInsuranceCtrl)
  }
}
