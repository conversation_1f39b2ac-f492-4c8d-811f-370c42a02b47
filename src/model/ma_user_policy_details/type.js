const {
  GraphQLID,
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList,
  GraphQLFloat,
  GraphQLBoolean,
  GraphQLEnumType
} = require('graphql')
const commonEnum = require('../commonEnum')
const scalarType = require('../commonScalar')

const genderEnum = new GraphQLEnumType({
  name: 'GenderEnum',
  values: {
    MALE: {
      value: 'M'
    },
    FEMALE: {
      value: 'F'
    },
    OTHER: {
      value: 'O'
    }
  }
})

const policyDaysUnits = new GraphQLEnumType({
  name: 'policyDaysUnits',
  values: {
    DAYS: {
      value: 'D'
    },
    MONTHS: {
      value: 'M'
    },
    YEAR: {
      value: 'Y'
    }
  }
})

const nomineesListData = new GraphQLObjectType({
  name: 'nomineesListData',
  description: 'nominees List Data data List',
  fields: {
    nominee_id: {
      type: GraphQLID
    },
    nominee_first_name: {
      type: GraphQLString
    },
    nominee_last_name: {
      type: GraphQLString
    },
    nominee_dob: {
      type: scalarType.onlyDateScalar
    },
    nominee_gender: {
      type: genderEnum
    },
    nominee_relationship: {
      type: GraphQLString
    },
    ma_user_policy_detail_id: {
      type: GraphQLInt
    }
  }
})

const policyCustomerDetails = new GraphQLObjectType({
  name: 'policyCustomerDetails',
  description: 'policyCustomerDetails List',
  fields: {
    ma_user_policy_detail_id: {
      type: GraphQLID
    },
    customer_salutation_desc: {
      type: GraphQLString
    },
    customer_first_name: {
      type: GraphQLString
    },
    customer_last_name: {
      type: GraphQLString
    },
    customer_gender: {
      type: genderEnum
    },
    customer_dob: {
      type: scalarType.onlyDateScalar
    },
    current_policy_expiry_date: {
      type: scalarType.onlyDateScalar
    },
    next_policy_commence_date: {
      type: scalarType.onlyDateScalar
    },
    customer_email_id: {
      type: GraphQLString
    },
    customer_address_one: {
      type: GraphQLString
    },
    customer_address_two: {
      type: GraphQLString
    },
    customer_state_code: {
      type: GraphQLString
    },
    customer_pincode: {
      type: GraphQLInt
    },
    customer_nominee_list: {
      type: new GraphQLList(nomineesListData)
    }
  }
})

const policystatedata = new GraphQLObjectType({
  name: 'policystatedata',
  description: 'policy state data List',
  fields: {
    state_id: {
      type: GraphQLID
    },
    state_name: {
      type: GraphQLString
    },
    state_code: {
      type: GraphQLString
    }
  }
})

const policyPlans = new GraphQLObjectType({
  name: 'policyPlans',
  description: 'policy plans data List',
  fields: {
    policy_id: {
      type: GraphQLID
    },
    policy_code: {
      type: GraphQLString
    },
    policy_title: {
      type: GraphQLString
    },
    policy_desc: {
      type: GraphQLString
    },
    policy_amount: {
      type: scalarType.amountScalar
    },
    policy_provider: {
      type: GraphQLString
    },
    policy_duration: {
      type: GraphQLInt
    },
    policy_duration_type: {
      type: policyDaysUnits
    }
  }
})

module.exports = new GraphQLObjectType({
  name: 'PolicyDetails',
  description: 'PolicyDetails',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    old_policy_holder: {
      type: GraphQLBoolean
    },
    customer_details: {
      type: policyCustomerDetails
    },
    nominee_list: {
      type: new GraphQLList(nomineesListData)
    },
    policy_state_data: {
      type: new GraphQLList(policystatedata)
    },
    total_premium: {
      type: scalarType.amountScalar
    },
    ma_user_policy_detail_id: {
      type: GraphQLInt
    },
    aggregator_order_id: {
      type: GraphQLString
    },
    age: {
      type: GraphQLInt
    },
    policyPlans: {
      type: new GraphQLList(policyPlans)
    },
    coi: {
      type: GraphQLString
    },
    dogh: {
      type: GraphQLString
    }
  }
})
