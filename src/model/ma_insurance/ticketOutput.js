const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLInt,
  GraphQLID
} = require('graphql')
// const commonEnum = require('../commonEnum')

module.exports = new GraphQLObjectType({
  name: 'TicketOutput',
  description: 'Ticket Listing',
  fields: {
    ma_tickets_id: {
      type: GraphQLID
    },
    /* ticket_type: { // ENUM
      type: commonEnum.ticketTypeOutput
    }, */
    ma_user_id: {
      type: GraphQLInt
    },
    uic: {
      type: GraphQLString
    },
    ticket_message: {
      type: GraphQLString
    },
    action_by: {
      type: GraphQLInt
    },
    ticket_status: { // ENUM
      type: GraphQLString
    },
    /* ticket_status: { // ENUM
      type: commonEnum.ticketStatusEnum
    }, */
    remarks: {
      type: GraphQLString
    }
  }
})
