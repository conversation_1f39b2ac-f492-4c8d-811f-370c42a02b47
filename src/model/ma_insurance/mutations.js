const {
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull,
  DateTime,
  // GraphQLID,
  GraphQLFloat
  // GraphQLList

} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const commonScalar = require('../commonScalar')
const insuranceController = require('../../controller/insurance/insuranceController')
const riskcovrySachetController = require('../../controller/insurance/riskcovrySachetController')
const lombardInsuranceController = require('../../controller/insurance/lombardInsuranceController')
const starHealthInsuranceController = require('../../controller/insurance/starHealthInsuranceController')


// Defines the mutations
module.exports = {
  addInsUser: {
    type,
    args: {
      companyId: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      retailId: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      },
      /* amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      }, */
      pmode: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderId: {
        type: new GraphQLNonNull(GraphQLString)
      },
      provider_name: {
        type: GraphQLString
      },
      cat: {
        type: GraphQLString
      }
    },
    resolve: insuranceController.addInsUser_v2.bind(insuranceController) // new method added changed to version v2
  },
  updateInsRecon: {
    type,
    args: {
      limit: {
        type: GraphQLInt
      }
    },
    resolve: insuranceController.updateInsRecon.bind(insuranceController)
  },
  premiumDeduction: {
    type,
    args: {
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      banktxnid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      checksum: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: insuranceController.premiumDeduction.bind(insuranceController)
  },
  policyConfirmation: {
    type,
    args: {
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      banktxnid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      banktxntime: {
        type: GraphQLString
      },
      policystatus: {
        type: new GraphQLNonNull(GraphQLString)
      },
      policytype: {
        type: new GraphQLNonNull(GraphQLString)
      },
      checksum: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: insuranceController.policyConfirmation_v2.bind(insuranceController) // new method added changed to version v2
  },
  orderVerification: {
    type,
    args: {
      limit: {
        type: GraphQLInt
      }
    },
    resolve: insuranceController.orderVerification.bind(insuranceController)
  },
  riskcovrySachetInitiateTransaction: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      category_code: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      source: {
        type: new GraphQLNonNull(GraphQLString)
      },
      security_pin: {
        type: GraphQLString
      },
      proposer_mobile: {
        type: GraphQLString
      }
    },
    resolve: riskcovrySachetController.riskcovrySachetInitiateTransaction.bind(riskcovrySachetController)
  },
  riskcovryCreateOrder: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      form_data: {
        type: GraphQLString
      },
      category_name: {
        type: GraphQLString
      },
      category_code: {
        type: GraphQLString
      },
      expiry_period: {
        type: GraphQLInt
      },
      source: {
        type: GraphQLString
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      }
    },
    resolve: riskcovrySachetController.riskcovryCreateOrder.bind(riskcovrySachetController)
  },
  lombardCreateOrder: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      form_data: {
        type: GraphQLString
      },
      category_name: {
        type: GraphQLString
      },
      category_code: {
        type: GraphQLString
      },
      expiry_period: {
        type: GraphQLInt
      },
      source: {
        type: GraphQLString
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      }
    },
    resolve: lombardInsuranceController.lombardCreateOrder.bind(lombardInsuranceController)
  },
  iciciLombardSachetInitiateTransaction: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      category_code: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      source: {
        type: new GraphQLNonNull(GraphQLString)
      },
      security_pin: {
        type: GraphQLString
      },
      proposer_mobile: {
        type: GraphQLString
      }
    },
    resolve: lombardInsuranceController.iciciLombardSachetInitiateTransaction.bind(lombardInsuranceController)
  },
  starHealthCreateOrder: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      form_data: {
        type: GraphQLString
      },
      category_name: {
        type: GraphQLString
      },
      category_code: {
        type: GraphQLString
      },
      expiry_period: {
        type: GraphQLInt
      },
      source: {
        type: GraphQLString
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      }
    },
    resolve: starHealthInsuranceController.starHealthCreateOrder.bind(starHealthInsuranceController)
},
  starHealthSachetInitiateTransaction: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      category_code: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      source: {
        type: new GraphQLNonNull(GraphQLString)
      },
      security_pin: {
        type: GraphQLString
      },
      proposer_mobile: {
        type: GraphQLString
      }
    },
    resolve: starHealthInsuranceController.starHealthSachetInitiateTransaction.bind(starHealthInsuranceController)
}
}
