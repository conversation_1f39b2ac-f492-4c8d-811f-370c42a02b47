const {
  GraphQLID,
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLDateTime,
  GraphQLList,
  GraphQLBoolean
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')
const commonEnum = require('../commonEnum')
const scalarType = require('../commonScalar')
// const ticketOutput = require('./ticketOutput')
// const ticketTypeOutput = require('./ticketTypeOutput')
/* const bookType = new GraphQLObjectType({
  name: 'dddd',
  fields: {
    orderid: { type: GraphQLString }
    //demo : { type: GraphQLString }
  }
}) */

const insuranceProviderList = new GraphQLObjectType({
  name: 'InsuranceList',
  description: 'Insurance Category List Response',
  fields: {
    category_code: {
      type: GraphQLString
    },
    category_name: {
      type: GraphQLString
    },
    provider_id: {
      type: GraphQLInt
    },
    category_id: {
      type: GraphQLString
    },
    app_icon: {
      type: GraphQLString
    },
    web_icon: {
      type: GraphQLString
    },
    redirection: {
      type: GraphQLBoolean
    },
    type: {
      type: GraphQLString
    },
    amount: {
      type: scalarType.amountScalar
    },
    expiry_period: {
      type: GraphQLString
    },
    description: {
      type: GraphQLString
    },
    policy_name: {
      type: GraphQLString
    },
    validity: {
      type: GraphQLString
    },
    policy_wordings: {
      type: GraphQLString
    }
  }
})

const activePolicy = new GraphQLObjectType({
  name: 'ActivePolicy',
  description: 'Active Policy',
  fields: {
    category_name: {
      type: GraphQLString
    },
    category_code: {
      type: GraphQLString
    },
    category_type: {
      type: GraphQLString
    },
    category_flag: {
      type: GraphQLString
    },
    amount: {
      type: GraphQLInt
    },
    expiry_period: {
      type: GraphQLInt
    },
    description: {
      type: GraphQLString
    },
    policy_name: {
      type: GraphQLString
    },
    validity: {
      type: GraphQLString
    },
    app_icon: {
      type: GraphQLString
    },
    web_icon: {
      type: GraphQLString
    },
    tncUrl: {
      type: GraphQLString
    },
    policy_wordings: {
      type: GraphQLString
    }
  }
})

const insurance = new GraphQLObjectType({
  name: 'Insurance',
  description: 'Insurance response type',
  fields: {
    message: {
      type: GraphQLString
    },
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    orderid: {
      type: GraphQLString
    },
    referenceid: {
      type: GraphQLInt
    },
    companyid: {
      type: GraphQLInt
    },
    pmode: {
      type: GraphQLInt
    },
    balance: {
      type: GraphQLString
    },
    url: {
      type: GraphQLString
    },
    transaction_status: {
      type: commonEnum.maStatus
    },
    insurancelist: {
      type: GraphQLList(insuranceProviderList)
    },
    activePolicy: {
      type: GraphQLList(activePolicy)
    },
    dynamicForm: {
      type: GraphQLJSON
    },
    policyDetails: {
      type: GraphQLJSON
    },
    refid: {
      type: GraphQLString
    },
    tncUrl: {
      type: GraphQLString
    },
    coi: {
      type: GraphQLString
    },
    name: {
      type: GraphQLString
    },
    policy_name: {
      type: GraphQLString
    },
    amount: {
      type: scalarType.amountScalar
    },
    policy_number: {
      type: GraphQLString
    },
    transaction_time: {
      type: GraphQLString
    },
    expiry_date: {
      type: GraphQLString
    },
    logo: {
      type: GraphQLString
    },
    customer_mobile: {
      type: GraphQLString
    }
    /* dddd :{
      type: [GraphQLString]
    }, */
  }
})

module.exports = insurance
