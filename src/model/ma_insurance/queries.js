const {
  GraphQLNonNull,
  GraphQLInt,
  GraphQLString
} = require('graphql')
const type = require('./type')
const ticketsController = require('../../controller/tickets/ticketsController')
const insuranceController = require('../../controller/insurance/insuranceController')
const riskcovrySachetController = require('../../controller/insurance/riskcovrySachetController')
const lombardInsuranceController = require('../../controller/insurance/lombardInsuranceController')
const starHealthInsuranceController = require('../../controller/insurance/starHealthInsuranceController')

// Defines the queries
module.exports = {
  getInsuranceList: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: insuranceController.getInsuranceList.bind(insuranceController)
  },
  getTicketsListing: {
    type,
    args: {
      uic: {
        type: GraphQLString
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      }

    },
    resolve: ticketsController.getTicketsListing.bind(ticketsController)
  },
  getTicketTypeList: {
    type: type,
    args: {
    },
    resolve: ticketsController.getTicketTypeList.bind(ticketsController)
  },
  checkActivePolicies: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      },
      proposer_mobile: {
        type: GraphQLString
      }
    },
    resolve: riskcovrySachetController.checkActivePolicy.bind(riskcovrySachetController)
  },
  getDynamicForm: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      },
      proposer_mobile: {
        type: GraphQLString
      },
      orderid: {
        type: GraphQLString
      },
      category_code: {
        type: GraphQLString
      }
    },
    resolve: riskcovrySachetController.getDynamicForm.bind(riskcovrySachetController)
  },
  riskcovryTransactionDetails: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_transaction_master_id: {
        type: GraphQLInt
      }
    },
    resolve: riskcovrySachetController.riskcovryTransactionDetails.bind(riskcovrySachetController)
  },
  iciciLombardGetDynamicForm: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      source: {
        type: new GraphQLNonNull(GraphQLString)
      },
      proposer_mobile: {
        type: GraphQLString
      },
      orderid: {
        type: GraphQLString
      },
      category_code: {
        type: GraphQLString
      }
    },
    resolve: lombardInsuranceController.iciciLombardGetDynamicForm.bind(lombardInsuranceController)
  },
  lombardTransactionDetails: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_transaction_master_id: {
        type: GraphQLInt
      }
    },
    resolve: lombardInsuranceController.lombardTransactionDetails.bind(lombardInsuranceController)
  },
  iciciLombardCheckActivePolicy: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: GraphQLInt
      },
      proposer_mobile: {
        type: GraphQLString
      }
    },
    resolve: lombardInsuranceController.iciciLombardCheckActivePolicy.bind(lombardInsuranceController)
   },
  starHealthGetDynamicForm: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      source: {
        type: new GraphQLNonNull(GraphQLString)
      },
      proposer_mobile: {
        type: GraphQLString
      },
      orderid: {
        type: GraphQLString
      },
      category_code: {
        type: GraphQLString
      }
    },
    resolve: starHealthInsuranceController.starHealthGetDynamicForm.bind(starHealthInsuranceController)
   },


}
