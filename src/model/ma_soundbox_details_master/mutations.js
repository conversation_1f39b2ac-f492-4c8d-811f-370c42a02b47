const { GraphQLString, GraphQLInt, GraphQLNonNull } = require('graphql')
const type = require('./type')
const SoundBoxManagementController = require('../../controller/soundBoxManagement/soundBoxManagementController')

module.exports = {
  registerDevice: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      device_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      vpa_id: {
        type: GraphQLString
      }
    },
    resolve: SoundBoxManagementController.registerDevice.bind(SoundBoxManagementController)
  },
  unregisterDevice: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      device_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: SoundBoxManagementController.unregisterDevice.bind(SoundBoxManagementController)
  },
  updateSelectedTransactionType: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      key: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: SoundBoxManagementController.updateSelectedTransactionType.bind(SoundBoxManagementController)
  },
  activateNewSim: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      sim_number: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: SoundBoxManagementController.activateNewSim.bind(SoundBoxManagementController)
  },
  simRenewalTransaction: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sim_no: {
        type: new GraphQLNonNull(GraphQLString)
      },
      security_pin:{
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: SoundBoxManagementController.simCardRenewalTransaction.bind(SoundBoxManagementController)
  }
}
