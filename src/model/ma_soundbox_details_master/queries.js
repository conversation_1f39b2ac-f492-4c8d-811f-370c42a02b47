const { GraphQLInt, GraphQLNonNull, GraphQLString } = require('graphql')
const type = require('./type')
const SoundBoxManagementController = require('../../controller/soundBoxManagement/soundBoxManagementController')

module.exports = {
  getDeviceList: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: SoundBoxManagementController.getDeviceList.bind(SoundBoxManagementController)
  },
  getSoundBoxQrCodeForUpi: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: SoundBoxManagementController.getSoundBoxQrCodeForUpi.bind(SoundBoxManagementController)
  },
  fetchSelectedTransactionType: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: SoundBoxManagementController.fetchSelectedTransactionType.bind(SoundBoxManagementController)
  },
  checkSimActivationStatus: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      simCard_no: {
        type: (GraphQLString)
      }
    },
    resolve: SoundBoxManagementController.checkSimActivationStatus.bind(SoundBoxManagementController)
  },
  simRenewalReceiptDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: (GraphQLString)
      }
    },
    resolve: SoundBoxManagementController.simRenewalReceiptDetails.bind(SoundBoxManagementController)
  }
}
