const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList,
  GraphQLBoolean,
  GraphQLFloat
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')

const deviceDetail = new GraphQLObjectType({
  name: 'DeviceDetail',
  description: 'SoundBox Management Device Detail',
  fields: {
    device_id: { type: GraphQLString },
    vpa_id: { type: GraphQLString },
    device_status: { type: GraphQLString }
  }
})

const soundboxSimActivationData = new GraphQLObjectType({
  name: 'soundboxSimActivationData',
  description: 'sound box sim activation details',
  fields: {
    order_id: { type: GraphQLString },
    operator: { type: GraphQLString },
    sim_number: { type: GraphQLString },
    amount: { type: GraphQLString }, // or GraphQLFloat if you prefer
    remarks: { type: GraphQLString },
    transaction_date_time: { type: GraphQLString },
    address: { type: GraphQLString }
  }
})

module.exports = new GraphQLObjectType({
  name: 'SoundBox',
  description: 'SoundBox Management',
  fields: {
    status: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    message: {
      type: new GraphQLNonNull(GraphQLString)
    },
    respcode: {
      type: GraphQLInt
    },
    action_code: {
      type: GraphQLInt
    },
    device_detail: {
      type: deviceDetail
    },
    vpaQrDownloadLink: {
      type: GraphQLJSON
    },
    selectedTransactionTypeData: {
      type: GraphQLJSON
    },
    updateSelectedTransactionType: {
      type: GraphQLJSON
    },
    sim_renewal: {
      type: GraphQLString
    },
    valid_until: {
      type: GraphQLString
    },
    expiry_message: {
      type: GraphQLString
    },
    sim_register: {
      type: GraphQLString
    },
    sim_number: {
      type: GraphQLString
    },
    title: {
      type: GraphQLString
    },
    sim_status: {
      type: GraphQLString
    },
    transaction_receipt_data: {
      type: new GraphQLList(soundboxSimActivationData)
    },
    receiptData: {
      type: GraphQLJSON
    },
    amount: {
      type: GraphQLFloat
    },
    transaction_status: {
      type: GraphQLString
    }
  }
})
