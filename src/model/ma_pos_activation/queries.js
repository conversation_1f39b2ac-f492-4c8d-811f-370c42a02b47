const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat
} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const posActivationController = require('../../controller/posActivation/posActivationController')

// Defines the queries
module.exports = {
  getActivatedPOSDevices: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      device_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: posActivationController.getActivatedPOSDevices.bind(posActivationController)
  },
  getPosReceipt: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      order_id: {
        type: GraphQLString
      },
      airpay_id: {
        type: GraphQLString
      }
    },
    resolve: posActivationController.getPosReceipt.bind(posActivationController)
  },
  getPosTransactionDetails: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      order_id: {
        type: GraphQLString
      },
      airpay_id: {
        type: GraphQLString
      },
      datefrom: {
        type: new GraphQLNonNull(GraphQLString)
      },
      dateto: {
        type: new GraphQLNonNull(GraphQLString)
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      transaction_type: {
        type: GraphQLString
      }
    },
    resolve: posActivationController.getPosTransactionDetails.bind(posActivationController)
  },
  checkSerialNo: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      serial_no: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: posActivationController.checkSerialNo.bind(posActivationController)
  },
  getPosDetailForMerchant: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: posActivationController.getPosDetailForMerchant.bind(posActivationController)
  },
  getDeviceTypeManual: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: posActivationController.getDeviceTypeManual.bind(posActivationController)
  },
  getposSDKdeviceDetails: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      app_type :{
        type : new GraphQLNonNull(GraphQLString)
      },
      app_version :{
        type : new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: posActivationController.getposSDKdeviceDetails.bind(posActivationController)
  }
}
