const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLFloat,
  GraphQLList
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')
const enumType = require('../commonEnum')
const scalarType = require('../commonScalar')

const posMerchantDetailsData = new GraphQLObjectType({
  name: 'posMerchantDetailsData',
  description: 'get pos details of merchants',
  fields: {
    device_type: {
      type: GraphQLString
    },
    pos_serial_no: {
      type: GraphQLString
    },
    airpay_mid: {
      type: GraphQLString
    },
    airpay_tid: {
      type: GraphQLString
    },
    activation_date: {
      type: GraphQLString
    },
    pos_enable: {
      type: GraphQLString
    },
    activation_required: {
      type: GraphQLString
    },
    activation_source: {
      type: GraphQLString
    }
  }
})

const deviceManualUrlData = new GraphQLObjectType({
  name: 'deviceManualUrlData',
  description: 'get Device Manual Urls',
  fields: {
    device_type: {
      type: GraphQLString
    },
    file_path: {
      type: GraphQLString
    }
  }
})


const deviceList = new GraphQLObjectType({
  name: 'deviceList',
  description: 'get Device details',
  fields: {
    device_type: {
      type: GraphQLString
    },
    serial_no: {
      type: GraphQLString
    },
    terminal_id: {
      type: GraphQLString
    }
  }
})

const posDetail = new GraphQLObjectType({
  name: 'POS',
  description: 'POS details',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    device_type: {
      type: GraphQLString
    },
    serial_no: {
      type: GraphQLString
    },
    device_status: {
      type: GraphQLString
    },
    posMerchantDetails: {
      type: GraphQLList(posMerchantDetailsData)
    },
    terminalid: {
      type: GraphQLString
    },
    deviceManualUrl: {
      type: GraphQLList(deviceManualUrlData)
    },
    steps_description: {
      type: GraphQLString
    },
    merchant_id: {
      type: GraphQLInt
    },
    file_path: {
      type: GraphQLString
    },
    pos_enable: {
      type: GraphQLString
    },
    device_list: {
      type: GraphQLList(deviceList)
    },
    nextFlag: {
      type: GraphQLBoolean
    },
    receipt_data: {
      type: GraphQLJSON
    },
    action_code: {
      type: GraphQLString
    },
    checkPosUpdate :{
      type: GraphQLJSON
    },
    updateReqiured :{
      type : GraphQLBoolean
    }
  }
})


module.exports = posDetail
