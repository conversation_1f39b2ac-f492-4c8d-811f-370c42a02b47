const {
  GraphQLID,
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLList,
  GraphQLBoolean
} = require('graphql')
const type = require('./type')
// const enumType = require('../commonEnum')
// const scalarType = require('../commonScalar')
const posActivationController = require('../../controller/posActivation/posActivationController')
const { GraphQLJSON } = require('graphql-type-json')

// Defines the mutations
module.exports = {
  posActivate: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      serial_no: {
        type: new GraphQLNonNull(GraphQLString)
      },
      isMobile: {
        type: GraphQLBoolean
      }
    },
    resolve: posActivationController.posActivate.bind(posActivationController)
  },
  enablePos: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      serial_no: {
        type: new GraphQLNonNull(GraphQLString)
      },
      isMobile: {
        type: GraphQLBoolean
      }
    },
    resolve: posActivationController.enablePos.bind(posActivationController)
  },
  updatePOSTransactionDetails: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      order_id: {
        type: GraphQLString
      },
      receipt_data: {
        type: GraphQLJSON
      }
    },
    resolve: posActivationController.updatePOSTransactionDetails.bind(posActivationController)
  }
}
