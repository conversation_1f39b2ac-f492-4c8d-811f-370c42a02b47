const {
  GraphQLID,
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList
} = require('graphql')
const commonEnum = require('../commonEnum')
const scalarType = require('../commonScalar')

const khatadata = new GraphQLObjectType({
  name: 'KhataBookAccountData',
  description: 'Khata Book Account Master Data',
  fields: {
    ma_kb_account_master_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    ma_user_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    userid: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    name: {
      type: GraphQLString
    },
    business_name: {
      type: new GraphQLNonNull(GraphQLString)
    },
    mobile: {
      type: GraphQLString
    },
    addedon: {
      type: new GraphQLNonNull(scalarType.dateScalar)
    }
  }
})

// Defines the type
module.exports = new GraphQLObjectType({
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>Account',
  description: 'Khata Book Account Master',
  fields: {
    ma_kb_account_master_id: {
      type: GraphQLInt
    },
    ma_user_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    userid: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    name: {
      type: GraphQLString
    },
    business_name: {
      type: new GraphQLNonNull(GraphQLString)
    },
    mobile: {
      type: GraphQLString
    },
    message: {
      type: GraphQLString
    },
    status: {
      type: GraphQLInt
    },
    addedon: {
      type: new GraphQLNonNull(scalarType.dateScalar)
    },
    respcode: {
      type: GraphQLInt
    },
    nextFlag: {
      type: GraphQLBoolean
    },
    khatadata: {
      type: new GraphQLList(khatadata)
    },
    action_code: {
      type: GraphQLInt
    }
  }
})
