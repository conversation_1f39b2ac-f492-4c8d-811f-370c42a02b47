const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList
} = require('graphql')
const commonEnum = require('../commonEnum')

// Defines the type
module.exports = new GraphQLObjectType({
  name: 'Umang',
  description: 'Umang',
  fields: {
    ma_user_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    userid: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    message: {
      type: GraphQLString
    },
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    umang_iframe_url: {
      type: GraphQLString
    },
    action_code: {
      type: GraphQLInt
    },
    department_name: {
      type: GraphQLString
    },
    service_name: {
      type: GraphQLString
    },
    department_id: {
      type: GraphQLInt
    },
    service_id: {
      type: GraphQLInt
    },
    ma_transaction_master_id: {
      type: GraphQLInt
    },
    agent_id: {
      type: GraphQLString
    },
    tracker_id: {
      type: GraphQLString
    },
    customer_name: {
      type: GraphQLString
    },
    address: {
      type: GraphQLString
    },
    transaction_time: {
      type: GraphQLString
    },
    transaction_reason: {
      type: GraphQLString
    },
    transaction_status: {
      type: commonEnum.maStatus
    },
    aggregator_order_id: {
      type: GraphQLString
    },
    aggregator_txn_id: {
      type: GraphQLString
    },
    amount: {
      type: GraphQLString
    }
  }
})
