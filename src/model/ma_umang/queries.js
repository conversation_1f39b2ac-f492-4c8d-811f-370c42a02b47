const {
  GraphQLInt,
  GraphQLNonNull,
  GraphQLString
} = require('graphql')
const type = require('./type')
const umang = require('../../controller/umang/umangController')
const commonEnum = require('../commonEnum')

module.exports = ({
  getUmangIframe: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: umang.getUmangIframe.bind(umang)
  },

  getUmangReceiptDetails: {
    type,
    args: {
      ma_transaction_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: umang.getUmangReceiptDetails.bind(umang)

  }
})
