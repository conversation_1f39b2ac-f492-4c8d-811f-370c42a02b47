const {
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull,

} = require('graphql')
const type = require('./type')
const umangController = require('../../controller/umang/umangController')

// Defines the mutations
module.exports = {
  submitPanApplication: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      full_name: {
        type: GraphQLString
      },
      mobile_number: {
        type: GraphQLString
      },
      dob: {
        type: GraphQLString
      },
      isNew: {
        type: GraphQLString
      },
      request_source: {
        type: GraphQLString
      }
    },
    resolve: umangController.submitPanApplication.bind(umangController)
  }
}
