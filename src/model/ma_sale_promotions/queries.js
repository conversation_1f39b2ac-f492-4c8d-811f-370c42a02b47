const {
  GraphQLString,
  GraphQLNonNull,
  GraphQLInt
} = require('graphql')
const type = require('./type')
const salePromotionsController = require('../../controller/salePromotions/salePromotionsController')
const commonEnum = require('../commonEnum')

module.exports = {
  offerBanners: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      banner_location: {
        type: GraphQLString
      }
    },
    resolve: salePromotionsController.offerBanners.bind(salePromotionsController)
  },
  commercialDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: salePromotionsController.commercialDetails.bind(salePromotionsController)
  }
}
