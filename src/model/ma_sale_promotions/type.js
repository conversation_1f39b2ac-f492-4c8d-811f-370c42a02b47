const {

  GraphQLString,
  GraphQLObjectType,
  GraphQLList,
  GraphQLInt
} = require('graphql')
const scalarType = require('../commonScalar')
const enumType = require('../commonEnum')
const offerBannersDetails = new GraphQLObjectType({

  name: 'offerBannersDetails',
  description: 'Offer Banner Details',
  fields: {
    imageid: {
      type: GraphQLString
    },
    imagepath: {
      type: GraphQLString
    },
    imagename: {
      type: GraphQLString
    },
    hyperlink: {
      type: GraphQLString
    },
    show_in_app: {
      type: enumType.YesNoEnum
    },
    show_in_web: {
      type: enumType.YesNoEnum
    },
    link_type: {
      type: enumType.LinkTypeEnum
    },
    campaign_id: {
      type: GraphQLInt
    },
    campaign_name: {
      type: GraphQLString
    }
  }
})

const commercialDetails = new GraphQLObjectType({
  name: 'commercialDetails',
  description: 'Commercial Details',
  fields: {
    title: {
      type: GraphQLString
    },
    pdf_path: {
      type: GraphQLString
    }
  }
})

const salePromotions = new GraphQLObjectType({

  name: 'salePromotions',
  description: 'Sale Promotions',
  fields: {
    status: {
      type: GraphQLString
    },
    respcode: {
      type: GraphQLString
    },
    message: {
      type: GraphQLString
    },
    offerBannersList: {
      type: GraphQLList(offerBannersDetails)
    },
    carouselChangeTime: {
      type: GraphQLString
    },
    commercialList: {
      type: commercialDetails
    }
  }

})

module.exports = salePromotions
