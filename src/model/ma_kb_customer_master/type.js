const {
  GraphQLID,
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList
} = require('graphql')
const commonEnum = require('../commonEnum')
const scalarType = require('../commonScalar')

const Customerdata = new GraphQLObjectType({
  name: 'KhataBookCustomerData',
  description: 'Khata Book Customer Master Data',
  fields: {
    ma_kb_customer_master_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    ma_kb_account_master_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    ma_user_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    userid: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    name: {
      type: GraphQLString
    },
    email: {
      type: GraphQLString
    },
    address1: {
      type: GraphQLString
    },
    address2: {
      type: GraphQLString
    },
    pincode: {
      type: GraphQLString
    },
    state_name: {
      type: GraphQLString
    },
    city_name: {
      type: GraphQLString
    },
    uuid: {
      type: new GraphQLNonNull(GraphQLString)
    },
    business_name: {
      type: new GraphQLNonNull(GraphQLString)
    },
    mobile: {
      type: GraphQLString
    },
    attachment: {
      type: GraphQLString
    },
    addedon: {
      type: new GraphQLNonNull(scalarType.dateScalar)
    }
  }
})

// Defines the type
module.exports = new GraphQLObjectType({
  name: 'KhataBookCustomer',
  description: 'Khata Book Customer Master',
  fields: {
    message: {
      type: GraphQLString
    },
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    nextFlag: {
      type: GraphQLBoolean
    },
    credit_given: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    credit_received: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    flag: {
      type: commonEnum.khataCreditType
    },
    final_bal: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    Customerdata: {
      type: new GraphQLList(Customerdata)
    },
    action_code: {
      type: GraphQLInt
    }
  }
})
