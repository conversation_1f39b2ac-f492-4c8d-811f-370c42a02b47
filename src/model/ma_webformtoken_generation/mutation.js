const {
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull,
  DateTime,
  // GraphQLID,
  GraphQLFloat
  // GraphQLList

} = require('graphql')
const type = require('./type')
const webformtokenContoller = require('../../controller/webformtoken/webformtokenController')

// Defines the mutations
module.exports = {
  getCaptcha: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      form_type: {
        type: GraphQLString
      }
    },
    resolve: webformtokenContoller.getCaptcha.bind(webformtokenContoller)
  }
}
