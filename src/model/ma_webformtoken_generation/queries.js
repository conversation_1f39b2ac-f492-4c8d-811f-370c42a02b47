const {
  GraphQLNonNull,
  GraphQLInt,
  GraphQLString,
  graphql
} = require('graphql')
const type = require('./type')
const webformtokenController = require('../../controller/webformtoken/webformtokenController')

// Defines the queries
module.exports = {
  verifyCaptchaApp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      form_type: {
        type: GraphQLString
      },
      token: {
        type: GraphQLString
      }
    },
    resolve: webformtokenController.verifyCaptchaApp.bind(webformtokenController)
  }

}
