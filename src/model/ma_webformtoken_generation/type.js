const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')

const captchaGeneration = new GraphQLObjectType({
  name: 'captchaGeneration',
  description: 'Captcha Generation response',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    sessionId: {
      type: GraphQLString
    }
  }
  /* dddd :{
          type: [GraphQLString]
        }, */

})

module.exports = captchaGeneration
