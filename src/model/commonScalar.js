const { GraphQLScalarType } = require('graphql')
const { GraphQLError } = require('graphql')

const accountScalar = new GraphQLScalarType({
  name: 'accountNo',
  description: 'This is custom scalar account Number',
  // Replaces the string upto last 4 digits with a '*'
  serialize (value) {
    value = value.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    return ast.value
  }
})

const dateScalar = new GraphQLScalarType({
  name: 'date',
  description: 'This is custom scalar for datetime formatting',
  serialize (value) {
    if (/^(\d{2})-(\d{2})-(\d{4}) (\d{2}):(\d{2}):(\d{2}) (AM|PM)$/.test(value)) {
      return value
    }

    var today = new Date(value)
    var cDate = today.getDate()
    var cMonth = today.getMonth() + 1
    var cYear = today.getFullYear()
    if (cDate < 10) {
      cDate = `0${cDate}`
    }
    if (cMonth < 10) {
      cMonth = `0${cMonth}`
    }
    var cHour = today.getHours()
    var cMin = today.getMinutes()
    var cSec = today.getSeconds()

    if (cHour < 10) {
      cHour = `0${cHour}`
    }
    if (cMin < 10) {
      cMin = `0${cMin}`
    }

    if (cSec < 10) {
      cSec = `0${cSec}`
    }

    value = `${cDate}-${cMonth}-${cYear} ${cHour}:${cMin}:${cSec}`
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    return ast.value
  }
})

const amountScalar = new GraphQLScalarType({
  name: 'amount',
  description: 'This is custom scalar for amount with 2 decimal formatting',
  serialize (value) {
    value = isNaN(value) ? 0 : (+value).toFixed(2)
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    const res = isNaN(ast.value)
    if (res) {
      throw new GraphQLError('Enter valid amount')
    } else {
      if (ast.value > 0 && ast.value <= 99999999.9999) {
        return parseFloat(ast.value)
        // return ast.value
      } else {
        throw new GraphQLError('Enter amount greater than 0 and less than 99999999.9999')
      }
    }
  }
})

const amountScalarNew = new GraphQLScalarType({
  name: 'amountNew',
  description: 'This is custom scalar for amount with 2 decimal formatting',
  serialize (value) {
    return isNaN(value) ? 0 : (+value).toFixed(2)
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    const res = isNaN(ast.value)
    if (res) {
      throw new GraphQLError('Enter valid amount')
    } else {
      if (ast.value >= 0 && ast.value <= 99999999.9999) {
        return parseFloat(ast.value)
        // return ast.value
      } else {
        throw new GraphQLError('Enter amount greater than or equal to 0 and less than 99999999.9999')
      }
    }
  }
})


const bbpsamountScalar = new GraphQLScalarType({
  name: 'amountbbps',
  description: 'This is custom scalar for amount with 2 decimal formatting',
  serialize (value) {
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    const res = isNaN(ast.value)
    if (res) {
      throw new GraphQLError('Enter valid amount')
    } else {
      if (ast.value > 0) {
        return ast.value
        // return ast.value
      } else {
        throw new GraphQLError('Enter amount greater than 0')
      }
    }
  }
})

const mobileScalar = new GraphQLScalarType({
  name: 'Mobile',
  description: 'This is custom scalar for numeric validation of mobile number',
  serialize (value) {
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    const mobileFormat = /^[0-9]{3,15}$/
    if (mobileFormat.test(ast.value)) {
      return ast.value
    }
    throw new GraphQLError('Invalid Mobile number')
  }
})

const onlyDateScalar = new GraphQLScalarType({
  name: 'onlyDateScalar',
  description: 'This is custom scalar for date formatting',
  serialize (value) {
    var today = new Date(value)
    var cDate = today.getDate()
    var cMonth = today.getMonth() + 1
    var cYear = today.getFullYear()
    if (cDate < 10) {
      cDate = `0${cDate}`
    }
    if (cMonth < 10) {
      cMonth = `0${cMonth}`
    }
    var cHour = today.getHours()
    var cMin = today.getMinutes()
    var cSec = today.getSeconds()
    value = `${cDate}-${cMonth}-${cYear}`
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    return ast.value
  }
})

const rechargeTypeScalar = new GraphQLScalarType({
  name: 'rechargeTypeScalar',
  description: 'This is custom scalar for rechargeType',
  serialize (value) {
    return value
  },
  parseValue (value) {
    return value
  },
  parseLiteral (ast) {
    switch (ast.value) {
      case 'Mobile':
      case 'DTH':
        return ast.value
      default:
        throw new GraphQLError('Invalid Recharge type')
    }
  }
})

module.exports = { accountScalar, dateScalar, amountScalar, amountScalarNew, bbpsamountScalar, mobileScalar, onlyDateScalar, rechargeTypeScalar }
