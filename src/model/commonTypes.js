const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLInt,
  GraphQLList,
  GraphQLBoolean
} = require('graphql')
const commonEnum = require('./commonEnum')
const scalarType = require('./commonScalar')

const tryWithOtherBankModeList = new GraphQLObjectType({
  name: 'tryWithOtherBankModeList',
  description: 'transfer Mode List',
  fields: {
    ma_bank_type_id: {
      type: GraphQLInt
    },
    transaction_type: {
      type: commonEnum.transferMode
    },
    priority: {
      type: GraphQLInt
    }
  }
})

const tryWithOtherBankList = new GraphQLObjectType({
  name: 'tryWithOtherBankList',
  description: 'try With Other Bank List',
  fields: {
    bank_name: {
      type: GraphQLString
    },
    bank_logo: {
      type: GraphQLString
    },
    dailyLimit: {
      type: GraphQLInt
    },
    monthlyLimit: {
      type: GraphQLInt
    },
    remainingLimit: {
      type: GraphQLInt
    },
    consumedLimit: {
      type: GraphQLInt
    },
    bankRegisterStatus: {
      type: commonEnum.remitterBoardingStatus
    },
    bank_otp: {
      type: commonEnum.bankOTPRequired
    },
    transfer_mode: {
      type: new GraphQLList(tryWithOtherBankModeList)
    },
    ma_bank_on_boarding_id: {
      type: GraphQLInt
    },
    isBeneMobileMandatory: {
      type: GraphQLBoolean
    }
  }
})

module.exports = {
  tryWithOtherBankList
}
