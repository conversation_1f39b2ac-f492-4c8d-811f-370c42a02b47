const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLInt,
  GraphQLList
} = require('graphql')

const enumType = require('../commonEnum')
const noticesList = new GraphQLObjectType({
  name: 'noticesList',
  description: 'Notices Dataset',
  fields: {
    noticetype: {
      type: enumType.noticeType
    },
    ma_user_id: {
      type: GraphQLInt
    },
    displaytype: {
      type: enumType.displayType
    },
    messageText: {
      type: GraphQLString
    },
    url: {
      type: GraphQLString
    },
    notice_flag: {
      type: enumType.noticeFlag
    },
    addedon: {
      type: GraphQLString
    },
    link_type: {
      type: enumType.LinkTypeEnum
    }
  }
})
// Defines the type
module.exports = new GraphQLObjectType({
  name: 'Notice',
  description: 'Notices and marquee',
  fields: {
    noticetype: {
      type: enumType.noticeType
    },
    ma_user_id: {
      type: GraphQLInt
    },
    displaytype: {
      type: enumType.displayType
    },
    messageText: {
      type: Graph<PERSON>String
    },
    status: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    respcode: {
      type: GraphQLInt
    },
    notice_flag: {
      type: enumType.noticeFlag
    },
    list: {
      type: new GraphQLList(noticesList)
    },
    count: {
      type: GraphQLString
    }
  }
})
