const {
  GraphQLString,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const type = require('./type')
const enumType = require('../commonEnum')
const notice = require('../../controller/notice/noticeController')

module.exports = ({
  getNotices: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: GraphQLInt
      },
      displaytype: {
        type: enumType.displayType
      },
      limit: {
        type: GraphQLInt
      },
      offset: {
        type: GraphQLInt
      }
    },
    resolve: notice.getNotices.bind(notice)
  }

})
