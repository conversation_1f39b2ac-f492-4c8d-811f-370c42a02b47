const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt
} = require('graphql')
const type = require('./type')
const khataBook = require('../../controller/khataBook/khataBookController')
const commonEnum = require('../commonEnum')
const commonScalar = require('../commonScalar')

module.exports = ({
  createTransaction: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      transaction_date: {
        type: new GraphQLNonNull(GraphQLString)
      },
      description: {
        type: GraphQLString
      },
      attachment: {
        type: GraphQLString
      },
      uploadid: {
        type: GraphQLInt
      },
      bill_no: {
        type: GraphQLString
      },
      credit_type: {
        type: commonEnum.khataCreditType
      }
    },
    resolve: khataBook.createTransaction.bind(khataBook)
  },
  editTransaction: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_kb_transaction_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      transaction_date: {
        type: new GraphQLNonNull(GraphQLString)
      },
      description: {
        type: GraphQLString
      },
      attachment: {
        type: GraphQLString
      },
      uploadid: {
        type: GraphQLInt
      }
    },
    resolve: khataBook.editTransaction.bind(khataBook)
  },
  deleteTransaction: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_kb_transaction_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: khataBook.deleteTransaction.bind(khataBook)
  }
})