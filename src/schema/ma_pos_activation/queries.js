const { GraphQLObjectType } = require('graphql')
const posQueries = require('../../model/ma_pos_activation/queries')

module.exports = new GraphQLObjectType({
  name: 'RootQueryType',
  fields: {
    getActivatedPOSDevices: posQueries.getActivatedPOSDevices,
    getPosReceipt: posQueries.getPosReceipt,
    getPosTransactionDetails: posQueries.getPosTransactionDetails,
    checkSerialNo: posQueries.checkSerialNo,
    getDeviceTypeManual: posQueries.getDeviceTypeManual,
    getPosDetailForMerchant: posQueries.getPosDetailForMerchant,
    getposSDKdeviceDetails : posQueries.getposSDKdeviceDetails
  }
})
