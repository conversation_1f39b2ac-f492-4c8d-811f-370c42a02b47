const { GraphQLObjectType } = require('graphql')
const ticketsQueries = require('../../model/ma_insurance/queries')
const policyQueries = require('../../model/ma_user_policy_details/queries')

module.exports = new GraphQLObjectType({
  name: 'RootQueryType',
  fields: {
    getTicketsListing: ticketsQueries.getTicketsListing,
    getTicketTypeList: ticketsQueries.getTicketTypeList,
    checkActivePolicy: policyQueries.checkActivePolicy,
    getPolicyPlans: policyQueries.getPolicyPlans,
    getInsuranceList: ticketsQueries.getInsuranceList,
    checkActivePolicies: ticketsQueries.checkActivePolicies,
    getDynamicForm: ticketsQueries.getDynamicForm,
    riskcovryTransactionDetails: ticketsQueries.riskcovryTransactionDetails,
    iciciLombardCheckActivePolicy: ticketsQueries.iciciLombardCheckActivePolicy,
    iciciLombardGetDynamicForm: ticketsQueries.iciciLombardGetDynamicForm,
    lombardTransactionDetails: ticketsQueries.lombardTransactionDetails,
    starHealthGetDynamicForm: ticketsQueries.starHealthGetDynamicForm,
  }
})
