const { GraphQLObjectType } = require('graphql')
const insuranceMutation = require('../../model/ma_insurance/mutations')
const policyMutations = require('../../model/ma_user_policy_details/mutations')

module.exports = new GraphQLObjectType({
  name: 'RootMutationsType',
  fields: {
    addInsUser: insuranceMutation.addInsUser,
    updateInsRecon: insuranceMutation.updateInsRecon,
    premiumDeduction: insuranceMutation.premiumDeduction,
    policyConfirmation: insuranceMutation.policyConfirmation,
    orderVerification: insuranceMutation.orderVerification,
    calculatePremium: policyMutations.calculatePremium,
    doPolicyTransaction: policyMutations.doPolicyTransaction,
    verifyKotakOtp: policyMutations.verifyKotakOtp,
    resentSuccessSMS: policyMutations.resentSuccessSMS,
    resentKotakOtp: policyMutations.resentKotakOtp,
    riskcovrySachetInitiateTransaction: insuranceMutation.riskcovrySachetInitiateTransaction,
    riskcovryCreateOrder: insuranceMutation.riskcovryCreateOrder,
    lombardCreateOrder: insuranceMutation.lombardCreateOrder,
    iciciLombardSachetInitiateTransaction:insuranceMutation.iciciLombardSachetInitiateTransaction,
    starHealthCreateOrder: insuranceMutation.starHealthCreateOrder,
    starHealthSachetInitiateTransaction:insuranceMutation.starHealthSachetInitiateTransaction
  }
})
